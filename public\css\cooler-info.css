.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.image-preview {
    transition: all 0.3s ease;
}

.image-preview:hover {
    transform: scale(1.05);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 表格容器优化 */
.table-wrapper {
    width: 100%;
    overflow-x: auto;
    margin-bottom: 1rem;
}

/* 修复表格溢出问题 */
.table-wrapper table {
    width: 100%;
    table-layout: fixed;
}

/* 型号列样式调整 - 增加宽度并允许内容完整显示 */
.cooler-table th.col-model,
.cooler-table td:first-child {
    width: 300px;
    min-width: 300px;
    max-width: 300px;
    white-space: normal;
    word-wrap: break-word;
    word-break: break-all;
}

/* 保持操作列宽度 */
.cooler-table th.col-action,
.action-column {
    width: 130px;
    min-width: 130px;
}

/* 其他列采用自适应宽度 */
.cooler-table th:not(.col-model):not(.col-action),
.cooler-table td:not(:first-child):not(.action-column) {
    width: auto;
}

/* 型号内容样式 */
.model-text {
    font-weight: medium;
    line-height: 1.3;
    padding: 2px 0;
}

/* 表格外部容器阴影效果加强 */
.table-container {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border-radius: 0.5rem;
    background-color: white;
}

/* 暗黑模式样式 */
.dark-mode {
    --bg-primary: #121212;
    --bg-secondary: #1e1e1e;
    --bg-card: #252525;
    --bg-input: #2a2a2a;
    --text-primary: #f5f5f5;
    --text-secondary: #a0aec0;
    --border-color: #333333;
    --input-bg: #2a2a2a;
    --input-text: #e2e8f0;
    --button-primary-bg: #2563eb;
    --button-primary-hover: #1d4ed8;
    --button-primary-text: #ffffff;
    --highlight-color: #3b82f6;
    --accent-color: #8b5cf6;
    --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

body.dark-mode {
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.dark-mode .bg-white {
    background-color: var(--bg-secondary);
}

.dark-mode .bg-gray-50 {
    background-color: var(--bg-primary);
}

.dark-mode .bg-gray-100 {
    background-color: #1a1a1a;
}

.dark-mode .text-gray-700,
.dark-mode .text-gray-800,
.dark-mode .text-gray-900 {
    color: var(--text-primary);
}

.dark-mode .text-gray-500,
.dark-mode .text-gray-600 {
    color: var(--text-secondary);
}

.dark-mode .border-gray-200,
.dark-mode .border-gray-300 {
    border-color: var(--border-color);
}

.dark-mode input,
.dark-mode select,
.dark-mode textarea {
    background-color: var(--input-bg);
    color: var(--input-text);
    border-color: var(--border-color);
}

.dark-mode .shadow-md {
    box-shadow: var(--card-shadow);
}

.dark-mode .bg-cyan-600 {
    background-color: #0284c7;
}

.dark-mode .hover\:bg-cyan-700:hover {
    background-color: #0369a1;
}

.dark-mode .text-cyan-700 {
    color: #0ea5e9;
}

.dark-mode .text-cyan-800 {
    color: #0ea5e9;
}

/* 表格美化 */
.dark-mode table thead {
    background-color: #1a1a1a;
}

.dark-mode table th {
    color: #94a3b8;
}

.dark-mode table tbody tr {
    border-color: #333;
    border-bottom: none;
    /* 移除表格行的底部边框 */
}

.dark-mode table tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.03);
}

/* 移除表格所有横线 */
.dark-mode .divide-y,
.dark-mode table,
.dark-mode tbody,
.dark-mode tr {
    border: none !important;
}

.dark-mode table tr:not(:last-child) {
    border-bottom: none !important;
}

.dark-mode .border-b,
.dark-mode .border-t {
    border-top: none !important;
    border-bottom: none !important;
}

/* 替代横线的视觉分隔 - 使用更微妙的行间距和悬停效果 */
.dark-mode table tbody tr {
    padding: 8px 0;
    transition: background-color 0.15s ease-in-out;
}

.dark-mode table tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

/* 表单美化 */
.dark-mode input:focus,
.dark-mode select:focus,
.dark-mode textarea:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
}

/* 按钮美化 */
.dark-mode button[type="submit"],
.dark-mode .bg-cyan-600 {
    background-color: var(--button-primary-bg);
    transition: all 0.2s ease;
}

.dark-mode button[type="submit"]:hover,
.dark-mode .bg-cyan-600:hover {
    background-color: var(--button-primary-hover);
    transform: translateY(-1px);
}

/* 卡片边框和阴影美化 */
.dark-mode .rounded-lg {
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.dark-mode .shadow-md {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
}

/* 模态框美化 */
.dark-mode #coolerModal .bg-white {
    background-color: var(--bg-card);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

/* 标签样式调整 */
.dark-mode .spec-tag {
    opacity: 0.85;
}

/* 暗黑模式下的特殊元素 */
.dark-mode #darkModeToggle {
    background-color: #fbbf24;
    color: #92400e;
}

.dark-mode #darkModeToggle:hover {
    background-color: #f59e0b;
}

/* 智能识别输入区域暗夜模式样式 */
.dark-mode .border-gray-200 {
    border-color: #333 !important;
}

.dark-mode .bg-cyan-50 {
    background-color: rgba(6, 182, 212, 0.08) !important;
    border-color: rgba(6, 182, 212, 0.2) !important;
}

.dark-mode #smartInput {
    background-color: #2a2a2a;
    color: #e2e8f0;
    border-color: #4a5568;
}

.dark-mode #autoFillBtn {
    background-color: #0284c7;
}

.dark-mode #autoFillBtn:hover {
    background-color: #0369a1;
}

.dark-mode .text-cyan-600 {
    color: #22d3ee !important;
}

/* 暗黑模式过渡效果 */
body {
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* 全局表格修复 */
.table-wrapper {
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;
}

/* 确保内容不会被裁剪 */
* {
    box-sizing: border-box;
}

.container {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
}

/* 表格容器内部元素修复 */
.table-wrapper table {
    width: 100%;
}

/* 文本截断样式 */
.text-truncate {
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: calc(100% - 10px) !important;
    display: inline-block !important;
}

/* 表格样式 */
.table-compact td {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}

.model-column {
    min-width: 180px;
}

.action-column {
    min-width: 130px !important;
    width: 130px !important;
}

/* 标签样式 */
.spec-tag {
    padding: 3px 8px !important;
    border-radius: 4px !important;
    font-size: 0.75rem !important;
    font-weight: 500 !important;
    line-height: 1.5 !important;
    display: inline-block !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: fit-content !important;
    margin: 2px 4px 2px 0 !important;
    transition: all 0.2s ease;
}

.spec-tag:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.spec-tag.socket {
    background-color: rgba(180, 83, 9, 0.1) !important;
    color: #b45309 !important;
    border: 1px solid rgba(180, 83, 9, 0.2) !important;
}

.spec-tag.cores {
    background-color: rgba(22, 101, 52, 0.1) !important;
    color: #166534 !important;
    border: 1px solid rgba(22, 101, 52, 0.2) !important;
}

.spec-tag.freq {
    background-color: rgba(76, 29, 149, 0.1) !important;
    color: #4c1d95 !important;
    border: 1px solid rgba(76, 29, 149, 0.2) !important;
}

.spec-tag.process {
    background-color: rgba(6, 95, 70, 0.1) !important;
    color: #065f46 !important;
    border: 1px solid rgba(6, 95, 70, 0.2) !important;
}

.spec-tag.tdp {
    background-color: rgba(194, 65, 12, 0.1) !important;
    color: #c2410c !important;
    border: 1px solid rgba(194, 65, 12, 0.2) !important;
}

.spec-tag.noise {
    background-color: rgba(79, 70, 229, 0.1) !important;
    color: #4f46e5 !important;
    border: 1px solid rgba(79, 70, 229, 0.2) !important;
}

.spec-tag.height {
    background-color: rgba(16, 185, 129, 0.1) !important;
    color: #10b981 !important;
    border: 1px solid rgba(16, 185, 129, 0.2) !important;
}

.spec-tag.fan {
    background-color: rgba(219, 39, 119, 0.1) !important;
    color: #db2777 !important;
    border: 1px solid rgba(219, 39, 119, 0.2) !important;
}

.spec-tag.heatpipe {
    background-color: rgba(217, 119, 6, 0.1) !important;
    color: #d97706 !important;
    border: 1px solid rgba(217, 119, 6, 0.2) !important;
}

/* 品牌标签 */
.cooler-badge {
    padding: 4px 10px !important;
    font-weight: 600 !important;
    border-radius: 4px !important;
    display: inline-block !important;
    transition: transform 0.2s ease;
    white-space: nowrap !important;
}

.cooler-badge:hover {
    transform: scale(1.05);
}

/* 确保品牌样式优先级最高 */
span.cooler-badge {
    padding: 4px 10px !important;
    font-weight: 600 !important;
    border-radius: 4px !important;
    display: inline-block !important;
    white-space: nowrap !important;
}

/* 不同品牌对应不同颜色 - 亮色模式 */
.cooler-brand-noctua,
span.cooler-brand-noctua {
    background-color: rgba(161, 98, 7, 0.1) !important;
    color: #854d0e !important;
    border: 1px solid rgba(161, 98, 7, 0.2) !important;
}

.cooler-brand-deepcool,
span.cooler-brand-deepcool {
    background-color: rgba(2, 132, 199, 0.1) !important;
    color: #0284c7 !important;
    border: 1px solid rgba(2, 132, 199, 0.2) !important;
}

.cooler-brand-coolermaster,
span.cooler-brand-coolermaster {
    background-color: rgba(124, 58, 237, 0.1) !important;
    color: #7c3aed !important;
    border: 1px solid rgba(124, 58, 237, 0.2) !important;
}

.cooler-brand-corsair,
span.cooler-brand-corsair {
    background-color: rgba(234, 179, 8, 0.1) !important;
    color: #a16207 !important;
    border: 1px solid rgba(234, 179, 8, 0.2) !important;
}

.cooler-brand-thermalright,
span.cooler-brand-thermalright {
    background-color: rgba(239, 68, 68, 0.1) !important;
    color: #dc2626 !important;
    border: 1px solid rgba(239, 68, 68, 0.2) !important;
}

.cooler-brand-idcooling,
span.cooler-brand-idcooling {
    background-color: rgba(16, 185, 129, 0.1) !important;
    color: #059669 !important;
    border: 1px solid rgba(16, 185, 129, 0.2) !important;
}

.cooler-brand-scythe,
span.cooler-brand-scythe {
    background-color: rgba(139, 92, 246, 0.1) !important;
    color: #8b5cf6 !important;
    border: 1px solid rgba(139, 92, 246, 0.2) !important;
}

.cooler-brand-arctic,
span.cooler-brand-arctic {
    background-color: rgba(6, 182, 212, 0.1) !important;
    color: #0891b2 !important;
    border: 1px solid rgba(6, 182, 212, 0.2) !important;
}

.cooler-brand-thermaltake,
span.cooler-brand-thermaltake {
    background-color: rgba(239, 68, 68, 0.1) !important;
    color: #dc2626 !important;
    border: 1px solid rgba(239, 68, 68, 0.2) !important;
}

.cooler-brand-amd,
span.cooler-brand-amd {
    background-color: rgba(239, 68, 68, 0.1) !important;
    color: #dc2626 !important;
    border: 1px solid rgba(239, 68, 68, 0.2) !important;
}

.cooler-brand-intel,
span.cooler-brand-intel {
    background-color: rgba(2, 132, 199, 0.1) !important;
    color: #0284c7 !important;
    border: 1px solid rgba(2, 132, 199, 0.2) !important;
}

.cooler-brand-aigo,
span.cooler-brand-aigo {
    background-color: rgba(239, 68, 68, 0.1) !important;
    color: #dc2626 !important;
    border: 1px solid rgba(239, 68, 68, 0.2) !important;
}

.cooler-brand-huntkey,
span.cooler-brand-huntkey {
    background-color: rgba(16, 185, 129, 0.1) !important;
    color: #059669 !important;
    border: 1px solid rgba(16, 185, 129, 0.2) !important;
}

.cooler-brand-peninsula,
span.cooler-brand-peninsula {
    background-color: rgba(124, 58, 237, 0.1) !important;
    color: #7c3aed !important;
    border: 1px solid rgba(124, 58, 237, 0.2) !important;
}

.cooler-brand-msi,
span.cooler-brand-msi {
    background-color: rgba(239, 68, 68, 0.1) !important;
    color: #dc2626 !important;
    border: 1px solid rgba(239, 68, 68, 0.2) !important;
}

.cooler-brand-aoc,
span.cooler-brand-aoc {
    background-color: rgba(2, 132, 199, 0.1) !important;
    color: #0284c7 !important;
    border: 1px solid rgba(2, 132, 199, 0.2) !important;
}

.cooler-brand-valkyrie,
span.cooler-brand-valkyrie {
    background-color: rgba(139, 92, 246, 0.1) !important;
    color: #8b5cf6 !important;
    border: 1px solid rgba(139, 92, 246, 0.2) !important;
}

.cooler-brand-segotep,
span.cooler-brand-segotep {
    background-color: rgba(234, 179, 8, 0.1) !important;
    color: #a16207 !important;
    border: 1px solid rgba(234, 179, 8, 0.2) !important;
}

.cooler-brand-tantalum,
span.cooler-brand-tantalum {
    background-color: rgba(6, 182, 212, 0.1) !important;
    color: #0891b2 !important;
    border: 1px solid rgba(6, 182, 212, 0.2) !important;
}

.cooler-brand-krypton,
span.cooler-brand-krypton {
    background-color: rgba(16, 185, 129, 0.1) !important;
    color: #059669 !important;
    border: 1px solid rgba(16, 185, 129, 0.2) !important;
}

.cooler-brand-coolio,
span.cooler-brand-coolio {
    background-color: rgba(219, 39, 119, 0.1) !important;
    color: #db2777 !important;
    border: 1px solid rgba(219, 39, 119, 0.2) !important;
}

.cooler-brand-jonsbo,
span.cooler-brand-jonsbo {
    background-color: rgba(124, 58, 237, 0.1) !important;
    color: #7c3aed !important;
    border: 1px solid rgba(124, 58, 237, 0.2) !important;
}

.cooler-brand-lianli,
span.cooler-brand-lianli {
    background-color: rgba(6, 182, 212, 0.1) !important;
    color: #0891b2 !important;
    border: 1px solid rgba(6, 182, 212, 0.2) !important;
}

.cooler-brand-geometric,
span.cooler-brand-geometric {
    background-color: rgba(139, 92, 246, 0.1) !important;
    color: #8b5cf6 !important;
    border: 1px solid rgba(139, 92, 246, 0.2) !important;
}

.cooler-brand-other,
span.cooler-brand-other {
    background-color: rgba(107, 114, 128, 0.1) !important;
    color: #4b5563 !important;
    border: 1px solid rgba(107, 114, 128, 0.2) !important;
}

/* 暗黑模式下的品牌颜色 */
.dark-mode .cooler-brand-noctua,
.dark-mode span.cooler-brand-noctua {
    background-color: rgba(217, 119, 6, 0.15) !important;
    color: #fbbf24 !important;
    border: 1px solid rgba(217, 119, 6, 0.3) !important;
}

.dark-mode .cooler-brand-deepcool,
.dark-mode span.cooler-brand-deepcool {
    background-color: rgba(59, 130, 246, 0.15) !important;
    color: #60a5fa !important;
    border: 1px solid rgba(59, 130, 246, 0.3) !important;
}

.dark-mode .cooler-brand-coolermaster,
.dark-mode span.cooler-brand-coolermaster {
    background-color: rgba(168, 85, 247, 0.15) !important;
    color: #c084fc !important;
    border: 1px solid rgba(168, 85, 247, 0.3) !important;
}

.dark-mode .cooler-brand-corsair,
.dark-mode span.cooler-brand-corsair {
    background-color: rgba(251, 191, 36, 0.15) !important;
    color: #fcd34d !important;
    border: 1px solid rgba(251, 191, 36, 0.3) !important;
}

.dark-mode .cooler-brand-thermalright,
.dark-mode span.cooler-brand-thermalright {
    background-color: rgba(248, 113, 113, 0.15) !important;
    color: #f87171 !important;
    border: 1px solid rgba(248, 113, 113, 0.3) !important;
}

.dark-mode .cooler-brand-idcooling,
.dark-mode span.cooler-brand-idcooling {
    background-color: rgba(52, 211, 153, 0.15) !important;
    color: #34d399 !important;
    border: 1px solid rgba(52, 211, 153, 0.3) !important;
}

.dark-mode .cooler-brand-scythe,
.dark-mode span.cooler-brand-scythe {
    background-color: rgba(196, 181, 253, 0.15) !important;
    color: #c4b5fd !important;
    border: 1px solid rgba(196, 181, 253, 0.3) !important;
}

.dark-mode .cooler-brand-arctic,
.dark-mode span.cooler-brand-arctic {
    background-color: rgba(34, 211, 238, 0.15) !important;
    color: #22d3ee !important;
    border: 1px solid rgba(34, 211, 238, 0.3) !important;
}

.dark-mode .cooler-brand-thermaltake,
.dark-mode span.cooler-brand-thermaltake {
    background-color: rgba(248, 113, 113, 0.15) !important;
    color: #f87171 !important;
    border: 1px solid rgba(248, 113, 113, 0.3) !important;
}

.dark-mode .cooler-brand-amd,
.dark-mode span.cooler-brand-amd {
    background-color: rgba(248, 113, 113, 0.15) !important;
    color: #f87171 !important;
    border: 1px solid rgba(248, 113, 113, 0.3) !important;
}

.dark-mode .cooler-brand-intel,
.dark-mode span.cooler-brand-intel {
    background-color: rgba(59, 130, 246, 0.15) !important;
    color: #60a5fa !important;
    border: 1px solid rgba(59, 130, 246, 0.3) !important;
}

.dark-mode .cooler-brand-aigo,
.dark-mode span.cooler-brand-aigo {
    background-color: rgba(248, 113, 113, 0.15) !important;
    color: #f87171 !important;
    border: 1px solid rgba(248, 113, 113, 0.3) !important;
}

.dark-mode .cooler-brand-huntkey,
.dark-mode span.cooler-brand-huntkey {
    background-color: rgba(52, 211, 153, 0.15) !important;
    color: #34d399 !important;
    border: 1px solid rgba(52, 211, 153, 0.3) !important;
}

.dark-mode .cooler-brand-peninsula,
.dark-mode span.cooler-brand-peninsula {
    background-color: rgba(168, 85, 247, 0.15) !important;
    color: #c084fc !important;
    border: 1px solid rgba(168, 85, 247, 0.3) !important;
}

.dark-mode .cooler-brand-msi,
.dark-mode span.cooler-brand-msi {
    background-color: rgba(248, 113, 113, 0.15) !important;
    color: #f87171 !important;
    border: 1px solid rgba(248, 113, 113, 0.3) !important;
}

.dark-mode .cooler-brand-aoc,
.dark-mode span.cooler-brand-aoc {
    background-color: rgba(59, 130, 246, 0.15) !important;
    color: #60a5fa !important;
    border: 1px solid rgba(59, 130, 246, 0.3) !important;
}

.dark-mode .cooler-brand-valkyrie,
.dark-mode span.cooler-brand-valkyrie {
    background-color: rgba(196, 181, 253, 0.15) !important;
    color: #c4b5fd !important;
    border: 1px solid rgba(196, 181, 253, 0.3) !important;
}

.dark-mode .cooler-brand-segotep,
.dark-mode span.cooler-brand-segotep {
    background-color: rgba(251, 191, 36, 0.15) !important;
    color: #fcd34d !important;
    border: 1px solid rgba(251, 191, 36, 0.3) !important;
}

.dark-mode .cooler-brand-tantalum,
.dark-mode span.cooler-brand-tantalum {
    background-color: rgba(34, 211, 238, 0.15) !important;
    color: #22d3ee !important;
    border: 1px solid rgba(34, 211, 238, 0.3) !important;
}

.dark-mode .cooler-brand-krypton,
.dark-mode span.cooler-brand-krypton {
    background-color: rgba(52, 211, 153, 0.15) !important;
    color: #34d399 !important;
    border: 1px solid rgba(52, 211, 153, 0.3) !important;
}

.dark-mode .cooler-brand-coolio,
.dark-mode span.cooler-brand-coolio {
    background-color: rgba(244, 114, 182, 0.15) !important;
    color: #f472b6 !important;
    border: 1px solid rgba(244, 114, 182, 0.3) !important;
}

.dark-mode .cooler-brand-jonsbo,
.dark-mode span.cooler-brand-jonsbo {
    background-color: rgba(168, 85, 247, 0.15) !important;
    color: #c084fc !important;
    border: 1px solid rgba(168, 85, 247, 0.3) !important;
}

.dark-mode .cooler-brand-lianli,
.dark-mode span.cooler-brand-lianli {
    background-color: rgba(34, 211, 238, 0.15) !important;
    color: #22d3ee !important;
    border: 1px solid rgba(34, 211, 238, 0.3) !important;
}

.dark-mode .cooler-brand-geometric,
.dark-mode span.cooler-brand-geometric {
    background-color: rgba(196, 181, 253, 0.15) !important;
    color: #c4b5fd !important;
    border: 1px solid rgba(196, 181, 253, 0.3) !important;
}

.dark-mode .cooler-brand-other,
.dark-mode span.cooler-brand-other {
    background-color: rgba(156, 163, 175, 0.15) !important;
    color: #d1d5db !important;
    border: 1px solid rgba(156, 163, 175, 0.3) !important;
}

/* 类型标签 */
.cooler-type {
    font-weight: 500 !important;
    padding: 3px 8px !important;
    border-radius: 4px !important;
    display: inline-block !important;
}

.cooler-type-air {
    background-color: rgba(6, 182, 212, 0.1) !important;
    color: #0891b2 !important;
    border: 1px solid rgba(6, 182, 212, 0.2) !important;
}

.cooler-type-water {
    background-color: rgba(37, 99, 235, 0.1) !important;
    color: #2563eb !important;
    border: 1px solid rgba(37, 99, 235, 0.2) !important;
}

.cooler-type-aio {
    background-color: rgba(79, 70, 229, 0.1) !important;
    color: #4f46e5 !important;
    border: 1px solid rgba(79, 70, 229, 0.2) !important;
}

/* 卡片式布局 */
.cooler-card {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 0 16px 0 !important;
    box-sizing: border-box !important;
    border: none;
    border-radius: 10px;
    overflow: hidden;
    background-color: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    padding: 16px !important;
    display: flex;
    flex-direction: column;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.cooler-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.cooler-card-header {
    padding: 12px 16px !important;
    background-color: #f8f9fa;
    border-bottom: 1px solid #eee;
    width: 100% !important;
    box-sizing: border-box !important;
}

.cooler-card-body {
    padding: 16px !important;
    min-height: 120px !important;
    width: 100% !important;
    box-sizing: border-box !important;
    overflow: hidden !important;
}

.cooler-card-footer {
    padding: 12px !important;
    background-color: #fcfcfc;
    border-top: 1px solid #eee;
    width: 100% !important;
    box-sizing: border-box !important;
}

/* 标签组 */
.tag-group {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin: 8px 0;
    width: 100%;
    box-sizing: border-box !important;
}

/* 移动端优化 */
@media (max-width: 640px) {

    /* 移动端布局顺序调整 - 表单在上，列表在下 */
    .order-1 {
        order: 2 !important;
    }

    .order-2 {
        order: 1 !important;
    }

    /* 页面容器修复 */
    .container {
        padding-left: 8px !important;
        padding-right: 8px !important;
    }

    /* 确保表格容器大小正确 */
    .table-container,
    .table-wrapper,
    .inline-block.min-w-full,
    .inline-block.min-w-full table {
        width: 100% !important;
        max-width: 100% !important;
        box-sizing: border-box !important;
        overflow-x: hidden !important;
    }

    /* 表格行适应屏幕宽度 */
    .table-modern.mobile-table tr {
        width: 100% !important;
        max-width: 100% !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        box-sizing: border-box !important;
    }

    /* 移动端卡片样式 */
    .cooler-card-outer-container {
        width: 100% !important;
        max-width: 100vw !important;
        box-sizing: border-box !important;
        padding: 0 4px !important;
        margin-bottom: 12px !important;
        position: relative !important;
    }

    .cooler-card-new {
        width: 100% !important;
        border-radius: 12px !important;
        overflow: hidden !important;
        background-color: white !important;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.07) !important;
        display: flex !important;
        flex-direction: column !important;
        min-width: 0 !important;
        transition: transform 0.2s ease, box-shadow 0.2s ease !important;
    }

    .mobile-stack {
        flex-direction: column;
    }

    .mobile-full-width {
        width: 100%;
    }

    .mobile-p-2 {
        padding: 0.5rem;
    }

    .mobile-text-sm {
        font-size: 0.875rem;
        line-height: 1.25rem;
    }

    .mobile-hidden {
        display: none;
    }

    .mobile-flex-col {
        flex-direction: column;
    }

    .mobile-w-full {
        width: 100%;
    }

    .mobile-mt-2 {
        margin-top: 0.5rem;
    }

    /* 新增移动端样式 */
    .table-compact {
        font-size: 0.8rem;
        width: 100%;
        table-layout: fixed;
    }

    .table-compact td,
    .table-compact th {
        padding: 0.5rem 0.25rem;
        word-break: break-word;
    }

    .btn-group-compact button {
        padding: 0.25rem 0.5rem;
        margin: 0 2px;
    }

    /* 改进的移动端散热器列表样式 */
    .table-compact td {
        font-size: 0.75rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 120px;
    }

    /* 操作按钮样式优化 */
    .action-buttons,
    .flex.justify-center {
        display: flex !important;
        gap: 4px !important;
        flex-wrap: nowrap !important;
        justify-content: center !important;
        min-width: 120px !important;
        visibility: visible !important;
    }

    .action-buttons button,
    .flex.justify-center button {
        min-width: 28px !important;
        width: 28px !important;
        height: 28px !important;
        padding: 0;
        display: flex !important;
        visibility: visible !important;
        align-items: center;
        justify-content: center;
        transition: transform 0.2s ease;
    }

    .action-buttons button:hover,
    .flex.justify-center button:hover {
        transform: translateY(-2px);
    }

    /* 型号列宽度调整 */
    .model-column {
        max-width: 80px !important;
        width: 40%;
    }

    /* 类型列宽度调整 */
    .type-column {
        max-width: 60px !important;
        width: 30%;
    }

    /* 操作列宽度调整 */
    .action-column {
        width: 30%;
    }

    /* 分页控件移动端优化 */
    .pagination-mobile {
        display: flex;
        justify-content: space-between;
        width: 100%;
        align-items: center;
    }

    .page-controls-mobile {
        display: flex;
        gap: 2px;
    }

    .page-controls-mobile button {
        min-width: 28px;
        height: 28px;
        padding: 0;
        font-size: 0.75rem;
    }

    #pageInfo {
        font-size: 0.7rem;
        padding: 2px 4px;
        white-space: nowrap;
    }

    /* 确保表格不会水平溢出 */
    .overflow-x-auto {
        max-width: 100vw;
        margin: 0;
        padding: 0;
    }

    /* 移动端表格优化 */
    @media (max-width: 640px) {
        .table-compact {
            width: 100%;
        }

        .table-compact th,
        .table-compact td {
            padding: 8px 4px;
        }

        .model-column {
            width: 40%;
        }

        .type-column {
            width: 30%;
        }

        .action-column {
            width: 30%;
            min-width: 100px !important;
        }

        /* 改进移动端操作按钮布局 */
        .action-buttons,
        .flex.justify-center {
            display: flex !important;
            flex-wrap: nowrap !important;
            justify-content: center !important;
            gap: 2px !important;
            min-width: 90px !important;
        }

        .action-btn {
            width: 28px !important;
            height: 28px !important;
            margin: 0 1px !important;
            padding: 0 !important;
            font-size: 0.8rem !important;
        }

        /* 确保按钮显示 */
        .view-btn,
        .edit-btn,
        .delete-btn {
            display: flex !important;
            visibility: visible !important;
            opacity: 1 !important;
        }
    }
}

/* 移动端卡片暗夜模式样式 */
.dark-mode .cooler-card-new {
    background-color: var(--bg-secondary) !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3) !important;
    border: 1px solid rgba(255, 255, 255, 0.05) !important;
}

/* 卡片头部暗色样式 */
.dark-mode .cooler-card-new>div:first-child {
    background-color: rgba(0, 0, 0, 0.2) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05) !important;
}

/* 卡片内容区域暗色样式 - 针对中间内容部分 */
.dark-mode .cooler-card-new>div:nth-child(2) {
    background-color: var(--bg-secondary) !important;
    color: var(--text-primary) !important;
}

/* 卡片底部暗色样式 */
.dark-mode .cooler-card-new>div:last-child {
    background-color: rgba(0, 0, 0, 0.1) !important;
    border-top: 1px solid rgba(255, 255, 255, 0.05) !important;
}

/* 卡片内文字颜色 */
.dark-mode .cooler-card-new h2,
.dark-mode .cooler-card-new h3,
.dark-mode .cooler-card-new p,
.dark-mode .cooler-card-new div {
    color: var(--text-primary) !important;
}

.dark-mode .cooler-card-new .text-gray-600,
.dark-mode .cooler-card-new .text-gray-500 {
    color: var(--text-secondary) !important;
}

/* 卡片内按钮样式 */
.dark-mode .cooler-card-new button {
    background-color: transparent !important;
}

.dark-mode .cooler-card-new button:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
}

/* 卡片图片容器 */
.dark-mode .cooler-card-new .flex-shrink-0 {
    background-color: #1a1a1a !important;
    border-color: #333 !important;
}

/* 针对移动端卡片内部显示的具体数据区域 */
.dark-mode .cooler-card-outer-container .bg-white {
    background-color: var(--bg-secondary) !important;
}

/* 针对卡片内的所有链接和文本 */
.dark-mode .cooler-card-outer-container a,
.dark-mode .cooler-card-outer-container span,
.dark-mode .cooler-card-outer-container p {
    color: var(--text-primary) !important;
}

/* 标签样式调整 - 排除品牌标签 */
.dark-mode .cooler-card-outer-container span[style*="background-color"]:not(.cooler-badge) {
    background-color: rgba(75, 85, 99, 0.3) !important;
    color: #e5e7eb !important;
    border: 1px solid rgba(75, 85, 99, 0.5) !important;
}

/* 特殊颜色标签 */
.dark-mode .cooler-card-outer-container [style*="155mm"] {
    background-color: rgba(16, 185, 129, 0.2) !important;
    color: #34d399 !important;
    border-color: rgba(16, 185, 129, 0.3) !important;
}

.dark-mode .cooler-card-outer-container [style*="150mm"] {
    background-color: rgba(16, 185, 129, 0.2) !important;
    color: #34d399 !important;
    border-color: rgba(16, 185, 129, 0.3) !important;
}

.dark-mode .cooler-card-outer-container [style*="≤29.4"] {
    background-color: rgba(79, 70, 229, 0.2) !important;
    color: #818cf8 !important;
    border-color: rgba(79, 70, 229, 0.3) !important;
}

.dark-mode .cooler-card-outer-container [style*="220W"],
.dark-mode .cooler-card-outer-container [style*="260W"],
.dark-mode .cooler-card-outer-container [style*="200W"] {
    background-color: rgba(219, 39, 119, 0.2) !important;
    color: #f472b6 !important;
    border-color: rgba(219, 39, 119, 0.3) !important;
}

/* 蓝色文字颜色调整 */
.dark-mode .cooler-card-outer-container [style*="Air"] {
    color: #60a5fa !important;
}

/* 风扇数量、高度等标签 */
.dark-mode .inline-flex.items-center.px-1\.5.py-0\.5.rounded.text-xs.font-medium {
    opacity: 0.85;
}

/* 添加品牌标签暗夜模式样式 - 排除品牌标签 */
.dark-mode .cooler-card-new span[style*="background-color"]:not(.cooler-badge) {
    background-color: rgba(75, 85, 99, 0.3) !important;
    color: #e5e7eb !important;
    border: 1px solid rgba(75, 85, 99, 0.5) !important;
}

/* 根据品牌设置标签颜色 */
.dark-mode .cooler-card-new span[style*="font-weight: bold; color:"] {
    color: #60a5fa !important;
}

/* 操作按钮特定样式 */
.action-btn {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 30px !important;
    height: 30px !important;
    border-radius: 50% !important;
    transition: all 0.2s !important;
    visibility: visible !important;
    opacity: 1 !important;
    margin: 0 2px;
}

.action-btn:hover {
    transform: translateY(-2px) !important;
}

.view-btn {
    color: #0284c7 !important;
}

.edit-btn {
    color: #16a34a !important;
}

.delete-btn {
    color: #dc2626 !important;
}

/* 模态框按钮移动端优化 */
@media (max-width: 640px) {
    #coolerModal .bg-white {
        padding-bottom: 20px !important;
    }

    #coolerDetails {
        margin-bottom: 0 !important;
    }

    #editBtn,
    #deleteBtn,
    #closeModalBtn {
        margin-top: 8px !important;
        margin-bottom: 8px !important;
        width: 90% !important;
        max-width: 90% !important;
        margin-left: auto !important;
        margin-right: auto !important;
        padding-top: 12px !important;
        padding-bottom: 12px !important;
        font-size: 16px !important;
        border-radius: 8px !important;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important;
    }

    #editBtn i,
    #deleteBtn i,
    #closeModalBtn i {
        margin-right: 8px !important;
        font-size: 16px !important;
    }
}