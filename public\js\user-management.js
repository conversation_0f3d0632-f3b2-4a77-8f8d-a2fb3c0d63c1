// 用户管理页面脚本

document.addEventListener('DOMContentLoaded', function() {
    // 分页相关变量
    let currentPage = 1;
    const pageSize = 10;
    let totalRecords = 0;
    let isEditing = false;
    let deleteUserId = null;
    let deleteUserName = '';

    // DOM 元素
    const userForm = document.getElementById('userForm');
    const userId = document.getElementById('userId');
    const username = document.getElementById('username');
    const password = document.getElementById('password');
    const role = document.getElementById('role');
    const submitBtn = document.getElementById('submitBtn');
    const submitBtnText = document.getElementById('submitBtnText');
    const resetBtn = document.getElementById('resetBtn');
    const formTitle = document.getElementById('formTitle');
    const userTableBody = document.getElementById('userTableBody');
    const userSearch = document.getElementById('userSearch');
    const roleFilter = document.getElementById('roleFilter');
    const resetFilterBtn = document.getElementById('resetFilterBtn');
    const prevPage = document.getElementById('prevPage');
    const nextPage = document.getElementById('nextPage');
    const currentPageDisplay = document.getElementById('currentPageDisplay');
    const totalPagesDisplay = document.getElementById('totalPagesDisplay');
    const totalRecordsElement = document.getElementById('totalRecords');
    const deleteModal = document.getElementById('deleteModal');
    const deleteUserNameElement = document.getElementById('deleteUserName');
    const cancelDeleteBtn = document.getElementById('cancelDeleteBtn');
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    const adminOnlyMessage = document.getElementById('adminOnlyMessage');
    const adminContent = document.getElementById('adminContent');

    // 主题切换相关元素
    const themeToggle = document.getElementById('themeToggle');
    const themeIcon = document.getElementById('themeIcon');
    const themeText = document.getElementById('themeText');

    // 密码转换相关元素
    const plainPassword = document.getElementById('plainPassword');
    const hashedPassword = document.getElementById('hashedPassword');
    const convertPasswordBtn = document.getElementById('convertPasswordBtn');
    const copyHashBtn = document.getElementById('copyHashBtn');

    // 初始化
    initTheme();
    init();

    // 初始化函数
    async function init() {
        // 检查管理员权限
        try {
            console.log('[用户管理] 开始权限检查...');
            const user = await validateToken();
            console.log('[用户管理] validateToken 返回结果:', user);

            if (!user) {
                console.log('[用户管理] 用户验证失败，没有有效用户');
                adminOnlyMessage.classList.remove('hidden');
                adminContent.classList.add('hidden');
                return;
            }

            // 直接检查用户角色，不使用isAdmin函数避免冲突
            const adminCheck = user.role === 'admin';
            console.log('[用户管理] 管理员检查结果:', adminCheck, '用户角色:', user.role);

            if (!adminCheck) {
                console.log('[用户管理] 用户不是管理员');
                adminOnlyMessage.classList.remove('hidden');
                adminContent.classList.add('hidden');
                return;
            }

            console.log('[用户管理] 权限检查通过，初始化页面');
            setupEventListeners();
            loadUsers();
        } catch (error) {
            console.error('[用户管理] 权限检查失败:', error);
            adminOnlyMessage.classList.remove('hidden');
            adminContent.classList.add('hidden');
        }
    }

    // 设置事件监听器
    function setupEventListeners() {
        // 表单提交
        userForm.addEventListener('submit', handleFormSubmit);

        // 重置按钮
        resetBtn.addEventListener('click', resetForm);

        // 搜索框
        userSearch.addEventListener('input', debounce(() => {
            currentPage = 1;
            loadUsers();
        }, 300));

        // 角色过滤器
        roleFilter.addEventListener('change', () => {
            currentPage = 1;
            loadUsers();
        });

        // 重置过滤器
        resetFilterBtn.addEventListener('click', () => {
            userSearch.value = '';
            roleFilter.value = 'all';
            currentPage = 1;
            loadUsers();
        });

        // 分页按钮
        prevPage.addEventListener('click', () => {
            if (currentPage > 1) {
                currentPage--;
                loadUsers();
            }
        });

        nextPage.addEventListener('click', () => {
            const totalPages = Math.ceil(totalRecords / pageSize);
            if (currentPage < totalPages) {
                currentPage++;
                loadUsers();
            }
        });

        // 删除确认对话框按钮
        cancelDeleteBtn.addEventListener('click', () => {
            deleteModal.classList.add('hidden');
            deleteUserId = null;
            deleteUserName = '';
        });

        confirmDeleteBtn.addEventListener('click', async () => {
            if (deleteUserId) {
                // 禁用按钮防止重复点击
                confirmDeleteBtn.disabled = true;
                confirmDeleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> 删除中...';

                try {
                    await deleteUser(deleteUserId);
                } finally {
                    // 恢复按钮状态
                    confirmDeleteBtn.disabled = false;
                    confirmDeleteBtn.innerHTML = '确认删除';
                }
            }
        });

        // 密码转换功能
        convertPasswordBtn.addEventListener('click', convertPassword);
        copyHashBtn.addEventListener('click', copyHashedPassword);

        // 主题切换功能
        themeToggle.addEventListener('click', toggleTheme);

        // 清空输入时禁用复制按钮
        plainPassword.addEventListener('input', () => {
            if (!plainPassword.value.trim()) {
                hashedPassword.value = '';
                copyHashBtn.disabled = true;
            }
        });
    }

    // 密码转换功能
    async function convertPassword() {
        const plainText = plainPassword.value.trim();
        if (!plainText) {
            showToast('请输入要转换的密码', 'error');
            plainPassword.focus();
            return;
        }

        try {
            convertPasswordBtn.disabled = true;
            convertPasswordBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> 转换中...';

            const response = await fetch('/api/hash-password', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ password: plainText })
            });

            if (!response.ok) {
                throw new Error('密码转换失败');
            }

            const data = await response.json();
            hashedPassword.value = data.hashed;
            copyHashBtn.disabled = false;
            showToast('密码转换成功', 'success');
        } catch (error) {
            console.error('密码转换失败:', error);
            showToast(error.message || '密码转换失败', 'error');
        } finally {
            convertPasswordBtn.disabled = false;
            convertPasswordBtn.innerHTML = '<i class="fas fa-exchange-alt mr-1"></i> 转换密码';
        }
    }

    // 复制哈希密码
    async function copyHashedPassword() {
        try {
            await navigator.clipboard.writeText(hashedPassword.value);
            showToast('哈希值已复制到剪贴板', 'success');
        } catch (error) {
            console.error('复制失败:', error);
            // 降级方案：选中文本
            hashedPassword.select();
            hashedPassword.setSelectionRange(0, 99999);
            try {
                document.execCommand('copy');
                showToast('哈希值已复制到剪贴板', 'success');
            } catch (fallbackError) {
                showToast('复制失败，请手动选择复制', 'error');
            }
        }
    }

    // 处理表单提交
    async function handleFormSubmit(e) {
        e.preventDefault();

        // 表单验证
        if (!username.value.trim()) {
            showToast('请输入用户名', 'error');
            username.focus();
            return;
        }

        // 验证用户名长度
        if (username.value.length < 6) {
            showToast('用户名至少需要6个字符', 'error');
            username.focus();
            return;
        }

        if (username.value.includes(' ')) {
            showToast('用户名不能包含空格', 'error');
            username.focus();
            return;
        }

        // 如果是新增用户或者编辑时输入了密码，则验证密码格式
        if (!isEditing || (isEditing && password.value)) {
            if (!isEditing && !password.value) {
                showToast('请输入密码', 'error');
                password.focus();
                return;
            }

            if (password.value && password.value.length < 6) {
                showToast('密码至少需要6个字符', 'error');
                password.focus();
                return;
            }

            if (password.value) {
                const hasLetter = /[a-zA-Z]/.test(password.value);
                const hasNumber = /[0-9]/.test(password.value);

                if (!hasLetter || !hasNumber) {
                    showToast('密码必须同时包含字母和数字', 'error');
                    password.focus();
                    return;
                }
            }
        }

        if (!role.value) {
            showToast('请选择用户角色', 'error');
            role.focus();
            return;
        }

        // 构建数据
        const userData = {
            username: username.value.trim(),
            role: role.value
        };

        // 如果有密码，则添加到数据中
        if (password.value) {
            userData.password = password.value;
        }

        // 发送请求
        const url = isEditing ? `/api/users/${userId.value}` : '/api/users';
        const method = isEditing ? 'PUT' : 'POST';

        try {
            showToast(isEditing ? '正在更新用户...' : '正在创建用户...', 'info');

            const response = await fetchWithAuth(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(userData)
            });

            if (!response.ok) {
                const data = await response.json();
                throw new Error(data.error || '操作失败');
            }

            const data = await response.json();
            showToast(isEditing ? '用户更新成功' : '用户创建成功', 'success');
            resetForm();
            loadUsers();
        } catch (error) {
            console.error('操作失败:', error);
            showToast(error.message || '操作失败', 'error');
        }
    }

    // 重置表单
    function resetForm() {
        userForm.reset();
        userId.value = '';
        isEditing = false;
        submitBtnText.textContent = '添加用户';
        formTitle.textContent = '添加用户';
        resetBtn.classList.add('hidden');
    }

    // 加载用户数据
    async function loadUsers() {
        // 构建API查询参数
        const params = new URLSearchParams();

        // 添加搜索条件
        if (userSearch && userSearch.value.trim()) {
            params.append('search', userSearch.value.trim());
        }

        // 添加角色过滤
        if (roleFilter && roleFilter.value !== 'all') {
            params.append('role', roleFilter.value);
        }

        // 添加分页参数
        params.append('page', currentPage);
        params.append('limit', pageSize);

        // 构建API URL
        const apiUrl = `/api/users?${params.toString()}`;

        // 显示加载状态
        userTableBody.innerHTML = `
            <tr>
                <td colspan="5" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                    <i class="fas fa-spinner fa-spin mr-2"></i> 加载中...
                </td>
            </tr>
        `;

        try {
            const response = await fetchWithAuth(apiUrl);

            if (!response.ok) {
                throw new Error('加载用户数据失败');
            }

            const data = await response.json();

            // 更新总记录数
            totalRecords = data.total || 0;

            // 渲染表格
            renderUserTable(data.data || []);

            // 更新分页信息
            updatePagination();
        } catch (error) {
            console.error('加载用户数据失败:', error);
            userTableBody.innerHTML = `
                <tr>
                    <td colspan="5" class="px-6 py-4 text-center text-red-500 dark:text-red-400">
                        加载失败: ${error.message}
                    </td>
                </tr>
            `;
        }
    }

    // 渲染用户表格
    function renderUserTable(users) {
        if (!users || users.length === 0) {
            userTableBody.innerHTML = `
                <tr>
                    <td colspan="5" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                        暂无数据
                    </td>
                </tr>
            `;
            return;
        }

        userTableBody.innerHTML = '';

        users.forEach(user => {
            const row = document.createElement('tr');
            row.className = 'hover:bg-gray-50 dark:hover:bg-gray-700';

            // 格式化创建时间
            const createdAt = new Date(user.created_at).toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });

            // 用户角色标签
            const roleLabel = user.role === 'admin' ?
                '<span class="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200">管理员</span>' :
                '<span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">普通用户</span>';

            row.innerHTML = `
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">${user.id}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">${user.username}</td>
                <td class="px-6 py-4 whitespace-nowrap">${roleLabel}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">${createdAt}</td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 mr-3 edit-btn" data-id="${user.id}">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                    <button class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 delete-btn" data-id="${user.id}" data-name="${user.username}">
                        <i class="fas fa-trash"></i> 删除
                    </button>
                </td>
            `;

            userTableBody.appendChild(row);
        });

        // 添加编辑和删除按钮的事件监听器
        document.querySelectorAll('.edit-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const userId = btn.getAttribute('data-id');
                editUser(userId);
            });
        });

        document.querySelectorAll('.delete-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const userId = btn.getAttribute('data-id');
                const userName = btn.getAttribute('data-name');
                showDeleteConfirmation(userId, userName);
            });
        });

        // 更新总记录数显示
        if (totalRecordsElement) {
            totalRecordsElement.textContent = totalRecords;
        }
    }

    // 更新分页信息
    function updatePagination() {
        const totalPages = Math.ceil(totalRecords / pageSize);

        // 更新当前页和总页数显示
        currentPageDisplay.textContent = currentPage;
        totalPagesDisplay.textContent = totalPages;

        // 启用/禁用上一页和下一页按钮
        prevPage.disabled = currentPage <= 1;
        nextPage.disabled = currentPage >= totalPages;
    }

    // 编辑用户
    async function editUser(id) {
        try {
            const response = await fetchWithAuth(`/api/users/${id}`);

            if (!response.ok) {
                throw new Error('获取用户信息失败');
            }

            const user = await response.json();

            // 填充表单
            userId.value = user.id;
            username.value = user.username;
            password.value = ''; // 清空密码字段
            role.value = user.role;

            // 更新表单状态
            isEditing = true;
            submitBtnText.textContent = '更新用户';
            formTitle.textContent = '编辑用户';
            resetBtn.classList.remove('hidden');

            // 滚动到表单
            userForm.scrollIntoView({ behavior: 'smooth' });
        } catch (error) {
            console.error('获取用户信息失败:', error);
            showToast(error.message || '获取用户信息失败', 'error');
        }
    }

    // 显示删除确认
    function showDeleteConfirmation(id, name) {
        deleteUserId = id;
        deleteUserName = name;
        deleteUserNameElement.textContent = name;
        deleteModal.classList.remove('hidden');
    }

    // 删除用户
    async function deleteUser(id) {
        console.log('[删除用户] 开始删除用户，ID:', id);

        try {
            const response = await fetchWithAuth(`/api/users/${id}`, {
                method: 'DELETE'
            });

            console.log('[删除用户] API响应:', response);

            if (!response) {
                // fetchWithAuth返回null表示已经重定向到登录页面
                console.log('[删除用户] fetchWithAuth返回null，可能已重定向到登录页面');
                return;
            }

            console.log('[删除用户] 响应状态码:', response.status);

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                console.error('[删除用户] API返回错误:', errorData);
                throw new Error(errorData.error || '删除用户失败');
            }

            console.log('[删除用户] 删除成功');
            showToast('用户删除成功', 'success');
            deleteModal.classList.add('hidden');
            deleteUserId = null;
            deleteUserName = '';
            loadUsers();
        } catch (error) {
            console.error('[删除用户] 删除失败:', error);
            showToast(error.message || '删除用户失败', 'error');
            deleteModal.classList.add('hidden');
            deleteUserId = null;
            deleteUserName = '';
        }
    }

    // 显示Toast通知
    function showToast(message, type = 'success') {
        const toast = document.getElementById('toast');
        const toastMessage = document.getElementById('toastMessage');
        const toastIcon = document.getElementById('toastIcon');

        toastMessage.textContent = message;

        // 设置图标和颜色
        if (type === 'error') {
            toastIcon.className = 'fas fa-times-circle text-red-400';
        } else if (type === 'info') {
            toastIcon.className = 'fas fa-info-circle text-blue-400';
        } else {
            toastIcon.className = 'fas fa-check-circle text-green-400';
        }

        // 显示Toast
        toast.classList.remove('translate-y-[-100%]');

        // 3秒后隐藏
        setTimeout(() => {
            toast.classList.add('translate-y-[-100%]');
        }, 3000);
    }

    // 防抖函数
    function debounce(func, delay) {
        let timeout;
        return function() {
            const context = this;
            const args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), delay);
        };
    }

    // 主题切换相关函数
    function initTheme() {
        // 从localStorage获取保存的主题，默认为亮色主题
        const savedTheme = localStorage.getItem('theme') || 'light';
        applyTheme(savedTheme);
    }

    function toggleTheme() {
        const currentTheme = document.documentElement.classList.contains('dark') ? 'dark' : 'light';
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        applyTheme(newTheme);
        localStorage.setItem('theme', newTheme);
    }

    function applyTheme(theme) {
        if (theme === 'dark') {
            document.documentElement.classList.add('dark');
            themeIcon.className = 'fas fa-sun mr-1';
            themeText.textContent = '亮色';
        } else {
            document.documentElement.classList.remove('dark');
            themeIcon.className = 'fas fa-moon mr-1';
            themeText.textContent = '暗色';
        }
    }
});