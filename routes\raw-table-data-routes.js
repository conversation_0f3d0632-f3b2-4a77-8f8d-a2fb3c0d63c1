// routes/raw-table-data-routes.js - 处理原始表格数据的API路由
const express = require('express');
const router = express.Router();
const { body, query, param, validationResult } = require('express-validator');
const db = require('../db'); // 使用与项目其他部分相同的数据库连接
const { verifyToken, checkAdminRole } = require('../middleware/auth');

// 确保表存在的函数
async function ensureRawTableDataTablesExist() {
    try {
        await db.query(`
            CREATE TABLE IF NOT EXISTS raw_table_data (
                id INTEGER PRIMARY KEY AUTO_INCREMENT,
                user_id INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                total_amount INTEGER NOT NULL,
                raw_data TEXT NOT NULL,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            )
        `);
        console.log('raw_table_data 表已创建或已存在');

        // 检查是否需要添加user_id字段（为了兼容已存在的表）
        try {
            await db.query(`
                ALTER TABLE raw_table_data
                ADD COLUMN user_id INTEGER NOT NULL DEFAULT 1 AFTER id
            `);
            console.log('已为raw_table_data表添加user_id字段');
        } catch (alterError) {
            // 如果字段已存在，忽略错误
            if (!alterError.message.includes('Duplicate column name')) {
                console.log('user_id字段可能已存在，跳过添加');
            }
        }

    } catch (error) {
        console.error('创建 raw_table_data 表失败:', error);
    }
}

// 启动时检查表
ensureRawTableDataTablesExist();

// POST /api/raw-table-data - 创建记录
router.post('/', [
    body('total_amount').isInt({ min: 0 }).withMessage('总金额必须是非负整数'),
    body('raw_data').isString().notEmpty().withMessage('原始数据不能为空')
], async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ success: false, message: '输入验证失败', errors: errors.array() });
    }

    try {
        const { total_amount, raw_data } = req.body;
        const [result] = await db.query(
            'INSERT INTO raw_table_data (user_id, total_amount, raw_data) VALUES (?, ?, ?)',
            [req.user.id, total_amount, raw_data]
        );
        res.status(201).json({
            success: true,
            message: '原始表格数据已保存',
            recordId: result.insertId
        });
    } catch (error) {
        console.error('保存原始表格数据出错:', error);
        res.status(500).json({ success: false, message: '内部服务器错误', error: error.message });
    }
});

// GET /api/raw-table-data - 获取列表
router.get('/', [
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 100 }),
    query('search').optional().isString(),
    query('category').optional().isString()
], async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ success: false, message: '输入验证失败', errors: errors.array() });
    }

    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const offset = (page - 1) * limit;
        const search = req.query.search || '';
        const category = req.query.category || 'all';

        let countQuery = 'SELECT COUNT(*) as total FROM raw_table_data rtd LEFT JOIN users u ON rtd.user_id = u.id';
        let recordsQuery = 'SELECT rtd.id, rtd.created_at, rtd.total_amount, SUBSTRING(rtd.raw_data, 1, 100) as raw_data, u.username as created_by FROM raw_table_data rtd LEFT JOIN users u ON rtd.user_id = u.id';
        const queryParams = [];

        if (search) {
            let whereClause = '';
            if (category === 'raw_data') {
                whereClause = ' WHERE rtd.raw_data LIKE ?';
                queryParams.push(`%${search}%`);
            } else if (category === 'date') {
                whereClause = " WHERE DATE_FORMAT(rtd.created_at, '%Y-%m-%d') LIKE ?";
                queryParams.push(`%${search}%`);
            } else {
                whereClause = " WHERE (rtd.raw_data LIKE ? OR DATE_FORMAT(rtd.created_at, '%Y-%m-%d') LIKE ? OR u.username LIKE ?)";
                queryParams.push(`%${search}%`, `%${search}%`, `%${search}%`);
            }
            countQuery += whereClause;
            recordsQuery += whereClause;
        }

        recordsQuery += ' ORDER BY rtd.created_at DESC LIMIT ? OFFSET ?';
        const pagingParams = [...queryParams, limit, offset];

        const [countResult] = await db.query(countQuery, queryParams);
        const [records] = await db.query(recordsQuery, pagingParams);

        const total = countResult[0].total;

        res.json({
            success: true,
            total,
            page,
            limit,
            records
        });
    } catch (error) {
        console.error('获取原始表格数据列表出错:', error);
        res.status(500).json({ success: false, message: '内部服务器错误', error: error.message });
    }
});

// GET /api/raw-table-data/:id - 获取单条记录
router.get('/:id', [
    param('id').isInt().withMessage('ID必须是整数')
], async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ success: false, message: '输入验证失败', errors: errors.array() });
    }
    try {
        const { id } = req.params;
        const [records] = await db.query(`
            SELECT rtd.*, u.username as created_by
            FROM raw_table_data rtd
            LEFT JOIN users u ON rtd.user_id = u.id
            WHERE rtd.id = ?
        `, [id]);
        if (records.length === 0) {
            return res.status(404).json({ success: false, message: '找不到记录' });
        }
        res.json({ success: true, record: records[0] });
    } catch (error) {
        console.error('获取原始表格数据详情出错:', error);
        res.status(500).json({ success: false, message: '内部服务器错误', error: error.message });
    }
});

// DELETE /api/raw-table-data/:id - 删除记录 (仅限管理员)
router.delete('/:id', checkAdminRole, [
    param('id').isInt().withMessage('ID必须是整数')
], async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ success: false, message: '输入验证失败', errors: errors.array() });
    }
    try {
        const { id } = req.params;
        const [result] = await db.query('DELETE FROM raw_table_data WHERE id = ?', [id]);
        if (result.affectedRows === 0) {
            return res.status(404).json({ success: false, message: '找不到要删除的记录' });
        }
        res.json({ success: true, message: '记录已删除' });
    } catch (error) {
        console.error('删除原始表格数据出错:', error);
        res.status(500).json({ success: false, message: '内部服务器错误', error: error.message });
    }
});

module.exports = router; 