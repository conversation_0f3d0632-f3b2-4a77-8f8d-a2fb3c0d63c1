<!DOCTYPE html>
<html lang="zh-CN" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{RESOURCE_NAME}}信息管理 - 电脑配件管理系统</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    animation: {
                        'fade-in': 'fadeIn 0.3s ease-in-out',
                        'slide-up': 'slideUp 0.3s ease-out',
                        'bounce-gentle': 'bounceGentle 0.6s ease-in-out'
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(10px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' }
                        },
                        bounceGentle: {
                            '0%, 100%': { transform: 'translateY(0)' },
                            '50%': { transform: 'translateY(-5px)' }
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- ViewerJS for image preview -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/viewerjs@1.11.6/dist/viewer.min.css">
    <script src="https://cdn.jsdelivr.net/npm/viewerjs@1.11.6/dist/viewer.min.js"></script>
    
    <!-- Custom Styles -->
    <style>
        /* 主题切换动画 */
        * {
            transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
        }
        
        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
        }
        
        ::-webkit-scrollbar-track {
            @apply bg-gray-100 dark:bg-gray-800;
        }
        
        ::-webkit-scrollbar-thumb {
            @apply bg-gray-300 dark:bg-gray-600 rounded-full;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            @apply bg-gray-400 dark:bg-gray-500;
        }
        
        /* 移动端优化 */
        @media (max-width: 768px) {
            .mobile-full-width {
                width: 100% !important;
            }
            
            .mobile-stack {
                flex-direction: column !important;
            }
            
            .mobile-hidden {
                display: none !important;
            }
        }
        
        /* 触摸友好的按钮 */
        .touch-target {
            min-height: 44px;
            min-width: 44px;
        }
        
        /* 加载动画 */
        .loading-spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 卡片悬停效果 */
        .card-hover {
            transition: all 0.3s ease;
        }
        
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        
        /* 表格行悬停效果 */
        .table-row-hover {
            transition: all 0.2s ease;
        }

        /* 图片预览效果 */
        .image-preview {
            transition: all 0.3s ease;
        }

        .image-preview:hover {
            transform: scale(1.05);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        
        .table-row-hover:hover {
            @apply bg-blue-50 dark:bg-gray-700;
        }
        
        /* 表单输入框焦点效果 */
        .form-input {
            transition: all 0.2s ease;
        }
        
        .form-input:focus {
            @apply ring-2 ring-blue-500 border-blue-500;
        }
        
        /* 主题切换按钮 */
        .theme-toggle {
            position: relative;
            overflow: hidden;
        }
        
        .theme-toggle::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        
        .theme-toggle:hover::before {
            left: 100%;
        }
    </style>
</head>

<body class="h-full bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors duration-300">
    <!-- 导航栏 -->
    <nav class="bg-white dark:bg-gray-800 shadow-lg border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- 左侧标题 -->
                <div class="flex items-center space-x-4">
                    <button onclick="history.back()" 
                            class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors touch-target">
                        <i class="fas fa-arrow-left text-gray-600 dark:text-gray-300"></i>
                    </button>
                    <div class="flex items-center space-x-3">
                        <i class="{{ICON_CLASS}} text-2xl text-blue-600 dark:text-blue-400"></i>
                        <div>
                            <h1 class="text-xl font-bold text-gray-900 dark:text-white">{{RESOURCE_NAME}}管理</h1>
                            <p class="text-sm text-gray-500 dark:text-gray-400 hidden sm:block">{{PAGE_DESCRIPTION}}</p>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧操作按钮 -->
                <div class="flex items-center space-x-2">
                    <!-- 主题切换按钮 -->
                    <button id="themeToggle" 
                            class="theme-toggle p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors touch-target"
                            title="切换主题">
                        <i class="fas fa-moon dark:hidden text-gray-600"></i>
                        <i class="fas fa-sun hidden dark:block text-yellow-400"></i>
                    </button>
                    
                    <!-- 首页按钮 -->
                    <a href="/pc-components.html" 
                       class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors touch-target flex items-center space-x-2">
                        <i class="fas fa-home"></i>
                        <span class="hidden sm:inline">首页</span>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <!-- 移动端优先的响应式布局 -->
        <div class="grid grid-cols-1 lg:grid-cols-12 gap-6">
            <!-- 左侧表单区域 (移动端全宽，桌面端占4列) -->
            <div class="lg:col-span-4">
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 card-hover">
                    <div class="p-6">
                        <div class="flex items-center space-x-3 mb-6">
                            <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                                <i class="fas fa-plus text-blue-600 dark:text-blue-400"></i>
                            </div>
                            <div>
                                <h2 class="text-lg font-semibold text-gray-900 dark:text-white">添加{{RESOURCE_NAME}}</h2>
                                <p class="text-sm text-gray-500 dark:text-gray-400">填写{{RESOURCE_NAME}}详细信息</p>
                            </div>
                        </div>
                        
                        <!-- 智能识别区域 -->
                        <div class="mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                            <div class="flex items-center space-x-2 mb-3">
                                <i class="fas fa-magic text-blue-600 dark:text-blue-400"></i>
                                <span class="font-medium text-blue-900 dark:text-blue-100">智能识别</span>
                            </div>
                            <textarea id="smartInput" 
                                      placeholder="粘贴{{RESOURCE_NAME}}规格信息，系统将自动识别并填充表单..."
                                      class="w-full p-3 border border-blue-200 dark:border-blue-700 rounded-lg resize-none bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 form-input"
                                      rows="3"></textarea>
                            <button id="autoFillBtn" 
                                    class="mt-3 w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors touch-target flex items-center justify-center space-x-2">
                                <i class="fas fa-wand-magic-sparkles"></i>
                                <span>智能解析</span>
                            </button>
                        </div>
                        
                        <!-- 动态表单字段将在这里生成 -->
                        <form id="{{RESOURCE_JS_PROPERTY}}Form" class="space-y-4">
                            {{DYNAMIC_FORM_FIELDS}}
                            
                            <!-- 图片上传 -->
                            <div>
                                <label for="image-upload" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">{{RESOURCE_NAME}}图片</label>
                                <div class="mt-1 flex justify-center px-4 sm:px-6 pt-3 sm:pt-5 pb-4 sm:pb-6 border-2 border-gray-300 dark:border-gray-600 border-dashed rounded-md">
                                    <div class="space-y-1 text-center">
                                        <div class="flex text-sm text-gray-600 dark:text-gray-400 items-center justify-center flex-wrap">
                                            <label for="image-upload"
                                                class="relative cursor-pointer bg-white dark:bg-gray-800 rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none">
                                                <span>上传图片</span>
                                                <input id="image-upload" name="image-upload" type="file" accept="image/*"
                                                    class="sr-only">
                                            </label>
                                            <p class="pl-1 mobile-hidden">或将图片拖拽到此处</p>
                                        </div>
                                        <p class="text-xs text-gray-500 dark:text-gray-400">PNG, JPG, GIF 格式（将自动转换为WebP格式以提高加载速度）</p>
                                    </div>
                                </div>
                                <div id="imagePreviewContainer" class="mt-2 hidden">
                                    <div class="relative inline-block">
                                        <img id="imagePreview" src="#" alt="预览图"
                                            class="h-24 sm:h-32 rounded-md shadow image-preview">
                                        <button type="button" id="removeImageBtn"
                                            class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 sm:w-6 sm:h-6 flex items-center justify-center hover:bg-red-600">
                                            <i class="fas fa-times text-xs"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 提交按钮 -->
                            <button type="submit" 
                                    class="w-full px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium rounded-lg transition-all duration-200 touch-target flex items-center justify-center space-x-2 shadow-lg hover:shadow-xl">
                                <i class="fas fa-save"></i>
                                <span>保存{{RESOURCE_NAME}}信息</span>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- 右侧列表区域 (移动端全宽，桌面端占8列) -->
            <div class="lg:col-span-8">
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 card-hover">
                    <div class="p-6">
                        <!-- 列表头部 -->
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 space-y-4 sm:space-y-0">
                            <div class="flex items-center space-x-3">
                                <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                                    <i class="fas fa-list text-green-600 dark:text-green-400"></i>
                                </div>
                                <div>
                                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white">{{RESOURCE_NAME}}列表</h2>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">共 <span id="totalCount">0</span> 条记录</p>
                                </div>
                            </div>
                            
                            <!-- 操作按钮组 -->
                            <div class="flex flex-wrap items-center gap-2">
                                <button id="editBtn" disabled
                                        class="px-4 py-2 bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-400 text-white rounded-lg transition-colors touch-target flex items-center space-x-2">
                                    <i class="fas fa-edit"></i>
                                    <span class="hidden sm:inline">编辑</span>
                                </button>
                                <button id="deleteBtn" disabled
                                        class="px-4 py-2 bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white rounded-lg transition-colors touch-target flex items-center space-x-2">
                                    <i class="fas fa-trash"></i>
                                    <span class="hidden sm:inline">删除</span>
                                </button>
                            </div>
                        </div>
                        
                        <!-- 搜索和筛选区域 -->
                        <div class="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                                <div>
                                    <input type="text" id="{{RESOURCE_JS_PROPERTY}}Search"
                                           placeholder="搜索{{RESOURCE_NAME}}..."
                                           class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 form-input">
                                </div>
                                <div>
                                    <select id="brandFilter" 
                                            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 form-input">
                                        <option value="all">所有品牌</option>
                                    </select>
                                </div>
                                <div class="flex space-x-2">
                                    <button id="searchBtn" 
                                            class="flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors touch-target flex items-center justify-center space-x-2">
                                        <i class="fas fa-search"></i>
                                        <span>搜索</span>
                                    </button>
                                    <button id="resetFilterBtn" 
                                            class="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors touch-target">
                                        <i class="fas fa-undo"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 数据表格 -->
                        <div class="overflow-x-auto">
                            <div id="loadingState" class="hidden text-center py-8">
                                <div class="loading-spinner mx-auto mb-4"></div>
                                <p class="text-gray-500 dark:text-gray-400">加载中...</p>
                            </div>
                            
                            <table id="{{RESOURCE_JS_PROPERTY}}Table" class="w-full table-auto">
                                <thead class="bg-gray-50 dark:bg-gray-700">
                                    <tr>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                            <input type="checkbox" id="selectAll" class="rounded border-gray-300 dark:border-gray-600">
                                        </th>
                                        {{DYNAMIC_TABLE_HEADERS}}
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="{{RESOURCE_JS_PROPERTY}}TableBody" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                    <!-- 动态生成的表格行将在这里显示 -->
                                </tbody>
                            </table>
                            
                            <!-- 空状态 -->
                            <div id="emptyState" class="hidden text-center py-12">
                                <i class="fas fa-inbox text-4xl text-gray-400 dark:text-gray-500 mb-4"></i>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">暂无数据</h3>
                                <p class="text-gray-500 dark:text-gray-400">还没有添加任何{{RESOURCE_NAME}}信息</p>
                            </div>
                        </div>
                        
                        <!-- 分页 -->
                        <div class="mt-6 flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0">
                            <div class="text-sm text-gray-700 dark:text-gray-300">
                                显示第 <span id="startRecord">1</span> 到 <span id="endRecord">10</span> 条，共 <span id="totalRecords">0</span> 条记录
                            </div>
                            <div class="flex items-center space-x-2">
                                <button id="prevPage" 
                                        class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors touch-target">
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                <div id="pageNumbers" class="flex space-x-1">
                                    <!-- 动态生成的页码按钮 -->
                                </div>
                                <button id="nextPage" 
                                        class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors touch-target">
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 模态框容器 -->
    <div id="modalContainer"></div>
    
    <!-- Toast 通知容器 -->
    <div id="toastContainer" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <!-- JavaScript 文件引用 -->
    <script src="/js/{{RESOURCE_NAME_LOWER}}-info.js"></script>
</body>
</html>
