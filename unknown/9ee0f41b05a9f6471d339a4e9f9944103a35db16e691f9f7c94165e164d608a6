const mysql = require('mysql2');
// require('dotenv').config(); // 禁用dotenv

// 先创建一个不指定数据库的连接
const initPool = mysql.createPool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'root',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

const dbName = process.env.DB_NAME || 'delivery_system';

// 使用promise包装initPool
const initPoolPromise = initPool.promise();

// 创建数据库（如果不存在）
initPoolPromise.query(`CREATE DATABASE IF NOT EXISTS ${dbName}`)
  .then(() => {
    console.log(`确保数据库 ${dbName} 存在`);
  })
  .catch(err => {
    console.error('创建数据库失败:', err);
    process.exit(1);
  });

// 使用指定数据库的连接池
const pool = mysql.createPool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'root',
  database: process.env.DB_NAME || 'delivery_system',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

// 创建基础表结构
const createTables = async () => {
  const dbPool = pool.promise();
  try {
    // 创建stores表
    await dbPool.query(`
      CREATE TABLE IF NOT EXISTS stores (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(100) NOT NULL
      )
    `);
    console.log('创建stores表成功');

    // 创建addresses表
    await dbPool.query(`
      CREATE TABLE IF NOT EXISTS addresses (
        id INT PRIMARY KEY AUTO_INCREMENT,
        address TEXT NOT NULL
      )
    `);
    console.log('创建addresses表成功');

    // 创建delivery_records表
    await dbPool.query(`
      CREATE TABLE IF NOT EXISTS delivery_records (
        id INT PRIMARY KEY AUTO_INCREMENT,
        store_id INT NOT NULL,
        address_id INT NOT NULL,
        image_url VARCHAR(255),
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (store_id) REFERENCES stores(id),
        FOREIGN KEY (address_id) REFERENCES addresses(id)
      )
    `);
    console.log('创建delivery_records表成功');
  } catch (err) {
    console.error('创建基础表结构失败:', err);
  }
};

// 执行表创建
createTables();

module.exports = pool.promise();
