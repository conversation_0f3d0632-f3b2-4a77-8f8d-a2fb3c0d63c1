(function() {


    // 调试信息
    console.log('页面开始加载...');
    
    try {
        // 初始化变量
        let currentPage = 1;
        const pageSize = 10;
        let totalRecords = 0;
        let feedbacks = [];
        let currentFeedbackId = null;
        let wordCloudInstance = null;
        let timeChart = null;
        let typeChart = null;
        let locationChart = null;
        let categoryChart = null;
        
        // DOM元素
        const feedbackForm = document.getElementById('feedbackForm');
        const storeSearch = document.getElementById('storeSearch');
        const storeSearchResults = document.getElementById('storeSearchResults');
        const storeLocation = document.getElementById('storeLocation');
        const feedbackContent = document.getElementById('feedbackContent');
        const notes = document.getElementById('notes');
        const feedbackImage = document.getElementById('feedbackImage');
        const imagePreview = document.getElementById('imagePreview');
        const imagePreviewContainer = document.getElementById('imagePreviewContainer');
        const removeImageBtn = document.getElementById('removeImageBtn');
        const feedbackTableBody = document.getElementById('feedbackTableBody');
        const feedbackSearch = document.getElementById('feedbackSearch');
        const statusFilter = document.getElementById('statusFilter');
        const prevPageBtn = document.getElementById('prevPage');
        const nextPageBtn = document.getElementById('nextPage');
        const pageInfoSpan = document.getElementById('pageInfo');
        const totalCountSpan = document.getElementById('totalCount');
        const feedbackModal = document.getElementById('feedbackModal');
        const closeModal = document.getElementById('closeModal');
        const closeModalBtn = document.getElementById('closeModalBtn');
        const feedbackDetails = document.getElementById('feedbackDetails');
        const updateStatusBtn = document.getElementById('updateStatusBtn');
        const statusModal = document.getElementById('statusModal');
        const closeStatusModal = document.getElementById('closeStatusModal');
        const cancelStatusBtn = document.getElementById('cancelStatusBtn');
        const feedbackStatus = document.getElementById('feedbackStatus');
        const saveStatusBtn = document.getElementById('saveStatusBtn');
        
        console.log('DOM元素加载完成...');
        
        // 初始化
        init();
        
        // 初始化函数
        function init() {
            console.log('开始初始化...');
            
            try {
                // 设置事件监听器
                setupEventListeners();
                console.log('事件监听器设置完成');
                
                // 加载地点选项
                try {
                    loadLocations();
                    console.log('地点加载请求已发送');
                } catch (e) {
                    console.error('加载地点失败:', e);
                    showErrorMessage('加载地点选项失败');
                }
                
                // 加载反馈数据
                try {
                    loadFeedbacks();
                    console.log('反馈数据加载请求已发送');
                } catch (e) {
                    console.error('加载反馈数据失败:', e);
                    showErrorMessage('加载反馈数据失败');
                    // 确保表格显示空状态
                    renderEmptyFeedbackTable();
                }
                
                // 初始化图表
                try {
                    initCharts();
                    console.log('图表初始化请求已发送');
                } catch (e) {
                    console.error('初始化图表失败:', e);
                    showErrorMessage('初始化图表失败');
                }
                
                console.log('初始化完成');
            } catch (error) {
                console.error('初始化过程中发生错误:', error);
                showErrorMessage('页面初始化失败: ' + error.message);
            }
        }
        
        // 显示空的反馈表格
        function renderEmptyFeedbackTable() {
            feedbackTableBody.innerHTML = `
                <tr>
                    <td colspan="6" class="px-3 py-4 text-center text-gray-500">
                        暂无数据
                    </td>
                </tr>
            `;
            totalRecords = 0;
            updatePagination();
        }
        
        // 加载地点选项
        function loadLocations() {
            fetchWithAuth('/api/addresses')
                .then(response => {
                    if (!response || !response.ok) {
                        if (response.status === 401) {
                            showAuthError();
                            return Promise.reject(new Error('未授权访问，请先登录'));
                        }
                        return Promise.reject(new Error('加载地点失败: ' + response.statusText));
                    }
                    return response.json();
                })
                .then(data => {
                    storeLocation.innerHTML = '<option value="">选择地点</option>';
                    data.forEach(location => {
                        const option = document.createElement('option');
                        option.value = location.id;
                        option.textContent = location.address;
                        storeLocation.appendChild(option);
                    });
                })
                .catch(error => {
                    console.error('加载地点失败:', error);
                    // 显示用户友好的错误信息
                    const errorOption = document.createElement('option');
                    errorOption.value = "";
                    errorOption.textContent = "加载地点失败";
                    storeLocation.innerHTML = '';
                    storeLocation.appendChild(errorOption);
                });
        }
        
        // 设置事件监听器
        function setupEventListeners() {
            // 店铺搜索
            storeSearch.addEventListener('input', debounce(handleStoreSearch, 300));
            storeSearch.addEventListener('focus', () => {
                if (storeSearch.value.trim() !== '') {
                    storeSearchResults.classList.remove('hidden');
                }
            });
            
            // 点击document时关闭搜索结果
            document.addEventListener('click', (e) => {
                if (!storeSearch.contains(e.target) && !storeSearchResults.contains(e.target)) {
                    storeSearchResults.classList.add('hidden');
                }
            });
            
            // 图片预览
            feedbackImage.addEventListener('change', handleImageUpload);
            removeImageBtn.addEventListener('click', removeImage);
            
            // 表单提交
            feedbackForm.addEventListener('submit', handleFormSubmit);
            
            // 分页和筛选
            prevPageBtn.addEventListener('click', () => changePage(-1));
            nextPageBtn.addEventListener('click', () => changePage(1));
            feedbackSearch.addEventListener('input', debounce(() => {
                currentPage = 1;
                loadFeedbacks();
            }, 300));
            statusFilter.addEventListener('change', () => {
                currentPage = 1;
                loadFeedbacks();
            });
            
            // 模态框
            closeModal.addEventListener('click', () => feedbackModal.classList.add('hidden'));
            closeModalBtn.addEventListener('click', () => feedbackModal.classList.add('hidden'));
            updateStatusBtn.addEventListener('click', openStatusModal);
            closeStatusModal.addEventListener('click', () => statusModal.classList.add('hidden'));
            cancelStatusBtn.addEventListener('click', () => statusModal.classList.add('hidden'));
            saveStatusBtn.addEventListener('click', updateFeedbackStatus);
            
            // 图片模态框
            const imageModal = document.getElementById('imageModal');
            const closeImageModal = document.getElementById('closeImageModal');
            if (imageModal && closeImageModal) {
                // 关闭按钮
                closeImageModal.addEventListener('click', () => {
                    imageModal.classList.add('hidden');
                });
                
                // 点击背景关闭
                imageModal.addEventListener('click', (e) => {
                    if (e.target === imageModal) {
                        imageModal.classList.add('hidden');
                    }
                });
                
                // 按Esc键关闭
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape' && !imageModal.classList.contains('hidden')) {
                        imageModal.classList.add('hidden');
                    }
                });
            }
        }
        
        // 店铺搜索处理
        function handleStoreSearch() {
            const query = storeSearch.value.trim();
            if (query === '') {
                storeSearchResults.classList.add('hidden');
                return;
            }
            
            // 显示加载状态
            storeSearchResults.innerHTML = '<div class="px-4 py-2 text-sm text-gray-500">搜索中...</div>';
            storeSearchResults.classList.remove('hidden');
            
            console.log('开始搜索商铺:', query);
            
            // 首页使用的是无参数的GET请求，我们尝试改用相同的方式
            fetchWithAuth('/api/stores')
                .then(response => {
                    if (!response || !response.ok) {
                        throw new Error('搜索店铺失败: ' + response.statusText);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('商铺搜索原始结果:', data);
                    
                    // 准备拼音实例
                    let pinyinInstance = null;
                    if (typeof Pinyin !== 'undefined') {
                        try {
                            pinyinInstance = new Pinyin();
                            console.log('Pinyin instance created successfully');
                        } catch (e) {
                            console.error('Failed to create Pinyin instance:', e);
                        }
                    }
                    
                    // 手动过滤结果
                    const lowerQuery = query.toLowerCase();
                    const filteredData = data.filter(store => {
                        // 直接名称匹配
                        if (store.name.toLowerCase().includes(lowerQuery)) {
                            return true;
                        }
                        
                        // 拼音首字母匹配
                        if (pinyinInstance) {
                            try {
                                // 获取拼音首字母
                                const firstLetters = pinyinInstance.getCamelChars(store.name);
                                if (firstLetters && firstLetters.toLowerCase().includes(lowerQuery)) {
                                    return true;
                                }
                                
                                // 获取全拼音
                                const fullPinyin = pinyinInstance.getFullChars(store.name);
                                if (fullPinyin && fullPinyin.toLowerCase().includes(lowerQuery)) {
                                    return true;
                                }
                            } catch (e) {
                                console.error('拼音匹配出错:', e);
                            }
                        }
                        
                        return false;
                    });
                    
                    console.log('过滤后的商铺列表:', filteredData);
                    
                    storeSearchResults.innerHTML = '';
                    
                    if (filteredData.length === 0) {
                        const noResult = document.createElement('div');
                        noResult.className = 'px-4 py-2 text-sm text-gray-500';
                        noResult.textContent = '未找到匹配的店铺';
                        storeSearchResults.appendChild(noResult);
                    } else {
                        filteredData.forEach(store => {
                            const item = document.createElement('div');
                            item.className = 'px-4 py-2 hover:bg-gray-100 cursor-pointer text-sm';
                            
                            // 创建强调匹配部分的文本
                            const highlightedName = highlightText(store.name, query);
                            item.innerHTML = highlightedName;
                            
                            // 确保store.id作为字符串存储
                            const storeId = store.id.toString();
                            
                            item.addEventListener('click', () => {
                                console.log('选择商铺:', store.name, '商铺ID:', storeId);
                                storeSearch.value = store.name;
                                storeSearch.dataset.id = storeId; // 将ID存储在数据集中
                                
                                // 在UI中明确显示已选择状态
                                storeSearch.classList.add('border-green-500');
                                const selectedIndicator = document.createElement('div');
                                selectedIndicator.className = 'text-xs text-green-600 mt-1';
                                selectedIndicator.textContent = `已选择: ${store.name} (ID: ${storeId})`;
                                selectedIndicator.id = 'selected-store-indicator';
                                
                                // 移除旧的指示器
                                const oldIndicator = document.getElementById('selected-store-indicator');
                                if (oldIndicator) {
                                    oldIndicator.remove();
                                }
                                
                                // 添加新的指示器
                                storeSearch.parentNode.appendChild(selectedIndicator);
                                
                                storeSearchResults.classList.add('hidden');
                                
                                // 如果店铺有默认地址，自动选择对应地址
                                if (store.default_address_id) {
                                    storeLocation.value = store.default_address_id;
                                }
                            });
                            storeSearchResults.appendChild(item);
                        });
                    }
                })
                .catch(error => {
                    console.error('搜索店铺失败:', error);
                    storeSearchResults.innerHTML = `
                        <div class="px-4 py-2 text-sm text-red-500">
                            搜索失败: ${error.message}
                        </div>
                    `;
                });
        }
        
        // 获取拼音首字母
        function getFirstLetters(text) {
            if (!text) return '';
            
            try {
                console.log('Testing Pinyin availability:', {
                    isPinyinDefined: typeof Pinyin !== 'undefined',
                    hasPinyinUtil: typeof pinyinUtil !== 'undefined'
                });
                
                // 直接使用js-pinyin.js
                if (typeof Pinyin !== 'undefined') {
                    try {
                        const pinyinInstance = new Pinyin();
                        console.log('Using Pinyin instance with getCamelChars');
                        return pinyinInstance.getCamelChars(text);
                    } catch (e) {
                        console.error('Error creating Pinyin instance:', e);
                    }
                }
                
                // 备选: 使用pinyinUtil
                if (typeof pinyinUtil !== 'undefined' && pinyinUtil.getFirstLetters) {
                    console.log('Using pinyinUtil.getFirstLetters');
                    return pinyinUtil.getFirstLetters(text);
                }
                
                console.warn('No valid pinyin conversion method found, returning original text');
                return text;
            } catch (e) {
                console.error('Error in getFirstLetters:', e);
                return text;
            }
        }
        
        // 高亮显示文本中匹配的部分
        function highlightText(text, query) {
            if (!query || !text) return text;
            
            try {
                const lowerText = text.toLowerCase();
                const lowerQuery = query.toLowerCase();
                
                // 直接文本匹配
                if (lowerText.includes(lowerQuery)) {
                    const index = lowerText.indexOf(lowerQuery);
                    const beforeMatch = text.substring(0, index);
                    const match = text.substring(index, index + query.length);
                    const afterMatch = text.substring(index + query.length);
                    
                    return `${beforeMatch}<span class="font-bold text-indigo-600">${match}</span>${afterMatch}`;
                } else {
                    // 检查拼音首字母匹配
                    const pinyinFirst = getFirstLetters(text);
                    console.log('Pinyin first letters:', pinyinFirst, 'for text:', text, 'query:', lowerQuery);
                    
                    if (pinyinFirst && pinyinFirst.toLowerCase().includes(lowerQuery)) {
                        // 显示拼音首字母提示
                        return `<span class="font-normal">${text}</span> <span class="text-xs text-gray-500">(${pinyinFirst})</span>`;
                    }
                    
                    // 尝试全拼音匹配
                    if (typeof Pinyin !== 'undefined') {
                        try {
                            const pinyinInstance = new Pinyin();
                            const fullPinyin = pinyinInstance.getFullChars(text);
                            
                            if (fullPinyin && fullPinyin.toLowerCase().includes(lowerQuery)) {
                                // 显示全拼音提示
                                return `<span class="font-normal">${text}</span> <span class="text-xs text-gray-500">(${fullPinyin})</span>`;
                            }
                        } catch (e) {
                            console.error('Error getting full pinyin:', e);
                        }
                    }
                }
            } catch (e) {
                console.error('高亮文本失败:', e);
            }
            
            return text;
        }
        
        // 图片上传处理
        function handleImageUpload(e) {
            const file = e.target.files[0];
            if (!file) return;
            
            // 验证文件类型
            const validTypes = ['image/jpeg', 'image/png', 'image/gif'];
            if (!validTypes.includes(file.type)) {
                alert('请上传JPG、PNG或GIF格式的图片');
                return;
            }
            
            // 验证文件大小
            if (file.size > 10 * 1024 * 1024) { // 10MB
                alert('图片大小不能超过10MB');
                return;
            }
            
            const reader = new FileReader();
            reader.onload = function(event) {
                imagePreview.src = event.target.result;
                imagePreviewContainer.classList.remove('hidden');
            };
            reader.readAsDataURL(file);
        }
        
        // 移除图片
        function removeImage() {
            feedbackImage.value = '';
            imagePreview.src = '';
            imagePreviewContainer.classList.add('hidden');
        }
        
        // 表单提交处理
        function handleFormSubmit(e) {
            e.preventDefault();
            
            // 清除旧的错误信息
            clearFormErrors();
            
            // 表单验证
            let hasErrors = false;
            
            console.log('提交表单 - 商铺名称:', storeSearch.value);
            console.log('提交表单 - 商铺ID:', storeSearch.dataset.id);
            console.log('提交表单 - 商铺地点:', storeLocation.value);
            
            if (!storeSearch.value.trim()) {
                displayError(storeSearch, '请选择或输入商铺名称');
                hasErrors = true;
            }
            
            if (!storeLocation.value) {
                displayError(storeLocation, '请选择商铺地点');
                hasErrors = true;
            }
            
            if (!feedbackContent.value.trim()) {
                displayError(feedbackContent, '请输入反馈问题');
                hasErrors = true;
            }
            
            if (hasErrors) {
                return;
            }
            
            // 显示提交中状态
            const submitBtn = feedbackForm.querySelector('button[type="submit"]');
            const originalBtnText = submitBtn.innerHTML;
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> 提交中...';
            
            // 创建FormData对象
            const formData = new FormData();
            
            // 如果有store_id使用id，否则使用新名称
            if (storeSearch.dataset.id) {
                console.log('使用商铺ID提交:', storeSearch.dataset.id);
                formData.append('store_id', storeSearch.dataset.id);
            } else {
                console.log('使用商铺名称提交:', storeSearch.value.trim());
                formData.append('store_name', storeSearch.value.trim());
            }
            
            // 确保地点ID是有效的
            if (storeLocation.value) {
                formData.append('store_location', storeLocation.value);
            }
            
            formData.append('feedback_content', feedbackContent.value.trim());
            formData.append('notes', notes.value.trim());
            
            if (feedbackImage.files.length > 0) {
                formData.append('image', feedbackImage.files[0]);
            }
            
            // 输出表单数据以便调试
            console.log('表单数据:');
            for (let pair of formData.entries()) {
                console.log(pair[0], pair[1]);
            }
            
            // 提交到服务器
            fetchWithAuth('/api/store-feedback', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (!response || !response.ok) {
                    return response.json().then(data => {
                        throw new Error(data.error || '提交失败');
                    });
                }
                return response.json();
            })
            .then(data => {
                // 显示成功消息
                showSuccessMessage('反馈提交成功！');
                
                // 重置表单
                feedbackForm.reset();
                removeImage();
                
                // 清除商铺数据和选择指示器
                storeSearch.dataset.id = '';
                storeSearch.classList.remove('border-green-500');
                const indicator = document.getElementById('selected-store-indicator');
                if (indicator) {
                    indicator.remove();
                }
                
                // 重新加载数据
                loadFeedbacks();
                
                // 更新图表
                initCharts();
            })
            .catch(error => {
                console.error('提交反馈失败:', error);
                showErrorMessage('提交失败: ' + error.message);
            })
            .finally(() => {
                // 恢复按钮状态
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalBtnText;
            });
        }
        
        // 显示错误信息
        function displayError(inputElement, message) {
            // 添加红色边框
            inputElement.classList.add('border-red-500');
            
            // 创建错误消息元素
            const errorDiv = document.createElement('div');
            errorDiv.className = 'text-red-500 text-sm mt-1';
            errorDiv.textContent = message;
            
            // 插入到输入字段后面
            inputElement.parentNode.appendChild(errorDiv);
        }
        
        // 清除表单错误信息
        function clearFormErrors() {
            // 移除所有红色边框
            const formInputs = feedbackForm.querySelectorAll('input, select, textarea');
            formInputs.forEach(input => {
                input.classList.remove('border-red-500');
            });
            
            // 移除所有错误消息
            const errorMessages = feedbackForm.querySelectorAll('.text-red-500');
            errorMessages.forEach(el => el.remove());
        }
        
        // 显示成功消息
        function showSuccessMessage(message) {
            const alertDiv = document.createElement('div');
            alertDiv.className = 'fixed top-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded z-50';
            alertDiv.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-check-circle mr-2"></i>
                    <span>${message}</span>
                </div>
            `;
            document.body.appendChild(alertDiv);
            
            // 确保3秒后无论如何都关闭加载提示
            setTimeout(() => {
                alertDiv.classList.add('opacity-0', 'transition-opacity', 'duration-500');
                setTimeout(() => alertDiv.remove(), 500);
            }, 3000);
        }
        
        // 显示错误消息
        function showErrorMessage(message) {
            const alertDiv = document.createElement('div');
            alertDiv.className = 'fixed top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded z-50';
            alertDiv.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-exclamation-circle mr-2"></i>
                    <span>${message}</span>
                </div>
            `;
            document.body.appendChild(alertDiv);
            
            // 确保3秒后无论如何都关闭加载提示
            setTimeout(() => {
                alertDiv.classList.add('opacity-0', 'transition-opacity', 'duration-500');
                setTimeout(() => alertDiv.remove(), 500);
            }, 3000);
        }
        
        // 加载反馈数据
        function loadFeedbacks() {
            const searchQuery = feedbackSearch.value.trim();
            const statusValue = statusFilter.value;
            
            let url = `/api/store-feedback?page=${currentPage}&limit=${pageSize}`;
            if (searchQuery) {
                url += `&search=${encodeURIComponent(searchQuery)}`;
            }
            if (statusValue !== 'all') {
                url += `&status=${encodeURIComponent(statusValue)}`;
            }
            
            fetchWithAuth(url)
                .then(response => {
                    if (!response || !response.ok) {
                        if (response.status === 401) {
                            showAuthError();
                            return Promise.reject(new Error('未授权访问，请先登录'));
                        }
                        return Promise.reject(new Error('加载反馈数据失败: ' + response.statusText));
                    }
                    return response.json();
                })
                .then(data => {
                    feedbacks = data.feedbacks || [];
                    totalRecords = data.total || 0;
                    
                    renderFeedbackTable();
                    updatePagination();
                    
                    // 如果包含分析数据，则直接调用分析函数
                    if (data.analytics) {
                        try {
                            renderAnalytics(data.analytics);
                        } catch (error) {
                            console.error('渲染分析数据失败:', error);
                        }
                    }
                })
                .catch(error => {
                    console.error('加载反馈数据失败:', error);
                    // 显示用户友好的错误消息
                    feedbackTableBody.innerHTML = `
                        <tr>
                            <td colspan="6" class="px-3 py-4 text-center text-red-500">
                                <div class="flex flex-col items-center">
                                    <i class="fas fa-exclamation-circle text-xl mb-2"></i>
                                    <p>加载数据失败</p>
                                    <p class="text-sm">${error.message || '请稍后重试'}</p>
                                </div>
                            </td>
                        </tr>
                    `;
                    // 确保分页信息正确
                    totalRecords = 0;
                    updatePagination();
                });
        }
        
        // 渲染反馈表格
        function renderFeedbackTable() {
            feedbackTableBody.innerHTML = '';
            
            if (feedbacks.length === 0) {
                const emptyRow = document.createElement('tr');
                const emptyCell = document.createElement('td');
                emptyCell.colSpan = 3; // 3列
                emptyCell.className = 'px-3 py-4 text-center text-gray-500';
                emptyCell.textContent = '暂无数据';
                emptyRow.appendChild(emptyCell);
                feedbackTableBody.appendChild(emptyRow);
                return;
            }
            
            feedbacks.forEach(feedback => {
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50';
                
                // 店铺名称列
                const storeCell = document.createElement('td');
                storeCell.className = 'px-3 py-2 whitespace-nowrap text-sm';
                
                // 创建店铺名称和时间的容器
                const storeInfoContainer = document.createElement('div');
                storeInfoContainer.className = 'flex flex-col';
                
                // 店铺名称
                const storeName = document.createElement('div');
                storeName.className = 'font-medium text-gray-900';
                storeName.textContent = feedback.store_name;
                storeInfoContainer.appendChild(storeName);
                
                // 时间
                const timeStamp = document.createElement('div');
                timeStamp.className = 'text-xs text-gray-500';
                timeStamp.textContent = formatDate(feedback.feedback_time);
                storeInfoContainer.appendChild(timeStamp);
                
                storeCell.appendChild(storeInfoContainer);
                row.appendChild(storeCell);
                
                // 图片列
                const imageCell = document.createElement('td');
                imageCell.className = 'px-3 py-2 whitespace-nowrap text-sm';
                
                if (feedback.image_url) {
                    const imageContainer = document.createElement('div');
                    imageContainer.className = 'flex-shrink-0 h-10 w-10 cursor-pointer';
                    
                    const image = document.createElement('img');
                    image.className = 'h-10 w-10 rounded-md object-cover hover:opacity-80 transition-opacity';
                    image.src = feedback.image_url;
                    image.alt = "反馈图片";
                    image.title = "点击查看大图";
                    
                    // 添加点击查看大图功能
                    imageContainer.addEventListener('click', () => {
                        openImageModal(feedback.image_url);
                    });
                    
                    imageContainer.appendChild(image);
                    imageCell.appendChild(imageContainer);
                } else {
                    imageCell.textContent = '-';
                }
                
                row.appendChild(imageCell);
                
                // 操作列
                const actionCell = document.createElement('td');
                actionCell.className = 'px-3 py-2 whitespace-nowrap text-sm text-right';
                
                // 操作按钮容器
                const actionContainer = document.createElement('div');
                actionContainer.className = 'flex justify-end space-x-2';
                
                // 查看按钮
                const viewButton = document.createElement('button');
                viewButton.className = 'text-indigo-600 hover:text-indigo-900 bg-blue-100 rounded-full w-6 h-6 flex items-center justify-center';
                viewButton.innerHTML = '<i class="fas fa-eye"></i>';
                viewButton.title = '查看详情';
                viewButton.addEventListener('click', () => viewFeedbackDetails(feedback.id));
                actionContainer.appendChild(viewButton);
                
                // 编辑按钮
                const editButton = document.createElement('button');
                editButton.className = 'text-indigo-600 hover:text-indigo-900 bg-blue-100 rounded-full w-6 h-6 flex items-center justify-center';
                editButton.innerHTML = '<i class="fas fa-edit"></i>';
                editButton.title = '编辑';
                editButton.addEventListener('click', () => viewFeedbackDetails(feedback.id));
                actionContainer.appendChild(editButton);
                
                // 删除按钮
                const deleteButton = document.createElement('button');
                deleteButton.className = 'text-red-600 hover:text-red-900 bg-red-100 rounded-full w-6 h-6 flex items-center justify-center';
                deleteButton.innerHTML = '<i class="fas fa-trash"></i>';
                deleteButton.title = '删除';
                deleteButton.addEventListener('click', () => deleteFeedback(feedback.id));
                actionContainer.appendChild(deleteButton);
                
                actionCell.appendChild(actionContainer);
                row.appendChild(actionCell);
                
                feedbackTableBody.appendChild(row);
            });
        }
        
        // 更新分页
        function updatePagination() {
            const totalPages = Math.ceil(totalRecords / pageSize);
            pageInfoSpan.textContent = `第 ${currentPage} 页 / 共 ${totalPages} 页`;
            totalCountSpan.textContent = `共 ${totalRecords} 条记录`;
            
            prevPageBtn.disabled = currentPage <= 1;
            nextPageBtn.disabled = currentPage >= totalPages;
            
            if (prevPageBtn.disabled) {
                prevPageBtn.classList.add('opacity-50');
            } else {
                prevPageBtn.classList.remove('opacity-50');
            }
            
            if (nextPageBtn.disabled) {
                nextPageBtn.classList.add('opacity-50');
            } else {
                nextPageBtn.classList.remove('opacity-50');
            }
        }
        
        // 切换页面
        function changePage(change) {
            const newPage = currentPage + change;
            if (newPage < 1) return;
            
            const totalPages = Math.ceil(totalRecords / pageSize);
            if (newPage > totalPages) return;
            
            currentPage = newPage;
            loadFeedbacks();
        }
        
        // 查看反馈详情
        function viewFeedbackDetails(id) {
            fetchWithAuth(`/api/store-feedback/${id}`)
                .then(response => response.json())
                .then(data => {
                    currentFeedbackId = id;
                    renderFeedbackDetails(data);
                    feedbackModal.classList.remove('hidden');
                })
                .catch(error => console.error('获取反馈详情失败:', error));
        }
        
        // 渲染反馈详情
        function renderFeedbackDetails(feedback) {
            feedbackDetails.innerHTML = `
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                        <p class="text-sm text-gray-500">商铺名称</p>
                        <p class="font-medium">${feedback.store_name}</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">商铺地点</p>
                        <p class="font-medium">${feedback.store_location}</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">提交时间</p>
                        <p class="font-medium">${formatDate(feedback.feedback_time)}</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">状态</p>
                        <p><span class="px-2 py-1 rounded-full text-xs ${getStatusStyle(feedback.status)}">${getStatusText(feedback.status)}</span></p>
                    </div>
                </div>
                <div class="mt-4">
                    <p class="text-sm text-gray-500">反馈问题</p>
                    <p class="mt-1 whitespace-pre-wrap">${feedback.feedback_content}</p>
                </div>
                ${feedback.notes ? `
                    <div class="mt-4">
                        <p class="text-sm text-gray-500">备注</p>
                        <p class="mt-1 whitespace-pre-wrap">${feedback.notes}</p>
                    </div>
                ` : ''}
                ${feedback.image_url ? `
                    <div class="mt-4">
                        <p class="text-sm text-gray-500">图片</p>
                        <div class="mt-2">
                            <img src="${feedback.image_url}" alt="反馈图片" class="max-w-full max-h-64 rounded-md cursor-pointer hover:opacity-90 transition-opacity" onclick="openImageModal('${feedback.image_url}')" title="点击查看大图">
                        </div>
                    </div>
                ` : ''}
            `;
            
            // 更新状态下拉框的值
            feedbackStatus.value = feedback.status;
        }
        
        // 打开状态模态框
        function openStatusModal() {
            statusModal.classList.remove('hidden');
        }
        
        // 更新反馈状态
        function updateFeedbackStatus() {
            const newStatus = feedbackStatus.value;
            
            fetchWithAuth(`/api/store-feedback/${currentFeedbackId}/status`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ status: newStatus })
            })
            .then(response => {
                if (!response || !response.ok) {
                    throw new Error('更新失败');
                }
                return response.json();
            })
            .then(data => {
                statusModal.classList.add('hidden');
                feedbackModal.classList.add('hidden');
                loadFeedbacks(); // 刷新数据
                initCharts(); // 更新图表
            })
            .catch(error => {
                console.error('更新状态失败:', error);
                alert('更新状态失败，请稍后重试');
            });
        }
        
        // 删除反馈
        function deleteFeedback(id) {
            if (!confirm('确定要删除这条反馈记录吗？')) return;
            
            fetchWithAuth(`/api/store-feedback/${id}`, {
                method: 'DELETE'
            })
            .then(response => {
                if (!response || !response.ok) {
                    throw new Error('删除失败');
                }
                return response.json();
            })
            .then(data => {
                loadFeedbacks(); // 刷新数据
                initCharts(); // 更新图表
            })
            .catch(error => {
                console.error('删除反馈失败:', error);
                alert('删除失败，请稍后重试');
            });
        }
        
        // 初始化图表
        function initCharts() {
            try {
                // 检查Chart.js是否可用
                if (typeof Chart === 'undefined') {
                    console.error('Chart.js 未加载');
                    document.querySelectorAll('.chart-error').forEach(el => {
                        el.textContent = 'Chart.js 未加载，无法渲染图表';
                        el.classList.remove('hidden');
                    });
                    return;
                }
                
                fetchWithAuth('/api/store-feedback/analytics')
                    .then(response => {
                        if (!response || !response.ok) {
                            if (response.status === 401) {
                                // 不要重复显示认证错误
                                return Promise.reject(new Error('未授权访问'));
                            }
                            return Promise.reject(new Error('获取分析数据失败: ' + response.statusText));
                        }
                        return response.json();
                    })
                    .then(data => {
                        renderAnalytics(data);
                    })
                    .catch(error => {
                        console.error('获取分析数据失败:', error);
                        // 显示所有图表区域的错误状态
                        const chartContainers = [
                            document.getElementById('wordCloudContainer'),
                            document.getElementById('feedbackTimeChart').parentNode,
                            document.getElementById('feedbackTypeChart').parentNode,
                            document.getElementById('locationChart').parentNode,
                            document.getElementById('categoryChart').parentNode,
                            document.getElementById('resolutionTimeValue') ? document.getElementById('resolutionTimeValue').parentNode : null
                        ].filter(Boolean);
                        
                        chartContainers.forEach(container => {
                            if (container) {
                                container.innerHTML = `
                                    <div class="flex flex-col items-center justify-center h-full">
                                        <p class="text-gray-500">无法加载图表数据</p>
                                    </div>
                                `;
                            }
                        });
                    });
            } catch (error) {
                console.error('初始化图表失败:', error);
                
                document.querySelectorAll('.chart-error').forEach(el => {
                    el.textContent = '图表加载失败: ' + (error.message || '未知错误');
                    el.classList.remove('hidden');
                });
            }
        }
        
        // 渲染分析图表
        function renderAnalytics(data) {
            console.log('开始渲染分析图表...');
            
            if (!data) {
                console.error('分析数据为空');
                return;
            }
            
            try {
                // 渲染词云
                if (data.wordCloud) {
                    try {
                        renderWordCloud(data.wordCloud);
                        console.log('词云渲染完成');
                    } catch (e) {
                        console.error('词云渲染失败:', e);
                        const container = document.getElementById('wordCloudContainer');
                        if (container) {
                            container.innerHTML = '<p class="text-center text-red-500 mt-4">词云渲染失败</p>';
                        }
                    }
                }
                
                // 渲染时间分布图
                if (data.timeDistribution) {
                    try {
                        renderTimeChart(data.timeDistribution);
                        console.log('时间分布图渲染完成');
                    } catch (e) {
                        console.error('时间分布图渲染失败:', e);
                        const canvas = document.getElementById('feedbackTimeChart');
                        if (canvas && canvas.parentNode) {
                            canvas.parentNode.innerHTML = '<p class="text-center text-red-500 mt-4">时间分布图渲染失败</p>';
                        }
                    }
                }
                
                // 渲染状态分布图
                if (data.statusDistribution) {
                    try {
                        renderTypeChart(data.statusDistribution);
                        console.log('状态分布图渲染完成');
                    } catch (e) {
                        console.error('状态分布图渲染失败:', e);
                        const canvas = document.getElementById('feedbackTypeChart');
                        if (canvas && canvas.parentNode) {
                            canvas.parentNode.innerHTML = '<p class="text-center text-red-500 mt-4">状态分布图渲染失败</p>';
                        }
                    }
                }
                
                // 渲染地点分布图
                if (data.locationDistribution) {
                    try {
                        renderLocationChart(data.locationDistribution);
                        console.log('地点分布图渲染完成');
                    } catch (e) {
                        console.error('地点分布图渲染失败:', e);
                        const canvas = document.getElementById('locationChart');
                        if (canvas && canvas.parentNode) {
                            canvas.parentNode.innerHTML = '<p class="text-center text-red-500 mt-4">地点分布图渲染失败</p>';
                        }
                    }
                }
                
                // 渲染问题分类图
                if (data.categoryDistribution) {
                    try {
                        renderCategoryChart(data.categoryDistribution);
                        console.log('问题分类图渲染完成');
                    } catch (e) {
                        console.error('问题分类图渲染失败:', e);
                        const canvas = document.getElementById('categoryChart');
                        if (canvas && canvas.parentNode) {
                            canvas.parentNode.innerHTML = '<p class="text-center text-red-500 mt-4">问题分类图渲染失败</p>';
                        }
                    }
                }
                
                // 渲染解决时间
                if (data.resolutionTimeAvg) {
                    try {
                        renderResolutionTime(data.resolutionTimeAvg);
                        console.log('解决时间渲染完成');
                    } catch (e) {
                        console.error('解决时间渲染失败:', e);
                        const element = document.getElementById('resolutionTimeValue');
                        if (element) {
                            element.textContent = 'N/A';
                        }
                    }
                }
                
                // 渲染商铺排名
                if (data.storeRanking) {
                    try {
                        renderStoreRanking(data.storeRanking);
                        console.log('商铺排名渲染完成');
                    } catch (e) {
                        console.error('商铺排名渲染失败:', e);
                        const tableBody = document.getElementById('storeRankingBody');
                        if (tableBody) {
                            tableBody.innerHTML = '<tr><td colspan="3" class="text-center text-red-500 py-4">商铺排名渲染失败</td></tr>';
                        }
                    }
                }
                
                console.log('所有分析图表渲染完成');
            } catch (e) {
                console.error('渲染分析图表过程中发生错误:', e);
                showErrorMessage('数据分析渲染失败');
            }
        }
        
        // 渲染词云
        function renderWordCloud(wordCloudData) {
            const container = document.getElementById('wordCloudContainer');
            
            // 如果已经有词云实例，先销毁
            if (wordCloudInstance) {
                container.innerHTML = '';
            }
            
            if (!wordCloudData || wordCloudData.length === 0) {
                container.innerHTML = '<p class="text-center text-gray-500 mt-10">暂无数据</p>';
                return;
            }
            
            // 创建词云
            wordCloudInstance = WordCloud(container, {
                list: wordCloudData,
                gridSize: 16,
                weightFactor: 10,
                fontFamily: 'sans-serif',
                color: 'random-dark',
                rotateRatio: 0.5,
                backgroundColor: 'transparent'
            });
        }
        
        // 渲染时间分布图
        function renderTimeChart(timeData) {
            const canvas = document.getElementById('feedbackTimeChart');
            if (!canvas) {
                console.error('找不到时间分布图的画布元素');
                return;
            }
            
            const ctx = canvas.getContext('2d');
            
            // 如果已经有图表实例，先销毁
            if (timeChart) {
                timeChart.destroy();
            }
            
            if (!timeData || Object.keys(timeData).length === 0) {
                ctx.canvas.parentNode.style.display = 'flex';
                ctx.canvas.parentNode.style.justifyContent = 'center';
                ctx.canvas.parentNode.style.alignItems = 'center';
                ctx.canvas.style.display = 'none';
                
                const noDataText = document.createElement('p');
                noDataText.className = 'text-center text-gray-500';
                noDataText.textContent = '暂无数据';
                
                // 清除之前的内容
                while (ctx.canvas.parentNode.childNodes.length > 1) {
                    if (ctx.canvas.parentNode.lastChild !== ctx.canvas) {
                        ctx.canvas.parentNode.removeChild(ctx.canvas.parentNode.lastChild);
                    }
                }
                
                ctx.canvas.parentNode.appendChild(noDataText);
                return;
            }
            
            // 显示画布，移除可能存在的无数据文本
            ctx.canvas.style.display = 'block';
            ctx.canvas.parentNode.style.display = 'block';
            
            // 清除可能存在的文本节点
            Array.from(ctx.canvas.parentNode.childNodes).forEach(node => {
                if (node !== ctx.canvas) {
                    ctx.canvas.parentNode.removeChild(node);
                }
            });
            
            const labels = Object.keys(timeData);
            const data = Object.values(timeData);
            
            timeChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '反馈数量',
                        data: data,
                        backgroundColor: 'rgba(99, 102, 241, 0.6)',
                        borderColor: 'rgb(99, 102, 241)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                precision: 0
                            }
                        }
                    }
                }
            });
        }
        
        // 渲染状态分布图
        function renderTypeChart(typeData) {
            const canvas = document.getElementById('feedbackTypeChart');
            if (!canvas) {
                console.error('找不到状态分布图的画布元素');
                return;
            }
            
            const ctx = canvas.getContext('2d');
            
            // 如果已经有图表实例，先销毁
            if (typeChart) {
                typeChart.destroy();
            }
            
            if (!typeData || Object.keys(typeData).length === 0) {
                ctx.canvas.parentNode.style.display = 'flex';
                ctx.canvas.parentNode.style.justifyContent = 'center';
                ctx.canvas.parentNode.style.alignItems = 'center';
                ctx.canvas.style.display = 'none';
                
                const noDataText = document.createElement('p');
                noDataText.className = 'text-center text-gray-500';
                noDataText.textContent = '暂无数据';
                
                // 清除之前的内容
                while (ctx.canvas.parentNode.childNodes.length > 1) {
                    if (ctx.canvas.parentNode.lastChild !== ctx.canvas) {
                        ctx.canvas.parentNode.removeChild(ctx.canvas.parentNode.lastChild);
                    }
                }
                
                ctx.canvas.parentNode.appendChild(noDataText);
                return;
            }
            
            // 显示画布，移除可能存在的无数据文本
            ctx.canvas.style.display = 'block';
            ctx.canvas.parentNode.style.display = 'block';
            
            // 清除可能存在的文本节点
            Array.from(ctx.canvas.parentNode.childNodes).forEach(node => {
                if (node !== ctx.canvas) {
                    ctx.canvas.parentNode.removeChild(node);
                }
            });
            
            const labels = Object.keys(typeData).map(status => getStatusText(status));
            const data = Object.values(typeData);
            
            typeChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: labels,
                    datasets: [{
                        data: data,
                        backgroundColor: [
                            'rgba(245, 158, 11, 0.7)',  // 待处理
                            'rgba(59, 130, 246, 0.7)',  // 处理中
                            'rgba(16, 185, 129, 0.7)'   // 已解决
                        ],
                        borderColor: [
                            'rgb(245, 158, 11)',
                            'rgb(59, 130, 246)',
                            'rgb(16, 185, 129)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        }
        
        // 渲染地点分布图
        function renderLocationChart(locationData) {
            try {
                const canvas = document.getElementById('locationChart');
                if (!canvas) {
                    console.error('找不到地点分布图的画布元素');
                    const errorDiv = document.querySelector('.location-chart-container .chart-error');
                    if (errorDiv) {
                        errorDiv.textContent = '找不到地点分布图的画布元素';
                        errorDiv.classList.remove('hidden');
                    }
                    return;
                }
                
                const ctx = canvas.getContext('2d');
                
                // 如果已经有图表实例，先销毁
                if (locationChart) {
                    locationChart.destroy();
                }
                
                if (!locationData || Object.keys(locationData).length === 0) {
                    showNoDataMessage(ctx);
                    return;
                }
                
                // 显示画布，移除可能存在的无数据文本
                resetCanvas(ctx);
                
                const labels = Object.keys(locationData);
                const data = Object.values(locationData);
                
                locationChart = new Chart(ctx, {
                    type: 'pie',
                    data: {
                        labels: labels,
                        datasets: [{
                            data: data,
                            backgroundColor: [
                                'rgba(255, 99, 132, 0.7)',
                                'rgba(54, 162, 235, 0.7)',
                                'rgba(255, 206, 86, 0.7)',
                                'rgba(75, 192, 192, 0.7)',
                                'rgba(153, 102, 255, 0.7)'
                            ],
                            borderColor: [
                                'rgb(255, 99, 132)',
                                'rgb(54, 162, 235)',
                                'rgb(255, 206, 86)',
                                'rgb(75, 192, 192)',
                                'rgb(153, 102, 255)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'right',
                                labels: {
                                    boxWidth: 12,
                                    font: {
                                        size: 10
                                    }
                                }
                            }
                        }
                    }
                });
                
                // 成功渲染后隐藏错误消息
                const errorDiv = document.querySelector('.location-chart-container .chart-error');
                if (errorDiv) {
                    errorDiv.classList.add('hidden');
                }
            } catch (error) {
                console.error('地点分布图渲染失败:', error);
                const errorDiv = document.querySelector('.location-chart-container .chart-error');
                if (errorDiv) {
                    errorDiv.textContent = '地点分布图渲染失败';
                    errorDiv.classList.remove('hidden');
                }
            }
        }
        
        // 渲染问题分类图
        function renderCategoryChart(categoryData) {
            try {
                const canvas = document.getElementById('categoryChart');
                if (!canvas) {
                    console.error('找不到问题分类图的画布元素');
                    const errorDiv = document.querySelector('.category-chart-container .chart-error');
                    if (errorDiv) {
                        errorDiv.textContent = '找不到问题分类图的画布元素';
                        errorDiv.classList.remove('hidden');
                    }
                    return;
                }
                
                const ctx = canvas.getContext('2d');
                
                // 如果已经有图表实例，先销毁
                if (categoryChart) {
                    categoryChart.destroy();
                }
                
                if (!categoryData || Object.keys(categoryData).length === 0) {
                    showNoDataMessage(ctx);
                    return;
                }
                
                // 显示画布，移除可能存在的无数据文本
                resetCanvas(ctx);
                
                const labels = Object.keys(categoryData);
                const data = Object.values(categoryData);
                
                categoryChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: '问题数量',
                            data: data,
                            backgroundColor: 'rgba(75, 192, 192, 0.7)',
                            borderColor: 'rgb(75, 192, 192)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    precision: 0
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                display: false
                            }
                        }
                    }
                });
                
                // 成功渲染后隐藏错误消息
                const errorDiv = document.querySelector('.category-chart-container .chart-error');
                if (errorDiv) {
                    errorDiv.classList.add('hidden');
                }
            } catch (error) {
                console.error('问题分类图渲染失败:', error);
                const errorDiv = document.querySelector('.category-chart-container .chart-error');
                if (errorDiv) {
                    errorDiv.textContent = '问题分类图渲染失败';
                    errorDiv.classList.remove('hidden');
                }
            }
        }
        
        // 渲染解决时间
        function renderResolutionTime(timeData) {
            const valueElement = document.getElementById('resolutionTimeValue');
            const unitElement = document.getElementById('resolutionTimeUnit');
            
            if (!timeData) {
                valueElement.textContent = '0';
                unitElement.textContent = '小时';
                return;
            }
            
            valueElement.textContent = timeData.avg;
            unitElement.textContent = timeData.unit;
        }
        
        // 渲染商铺排名
        function renderStoreRanking(rankingData) {
            const tableBody = document.getElementById('storeRankingBody');
            tableBody.innerHTML = '';
            
            if (!rankingData || rankingData.length === 0) {
                const emptyRow = document.createElement('tr');
                const emptyCell = document.createElement('td');
                emptyCell.colSpan = 3;
                emptyCell.className = 'px-3 py-4 text-center text-gray-500';
                emptyCell.textContent = '暂无数据';
                emptyRow.appendChild(emptyCell);
                tableBody.appendChild(emptyRow);
                return;
            }
            
            rankingData.forEach((store, index) => {
                const row = document.createElement('tr');
                
                // 排名列
                const rankCell = document.createElement('td');
                rankCell.className = 'px-3 py-2 whitespace-nowrap text-sm';
                
                // 使用不同的样式显示前三名
                if (index < 3) {
                    const rankBadge = document.createElement('span');
                    rankBadge.className = `inline-flex items-center justify-center w-6 h-6 rounded-full text-white text-xs font-medium ${getTopRankColor(index)}`;
                    rankBadge.textContent = index + 1;
                    rankCell.appendChild(rankBadge);
                } else {
                    rankCell.textContent = index + 1;
                }
                
                row.appendChild(rankCell);
                
                // 商铺名称列
                const nameCell = document.createElement('td');
                nameCell.className = 'px-3 py-2 whitespace-nowrap text-sm';
                nameCell.textContent = store.name;
                row.appendChild(nameCell);
                
                // 反馈数量列
                const countCell = document.createElement('td');
                countCell.className = 'px-3 py-2 whitespace-nowrap text-sm';
                
                // 使用进度条显示数量
                const progressContainer = document.createElement('div');
                progressContainer.className = 'flex items-center';
                
                const progressBar = document.createElement('div');
                progressBar.className = 'w-24 bg-gray-200 rounded-full h-2.5 mr-2';
                
                const progress = document.createElement('div');
                // 计算进度比例（相对于最高数量）
                const maxCount = rankingData[0].count;
                const percentage = (store.count / maxCount) * 100;
                progress.className = `h-2.5 rounded-full ${getTopRankColor(index)}`;
                progress.style.width = `${percentage}%`;
                
                progressBar.appendChild(progress);
                progressContainer.appendChild(progressBar);
                
                const countText = document.createElement('span');
                countText.textContent = store.count;
                progressContainer.appendChild(countText);
                
                countCell.appendChild(progressContainer);
                row.appendChild(countCell);
                
                tableBody.appendChild(row);
            });
        }
        
        // 获取排名前三的颜色
        function getTopRankColor(index) {
            switch (index) {
                case 0: return 'bg-yellow-500'; // 金牌
                case 1: return 'bg-gray-400';   // 银牌
                case 2: return 'bg-yellow-700'; // 铜牌
                default: return 'bg-blue-500';  // 其他
            }
        }
        
        // 显示无数据消息
        function showNoDataMessage(ctx) {
            ctx.canvas.parentNode.style.display = 'flex';
            ctx.canvas.parentNode.style.justifyContent = 'center';
            ctx.canvas.parentNode.style.alignItems = 'center';
            ctx.canvas.style.display = 'none';
            
            const noDataText = document.createElement('p');
            noDataText.className = 'text-center text-gray-500';
            noDataText.textContent = '暂无数据';
            
            // 清除之前的内容
            while (ctx.canvas.parentNode.childNodes.length > 1) {
                if (ctx.canvas.parentNode.lastChild !== ctx.canvas) {
                    ctx.canvas.parentNode.removeChild(ctx.canvas.parentNode.lastChild);
                }
            }
            
            ctx.canvas.parentNode.appendChild(noDataText);
        }
        
        // 重置画布
        function resetCanvas(ctx) {
            ctx.canvas.style.display = 'block';
            ctx.canvas.parentNode.style.display = 'block';
            
            // 清除可能存在的文本节点
            Array.from(ctx.canvas.parentNode.childNodes).forEach(node => {
                if (node !== ctx.canvas) {
                    ctx.canvas.parentNode.removeChild(node);
                }
            });
        }
        
        // 辅助函数
        
        // 截断文本
        function truncateText(text, maxLength) {
            if (!text) return '';
            return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
        }
        
        // 格式化日期
        function formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN', { 
                year: 'numeric', 
                month: '2-digit', 
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        }
        
        // 获取状态样式
        function getStatusStyle(status) {
            switch (status) {
                case 'pending':
                    return 'bg-yellow-100 text-yellow-800';
                case 'in-progress':
                    return 'bg-blue-100 text-blue-800';
                case 'resolved':
                    return 'bg-green-100 text-green-800';
                default:
                    return 'bg-gray-100 text-gray-800';
            }
        }
        
        // 获取状态文本
        function getStatusText(status) {
            switch (status) {
                case 'pending':
                    return '待处理';
                case 'in-progress':
                    return '处理中';
                case 'resolved':
                    return '已解决';
                default:
                    return '未知';
            }
        }
        
        // 防抖函数
        function debounce(func, delay) {
            let timeout;
            return function() {
                const context = this;
                const args = arguments;
                clearTimeout(timeout);
                timeout = setTimeout(() => func.apply(context, args), delay);
            };
        }

        // 显示认证错误的函数
        function showAuthError() {
            // 检查是否已经显示了错误信息，防止重复显示
            if (document.getElementById('auth-error-message')) {
                return;
            }

            const errorMessage = document.createElement('div');
            errorMessage.id = 'auth-error-message';
            errorMessage.className = 'fixed top-0 left-0 right-0 bg-red-500 text-white text-center py-2 px-4 z-50';
            errorMessage.innerHTML = `
                <div class="flex items-center justify-center">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    <span>未登录或会话已过期，请先<a href="/login.html" class="underline font-bold ml-1">登录</a></span>
                </div>
            `;
            document.body.prepend(errorMessage);
        }

        // 打开图片模态框
        function openImageModal(imageUrl) {
            const imageModal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');
            
            if (imageModal && modalImage) {
                modalImage.src = imageUrl;
                modalImage.onload = () => {
                    // 图片加载完成后显示模态框
                    imageModal.classList.remove('hidden');
                };
            }
        }
    } catch (e) {
        console.error('页面初始化发生严重错误:', e);
        document.body.innerHTML += `
            <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                <div class="bg-white p-6 rounded-lg max-w-lg w-full">
                    <h2 class="text-xl font-bold text-red-600 mb-4">页面加载错误</h2>
                    <p class="mb-4">页面加载过程中发生错误，请刷新页面或联系管理员。</p>
                    <p class="text-sm text-gray-600 mb-4">错误信息: ${e.message || '未知错误'}</p>
                    <div class="flex justify-end">
                        <button onclick="location.reload()" class="px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700">
                            刷新页面
                        </button>
                    </div>
                </div>
            </div>
        `;
    }
})(); 