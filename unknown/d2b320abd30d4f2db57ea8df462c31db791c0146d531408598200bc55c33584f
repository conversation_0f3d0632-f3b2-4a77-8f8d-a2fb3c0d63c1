const express = require('express');
const router = express.Router();
const db = require('../config/db');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const sharp = require('sharp');

// 辅助函数：确保目录存在
const ensureDirectoryExistence = (filePath) => {
    const dirname = path.dirname(filePath);
    if (fs.existsSync(dirname)) {
        return true;
    }
    ensureDirectoryExistence(dirname);
    fs.mkdirSync(dirname);
};

// 文件上传配置
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        const uploadDir = path.join(__dirname, '..', 'public', 'uploads', 'qa');
        ensureDirectoryExistence(uploadDir + '/');
        cb(null, uploadDir);
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const extension = path.extname(file.originalname);
        cb(null, 'qa-' + uniqueSuffix + extension);
    }
});

const upload = multer({
    storage: storage,
    limits: { fileSize: 10 * 1024 * 1024 }, // 10MB
    fileFilter: (req, file, cb) => {
        if (file.mimetype.startsWith('image/')) {
            cb(null, true);
        } else {
            cb(new Error('只允许上传图片文件!'), false);
        }
    }
});

const processImage = async (file) => {
    if (!file) return null;
    const originalPath = file.path;
    const filename = path.basename(file.filename, path.extname(file.filename));
    const webpFilename = `${filename}.webp`;
    const webpPath = path.join(file.destination, webpFilename);

    await sharp(originalPath)
        .webp({ quality: 100, lossless: true })
        .toFile(webpPath);
    
    fs.unlinkSync(originalPath);
    return `/uploads/qa/${webpFilename}`;
};

// 获取所有QA（分页、搜索、过滤）
router.get('/qa', async (req, res) => {
    try {
        const [rows] = await db.query('SELECT * FROM customer_service_qa ORDER BY created_at DESC');
        // 在将数据发送到前端之前，处理tags字段
        const processedRows = rows.map(qa => {
            if (qa.tags && typeof qa.tags === 'string') {
                // 将逗号分隔的字符串转换为数组，并去除空白
                qa.tags = qa.tags.split(',').map(tag => tag.trim()).filter(tag => tag);
            } else {
                // 如果没有标签或格式不正确，则返回空数组
                qa.tags = [];
            }
            return qa;
        });
        res.json({ data: processedRows });
    } catch (error) {
        console.error('获取QA列表失败:', error);
        res.status(500).json({ error: '获取QA列表失败' });
    }
});

// 获取单个QA
router.get('/qa/:id', async (req, res) => {
    try {
        const [rows] = await db.query('SELECT * FROM customer_service_qa WHERE id = ?', [req.params.id]);
        if (rows.length === 0) {
            return res.status(404).json({ error: 'QA未找到' });
        }
        // 模拟 tags 字段
        if (rows[0].tags && typeof rows[0].tags === 'string') {
            rows[0].tags = rows[0].tags.split(',').map(t => t.trim());
        } else {
            rows[0].tags = [];
        }
        res.json(rows[0]);
    } catch (error) {
        console.error('获取QA详情失败:', error);
        res.status(500).json({ error: '获取QA详情失败' });
    }
});

// 添加QA
router.post('/qa', upload.fields([{ name: 'questionImage', maxCount: 1 }, { name: 'solutionImage', maxCount: 1 }]), async (req, res) => {
    try {
        const { category, title, question, solution, tags } = req.body;
        
        const question_image_url = req.files.questionImage ? await processImage(req.files.questionImage[0]) : null;
        const solution_image_url = req.files.solutionImage ? await processImage(req.files.solutionImage[0]) : null;
        
        const [result] = await db.query(
            'INSERT INTO customer_service_qa (category, title, question, solution, tags, question_image_url, solution_image_url) VALUES (?, ?, ?, ?, ?, ?, ?)',
            [category, title, question, solution, tags, question_image_url, solution_image_url]
        );
        res.status(201).json({ id: result.insertId, message: 'QA添加成功' });
    } catch (error) {
        console.error('添加QA失败:', error);
        res.status(500).json({ error: '添加QA失败' });
    }
});

// 更新QA
router.put('/qa/:id', upload.fields([{ name: 'questionImage', maxCount: 1 }, { name: 'solutionImage', maxCount: 1 }]), async (req, res) => {
    try {
        const { id } = req.params;
        const { category, title, question, solution, tags, removeQuestionImage, removeSolutionImage } = req.body;

        const [existing] = await db.query('SELECT * FROM customer_service_qa WHERE id = ?', [id]);
        if (existing.length === 0) return res.status(404).json({ error: 'QA未找到' });

        let question_image_url = existing[0].question_image_url;
        if (removeQuestionImage === 'true' && question_image_url) {
            fs.unlink(path.join(__dirname, '..', 'public', question_image_url), () => {});
            question_image_url = null;
        }
        if (req.files.questionImage) {
            if (question_image_url) fs.unlink(path.join(__dirname, '..', 'public', question_image_url), () => {});
            question_image_url = await processImage(req.files.questionImage[0]);
        }

        let solution_image_url = existing[0].solution_image_url;
        if (removeSolutionImage === 'true' && solution_image_url) {
            fs.unlink(path.join(__dirname, '..', 'public', solution_image_url), () => {});
            solution_image_url = null;
        }
        if (req.files.solutionImage) {
            if (solution_image_url) fs.unlink(path.join(__dirname, '..', 'public', solution_image_url), () => {});
            solution_image_url = await processImage(req.files.solutionImage[0]);
        }

        await db.query(
            'UPDATE customer_service_qa SET category=?, title=?, question=?, solution=?, tags=?, question_image_url=?, solution_image_url=? WHERE id=?',
            [category, title, question, solution, tags, question_image_url, solution_image_url, id]
        );
        res.json({ message: 'QA更新成功' });
    } catch (error) {
        console.error('更新QA失败:', error);
        res.status(500).json({ error: '更新QA失败' });
    }
});

// 删除QA
router.delete('/qa/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const [existing] = await db.query('SELECT * FROM customer_service_qa WHERE id = ?', [id]);
        if (existing.length > 0) {
            if (existing[0].question_image_url) fs.unlink(path.join(__dirname, '..', 'public', existing[0].question_image_url), () => {});
            if (existing[0].solution_image_url) fs.unlink(path.join(__dirname, '..', 'public', existing[0].solution_image_url), () => {});
        }
        await db.query('DELETE FROM customer_service_qa WHERE id = ?', [id]);
        res.json({ message: 'QA删除成功' });
    } catch (error) {
        console.error('删除QA失败:', error);
        res.status(500).json({ error: '删除QA失败' });
    }
});

// 增加查看次数
router.post('/qa/:id/view', async (req, res) => {
    try {
        const { id } = req.params;
        await db.query('UPDATE customer_service_qa SET views = views + 1 WHERE id = ?', [id]);
        res.status(200).json({ message: '查看次数已更新' });
    } catch (error) {
        console.error('更新查看次数失败:', error);
        res.status(500).json({ error: '更新查看次数失败' });
    }
});

// 统计：按分类
router.get('/qa/stats/categories', async (req, res) => {
    try {
        const [rows] = await db.query('SELECT category, COUNT(*) as count FROM customer_service_qa GROUP BY category');
        res.json(rows);
    } catch (error) {
        console.error('获取分类统计失败:', error);
        res.status(500).json({ error: '获取分类统计失败' });
    }
});

// 统计：热门标签
router.get('/qa/stats/tags', async (req, res) => {
    try {
        const [rows] = await db.query('SELECT tags FROM customer_service_qa');
        const tagCounts = rows
            .flatMap(row => row.tags ? row.tags.split(',').map(t => t.trim()) : [])
            .reduce((acc, tag) => {
                if(tag) acc[tag] = (acc[tag] || 0) + 1;
                return acc;
            }, {});
        
        const sortedTags = Object.entries(tagCounts)
            .map(([tag, count]) => ({ tag, count }))
            .sort((a, b) => b.count - a.count);
            
        res.json(sortedTags);
    } catch (error) {
        console.error('获取标签统计失败:', error);
        res.status(500).json({ error: '获取标签统计失败' });
    }
});

module.exports = router; 