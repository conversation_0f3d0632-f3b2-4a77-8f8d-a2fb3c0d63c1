const db = require('./index');

// 重建PSU表
async function rebuildPsuTable() {
    try {
        // 检查表是否存在，不存在才创建
        const [tables] = await db.query("SHOW TABLES LIKE 'psus'");
        if (tables.length === 0) {
            console.log('PSU表不存在，开始创建...');
            
            await db.query(`
                CREATE TABLE psus (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    brand VARCHAR(100) NOT NULL,
                    model VARCHAR(255) NOT NULL,
                    wattage INT NOT NULL,
                    psu_length VARCHAR(50),
                    efficiency VARCHAR(50),
                    modular VARCHAR(20),
                    pcie_connector INT,
                    sata_count INT,
                    cpu_connector VARCHAR(50),
                    fan_size INT,
                    physical_length INT,
                    warranty INT,
                    price DECIMAL(10,2),
                    notes TEXT,
                    image_url VARCHAR(255),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )
            `);
            console.log('PSU表已创建');
            return true;
        } else {
            console.log('PSU表已存在，检查是否需要更新字段...');
            
            // 检查warranty字段是否存在，不存在则添加
            const [columns] = await db.query("SHOW COLUMNS FROM psus LIKE 'warranty'");
            if (columns.length === 0) {
                console.log('添加warranty字段...');
                await db.query(`ALTER TABLE psus ADD COLUMN warranty INT AFTER physical_length`);
                console.log('warranty字段已添加');
            }
            
            return true;
        }
    } catch (error) {
        console.error('PSU表操作失败:', error);
        return false;
    }
}

// 创建GPU表
async function createGpuTable() {
    try {
        // 检查表是否存在，不存在才创建
        const [tables] = await db.query("SHOW TABLES LIKE 'gpus'");
        if (tables.length === 0) {
            console.log('GPU表不存在，开始创建...');

            await db.query(`
                CREATE TABLE gpus (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    brand VARCHAR(100) NOT NULL,
                    model VARCHAR(255) NOT NULL,
                    chipset VARCHAR(100),
                    memory_size INT,
                    memory_type VARCHAR(50),
                    memory_bus INT,
                    core_clock INT,
                    boost_clock INT,
                    tdp INT,
                    dimensions VARCHAR(100),
                    power_connectors VARCHAR(100),
                    recommended_psu INT,
                    display_ports INT,
                    hdmi_ports INT,
                    price DECIMAL(10,2),
                    notes TEXT,
                    image_url VARCHAR(255),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )
            `);
            console.log('GPU表已创建');
            return true;
        } else {
            console.log('GPU表已存在');
            return true;
        }
    } catch (error) {
        console.error('GPU表操作失败:', error);
        return false;
    }
}

// 更新所有表结构
async function updateAllSchemas() {
    console.log('开始更新数据库表结构...');

    // 更新PSU表
    const psuResult = await rebuildPsuTable();
    console.log('PSU表更新结果:', psuResult ? '成功' : '失败');

    // 创建GPU表
    const gpuResult = await createGpuTable();
    console.log('GPU表创建结果:', gpuResult ? '成功' : '失败');

    console.log('数据库表结构更新完成');
}

// 如果直接运行此文件，则执行更新
if (require.main === module) {
    updateAllSchemas()
        .then(() => {
            console.log('数据库表结构更新完成，程序退出');
            process.exit(0);
        })
        .catch(error => {
            console.error('数据库表结构更新失败:', error);
            process.exit(1);
        });
} else {
    // 作为模块导出
    module.exports = {
        rebuildPsuTable,
        createGpuTable,
        updateAllSchemas
    };
}