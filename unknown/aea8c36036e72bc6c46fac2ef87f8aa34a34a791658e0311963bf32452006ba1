/**
 * 现代化代码生成器 - 重构版本
 * 支持响应式设计、主题切换、动态字段生成
 */

const express = require('express');
const fs = require('fs');
const path = require('path');
const sharp = require('sharp');

const router = express.Router();

// 使用现有的数据库连接
const db = require('../config/db');

/**
 * 动态字段生成系统
 */
class DynamicFieldGenerator {
    constructor() {
        // 标准字段配置
        this.standardFields = [
            { name: 'price', type: 'decimal', displayName: '价格', required: false },
            { name: 'notes', type: 'text', displayName: '备注', required: false },
            { name: 'image_url', type: 'varchar', displayName: '图片URL', required: false },
            { name: 'created_at', type: 'timestamp', displayName: '创建时间', required: false },
            { name: 'updated_at', type: 'timestamp', displayName: '更新时间', required: false }
        ];
        
        // 字段类型映射
        this.typeMapping = {
            'string': 'VARCHAR(255)',
            'integer': 'INT',
            'float': 'DECIMAL(10,2)',
            'text': 'TEXT',
            'boolean': 'BOOLEAN',
            'date': 'DATE',
            'datetime': 'DATETIME'
        };
        
        // 输入控件映射
        this.inputTypeMapping = {
            'string': 'text',
            'integer': 'number',
            'float': 'number',
            'text': 'textarea',
            'boolean': 'checkbox',
            'date': 'date',
            'datetime': 'datetime-local'
        };
    }
    
    /**
     * 生成完整的字段配置
     */
    generateFieldConfig(userFields) {
        const allFields = [...userFields];
        
        // 自动添加标准字段（如果用户没有定义）
        this.standardFields.forEach(standardField => {
            const exists = userFields.find(field => field.name === standardField.name);
            if (!exists) {
                allFields.push(standardField);
            }
        });
        
        return allFields;
    }
    
    /**
     * 生成数据库表结构SQL
     */
    generateTableSQL(tableName, fields) {
        let sql = `CREATE TABLE IF NOT EXISTS ${tableName} (\n`;
        sql += '    id INT AUTO_INCREMENT PRIMARY KEY,\n';
        
        fields.forEach(field => {
            if (field.name === 'created_at') {
                sql += `    ${field.name} TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n`;
            } else if (field.name === 'updated_at') {
                sql += `    ${field.name} TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n`;
            } else {
                const sqlType = this.typeMapping[field.type] || 'VARCHAR(255)';
                const nullable = field.required ? 'NOT NULL' : '';
                sql += `    ${field.name} ${sqlType} ${nullable},\n`;
            }
        });
        
        sql = sql.slice(0, -2) + '\n'; // 移除最后的逗号
        sql += ');';
        
        return sql;
    }
    
    /**
     * 生成HTML表单字段
     */
    generateFormFields(fields) {
        let html = '';
        
        fields.forEach(field => {
            // 跳过系统字段
            if (['price', 'notes', 'image_url', 'created_at', 'updated_at'].includes(field.name)) {
                return;
            }
            
            const inputType = this.inputTypeMapping[field.type] || 'text';
            const required = field.required ? 'required' : '';
            const requiredMark = field.required ? '<span class="text-red-500">*</span>' : '';
            
            if (inputType === 'textarea') {
                html += `
                    <div>
                        <label for="${field.name}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <i class="fas fa-align-left mr-1"></i>${field.displayName}${requiredMark}
                        </label>
                        <textarea id="${field.name}" name="${field.name}" ${required}
                                  class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 form-input resize-none"
                                  rows="3" placeholder="请输入${field.displayName}"></textarea>
                    </div>`;
            } else if (inputType === 'checkbox') {
                html += `
                    <div class="flex items-center space-x-3">
                        <input type="checkbox" id="${field.name}" name="${field.name}" value="1"
                               class="rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500">
                        <label for="${field.name}" class="text-sm font-medium text-gray-700 dark:text-gray-300">
                            ${field.displayName}${requiredMark}
                        </label>
                    </div>`;
            } else {
                const icon = this.getFieldIcon(field.type);
                html += `
                    <div>
                        <label for="${field.name}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <i class="${icon} mr-1"></i>${field.displayName}${requiredMark}
                        </label>
                        <input type="${inputType}" id="${field.name}" name="${field.name}" ${required}
                               class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 form-input"
                               placeholder="请输入${field.displayName}">
                    </div>`;
            }
        });
        
        // 添加价格和备注字段
        html += `
            <div>
                <label for="price" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    <i class="fas fa-tag mr-1"></i>价格
                </label>
                <div class="relative">
                    <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">¥</span>
                    <input type="number" id="price" name="price" step="0.01" min="0"
                           class="w-full pl-8 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 form-input"
                           placeholder="0.00">
                </div>
            </div>
            
            <div>
                <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    <i class="fas fa-sticky-note mr-1"></i>备注
                </label>
                <textarea id="notes" name="notes"
                          class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 form-input resize-none"
                          rows="3" placeholder="请输入备注信息"></textarea>
            </div>`;
        
        return html;
    }
    
    /**
     * 生成表格头部
     */
    generateTableHeaders(fields) {
        let html = '';
        
        // 只显示前4个用户字段
        const displayFields = fields.filter(f => !['price', 'notes', 'image_url', 'created_at', 'updated_at'].includes(f.name)).slice(0, 4);
        
        displayFields.forEach(field => {
            html += `
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    ${field.displayName}
                </th>`;
        });
        
        // 添加价格列
        html += `
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                价格
            </th>`;
        
        return html;
    }
    
    /**
     * 生成表格单元格代码
     */
    generateTableCells(fields, resourceName) {
        let code = '';
        
        // 只显示前4个用户字段
        const displayFields = fields.filter(f => !['price', 'notes', 'image_url', 'created_at', 'updated_at'].includes(f.name)).slice(0, 4);
        
        displayFields.forEach(field => {
            code += `
        // ${field.displayName}单元格
        const ${field.name}Cell = document.createElement('td');
        ${field.name}Cell.className = 'px-4 py-3 text-sm text-gray-900 dark:text-gray-100';
        ${field.name}Cell.textContent = item.${field.name} || '未知';
        row.appendChild(${field.name}Cell);
        `;
        });
        
        // 添加价格单元格
        code += `
        // 价格单元格
        const priceCell = document.createElement('td');
        priceCell.className = 'px-4 py-3 text-sm font-medium';
        if (item.price && typeof item.price === 'number') {
            priceCell.innerHTML = '<span class="text-green-600 dark:text-green-400">¥' + item.price.toFixed(2) + '</span>';
        } else {
            priceCell.innerHTML = '<span class="text-gray-400">未设置</span>';
        }
        row.appendChild(priceCell);
        `;
        
        return code;
    }
    
    /**
     * 获取字段图标
     */
    getFieldIcon(type) {
        const iconMap = {
            'string': 'fas fa-font',
            'integer': 'fas fa-hashtag',
            'float': 'fas fa-calculator',
            'text': 'fas fa-align-left',
            'boolean': 'fas fa-toggle-on',
            'date': 'fas fa-calendar',
            'datetime': 'fas fa-clock'
        };
        
        return iconMap[type] || 'fas fa-font';
    }
    
    /**
     * 生成表单数据收集代码
     */
    generateFormDataCollection(fields) {
        let code = '';

        // 收集所有字段（包括动态字段和标准字段）
        const allFields = [...fields];

        // 添加标准字段（如果不存在）
        const standardFields = ['price', 'notes', 'image_url'];
        standardFields.forEach(fieldName => {
            if (!allFields.find(f => f.name === fieldName)) {
                allFields.push({ name: fieldName, type: 'string' });
            }
        });

        allFields.forEach(field => {
            if (['created_at', 'updated_at', 'image_url'].includes(field.name)) {
                return; // 跳过时间戳字段和图片URL字段（图片URL由专门的逻辑处理）
            }

            if (field.type === 'boolean') {
                code += `            const ${field.name}Element = document.getElementById('${field.name}');\n`;
                code += `            if (${field.name}Element) formData.append('${field.name}', ${field.name}Element.checked ? '1' : '0');\n`;
            } else {
                code += `            const ${field.name}Element = document.getElementById('${field.name}');\n`;
                code += `            if (${field.name}Element) formData.append('${field.name}', ${field.name}Element.value || '');\n`;
            }
        });

        return code;
    }
    
    /**
     * 生成表单填充代码
     */
    generateFormFillCode(fields) {
        let code = '';
        
        fields.forEach(field => {
            if (['created_at', 'updated_at'].includes(field.name)) {
                return; // 跳过时间戳字段
            }
            
            if (field.type === 'boolean') {
                code += `            if (this.elements.${field.name}) this.elements.${field.name}.checked = !!response.${field.name};\n`;
            } else {
                code += `            if (this.elements.${field.name}) this.elements.${field.name}.value = response.${field.name} || '';\n`;
            }
        });
        
        return code;
    }
    
    /**
     * 生成详情字段代码
     */
    generateDetailFields(fields) {
        let html = '';
        
        fields.forEach(field => {
            if (['price', 'notes', 'image_url', 'created_at', 'updated_at'].includes(field.name)) {
                return; // 这些字段单独处理
            }
            
            html += `
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">${field.displayName}</label>
                                <p class="text-gray-900 dark:text-gray-100">\${item.${field.name} || '未知'}</p>
                            </div>`;
        });
        
        return html;
    }
}

/**
 * 现代化代码生成器主类
 */
class ModernCodeGenerator {
    constructor() {
        this.fieldGenerator = new DynamicFieldGenerator();
        this.templatePath = path.join(__dirname, '..', 'templates');
    }

    /**
     * 生成完整的模块代码
     */
    async generateModule(config) {
        const { chineseName, englishName, tableName, iconClass, pageDescription, fields } = config;

        console.log('开始生成现代化模块:', { chineseName, englishName, tableName });

        try {
            // 1. 生成完整字段配置
            const allFields = this.fieldGenerator.generateFieldConfig(fields);

            // 2. 创建数据库表
            await this.createDatabaseTable(tableName, allFields);

            // 3. 生成HTML页面
            await this.generateHTMLPage(chineseName, englishName, tableName, iconClass, pageDescription, allFields);

            // 4. 生成JavaScript文件
            await this.generateJavaScriptFile(chineseName, englishName, tableName, allFields);

            // 5. 生成路由文件
            await this.generateRouteFile(englishName, tableName, allFields);

            // 6. 创建默认图片
            await this.createDefaultImage(englishName);

            // 7. 更新主页面导航
            await this.updateMainNavigation(chineseName, englishName, iconClass, pageDescription);

            // 8. 更新app.js路由注册
            await this.updateAppRoutes(englishName, tableName);

            console.log('现代化模块生成完成');
            return { success: true, message: '模块生成成功' };

        } catch (error) {
            console.error('生成模块失败:', error);
            throw error;
        }
    }

    /**
     * 创建数据库表
     */
    async createDatabaseTable(tableName, fields) {
        try {
            const sql = this.fieldGenerator.generateTableSQL(tableName, fields);
            console.log('创建数据库表:', tableName);
            console.log('SQL:', sql);

            await db.execute(sql);
            console.log(`数据库表 ${tableName} 创建成功`);

        } catch (error) {
            console.error('创建数据库表失败:', error);
            throw error;
        }
    }

    /**
     * 生成HTML页面
     */
    async generateHTMLPage(chineseName, englishName, tableName, iconClass, pageDescription, fields) {
        const templatePath = path.join(this.templatePath, 'modern-base-template.html');
        let htmlTemplate = fs.readFileSync(templatePath, 'utf8');

        // 生成JavaScript属性名
        const jsPropertyName = this.generateJSPropertyName(englishName);

        // 替换基本信息
        htmlTemplate = htmlTemplate.replace(/\{\{RESOURCE_NAME\}\}/g, chineseName);
        htmlTemplate = htmlTemplate.replace(/\{\{RESOURCE_NAME_LOWER\}\}/g, englishName);
        htmlTemplate = htmlTemplate.replace(/\{\{RESOURCE_JS_PROPERTY\}\}/g, jsPropertyName);
        htmlTemplate = htmlTemplate.replace(/\{\{TABLE_NAME\}\}/g, tableName);
        htmlTemplate = htmlTemplate.replace(/\{\{ICON_CLASS\}\}/g, iconClass);
        htmlTemplate = htmlTemplate.replace(/\{\{PAGE_DESCRIPTION\}\}/g, pageDescription);

        // 生成动态表单字段
        const formFields = this.fieldGenerator.generateFormFields(fields);
        htmlTemplate = htmlTemplate.replace(/\{\{DYNAMIC_FORM_FIELDS\}\}/g, formFields);

        // 生成动态表格头部
        const tableHeaders = this.fieldGenerator.generateTableHeaders(fields);
        htmlTemplate = htmlTemplate.replace(/\{\{DYNAMIC_TABLE_HEADERS\}\}/g, tableHeaders);

        // 保存HTML文件
        const htmlFilePath = path.join(__dirname, '..', 'public', `${englishName}-info.html`);
        fs.writeFileSync(htmlFilePath, htmlTemplate, 'utf8');
        console.log(`HTML页面已生成: ${htmlFilePath}`);
    }

    /**
     * 生成JavaScript文件
     */
    async generateJavaScriptFile(chineseName, englishName, tableName, fields) {
        const templatePath = path.join(this.templatePath, 'modern-js-template.js');
        let jsTemplate = fs.readFileSync(templatePath, 'utf8');

        // 生成有效的类名和实例名
        const className = this.generateValidClassName(englishName);
        const instanceName = this.generateValidInstanceName(englishName);
        const jsPropertyName = this.generateJSPropertyName(englishName);

        // 替换基本信息 - 注意替换顺序，避免冲突
        jsTemplate = jsTemplate.replace(/\{\{RESOURCE_CLASS_NAME\}\}/g, className);
        jsTemplate = jsTemplate.replace(/\{\{RESOURCE_INSTANCE_NAME\}\}/g, instanceName);
        jsTemplate = jsTemplate.replace(/\{\{RESOURCE_JS_PROPERTY\}\}/g, jsPropertyName);
        jsTemplate = jsTemplate.replace(/\{\{RESOURCE_NAME_LOWER\}\}/g, englishName);
        jsTemplate = jsTemplate.replace(/\{\{TABLE_NAME\}\}/g, tableName);
        jsTemplate = jsTemplate.replace(/\{\{RESOURCE_NAME\}\}/g, chineseName);

        // 添加配置信息
        jsTemplate = this.addConfigurationToJS(jsTemplate, englishName, tableName);

        // 生成动态字段元素缓存
        const fieldElements = this.generateFieldElements(fields);
        jsTemplate = jsTemplate.replace(/\{\{DYNAMIC_FIELD_ELEMENTS\}\}/g, fieldElements);

        // 生成动态表格单元格代码
        const tableCells = this.fieldGenerator.generateTableCells(fields, englishName);
        jsTemplate = jsTemplate.replace(/\{\{DYNAMIC_TABLE_CELLS\}\}/g, tableCells);

        // 生成表单数据收集代码
        const formDataCollection = this.fieldGenerator.generateFormDataCollection(fields);
        jsTemplate = jsTemplate.replace(/\{\{DYNAMIC_FORM_DATA_COLLECTION\}\}/g, formDataCollection);

        // 生成表单填充代码
        const formFillCode = this.fieldGenerator.generateFormFillCode(fields);
        jsTemplate = jsTemplate.replace(/\{\{DYNAMIC_FORM_FILL\}\}/g, formFillCode);

        // 生成详情字段代码
        const detailFields = this.fieldGenerator.generateDetailFields(fields);
        jsTemplate = jsTemplate.replace(/\{\{DYNAMIC_DETAIL_FIELDS\}\}/g, detailFields);

        // 实例化代码已在模板中正确设置

        // 保存JavaScript文件
        const jsFilePath = path.join(__dirname, '..', 'public', 'js', `${englishName}-info.js`);
        fs.writeFileSync(jsFilePath, jsTemplate, 'utf8');
        console.log(`JavaScript文件已生成: ${jsFilePath}`);
    }

    /**
     * 生成有效的类名
     */
    generateValidClassName(englishName) {
        // 将kebab-case转换为PascalCase，确保是有效的JavaScript标识符
        const className = englishName
            .split('-')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join('');

        // 确保类名以字母开头，并且是有效的JavaScript标识符
        const validClassName = className.replace(/[^a-zA-Z0-9]/g, '') + 'Manager';

        return validClassName;
    }

    /**
     * 生成有效的实例名称（camelCase）
     */
    generateValidInstanceName(englishName) {
        // 将kebab-case转换为camelCase
        const parts = englishName.split('-');
        const camelCase = parts[0] + parts.slice(1)
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join('');

        // 确保是有效的JavaScript标识符
        const validInstanceName = camelCase.replace(/[^a-zA-Z0-9]/g, '') + 'Manager';

        return validInstanceName;
    }

    /**
     * 生成有效的JavaScript属性名（camelCase，用于DOM元素引用）
     */
    generateJSPropertyName(englishName) {
        // 将kebab-case转换为camelCase，用于JavaScript属性名
        const parts = englishName.split('-');
        const camelCase = parts[0] + parts.slice(1)
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join('');

        // 确保是有效的JavaScript标识符
        return camelCase.replace(/[^a-zA-Z0-9]/g, '');
    }

    /**
     * 添加配置信息到JavaScript
     */
    addConfigurationToJS(jsTemplate, englishName, tableName) {
        // 配置信息已经在模板中定义，这里只需要确保变量替换正确
        // {{TABLE_NAME}} 会在主替换过程中被替换

        // 添加resourceName配置（如果需要的话）
        if (!jsTemplate.includes('resourceName:')) {
            jsTemplate = jsTemplate.replace(
                'this.config = {',
                `this.config = {\n            resourceName: '${englishName}',`
            );
        }

        return jsTemplate;
    }



    /**
     * 生成字段元素缓存代码
     */
    generateFieldElements(fields) {
        let code = '';

        fields.forEach(field => {
            if (!['created_at', 'updated_at'].includes(field.name)) {
                code += `        this.elements.${field.name} = document.getElementById('${field.name}');\n`;
            }
        });

        return code;
    }

    /**
     * 生成路由文件
     */
    async generateRouteFile(englishName, tableName, fields) {
        // 生成有效的JavaScript变量名
        const configVarName = this.generateJSPropertyName(englishName) + 'Config';

        const routeTemplate = `const express = require('express');
const router = express.Router();
const resourceHandler = require('./resource-handler');

// ${tableName} 资源配置
const ${configVarName} = {
    resourceName: '${englishName}',
    tableName: '${tableName}',
    uploadDir: '${englishName}',
    defaultImage: 'images/default-${englishName}.png',
    searchFields: [${fields.slice(0, 3).map(f => `'${f.name}'`).join(', ')}],
    fieldMap: {
${fields.map(field => `        ${field.name}: { type: '${field.type}' }`).join(',\n')}
    }
};

// 使用通用资源处理器
router.use('/', resourceHandler(${configVarName}));

module.exports = router;`;

        const routeFilePath = path.join(__dirname, `${englishName}-routes.js`);
        fs.writeFileSync(routeFilePath, routeTemplate, 'utf8');
        console.log(`路由文件已生成: ${routeFilePath}`);
    }

    /**
     * 创建默认图片
     */
    async createDefaultImage(englishName) {
        const defaultImagePath = path.join(__dirname, '..', 'public', 'images', `default-${englishName}.png`);

        // 如果图片已存在，跳过
        if (fs.existsSync(defaultImagePath)) {
            console.log(`默认图片已存在: ${defaultImagePath}`);
            return;
        }

        try {
            // 创建一个简单的默认图片
            await sharp({
                create: {
                    width: 300,
                    height: 300,
                    channels: 4,
                    background: { r: 240, g: 240, b: 240, alpha: 1 }
                }
            })
            .png()
            .toFile(defaultImagePath);

            console.log(`默认图片已创建: ${defaultImagePath}`);
        } catch (error) {
            console.error('创建默认图片失败:', error);
        }
    }

    /**
     * 更新主页面导航
     */
    async updateMainNavigation(chineseName, englishName, iconClass, pageDescription) {
        console.log('更新主页面导航:', chineseName);

        try {
            const mainPagePath = path.join(__dirname, '..', 'public', 'pc-components.html');
            let mainPageContent = fs.readFileSync(mainPagePath, 'utf8');

            // 检查是否已经存在该卡片
            if (mainPageContent.includes(`href="/${englishName}-info.html"`)) {
                console.log(`${chineseName}卡片已存在，跳过添加`);
                return;
            }

            // 生成新的卡片HTML
            const newCardHtml = this.generateComponentCard(chineseName, englishName, iconClass, pageDescription);

            // 找到插入位置（在现代化代码生成器卡片之前）
            const insertPosition = mainPageContent.indexOf('<!-- 现代化代码生成器卡片 -->');

            if (insertPosition !== -1) {
                // 插入新卡片
                const beforeContent = mainPageContent.substring(0, insertPosition);
                const afterContent = mainPageContent.substring(insertPosition);

                mainPageContent = beforeContent + newCardHtml + '\n            ' + afterContent;

                // 写回文件
                fs.writeFileSync(mainPagePath, mainPageContent, 'utf8');
                console.log(`主页面已更新，添加了${chineseName}卡片`);

                // 同时更新统计区域
                await this.updateStatsSection(mainPageContent, chineseName, englishName, mainPagePath);

            } else {
                console.warn('未找到插入位置，跳过主页面更新');
            }

        } catch (error) {
            console.error('更新主页面失败:', error);
            // 不抛出错误，因为这不是关键功能
        }
    }

    /**
     * 更新统计区域
     */
    async updateStatsSection(mainPageContent, chineseName, englishName, mainPagePath) {
        try {
            // 生成统计卡片HTML
            const statsCardHtml = this.generateStatsCard(chineseName, englishName);

            // 找到统计区域的插入位置
            const statsInsertPosition = mainPageContent.indexOf('</div>\n        </div>\n    </div>');

            if (statsInsertPosition !== -1) {
                // 在统计区域末尾插入新的统计卡片
                const beforeStats = mainPageContent.substring(0, statsInsertPosition);
                const afterStats = mainPageContent.substring(statsInsertPosition);

                const updatedContent = beforeStats + statsCardHtml + '\n                ' + afterStats;

                // 写回文件
                fs.writeFileSync(mainPagePath, updatedContent, 'utf8');
                console.log(`统计区域已更新，添加了${chineseName}统计卡片`);
            }

        } catch (error) {
            console.error('更新统计区域失败:', error);
        }
    }

    /**
     * 生成统计卡片HTML
     */
    generateStatsCard(chineseName, englishName) {
        // 生成随机颜色
        const colors = [
            'blue', 'green', 'purple', 'red', 'yellow',
            'indigo', 'pink', 'gray', 'cyan', 'orange'
        ];
        const randomColor = colors[Math.floor(Math.random() * colors.length)];

        return `<div class="bg-${randomColor}-100 p-4 rounded-md hover:bg-${randomColor}-200 transition-colors cursor-pointer" onclick="window.location.href='/${englishName}-info.html'">
                    <h3 class="text-lg font-medium text-${randomColor}-800">${chineseName}</h3>
                    <p class="text-3xl font-bold text-${randomColor}-900" id="${englishName}-count">0</p>
                </div>`;
    }

    /**
     * 生成组件卡片HTML
     */
    generateComponentCard(chineseName, englishName, iconClass, pageDescription) {
        // 生成随机颜色
        const colors = [
            'bg-blue-500', 'bg-green-500', 'bg-purple-500', 'bg-red-500',
            'bg-yellow-500', 'bg-indigo-500', 'bg-pink-500', 'bg-gray-500',
            'bg-cyan-500', 'bg-orange-500', 'bg-teal-500', 'bg-lime-500'
        ];
        const randomColor = colors[Math.floor(Math.random() * colors.length)];
        const hoverColor = randomColor.replace('500', '600');

        return `<!-- ${chineseName}卡片 -->
            <div class="component-card bg-white rounded-lg shadow-md overflow-hidden">
                <div class="${randomColor} text-white p-4">
                    <h2 class="text-xl font-semibold flex items-center">
                        <i class="${iconClass} mr-2"></i> ${chineseName}
                    </h2>
                </div>
                <div class="p-5">
                    <p class="text-gray-600 mb-4">${pageDescription}</p>
                    <a href="/${englishName}-info.html"
                        class="block w-full ${randomColor} hover:${hoverColor} text-white text-center py-2 rounded-md">
                        <i class="fas fa-arrow-right mr-1"></i> 管理${chineseName}
                    </a>
                </div>
            </div>
        `;
    }

    /**
     * 更新app.js路由注册
     */
    async updateAppRoutes(englishName, tableName) {
        try {
            const appJsPath = path.join(__dirname, '..', 'app.js');
            let appContent = fs.readFileSync(appJsPath, 'utf8');

            // 生成路由导入语句
            const routeImport = `const ${this.generateJSPropertyName(englishName)}Routes = require('./routes/${englishName}-routes');`;

            // 生成路由使用语句
            const routeUse = `app.use('/api/${tableName}', ${this.generateJSPropertyName(englishName)}Routes);`;

            // 检查是否已经存在该路由
            if (appContent.includes(routeImport) || appContent.includes(`/api/${tableName}`)) {
                console.log(`路由 /api/${tableName} 已存在，跳过添加`);
                return;
            }

            // 在图片上传路由之后添加新路由
            const uploadRoutePattern = /app\.use\('\/api\/upload', uploadRoutes\);/;
            if (uploadRoutePattern.test(appContent)) {
                // 添加导入语句
                const importPattern = /(const uploadRoutes = require\('\.\/routes\/upload-routes'\);)/;
                appContent = appContent.replace(importPattern, `$1\nconst ${this.generateJSPropertyName(englishName)}Routes = require('./routes/${englishName}-routes');`);

                // 添加路由使用语句
                const usePattern = /(app\.use\('\/api\/upload', uploadRoutes\);)/;
                appContent = appContent.replace(usePattern, `$1\napp.use('/api/${tableName}', ${this.generateJSPropertyName(englishName)}Routes);`);

                fs.writeFileSync(appJsPath, appContent, 'utf8');
                console.log(`已将 /api/${tableName} 路由添加到公共路由中`);
            } else {
                console.warn('未找到上传路由，无法自动添加新路由');
            }

        } catch (error) {
            console.error('更新app.js路由失败:', error);
        }
    }
}

// API路由
router.post('/generate-code', async (req, res) => {
    try {
        const generator = new ModernCodeGenerator();
        const result = await generator.generateModule(req.body);
        res.json(result);
    } catch (error) {
        console.error('代码生成失败:', error);
        res.status(500).json({
            success: false,
            message: '代码生成失败: ' + error.message
        });
    }
});

module.exports = router;
