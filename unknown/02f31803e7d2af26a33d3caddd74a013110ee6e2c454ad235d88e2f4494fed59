/* 简单高效的过渡效果 */
body {
    transition: background-color 0.25s ease;
}

.bg-white,
.bg-gray-50,
.bg-gray-100,
.text-gray-700,
.text-gray-800,
.text-gray-900,
.border-gray-200,
.border-gray-300,
.ram-card,
#ramDetailModal,
input,
select,
textarea,
button {
    transition: background-color 0.25s ease, color 0.25s ease, border-color 0.25s ease;
}

.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.image-preview {
    transition: all 0.3s ease;
}

.image-preview:hover {
    transform: scale(1.05);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 标签样式 */
.spec-tag {
    padding: 3px 8px !important;
    border-radius: 4px !important;
    font-size: 0.75rem !important;
    font-weight: 500 !important;
    line-height: 1.5 !important;
    display: inline-block !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: fit-content !important;
    margin: 2px 4px 2px 0 !important;
    transition: all 0.2s ease;
}

.spec-tag.socket {
    background-color: rgba(180, 83, 9, 0.1) !important;
    color: #b45309 !important;
    border: 1px solid rgba(180, 83, 9, 0.2) !important;
}

.spec-tag.cores {
    background-color: rgba(22, 101, 52, 0.1) !important;
    color: #166534 !important;
    border: 1px solid rgba(22, 101, 52, 0.2) !important;
}

.spec-tag.freq {
    background-color: rgba(76, 29, 149, 0.1) !important;
    color: #4c1d95 !important;
    border: 1px solid rgba(76, 29, 149, 0.2) !important;
}

.spec-tag.process {
    background-color: rgba(6, 95, 70, 0.1) !important;
    color: #065f46 !important;
    border: 1px solid rgba(6, 95, 70, 0.2) !important;
}

.spec-tag.tdp {
    background-color: rgba(194, 65, 12, 0.1) !important;
    color: #c2410c !important;
    border: 1px solid rgba(194, 65, 12, 0.2) !important;
}

/* 卡片式布局 */
.ram-card {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 0 16px 0 !important;
    box-sizing: border-box !important;
    border: none;
    border-radius: 10px;
    overflow: hidden;
    background-color: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    padding: 16px !important;
    display: flex;
    flex-direction: column;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.ram-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.ram-card-header {
    padding: 12px 16px !important;
    background-color: #f8f9fa;
    border-bottom: 1px solid #eee;
    width: 100% !important;
    box-sizing: border-box !important;
}

.ram-card-body {
    padding: 16px !important;
    min-height: 120px !important;
    width: 100% !important;
    box-sizing: border-box !important;
    overflow: hidden !important;
}

.ram-card-footer {
    padding: 12px !important;
    background-color: #fcfcfc;
    border-top: 1px solid #eee;
    width: 100% !important;
    box-sizing: border-box !important;
}

/* 标签组 */
.tag-group {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin: 8px 0;
    width: 100%;
    box-sizing: border-box !important;
}

/* 操作按钮样式 */
.action-btn {
    transition: all 0.2s ease;
}

.action-btn:hover {
    transform: translateY(-2px);
}

.action-btn:active {
    transform: translateY(1px);
}

/* 移动端优化 */
@media (max-width: 640px) {
    .mobile-stack {
        flex-direction: column;
    }

    .mobile-full-width {
        width: 100%;
    }

    .mobile-p-2 {
        padding: 0.5rem;
    }

    .mobile-text-sm {
        font-size: 0.875rem;
        line-height: 1.25rem;
    }

    .mobile-hidden {
        display: none;
    }

    .mobile-flex-col {
        flex-direction: column;
    }

    .mobile-w-full {
        width: 100%;
    }

    .mobile-mt-2 {
        margin-top: 0.5rem;
    }

    /* 新增移动端样式 */
    .table-compact {
        font-size: 0.8rem;
        width: 100%;
        table-layout: fixed;
    }

    .table-compact td,
    .table-compact th {
        padding: 0.5rem 0.25rem;
        word-break: break-word;
    }

    .btn-group-compact button {
        padding: 0.25rem 0.5rem;
        margin: 0 2px;
    }

    /* 改进的移动端内存列表样式 */
    .table-compact td {
        font-size: 0.75rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 120px;
    }

    /* 操作按钮样式优化 */
    .action-buttons {
        display: flex;
        gap: 4px;
        flex-wrap: nowrap;
        justify-content: center;
    }

    .action-buttons button {
        min-width: 28px;
        height: 28px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* 内存型号列宽度调整 */
    .model-column {
        max-width: 80px !important;
        width: 35%;
    }

    /* 容量/类型列宽度调整 */
    .memory-column {
        max-width: 60px !important;
        width: 30%;
    }

    /* 操作列宽度调整 */
    .action-column {
        width: 35%;
    }

    /* 分页控件移动端优化 */
    .pagination-mobile {
        display: flex;
        justify-content: space-between;
        width: 100%;
        align-items: center;
    }

    .page-controls-mobile {
        display: flex;
        gap: 2px;
    }

    .page-controls-mobile button {
        min-width: 28px;
        height: 28px;
        padding: 0;
        font-size: 0.75rem;
    }

    #pageInfo {
        font-size: 0.7rem;
        padding: 2px 4px;
        white-space: nowrap;
    }

    /* 确保表格不会水平溢出 */
    .overflow-x-auto {
        max-width: 100vw;
        margin: 0;
        padding: 0;
    }

    /* 移动端卡片样式 */
    .ram-card {
        padding: 0 !important;
        margin-bottom: 12px;
    }

    .ram-card-header {
        padding: 10px 12px !important;
    }

    .ram-card-body {
        padding: 12px !important;
        min-height: auto !important;
    }

    .ram-card-footer {
        padding: 10px !important;
    }

    /* 移动端操作按钮样式 */
    .action-buttons {
        gap: 24px;
    }

    /* 移动端品牌标签 */
    .mobile-table .ram-badge,
    .ram-card .ram-badge {
        padding: 4px 10px !important;
        background-color: rgba(76, 175, 80, 0.1) !important;
        color: #4CAF50 !important;
        border: 1px solid rgba(76, 175, 80, 0.2) !important;
        font-weight: 600 !important;
        border-radius: 4px !important;
        font-size: 0.85rem;
        margin-right: 8px;
    }

    /* 移动端模态框优化 */
    #ramDetailModal {
        align-items: flex-start !important;
        padding-top: 0 !important;
        padding-bottom: 0 !important;
        overflow-y: hidden !important;
    }

    #ramDetailModal .mobile-modal-content {
        height: 100vh !important;
        max-height: 100vh !important;
        width: 100% !important;
        max-width: 100% !important;
        border-radius: 0 !important;
        overflow-y: auto !important;
        margin: 0 !important;
        padding-bottom: 80px !important;
        /* 给底部按钮留空间 */
    }

    /* 移动端专用关闭按钮 */
    .mobile-close-button {
        display: flex !important;
        position: fixed !important;
        bottom: 20px !important;
        right: 20px !important;
        width: 50px !important;
        height: 50px !important;
        border-radius: 50% !important;
        background-color: rgba(22, 163, 74, 0.9) !important;
        color: white !important;
        align-items: center !important;
        justify-content: center !important;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
        z-index: 100 !important;
        border: none !important;
    }
}

/* 暗黑模式样式 */
.dark-mode {
    --bg-primary: #121212;
    --bg-secondary: #1e1e1e;
    --bg-card: #252525;
    --bg-input: #2a2a2a;
    --text-primary: #f5f5f5;
    --text-secondary: #a0aec0;
    --border-color: #333333;
    --input-bg: #2a2a2a;
    --input-text: #e2e8f0;
    --button-primary-bg: #16a34a;
    --button-primary-hover: #15803d;
    --button-primary-text: #ffffff;
    --highlight-color: #4ade80;
    --accent-color: #4ade80;
    --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

body.dark-mode {
    background-color: var(--bg-primary) !important;
    color: var(--text-primary) !important;
}

.dark-mode .bg-white {
    background-color: var(--bg-secondary) !important;
}

.dark-mode .bg-gray-50 {
    background-color: var(--bg-primary) !important;
}

.dark-mode .bg-gray-100 {
    background-color: #1a1a1a !important;
}

.dark-mode .bg-green-50 {
    background-color: rgba(22, 163, 74, 0.1) !important;
}

.dark-mode .text-gray-700,
.dark-mode .text-gray-800,
.dark-mode .text-gray-900 {
    color: var(--text-primary) !important;
}

.dark-mode .text-gray-500,
.dark-mode .text-gray-600 {
    color: var(--text-secondary) !important;
}

.dark-mode .border-gray-200,
.dark-mode .border-gray-300 {
    border-color: var(--border-color);
}

.dark-mode input,
.dark-mode select,
.dark-mode textarea {
    background-color: var(--input-bg);
    color: var(--input-text);
    border-color: var(--border-color);
}

.dark-mode input:focus,
.dark-mode select:focus,
.dark-mode textarea:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(74, 222, 128, 0.2);
}

.dark-mode .shadow-md {
    box-shadow: var(--card-shadow);
}

.dark-mode .text-green-700 {
    color: var(--highlight-color);
}

.dark-mode .bg-green-600 {
    background-color: var(--button-primary-bg);
}

.dark-mode .hover\:bg-green-700:hover {
    background-color: var(--button-primary-hover);
}

/* 表格美化 */
.dark-mode table thead {
    background-color: #1a1a1a;
}

.dark-mode table th {
    color: #94a3b8;
}

.dark-mode table tbody tr {
    border-color: #333;
}

.dark-mode table tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.03);
}

.dark-mode .divide-y,
.dark-mode table tbody,
.dark-mode table tr {
    border-color: #333;
}

/* 模态框美化 */
.dark-mode #ramDetailModal .bg-white {
    background-color: var(--bg-card);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.dark-mode #ramDetailModal .border-b,
.dark-mode #ramDetailModal .border-t {
    border-color: var(--border-color);
}

.dark-mode .bg-blue-50 {
    background-color: rgba(59, 130, 246, 0.1);
}

.dark-mode .text-blue-700 {
    color: #60a5fa;
}

.dark-mode .text-blue-600 {
    color: #60a5fa;
}

.dark-mode .bg-green-100 {
    background-color: rgba(22, 163, 74, 0.15);
}

.dark-mode .text-green-800 {
    color: #4ade80;
}

.dark-mode .border-green-200 {
    border-color: rgba(74, 222, 128, 0.3);
}

.dark-mode .bg-purple-100 {
    background-color: rgba(139, 92, 246, 0.15);
}

.dark-mode .text-purple-800 {
    color: #a78bfa;
}

.dark-mode .border-purple-200 {
    border-color: rgba(167, 139, 250, 0.3);
}

.dark-mode .bg-amber-100 {
    background-color: rgba(245, 158, 11, 0.15);
}

.dark-mode .text-amber-800 {
    color: #fbbf24;
}

.dark-mode .border-amber-200 {
    border-color: rgba(251, 191, 36, 0.3);
}

.dark-mode .bg-indigo-100 {
    background-color: rgba(99, 102, 241, 0.15);
}

.dark-mode .text-indigo-800 {
    color: #818cf8;
}

.dark-mode .border-indigo-200 {
    border-color: rgba(129, 140, 248, 0.3);
}

/* 表格和卡片线条移除 */
.dark-mode .divide-y> :not([hidden])~ :not([hidden]),
.dark-mode thead,
.dark-mode table tbody tr,
.dark-mode .border-b,
.dark-mode .border-t {
    border: none !important;
}

.dark-mode .ram-card>div:nth-child(1),
/* Card Header */
.dark-mode .ram-card>div:nth-child(3)

/* Card Footer */
    {
    border: none !important;
}

/* 重置按钮样式 */
.dark-mode #resetFilterBtn {
    background-color: var(--bg-input);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.dark-mode #resetFilterBtn:hover {
    background-color: #373737;
    color: var(--text-primary);
}

/* 移动端卡片暗色模式 */
.dark-mode .ram-card {
    background-color: var(--bg-secondary) !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3) !important;
    border: 1px solid rgba(255, 255, 255, 0.05) !important;
}

.dark-mode .ram-card-outer-container {
    margin-bottom: 15px !important;
}