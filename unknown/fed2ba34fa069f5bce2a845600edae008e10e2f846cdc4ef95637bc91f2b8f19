/**
 * 简单的拼音搜索工具
 * 支持中文字符到拼音的转换和首字母提取
 */

// 定义拼音对象
const SimplePinyin = (function() {
    // 常用汉字拼音映射表 - 扩展版本
    const pinyinMap = {
        'a': '阿啊腌嗄锕吖嗣',
        'b': '把八宝爸百北不步变便别比必表病波博布部备半办保包报被本笔泊白柏班般板版办邦帮傍膀背悲碑北贝备倍背辈杯悲碑',
        'c': '才菜蔡草厕策层叉差产长常场厂超车成城程吃出处川穿传船窗床春次从村存错藏操曹草策测层插察产长场厂常昌',
        'd': '打大代带待单但当到道的得等地第点电店调掉东冬懂动都读度端短段对多夺达答搭代带待单担胆导岛倒刀到道达',
        'e': '饿恶鄂折感遡嚇娥峨蛙额厄鹅鳄额恩尔耳二',
        'f': '发法反饭方房放飞非费分服福府父付负富繁凡反返方防访放非飞肥费分份纷风封丰',
        'g': '该改概干感刚高搞告工功共狗够购古顾故官管馆关观光广规贵国果过各盖改干赶敢刚高搞告工共供',
        'h': '哈还海害含汉好喝河合何黑很红后候湖护花化划话怀坏欢环换黄回会婚活火伙获或货海害含函寒汗好号毫豪',
        'i': '一以已亿衣移依医易益意义议因音应英影院易意义益',
        'j': '几给己机记际家加间简见建件健江讲交教接街节结解姐介今金进近京经境静九酒久就举句决据军君',
        'k': '开看科可课空口苦块快筷宽矿况困扩卡客刊勘科可渴刻课肯坑空口',
        'l': '拉来蓝浪老乐类了离李里理力历利丽例连联脸练凉两辆谅疗料林临领另六龙楼路录旅绿乱论',
        'm': '马吗妈买卖满慢忙猫毛矛冒贸么没美每门们盟梦米面民明名命模母木目牧墓幕',
        'n': '那拿哪内南难男脑闹呢能你年念鸟宁牛农弄女',
        'o': '哦噢喔',
        'p': '怕拍牌派盘判旁胖跑朋片漂票品平评破普铺',
        'q': '七期其奇起气汽器前钱千强墙桥巧切且亲轻清情请庆穷求球区取去趣全权',
        'r': '然让热人认任日容肉如入',
        's': '三色森沙山上少绍社申身深神什生声师十时识实食使始史是事市视试手受首书树数水说思死四送诉速算虽随岁所缩',
        't': '他她它台太谈汤堂套特疼提题体替天条听厅停通同头图土团推退脱',
        'u': '哦噢喔',
        'v': '哦噢喔',
        'w': '外完玩晚万王网往忘望危卫为位未文问我无物五午务物',
        'x': '西息希系洗喜下夏先现线限香相想响向小校笑效些新心信星行形醒姓兴休许需选学',
        'y': '压呀牙验央杨阳洋羊养样要药也野业夜一以已亿衣移依医易益意义议因音应英影院易意义益由油游友有右于与遇雨语育员院原员远愿约月越云运',
        'z': '杂在再咱早造怎增展占战站张章找照者这着真整正证政之只知指值职支纸止至制治中众重州周洲主住助专转装准资子自字总组最昨走足租族神嘴最罪'
    };

    // 扩展常用词组的拼音映射
    const wordPinyinMap = {
        '一体': 'yiti',
        '一通': 'yitong',
        '一铭': 'yiming',
        '中科': 'zhongke',
        '中航油': 'zhonghangyou',
        '宿舍': 'sushe',
        '单元': 'danyuan',
        '乾元': 'qianyuan',
        '天城': 'tiancheng',
        '亿天': 'yitian',
        '亿联': 'yilian',
        '硕科': 'shuoke',
        '科技': 'keji',
        '众源': 'zhongyuan',
        '众联': 'zhonglian',
        '世纪': 'shiji',
        '佳业': 'jiaye',
        '佳杰': 'jiajie',
        '侨丰': 'qiaofeng',
        '西楼': 'xilou',
        '信林': 'xinlin',
        '兴运': 'xingyun',
        '冠珑': 'guanlong',
        '唐涛': 'tangtao',
        '刘培修': 'liupeixiu',
        '创铭': 'chuangming',
        '利邦': 'libang',
        '华洋': 'huayang',
        '华美': 'huamei',
        '华蔚': 'huawei',
        '华誉': 'huayu',
        '卓锦': 'zhuojin',
        '博天': 'botian',
        '博明': 'boming',
        '永顺': 'yongshun',
        '智城': 'zhicheng',
        '双流': 'shuangliu',
        '联创': 'lianchuang',
        '当天达': 'dangtianda',
        '叶三烘': 'yesanhong',
        '同欣': 'tongxin',
        '和丰': 'hefeng',
        '唐强': 'tangqiang',
        '多维': 'duowei',
        '天之火': 'tianzhihuo',
        '天恒': 'tianheng',
        '信佳': 'xinjia',
        '天晴': 'tianqing',
        '天就': 'tianjiu',
        '门市': 'menshi'
    };

    /**
     * 获取汉字的拼音首字母
     * @param {string} char 单个汉字
     * @return {string} 拼音首字母，如果不是汉字则返回原字符
     */
    function getFirstLetter(char) {
        // 如果是英文字母或数字，直接返回
        if (/[a-zA-Z0-9]/.test(char)) {
            return char.toLowerCase();
        }
        
        // 遍历拼音映射表
        for (const [letter, chars] of Object.entries(pinyinMap)) {
            if (chars.indexOf(char) !== -1) {
                return letter;
            }
        }
        
        // 如果找不到对应的拼音首字母，返回原字符
        return char;
    }

    /**
     * 获取字符串的拼音首字母
     * @param {string} str 字符串
     * @return {string} 拼音首字母组合
     */
    function getPinyinInitials(str) {
        if (!str || typeof str !== 'string') return '';
        
        // 先检查是否有整个词组的映射
        for (const [word, pinyin] of Object.entries(wordPinyinMap)) {
            if (str.includes(word)) {
                // 如果找到词组，替换为首字母
                const initials = pinyin.split('').filter(char => /[a-z]/.test(char)).map(char => char[0]).join('');
                // 返回词组的首字母
                return initials;
            }
        }
        
        // 如果没有找到词组，处理单个字符
        let result = '';
        for (let i = 0; i < str.length; i++) {
            result += getFirstLetter(str.charAt(i));
        }
        
        return result;
    }

    /**
     * 获取字符串的完整拼音
     * @param {string} str 字符串
     * @return {string} 完整拼音
     */
    function getFullPinyin(str) {
        if (!str || typeof str !== 'string') return '';
        
        // 先检查是否有整个词组的映射
        for (const [word, pinyin] of Object.entries(wordPinyinMap)) {
            if (str.includes(word)) {
                return str.replace(word, pinyin);
            }
        }
        
        // 如果没有找到词组，逐字处理
        let result = '';
        for (let i = 0; i < str.length; i++) {
            const char = str.charAt(i);
            let found = false;
            
            // 如果是英文字母或数字，直接添加
            if (/[a-zA-Z0-9]/.test(char)) {
                result += char.toLowerCase();
                continue;
            }
            
            // 遍历拼音映射表
            for (const [letter, chars] of Object.entries(pinyinMap)) {
                if (chars.indexOf(char) !== -1) {
                    result += letter;
                    found = true;
                    break;
                }
            }
            
            // 如果找不到对应的拼音，添加原字符
            if (!found) {
                result += char;
            }
        }
        
        return result;
    }

    // 暴露接口
    return {
        getFirstLetter: getFirstLetter,
        getPinyinInitials: getPinyinInitials,
        getFullPinyin: getFullPinyin
    };
})();
