<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电脑配件综合管理系统</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
        }
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="/css/index.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="/js/js-pinyin.js"></script>
    <script src="/js/pinyin-wrapper.js"></script>
    <script src="/js/edit-fix.js"></script>
</head>

<body class="bg-gray-50 dark:bg-gray-900 min-h-screen transition-colors duration-300">
    <div class="container mx-auto px-2 sm:px-4 py-4 sm:py-8">
        <header class="mb-4 sm:mb-8">
            <h1 class="text-xl sm:text-3xl font-bold text-indigo-700 dark:text-indigo-400 flex items-center">
                <i class="fas fa-truck mr-2 sm:mr-3"></i> 电脑配件综合管理系统
            </h1>
            <div class="flex justify-between items-center">
                <p class="text-gray-600 dark:text-gray-300 mt-1 sm:mt-2 text-sm sm:text-base">记录和管理您的送货信息</p>
                <div class="mt-2 flex space-x-2 items-center">
                    <!-- 主题切换按钮 -->
                    <button id="themeToggle"
                        class="p-2 rounded-lg bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200"
                        title="切换主题">
                        <i id="themeIcon" class="fas fa-moon text-sm"></i>
                    </button>
                    <div id="userRoleBadge"
                        class="hidden bg-blue-100 text-blue-800 text-xs font-semibold mr-2 px-2.5 py-0.5 rounded flex items-center">
                        <i class="fas fa-user mr-1"></i> <span id="userRoleText">普通用户</span>
                    </div>
                    <a href="/pc-components.html"
                        class="inline-block bg-indigo-600 text-white py-1 sm:py-2 px-2 sm:px-3 rounded-md hover:bg-indigo-700 text-sm sm:text-base">
                        <i class="fas fa-desktop mr-1"></i> 配件管理
                    </a>
                    <!-- <a href="/motherboard-info.html" class="inline-block bg-indigo-600 text-white py-1 sm:py-2 px-2 sm:px-3 rounded-md hover:bg-indigo-700 text-sm sm:text-base">
                        <i class="fas fa-microchip mr-1"></i> 主板信息管理
                    </a> -->
                    <a href="/store-feedback.html"
                        class="inline-block bg-indigo-600 text-white py-1 sm:py-2 px-2 sm:px-3 rounded-md hover:bg-indigo-700 text-sm sm:text-base">
                        <i class="fas fa-comment-alt mr-1"></i> 问题反馈
                    </a>
                    <a href="/customer-service-qa.html"
                        class="inline-block bg-indigo-600 text-white py-1 sm:py-2 px-2 sm:px-3 rounded-md hover:bg-indigo-700 text-sm sm:text-base">
                        <i class="fas fa-headset mr-1"></i> 客服问答
                    </a>
                    <a href="/common-phrases.html"
                        class="inline-block bg-indigo-600 text-white py-1 sm:py-2 px-2 sm:px-3 rounded-md hover:bg-indigo-700 text-sm sm:text-base">
                        <i class="fas fa-comments mr-1"></i> 话术管理
                    </a>

                    <a href="/excel-template-simple.html"
                        class="inline-block bg-purple-600 text-white py-1 sm:py-2 px-2 sm:px-3 rounded-md hover:bg-purple-700 text-sm sm:text-base">
                        <i class="fas fa-file-excel mr-1"></i> Excel模板
                    </a>
                    <a href="/excel-template-records.html"
                        class="inline-block bg-pink-600 text-white py-1 sm:py-2 px-2 sm:px-3 rounded-md hover:bg-pink-700 text-sm sm:text-base">
                        <i class="fas fa-list mr-1"></i> 模板记录
                    </a>
                    <a href="/revenue-management.html"
                        class="inline-block bg-green-600 text-white py-1 sm:py-2 px-2 sm:px-3 rounded-md hover:bg-green-700 text-sm sm:text-base">
                        <i class="fas fa-chart-line mr-1"></i> 创收管理
                    </a>

                </div>
            </div>
        </header>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-4 sm:gap-6">
            <!-- 左侧表单区域 -->
            <div class="lg:col-span-1 bg-white dark:bg-gray-800 p-3 sm:p-6 rounded-lg shadow-md">
                <h2 class="text-lg sm:text-xl font-semibold text-gray-800 dark:text-gray-200 mb-3 sm:mb-4 flex items-center">
                    <i class="fas fa-plus-circle mr-1 sm:mr-2 text-indigo-500 dark:text-indigo-400"></i> 新增记录
                </h2>

                <form id="deliveryForm" class="space-y-3 sm:space-y-4">
                    <div>
                        <label for="storeName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">店铺名称</label>
                        <div class="relative">
                            <div class="relative">
                                <input type="text" id="storeSearch"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 dark:border-gray-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base"
                                    placeholder="搜索或选择店铺...">
                                <select id="storeName" class="hidden">
                                    <option value="">选择店铺</option>
                                </select>
                                <div id="storeSearchResults"
                                    class="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 shadow-lg rounded-md border border-gray-200 dark:border-gray-500 max-h-60 overflow-auto hidden">
                                </div>
                            </div>
                            <button type="button" id="addStoreBtn"
                                class="absolute right-0 top-0 h-full px-2 text-indigo-600 hover:text-indigo-800">
                                <i class="fas fa-plus text-sm"></i>
                            </button>
                        </div>
                    </div>

                    <div id="newStoreContainer" class="hidden space-y-2">
                        <div>
                            <label for="newStoreName"
                                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">新增店铺名称</label>
                            <input type="text" id="newStoreName"
                                class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 dark:border-gray-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base">
                        </div>
                        <div>
                            <label for="newStoreAddress" class="block text-sm font-medium text-gray-700 mb-1">默认送货地点
                                (可选)</label>
                            <select id="newStoreAddress"
                                class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 dark:border-gray-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base">
                                <option value="">选择地点</option>
                                <!-- Options will be populated by JS -->
                            </select>
                        </div>
                        <div>
                            <button type="button" id="saveStoreBtn"
                                class="w-full px-2 sm:px-3 py-1 sm:py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 text-sm sm:text-base">
                                保存新店铺
                            </button>
                        </div>
                    </div>

                    <div>
                        <label for="deliveryAddress" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">送货地点</label>
                        <div class="relative">
                            <select id="deliveryAddress"
                                class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 dark:border-gray-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base">
                                <option value="">选择地点</option>
                            </select>
                            <button type="button" id="addAddressBtn"
                                class="absolute right-0 top-0 h-full px-2 text-indigo-600 hover:text-indigo-800">
                                <i class="fas fa-plus text-sm"></i>
                            </button>
                        </div>
                    </div>

                    <div id="newAddressContainer" class="hidden">
                        <label for="newAddress" class="block text-sm font-medium text-gray-700 mb-1">新增送货地点</label>
                        <div class="flex">
                            <input type="text" id="newAddress"
                                class="flex-1 px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 dark:border-gray-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-l-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base">
                            <button type="button" id="saveAddressBtn"
                                class="px-2 sm:px-3 py-1 sm:py-2 bg-indigo-600 text-white rounded-r-md hover:bg-indigo-700 text-sm sm:text-base">
                                保存
                            </button>
                        </div>
                    </div>

                    <div>
                        <label for="deliveryImage" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">送货图片</label>
                        <div
                            class="mt-1 flex justify-center px-4 sm:px-6 pt-3 sm:pt-5 pb-4 sm:pb-6 border-2 border-gray-300 dark:border-gray-600 border-dashed rounded-md bg-gray-50 dark:bg-gray-800">
                            <div class="space-y-1 text-center">
                                <div class="flex text-sm text-gray-600 dark:text-gray-400 items-center justify-center flex-wrap">
                                    <label for="deliveryImage"
                                        class="relative cursor-pointer bg-white dark:bg-gray-700 rounded-md font-medium text-indigo-600 dark:text-indigo-400 hover:text-indigo-500 dark:hover:text-indigo-300 focus-within:outline-none px-2 py-1">
                                        <span>上传图片</span>
                                        <input id="deliveryImage" name="deliveryImage" type="file" accept="image/*"
                                            class="sr-only">
                                    </label>
                                    <p class="pl-1 mobile-hidden">或将图片拖拽到此处</p>
                                </div>
                                <p class="text-xs text-gray-500 dark:text-gray-400">PNG, JPG, GIF 格式，大小不超过10MB</p>
                            </div>
                        </div>
                        <div id="imagePreviewContainer" class="mt-2 hidden">
                            <div class="relative inline-block">
                                <img id="imagePreview" src="#" alt="预览图"
                                    class="h-24 sm:h-32 rounded-md shadow image-preview">
                                <button type="button" id="removeImageBtn"
                                    class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 sm:w-6 sm:h-6 flex items-center justify-center hover:bg-red-600">
                                    <i class="fas fa-times text-xs"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div>
                        <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">备注</label>
                        <textarea id="notes" name="notes" rows="2"
                            class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 dark:border-gray-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base"
                            placeholder="可选的备注信息..."></textarea>
                    </div>

                    <!-- 配件明细部分 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">配件明细</label>
                        <!-- 添加搜索框
                        <div class="mb-2 flex space-x-2">
                            <input type="text" id="partsSearchInput" class="flex-1 px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base" placeholder="搜索配件...">
                            <button type="button" id="clearPartsSearchBtn" class="bg-gray-200 hover:bg-gray-300 px-2 py-1 rounded-md text-sm">清除</button>
                        </div> -->
                        <div id="partsContainer" class="space-y-2 bg-gray-50 dark:bg-gray-800 p-3 rounded-md border border-gray-200 dark:border-gray-600">
                            <!-- 配件项模板 -->
                            <div class="part-item space-y-2">
                                <div class="flex items-center space-x-2">
                                    <select
                                        class="part-type flex-1 min-w-0 px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 dark:border-gray-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base">
                                        <option value="">选择类型</option>
                                        <option value="CPU">CPU</option>
                                        <option value="主板">主板</option>
                                        <option value="内存">内存</option>
                                        <option value="硬盘">硬盘</option>
                                        <option value="显卡">显卡</option>
                                        <option value="电源">电源</option>
                                        <option value="机箱">机箱</option>
                                        <option value="散热器">散热器</option>
                                        <option value="显示器">显示器</option>
                                        <option value="其他">其他</option>
                                    </select>
                                    <input type="text"
                                        class="part-model flex-1 min-w-0 px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 dark:border-gray-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base"
                                        placeholder="型号">
                                </div>
                                <div class="flex items-center space-x-2">
                                    <label class="text-sm text-gray-500 dark:text-gray-400">数量</label>
                                    <input type="number"
                                        class="part-quantity w-20 px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 dark:border-gray-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base"
                                        value="1" min="1" step="1" placeholder="数量">
                                    <label class="text-sm text-gray-500 dark:text-gray-400">价格</label>
                                    <input type="number"
                                        class="part-price w-24 px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 dark:border-gray-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base"
                                        placeholder="价格">
                                    <div class="flex-grow"></div>
                                    <button type="button" class="remove-part px-2 py-1 text-red-600 hover:text-red-800">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <button type="button" id="addPartBtn"
                            class="mt-2 px-3 py-1 text-sm bg-indigo-50 text-indigo-600 hover:bg-indigo-100 rounded-md">
                            <i class="fas fa-plus mr-1"></i> 添加配件
                        </button>
                        <div id="totalPrice" class="mt-2 text-sm font-medium text-gray-700 dark:text-gray-300"></div>
                    </div>

                    <div class="flex space-x-2 sm:space-x-3 mobile-stack">
                        <button type="submit"
                            class="flex-1 bg-indigo-600 text-white py-1 sm:py-2 px-2 sm:px-4 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 text-sm sm:text-base">
                            <i class="fas fa-save mr-1 sm:mr-2"></i> 保存记录
                        </button>
                        <button type="reset"
                            class="flex-1 bg-gray-200 text-gray-800 py-1 sm:py-2 px-2 sm:px-4 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 text-sm sm:text-base">
                            <i class="fas fa-undo mr-1 sm:mr-2"></i> 重置
                        </button>
                    </div>
                </form>
            </div>

            <!-- 右侧记录列表和统计区域 -->
            <div class="lg:col-span-3">
                <!-- 统计图表区域 -->
                <div class="grid grid-cols-1 gap-4 sm:gap-6 mb-4 sm:mb-6">
                    <!-- 每日送货次数折线图 -->
                    <div class="bg-white dark:bg-gray-800 p-3 sm:p-6 rounded-lg shadow-md">
                        <h3 class="text-lg sm:text-xl font-semibold text-gray-800 dark:text-gray-200 mb-3 sm:mb-4 flex items-center">
                            <i class="fas fa-chart-line mr-1 sm:mr-2 text-indigo-500 dark:text-indigo-400"></i> 店铺每日送货次数
                        </h3>
                        <div class="relative" style="height: 300px;">
                            <canvas id="dailyDeliveriesChart"></canvas>
                        </div>
                        <div class="mt-3 flex justify-end items-center">
                            <div class="relative">
                                <input type="text" id="chartStoreSearch" class="text-sm border-gray-300 rounded-md w-40"
                                    placeholder="搜索店铺...">
                                <select id="storeFilterSelect" class="hidden">
                                    <option value="">所有店铺</option>
                                    <!-- 店铺选项将动态添加 -->
                                </select>
                                <div id="chartStoreSearchResults"
                                    class="absolute z-10 w-full mt-1 bg-white shadow-lg rounded-md border border-gray-200 max-h-60 overflow-auto hidden">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                        <div class="bg-white dark:bg-gray-800 p-3 sm:p-6 rounded-lg shadow-md">
                            <h3 class="text-lg sm:text-xl font-semibold text-gray-800 dark:text-gray-200 mb-3 sm:mb-4 flex items-center">
                                <i class="fas fa-chart-pie mr-2 text-indigo-500 dark:text-indigo-400"></i> 店铺分布统计
                            </h3>
                            <div class="relative h-64">
                                <canvas id="storeChart"></canvas>
                            </div>
                        </div>
                        <div class="bg-white dark:bg-gray-800 p-3 sm:p-6 rounded-lg shadow-md">
                            <h3 class="text-lg sm:text-xl font-semibold text-gray-800 dark:text-gray-200 mb-3 sm:mb-4 flex items-center">
                                <i class="fas fa-chart-bar mr-2 text-indigo-500 dark:text-indigo-400"></i> 送货地点统计
                            </h3>
                            <div class="relative h-64">
                                <canvas id="addressChart"></canvas>
                            </div>
                        </div>
                        <div class="bg-white dark:bg-gray-800 p-3 sm:p-6 rounded-lg shadow-md">
                            <h3 class="text-lg sm:text-xl font-semibold text-gray-800 dark:text-gray-200 mb-3 sm:mb-4 flex items-center">
                                <i class="fas fa-chart-line mr-2 text-indigo-500 dark:text-indigo-400"></i> 每日送货统计
                            </h3>
                            <div class="relative h-64">
                                <canvas id="dailyChart"></canvas>
                            </div>
                        </div>

                        <div class="bg-white dark:bg-gray-800 p-3 sm:p-6 rounded-lg shadow-md overflow-hidden">
                            <h3 class="text-lg sm:text-xl font-semibold text-gray-800 dark:text-gray-200 mb-3 sm:mb-4 flex items-center">
                                <i class="fas fa-chart-area mr-2 text-blue-600 dark:text-blue-400"></i> 每日送货总金额统计
                            </h3>
                            <div
                                class="relative h-72 sm:h-80 border border-gray-100 dark:border-gray-600 rounded-lg p-2 bg-gradient-to-br from-blue-50 to-white dark:from-gray-700 dark:to-gray-800 shadow-inner">
                                <canvas id="dailyAmountChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- 记录列表区域 -->
                    <div class="bg-white dark:bg-gray-800 p-3 sm:p-6 rounded-lg shadow-md">
                        <div class="flex justify-between items-center mb-4 sm:mb-6 mobile-stack">
                            <h2 class="text-lg sm:text-xl font-semibold text-gray-800 dark:text-gray-200 flex items-center">
                                <i class="fas fa-list-ul mr-1 sm:mr-2 text-indigo-500 dark:text-indigo-400"></i> 送货记录
                            </h2>
                            <div class="flex space-x-2 sm:space-x-3">
                                <button id="exportBtn"
                                    class="px-3 sm:px-4 py-1 sm:py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 text-sm sm:text-base">
                                    <i class="fas fa-file-export mr-1 sm:mr-2"></i> 导出数据
                                </button>
                                <label for="importFile"
                                    class="px-3 sm:px-4 py-1 sm:py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 text-sm sm:text-base cursor-pointer">
                                    <i class="fas fa-file-import mr-1 sm:mr-2"></i> 导入数据
                                    <input type="file" id="importFile" accept=".zip" class="hidden">
                                </label>
                                <div class="relative mt-2 sm:mt-0 mobile-full-width">
                                    <input type="text" id="searchInput" placeholder="搜索店铺或地址..."
                                        class="pl-8 sm:pl-10 pr-3 sm:pr-4 py-1 sm:py-2 border border-gray-300 dark:border-gray-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base w-full">
                                    <div class="absolute left-2 sm:left-3 top-1.5 sm:top-2.5 text-gray-400 dark:text-gray-500">
                                        <i class="fas fa-search text-sm"></i>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                <thead class="bg-gray-50 dark:bg-gray-700">
                                    <tr>
                                        <th scope="col"
                                            class="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            店铺名称</th>
                                        <th scope="col"
                                            class="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider mobile-hidden">
                                            送货地点</th>
                                        <th scope="col"
                                            class="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            图片</th>
                                        <th scope="col"
                                            class="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider mobile-hidden">
                                            时间</th>
                                        <th scope="col"
                                            class="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            操作</th>
                                    </tr>
                                </thead>
                                <tbody id="recordsTableBody" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                    <!-- 记录将通过JavaScript动态添加 -->
                                </tbody>
                            </table>
                        </div>

                        <div id="noRecordsMessage" class="text-center py-6 sm:py-8 text-gray-500 dark:text-gray-400">
                            <i class="fas fa-box-open text-3xl sm:text-4xl mb-2 sm:mb-3 text-gray-300 dark:text-gray-600"></i>
                            <p class="text-base sm:text-lg">暂无送货记录</p>
                            <p class="text-xs sm:text-sm mt-1">点击上方"新增记录"按钮添加第一条记录</p>
                        </div>

                        <div class="mt-3 sm:mt-4 flex justify-between items-center mobile-stack">
                            <div class="text-xs sm:text-sm text-gray-500 dark:text-gray-400" id="recordCount">共0条记录</div>
                            <div class="flex space-x-1 sm:space-x-2 mt-2 sm:mt-0">
                                <button id="prevPageBtn"
                                    class="px-2 sm:px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 disabled:opacity-50 text-sm"
                                    disabled>
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                <span id="pageInfo" class="px-2 sm:px-3 py-1 text-sm text-gray-700 dark:text-gray-300">1/1</span>
                                <button id="nextPageBtn"
                                    class="px-2 sm:px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 disabled:opacity-50 text-sm"
                                    disabled>
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 编辑记录模态框 -->
        <div id="editModal"
            class="fixed inset-0 bg-black bg-opacity-50 dark:bg-black dark:bg-opacity-70 flex items-center justify-center z-50 hidden p-2 sm:p-4">
            <div class="bg-white dark:bg-gray-900 rounded-lg shadow-xl w-full max-w-md fade-in border dark:border-gray-700">
                <div class="px-4 sm:px-6 py-3 sm:py-4 border-b border-gray-200 dark:border-gray-600">
                    <h3 class="text-base sm:text-lg font-semibold text-gray-900 dark:text-white">编辑送货记录</h3>
                </div>
                <div class="px-4 sm:px-6 py-3 sm:py-4">
                    <form id="editForm">
                        <input type="hidden" id="editRecordId">
                        <div class="mb-3 sm:mb-4">
                            <label for="editStoreName" class="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">店铺名称</label>
                            <select id="editStoreName"
                                class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 dark:border-gray-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base">
                                <!-- 选项将通过JavaScript动态添加 -->
                            </select>
                        </div>
                        <div class="mb-3 sm:mb-4">
                            <label for="editDeliveryAddress"
                                class="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">送货地点</label>
                            <select id="editDeliveryAddress"
                                class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 dark:border-gray-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base">
                                <!-- 选项将通过JavaScript动态添加 -->
                            </select>
                        </div>
                        <div class="mb-3 sm:mb-4">
                            <label for="editDeliveryImage"
                                class="block text-sm font-medium text-gray-700 mb-1">送货图片</label>
                            <div class="flex items-center space-x-2 sm:space-x-4">
                                <div id="editImagePreviewContainer"
                                    class="h-16 sm:h-20 rounded-md shadow overflow-hidden">
                                    <img id="editImagePreview" src="#" alt="当前图片" class="h-full w-auto object-cover">
                                </div>
                                <div>
                                    <label for="editDeliveryImage"
                                        class="cursor-pointer text-indigo-600 hover:text-indigo-800 text-xs sm:text-sm font-medium">
                                        <i class="fas fa-upload mr-1"></i> 更换图片
                                    </label>
                                    <input id="editDeliveryImage" name="editDeliveryImage" type="file" accept="image/*"
                                        class="sr-only">
                                    <button id="editRemoveImageBtn" type="button"
                                        class="block mt-1 text-red-600 hover:text-red-800 text-xs sm:text-sm font-medium">
                                        <i class="fas fa-trash-alt mr-1"></i> 移除图片
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3 sm:mb-4">
                            <label for="editNotes" class="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">备注</label>
                            <textarea id="editNotes" name="editNotes" rows="2"
                                class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 dark:border-gray-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base"
                                placeholder="可选的备注信息..."></textarea>
                        </div>

                        <div class="mb-3 sm:mb-4">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">配件明细</label>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-600 mb-2">
                                    <thead class="bg-gray-50 dark:bg-gray-700">
                                        <tr>
                                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">
                                                配件类型</th>
                                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">
                                                型号</th>
                                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">
                                                数量</th>
                                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">
                                                单价</th>
                                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">
                                                金额</th>
                                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">
                                                操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="editPartsTableBody" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600">
                                    </tbody>
                                    <tfoot class="bg-gray-50 dark:bg-gray-700">
                                        <tr>
                                            <td colspan="4"
                                                class="px-3 py-2 text-right text-sm font-medium text-gray-900 dark:text-gray-100">总计：</td>
                                            <td class="px-3 py-2 text-left text-sm font-medium text-indigo-600 dark:text-indigo-400"
                                                id="editTotalAmount">¥0.00</td>
                                            <td></td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                            <button type="button" id="editAddPartBtn"
                                class="mt-2 px-3 py-1 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 text-sm">
                                <i class="fas fa-plus mr-1"></i> 添加配件
                            </button>
                        </div>
                    </form>
                </div>
                <div class="px-4 sm:px-6 py-3 sm:py-4 border-t border-gray-200 dark:border-gray-600 flex justify-end space-x-2 sm:space-x-3">
                    <button id="cancelEditBtn" type="button"
                        class="px-3 sm:px-4 py-1 sm:py-2 bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 text-sm sm:text-base">
                        取消
                    </button>
                    <button id="saveEditBtn" type="button"
                        class="px-3 sm:px-4 py-1 sm:py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 text-sm sm:text-base">
                        保存更改
                    </button>
                </div>
            </div>
        </div>

        <!-- 删除确认模态框 -->
        <div id="deleteModal"
            class="fixed inset-0 bg-black bg-opacity-50 dark:bg-black dark:bg-opacity-70 flex items-center justify-center z-50 hidden p-2 sm:p-4">
            <div class="bg-white dark:bg-gray-900 rounded-lg shadow-xl w-full max-w-md fade-in border dark:border-gray-700">
                <div class="px-4 sm:px-6 py-3 sm:py-4 border-b border-gray-200 dark:border-gray-600">
                    <h3 class="text-base sm:text-lg font-semibold text-gray-900 dark:text-white">确认删除</h3>
                </div>
                <div class="px-4 sm:px-6 py-3 sm:py-4">
                    <p class="text-sm sm:text-base text-gray-700 dark:text-gray-200">您确定要删除这条送货记录吗？此操作无法撤销。</p>
                </div>
                <div class="px-4 sm:px-6 py-3 sm:py-4 border-t border-gray-200 dark:border-gray-600 flex justify-end space-x-2 sm:space-x-3">
                    <button id="cancelDeleteBtn" type="button"
                        class="px-3 sm:px-4 py-1 sm:py-2 bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 text-sm sm:text-base">
                        取消
                    </button>
                    <button id="confirmDeleteBtn" type="button"
                        class="px-3 sm:px-4 py-1 sm:py-2 bg-red-600 text-white rounded-md hover:bg-red-700 text-sm sm:text-base">
                        确认删除
                    </button>
                </div>
            </div>
        </div>

        <!-- 详情模态框 -->
        <div id="detailsModal"
            class="fixed inset-0 bg-black bg-opacity-50 dark:bg-black dark:bg-opacity-70 flex items-center justify-center z-50 hidden p-2 sm:p-4">
            <div class="bg-white dark:bg-gray-900 rounded-lg shadow-xl w-full max-w-2xl fade-in border dark:border-gray-700">
                <div class="px-4 sm:px-6 py-3 sm:py-4 border-b border-gray-200 dark:border-gray-600">
                    <h3 class="text-base sm:text-lg font-semibold text-gray-900 dark:text-white">送货记录详情</h3>
                </div>
                <div class="px-4 sm:px-6 py-3 sm:py-4">
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div>
                            <h4 class="font-medium text-gray-700 dark:text-gray-300 mb-2">基本信息</h4>
                            <div class="space-y-2">
                                <p><span class="text-gray-500 dark:text-gray-400">店铺名称：</span><span id="detailStoreName" class="text-gray-900 dark:text-gray-100"></span></p>
                                <p><span class="text-gray-500 dark:text-gray-400">送货地点：</span><span id="detailAddress" class="text-gray-900 dark:text-gray-100"></span></p>
                                <p><span class="text-gray-500 dark:text-gray-400">送货时间：</span><span id="detailTime" class="text-gray-900 dark:text-gray-100"></span></p>
                                <p><span class="text-gray-500 dark:text-gray-400">备注：</span><span id="detailNotes" class="text-gray-900 dark:text-gray-100"></span></p>
                            </div>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-700 dark:text-gray-300 mb-2">送货图片</h4>
                            <div id="detailImageContainer" class="border border-gray-200 dark:border-gray-600 rounded-lg p-2 bg-gray-50 dark:bg-gray-700">
                                <img id="detailImage" src="" alt="送货图片" class="max-h-40 rounded-md">
                            </div>
                        </div>
                    </div>
                    <div class="mt-4">
                        <h4 class="font-medium text-gray-700 dark:text-gray-300 mb-2">配件明细</h4>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                <thead class="bg-gray-50 dark:bg-gray-700">
                                    <tr>
                                        <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">配件类型
                                        </th>
                                        <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">型号
                                        </th>
                                        <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">数量
                                        </th>
                                        <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">单价
                                        </th>
                                        <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">金额
                                        </th>
                                    </tr>
                                </thead>
                                <tbody id="detailPartsTableBody" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                </tbody>
                                <tfoot class="bg-gray-50 dark:bg-gray-700">
                                    <tr>
                                        <td colspan="4" class="px-3 py-2 text-right text-sm font-medium text-gray-900 dark:text-gray-100">
                                            总计：</td>
                                        <td class="px-3 py-2 text-left text-sm font-medium text-indigo-600 dark:text-indigo-400"
                                            id="detailTotalAmount"></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="px-4 sm:px-6 py-3 sm:py-4 border-t border-gray-200 dark:border-gray-600 flex justify-end">
                    <button id="closeDetailsBtn" type="button"
                        class="px-3 sm:px-4 py-1 sm:py-2 bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 text-sm sm:text-base">
                        关闭
                    </button>
                </div>
            </div>
        </div>

        <!-- 调试信息面板（已隐藏） -->
        <div id="debugInfo"
            class="fixed bottom-0 left-0 bg-black bg-opacity-70 text-white p-4 m-4 rounded-lg z-50 max-w-lg overflow-auto max-h-64"
            style="display:none;">
            <h3 class="text-lg font-bold mb-2">调试信息</h3>
            <pre id="debugContent" class="text-xs whitespace-pre-wrap"></pre>
            <button onclick="this.parentElement.style.display='none'"
                class="absolute top-2 right-2 text-white">&times;</button>
        </div>
        <script src="/js/index.js"></script>

</body>
</html>