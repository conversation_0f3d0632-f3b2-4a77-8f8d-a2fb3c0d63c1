/**
 * 笔记搜索模块
 * 负责高级搜索功能
 */

class NotesSearch {
    constructor() {
        this.searchHistory = [];
        this.maxHistoryItems = 10;
        
        this.init();
    }
    
    init() {
        this.loadSearchHistory();
        this.bindEvents();
    }
    
    bindEvents() {
        // 搜索框增强功能
        const searchInput = document.getElementById('searchInput');
        
        // 搜索建议
        searchInput.addEventListener('focus', () => {
            this.showSearchSuggestions();
        });
        
        // 键盘导航
        searchInput.addEventListener('keydown', (e) => {
            this.handleSearchKeydown(e);
        });
        
        // 点击其他地方隐藏建议
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.search-container')) {
                this.hideSearchSuggestions();
            }
        });
    }
    
    showSearchSuggestions() {
        const searchContainer = document.querySelector('.search-container');
        let suggestionsContainer = document.getElementById('searchSuggestions');
        
        if (!suggestionsContainer) {
            suggestionsContainer = document.createElement('div');
            suggestionsContainer.id = 'searchSuggestions';
            suggestionsContainer.className = 'absolute top-full left-0 right-0 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg mt-1 z-10 max-h-64 overflow-y-auto hidden';
            searchContainer.appendChild(suggestionsContainer);
        }
        
        // 生成搜索建议内容
        const suggestions = this.generateSearchSuggestions();
        
        if (suggestions.length > 0) {
            suggestionsContainer.innerHTML = suggestions.join('');
            suggestionsContainer.classList.remove('hidden');
        } else {
            suggestionsContainer.classList.add('hidden');
        }
    }
    
    hideSearchSuggestions() {
        const suggestionsContainer = document.getElementById('searchSuggestions');
        if (suggestionsContainer) {
            suggestionsContainer.classList.add('hidden');
        }
    }
    
    generateSearchSuggestions() {
        const suggestions = [];
        
        // 搜索历史
        if (this.searchHistory.length > 0) {
            suggestions.push('<div class="px-3 py-2 text-xs font-semibold text-gray-500 dark:text-gray-400 border-b border-gray-200 dark:border-gray-700">搜索历史</div>');
            
            this.searchHistory.slice(0, 5).forEach(item => {
                suggestions.push(`
                    <div class="px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer flex items-center justify-between" onclick="notesSearch.selectSuggestion('${item}')">
                        <span class="flex items-center">
                            <i class="fas fa-history mr-2 text-gray-400"></i>
                            ${item}
                        </span>
                        <button onclick="event.stopPropagation(); notesSearch.removeFromHistory('${item}')" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                            <i class="fas fa-times text-xs"></i>
                        </button>
                    </div>
                `);
            });
        }
        
        // 搜索提示
        suggestions.push('<div class="px-3 py-2 text-xs font-semibold text-gray-500 dark:text-gray-400 border-b border-gray-200 dark:border-gray-700">搜索提示</div>');
        
        const searchTips = [
            { text: '配置信息', icon: 'fas fa-cog' },
            { text: '问题解决', icon: 'fas fa-tools' },
            { text: '技术文档', icon: 'fas fa-file-alt' },
            { text: '重要笔记', icon: 'fas fa-star' }
        ];
        
        searchTips.forEach(tip => {
            suggestions.push(`
                <div class="px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer" onclick="notesSearch.selectSuggestion('${tip.text}')">
                    <span class="flex items-center">
                        <i class="${tip.icon} mr-2 text-blue-500"></i>
                        ${tip.text}
                    </span>
                </div>
            `);
        });
        
        return suggestions;
    }
    
    selectSuggestion(text) {
        const searchInput = document.getElementById('searchInput');
        searchInput.value = text;
        
        // 触发搜索
        if (window.notesManager) {
            window.notesManager.handleSearch(text);
        }
        
        // 添加到搜索历史
        this.addToHistory(text);
        
        // 隐藏建议
        this.hideSearchSuggestions();
    }
    
    handleSearchKeydown(e) {
        const suggestionsContainer = document.getElementById('searchSuggestions');
        
        if (!suggestionsContainer || suggestionsContainer.classList.contains('hidden')) {
            return;
        }
        
        const suggestions = suggestionsContainer.querySelectorAll('[onclick*="selectSuggestion"]');
        let currentIndex = -1;
        
        // 找到当前选中的建议
        suggestions.forEach((suggestion, index) => {
            if (suggestion.classList.contains('bg-blue-100')) {
                currentIndex = index;
            }
        });
        
        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                currentIndex = Math.min(currentIndex + 1, suggestions.length - 1);
                this.highlightSuggestion(suggestions, currentIndex);
                break;
                
            case 'ArrowUp':
                e.preventDefault();
                currentIndex = Math.max(currentIndex - 1, 0);
                this.highlightSuggestion(suggestions, currentIndex);
                break;
                
            case 'Enter':
                if (currentIndex >= 0) {
                    e.preventDefault();
                    suggestions[currentIndex].click();
                }
                break;
                
            case 'Escape':
                this.hideSearchSuggestions();
                break;
        }
    }
    
    highlightSuggestion(suggestions, index) {
        suggestions.forEach((suggestion, i) => {
            if (i === index) {
                suggestion.classList.add('bg-blue-100', 'dark:bg-blue-900');
            } else {
                suggestion.classList.remove('bg-blue-100', 'dark:bg-blue-900');
            }
        });
    }
    
    addToHistory(searchTerm) {
        if (!searchTerm || searchTerm.trim() === '') {
            return;
        }
        
        const term = searchTerm.trim();
        
        // 移除重复项
        this.searchHistory = this.searchHistory.filter(item => item !== term);
        
        // 添加到开头
        this.searchHistory.unshift(term);
        
        // 限制历史记录数量
        if (this.searchHistory.length > this.maxHistoryItems) {
            this.searchHistory = this.searchHistory.slice(0, this.maxHistoryItems);
        }
        
        // 保存到本地存储
        this.saveSearchHistory();
    }
    
    removeFromHistory(searchTerm) {
        this.searchHistory = this.searchHistory.filter(item => item !== searchTerm);
        this.saveSearchHistory();
        this.showSearchSuggestions(); // 刷新建议列表
    }
    
    clearHistory() {
        this.searchHistory = [];
        this.saveSearchHistory();
        this.hideSearchSuggestions();
    }
    
    loadSearchHistory() {
        try {
            const saved = localStorage.getItem('notesSearchHistory');
            if (saved) {
                this.searchHistory = JSON.parse(saved);
            }
        } catch (error) {
            console.error('加载搜索历史失败:', error);
            this.searchHistory = [];
        }
    }
    
    saveSearchHistory() {
        try {
            localStorage.setItem('notesSearchHistory', JSON.stringify(this.searchHistory));
        } catch (error) {
            console.error('保存搜索历史失败:', error);
        }
    }
    
    // 高级搜索功能
    openAdvancedSearch() {
        const modal = this.createAdvancedSearchModal();
        document.body.appendChild(modal);
    }
    
    createAdvancedSearchModal() {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4';
        modal.innerHTML = `
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white">高级搜索</h2>
                </div>
                
                <div class="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
                    <form id="advancedSearchForm" class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">关键词</label>
                            <input type="text" id="advSearchKeyword" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">分类</label>
                                <select id="advSearchCategory" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                                    <option value="">所有分类</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">作者</label>
                                <select id="advSearchAuthor" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                                    <option value="">所有作者</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">开始日期</label>
                                <input type="date" id="advSearchDateFrom" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">结束日期</label>
                                <input type="date" id="advSearchDateTo" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">标签</label>
                            <div id="advSearchTags" class="border border-gray-300 dark:border-gray-600 rounded-lg p-3 min-h-[100px] max-h-32 overflow-y-auto">
                                <!-- 标签选择器将在这里动态生成 -->
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">排序方式</label>
                            <select id="advSearchSort" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                                <option value="relevance">相关性</option>
                                <option value="updated">最近更新</option>
                                <option value="created">创建时间</option>
                                <option value="title">标题</option>
                                <option value="views">浏览次数</option>
                            </select>
                        </div>
                    </form>
                </div>
                
                <div class="p-6 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-3">
                    <button type="button" onclick="this.closest('.fixed').remove()" class="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-600 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-500">
                        取消
                    </button>
                    <button type="button" onclick="notesSearch.executeAdvancedSearch(this)" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                        搜索
                    </button>
                </div>
            </div>
        `;
        
        // 加载高级搜索的选项数据
        this.loadAdvancedSearchOptions(modal);
        
        return modal;
    }
    
    async loadAdvancedSearchOptions(modal) {
        try {
            const token = localStorage.getItem('token');
            
            // 加载分类选项
            if (window.notesManager && window.notesManager.categories) {
                const categorySelect = modal.querySelector('#advSearchCategory');
                window.notesManager.categories.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category.id;
                    option.textContent = category.name;
                    categorySelect.appendChild(option);
                });
            }
            
            // 加载标签选项
            if (window.notesManager && window.notesManager.tags) {
                const tagsContainer = modal.querySelector('#advSearchTags');
                const tagsHtml = window.notesManager.tags.map(tag => `
                    <label class="flex items-center space-x-2 p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded cursor-pointer">
                        <input type="checkbox" value="${tag.id}" class="adv-search-tag">
                        <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium text-white" style="background-color: ${tag.color}">
                            ${tag.name}
                        </span>
                    </label>
                `).join('');
                tagsContainer.innerHTML = tagsHtml;
            }
            
        } catch (error) {
            console.error('加载高级搜索选项失败:', error);
        }
    }
    
    executeAdvancedSearch(button) {
        const modal = button.closest('.fixed');
        const formData = this.getAdvancedSearchData(modal);
        
        // 执行搜索
        if (window.notesManager) {
            window.notesManager.applyFilter(formData, '高级搜索');
        }
        
        // 关闭模态框
        modal.remove();
    }
    
    getAdvancedSearchData(modal) {
        const data = {};
        
        const keyword = modal.querySelector('#advSearchKeyword').value.trim();
        if (keyword) data.search = keyword;
        
        const category = modal.querySelector('#advSearchCategory').value;
        if (category) data.category_id = category;
        
        const author = modal.querySelector('#advSearchAuthor').value;
        if (author) data.author_id = author;
        
        const dateFrom = modal.querySelector('#advSearchDateFrom').value;
        if (dateFrom) data.date_from = dateFrom;
        
        const dateTo = modal.querySelector('#advSearchDateTo').value;
        if (dateTo) data.date_to = dateTo;
        
        const selectedTags = Array.from(modal.querySelectorAll('.adv-search-tag:checked')).map(cb => cb.value);
        if (selectedTags.length > 0) data.tag_ids = selectedTags;
        
        const sort = modal.querySelector('#advSearchSort').value;
        if (sort) data.sort = sort;
        
        return data;
    }
}

// 全局实例
let notesSearch;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    notesSearch = new NotesSearch();
    window.notesSearch = notesSearch; // 供其他模块使用
});
