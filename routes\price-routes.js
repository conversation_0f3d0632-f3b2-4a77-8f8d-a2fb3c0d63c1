// price-routes.js - 处理价格记录的API路由
const express = require('express');
const router = express.Router();
const { body, query, param, validationResult } = require('express-validator');
const db = require('../db');
const { verifyToken, checkAdminRole } = require('../middleware/auth');

// 注意：这里的表结构创建仅为参考，实际以数据库为准。
// 我们不会在运行时自动修改表结构，以防数据丢失。
async function ensurePriceTablesExist() {
    console.log('确保价格相关表已存在，但不会修改它们。');
}

// 启动时检查
ensurePriceTablesExist();

// POST /api/save-price-record - 创建价格记录
router.post('/save-price-record', [
    body('total_amount').isDecimal().withMessage('总金额必须是数字'),
    body('items').isArray({ min: 1 }).withMessage('items必须是至少包含一项的数组'),
    body('items.*.price').isDecimal().withMessage('单价必须是数字'),
    body('items.*.operation_type').isString().notEmpty().withMessage('操作类型不能为空'),
    body('items.*.description').isString().notEmpty().withMessage('描述不能为空')
], async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ success: false, message: '输入验证失败', errors: errors.array() });
    }

    const { total_amount, items } = req.body;
    const connection = await db.getConnection();

    try {
        await connection.beginTransaction();

        // 1. 在主表 price_records 中插入总金额和用户ID
        const [recordResult] = await connection.query(
            'INSERT INTO price_records (user_id, total_amount) VALUES (?, ?)',
            [req.user.id, total_amount]
        );
        const recordId = recordResult.insertId;

        // 2. 将每个项目插入到 price_items 表中
        const itemValues = items.map(item => [
            recordId,
            item.price,
            item.operation_type,
            item.description
        ]);

        await connection.query(
            'INSERT INTO price_items (record_id, price, operation_type, description) VALUES ?',
            [itemValues]
        );

        await connection.commit();
        res.status(201).json({ success: true, message: '价格记录已保存', recordId });
    } catch (error) {
        await connection.rollback();
        console.error('保存价格记录出错:', error);
        res.status(500).json({ success: false, message: '内部服务器错误', error: error.message });
    } finally {
        connection.release();
    }
});

// GET /api/price-records - 获取价格记录列表（带分页和搜索）
router.get('/price-records', [
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('每页记录数必须在1-100之间'),
    query('search').optional().isString(),
    query('category').optional().isString()
], async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ success: false, message: '输入验证失败', errors: errors.array() });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;
    const search = req.query.search || '';
    const category = req.query.category || 'all';

    try {
        // The main query to get the total count of records matching the filter (显示所有记录)
        let countQuery = 'SELECT COUNT(DISTINCT pr.id) as total FROM price_records pr';
        // The query to get the paginated records (显示所有记录，包含用户信息和配件明细)
        let recordsQuery = `
            SELECT pr.id, pr.created_at, pr.total_amount, COUNT(pi.id) as item_count, u.username as created_by,
                   GROUP_CONCAT(CONCAT(pi.operation_type, ' ', pi.description) SEPARATOR '; ') as item_details
            FROM price_records pr
            LEFT JOIN price_items pi ON pr.id = pi.record_id
            LEFT JOIN users u ON pr.user_id = u.id
        `;
        
        const queryParams = []; // 移除用户ID限制
        let whereClause = '';

        if (search) {
            if (category === 'description') {
                // To filter by description, we need to join price_items
                countQuery = 'SELECT COUNT(DISTINCT pr.id) as total FROM price_records pr INNER JOIN price_items pi ON pr.id = pi.record_id';
                whereClause = ` WHERE pi.description LIKE ?`;
                queryParams.push(`%${search}%`);
            } else if (category === 'price') {
                whereClause = ` WHERE pr.total_amount = ?`;
                queryParams.push(parseFloat(search) || 0);
            } else if (category === 'date') {
                whereClause = ` WHERE DATE_FORMAT(pr.created_at, '%Y-%m-%d') LIKE ?`;
                queryParams.push(`%${search}%`);
            } else { // 'all'
                // For 'all', the count is more complex
                countQuery = `SELECT COUNT(DISTINCT pr.id) as total FROM price_records pr LEFT JOIN price_items pi ON pr.id = pi.record_id LEFT JOIN users u ON pr.user_id = u.id`;
                whereClause = ` WHERE (pr.total_amount = ? OR DATE_FORMAT(pr.created_at, '%Y-%m-%d') LIKE ? OR pi.description LIKE ? OR u.username LIKE ?)`;
                queryParams.push(parseFloat(search) || 0, `%${search}%`, `%${search}%`, `%${search}%`);
            }
        }
        
        countQuery += whereClause;
        recordsQuery += whereClause;

        recordsQuery += ' GROUP BY pr.id, u.username ORDER BY pr.created_at DESC LIMIT ? OFFSET ?';
        const pagingParams = [...queryParams, limit, offset];

        const [countResult] = await db.query(countQuery, queryParams);
        const [records] = await db.query(recordsQuery, pagingParams);
        
        const total = countResult[0].total;

        res.json({ success: true, total, page, limit, records });
    } catch (error) {
        console.error('获取价格记录列表出错:', error);
        res.status(500).json({ success: false, message: '内部服务器错误', error: error.message });
    }
});


// GET /api/price-records/:id - 获取单条价格记录详情
router.get('/price-records/:id', [
    param('id').isInt().withMessage('ID必须是整数')
], async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ success: false, message: '输入验证失败', errors: errors.array() });
    }

    const { id } = req.params;

    try {
        const [records] = await db.query(`
            SELECT pr.*, u.username as created_by
            FROM price_records pr
            LEFT JOIN users u ON pr.user_id = u.id
            WHERE pr.id = ?
        `, [id]);
        if (records.length === 0) {
            return res.status(404).json({ success: false, message: '找不到记录' });
        }

        const [items] = await db.query('SELECT * FROM price_items WHERE record_id = ?', [id]);
        res.json({ success: true, record: records[0], items });
    } catch (error) {
        console.error('获取价格记录详情出错:', error);
        res.status(500).json({ success: false, message: '内部服务器错误', error: error.message });
    }
});


// PUT /api/price-records/:id - 更新价格记录
router.put('/price-records/:id', [
    param('id').isInt().withMessage('ID必须是整数'),
    body('total_amount').isDecimal().withMessage('总金额必须是数字'),
    body('items').isArray({ min: 1 }).withMessage('items必须是至少包含一项的数组')
], async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ success: false, message: '输入验证失败', errors: errors.array() });
    }

    const id = req.params.id;
    const { total_amount, items } = req.body;
    const connection = await db.getConnection();

    try {
        await connection.beginTransaction();

        await connection.query('UPDATE price_records SET total_amount = ? WHERE id = ?', [total_amount, id]);
        await connection.query('DELETE FROM price_items WHERE record_id = ?', [id]);

        const itemValues = items.map(item => [
            id,
            item.price,
            item.operation_type,
            item.description
        ]);
        await connection.query('INSERT INTO price_items (record_id, price, operation_type, description) VALUES ?', [itemValues]);

        await connection.commit();
        res.json({ success: true, message: '价格记录已更新', recordId: id });
    } catch (error) {
        await connection.rollback();
        console.error('更新价格记录出错:', error);
        res.status(500).json({ success: false, message: '内部服务器错误', error: error.message });
    } finally {
        connection.release();
    }
});


// DELETE /api/price-records/:id - 删除价格记录 (仅限管理员)
router.delete('/price-records/:id', checkAdminRole, [
    param('id').isInt().withMessage('ID必须是整数')
], async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ success: false, message: '输入验证失败', errors: errors.array() });
    }

    const { id } = req.params;

    try {
        // 由于设置了 ON DELETE CASCADE，删除主表记录会自动删除子表项目
        const [result] = await db.query('DELETE FROM price_records WHERE id = ?', [id]);
        if (result.affectedRows === 0) {
            return res.status(404).json({ success: false, message: '找不到要删除的记录' });
        }
        res.json({ success: true, message: '价格记录已删除', recordId: id });
    } catch (error) {
        console.error('删除价格记录出错:', error);
        res.status(500).json({ success: false, message: '内部服务器错误', error: error.message });
    }
});

// GET /api/price-analytics - 获取价格数据分析
router.get('/price-analytics', [
    query('days').optional().isInt({ min: 0 }).withMessage('天数必须是非负整数')
], async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ success: false, message: '输入验证失败', errors: errors.array() });
    }
    
    const days = parseInt(req.query.days) || 30;
    let dateFilter = '';
    if (days > 0) {
        dateFilter = `WHERE pr.created_at >= DATE_SUB(CURRENT_DATE(), INTERVAL ${days} DAY)`;
    }
    
    try {
        // 1. 每日金额趋势
        const [dailyAmounts] = await db.query(`
            SELECT DATE(pr.created_at) as date, SUM(pr.total_amount) as total_amount
            FROM price_records pr
            ${dateFilter}
            GROUP BY DATE(pr.created_at)
            ORDER BY date ASC
        `);
        
        // 2. 时间段分布
        const [timeDistribution] = await db.query(`
            SELECT 
                SUM(CASE WHEN HOUR(pr.created_at) BETWEEN 0 AND 5 THEN 1 ELSE 0 END) as early_morning,
                SUM(CASE WHEN HOUR(pr.created_at) BETWEEN 6 AND 11 THEN 1 ELSE 0 END) as morning,
                SUM(CASE WHEN HOUR(pr.created_at) BETWEEN 12 AND 17 THEN 1 ELSE 0 END) as afternoon,
                SUM(CASE WHEN HOUR(pr.created_at) BETWEEN 18 AND 23 THEN 1 ELSE 0 END) as evening
            FROM price_records pr
            ${dateFilter}
        `);
        
        // 3. 配件类型分布 - 使用 price_items 表
        const [partTypes] = await db.query(`
            SELECT 
                description, 
                COUNT(*) as count
            FROM price_items
            GROUP BY description
            ORDER BY count DESC
            LIMIT 20
        `);
        
        // 4. 操作类型分布 - 使用 price_items 表
        const [operationTypes] = await db.query(`
            SELECT 
                operation_type, 
                COUNT(*) as count
            FROM price_items
            GROUP BY operation_type
            ORDER BY count DESC
        `);
        
        // 5. 每日平均订单金额
        const [dailyAOV] = await db.query(`
            SELECT 
                DATE(pr.created_at) as date,
                AVG(pr.total_amount) as average_amount,
                COUNT(*) as order_count
            FROM price_records pr
            ${dateFilter}
            GROUP BY DATE(pr.created_at)
            ORDER BY date ASC
        `);
        
        res.json({
            success: true,
            dailyAmounts,
            timeDistribution: timeDistribution[0] || {},
            partTypes,
            operationTypeDistribution: operationTypes,
            dailyAOV
        });
    } catch (error) {
        console.error('获取价格数据分析出错:', error);
        res.status(500).json({ success: false, message: '内部服务器错误', error: error.message });
    }
});

module.exports = router; 