.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.image-preview {
    transition: all 0.3s ease;
}

.image-preview:hover {
    transform: scale(1.05);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 全局表格修复 */
.table-wrapper {
    width: 100%;
    max-width: 100%;
    overflow-x: auto;
}

/* 确保内容不会被裁剪 */
* {
    box-sizing: border-box;
}

.container {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
}

/* 表格容器内部元素修复 */
.table-wrapper table {
    width: 100%;
}

/* 文本截断样式 */
.text-truncate {
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: calc(100% - 10px) !important;
    display: inline-block !important;
}

/* 表格样式 */
.table-compact td {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}

.model-column {
    min-width: 180px;
}

/* 标签样式 */
.spec-tag {
    padding: 3px 8px !important;
    border-radius: 4px !important;
    font-size: 0.75rem !important;
    font-weight: 500 !important;
    line-height: 1.5 !important;
    display: inline-block !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: fit-content !important;
    margin: 2px 4px 2px 0 !important;
}

.spec-tag.socket {
    background-color: rgba(180, 83, 9, 0.1) !important;
    color: #b45309 !important;
    border: 1px solid rgba(180, 83, 9, 0.2) !important;
}

.spec-tag.cores {
    background-color: rgba(22, 101, 52, 0.1) !important;
    color: #166534 !important;
    border: 1px solid rgba(22, 101, 52, 0.2) !important;
}

.spec-tag.freq {
    background-color: rgba(76, 29, 149, 0.1) !important;
    color: #4c1d95 !important;
    border: 1px solid rgba(76, 29, 149, 0.2) !important;
}

.spec-tag.process {
    background-color: rgba(6, 95, 70, 0.1) !important;
    color: #065f46 !important;
    border: 1px solid rgba(6, 95, 70, 0.2) !important;
}

.spec-tag.tdp {
    background-color: rgba(194, 65, 12, 0.1) !important;
    color: #c2410c !important;
    border: 1px solid rgba(194, 65, 12, 0.2) !important;
}

/* 品牌标签 */
.storage-badge {
    padding: 4px 10px !important;
    font-weight: 600 !important;
    border-radius: 4px !important;
    display: inline-block !important;
}

.ssd-storage {
    background-color: rgba(5, 150, 105, 0.1) !important;
    color: #047857 !important;
    border: 1px solid rgba(5, 150, 105, 0.2) !important;
}

.hdd-storage {
    background-color: rgba(79, 70, 229, 0.1) !important;
    color: #4338ca !important;
    border: 1px solid rgba(79, 70, 229, 0.2) !important;
}

.nvme-storage {
    background-color: rgba(217, 119, 6, 0.1) !important;
    color: #b45309 !important;
    border: 1px solid rgba(217, 119, 6, 0.2) !important;
}

/* ViewerJS 自定义样式 - 移除所有自定义样式让其使用默认样式 */

/* 卡片式布局 */
.storage-card-outer-container {
    width: 100% !important;
    max-width: 100vw !important;
    box-sizing: border-box !important;
    padding: 0 4px !important;
    margin-bottom: 12px !important;
    position: relative !important;
}

.storage-card {
    width: 100% !important;
    max-width: 100% !important;
    border-radius: 12px;
    overflow: hidden;
    background-color: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    display: flex;
    flex-direction: column;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

/* 移动端优化 */
@media (max-width: 640px) {
    .mobile-stack {
        flex-direction: column;
    }

    .mobile-full-width {
        width: 100%;
    }

    .mobile-p-2 {
        padding: 0.5rem;
    }

    .mobile-text-sm {
        font-size: 0.875rem;
        line-height: 1.25rem;
    }

    .mobile-hidden {
        display: none;
    }

    .mobile-flex-col {
        flex-direction: column;
    }

    .mobile-w-full {
        width: 100%;
    }

    .mobile-mt-2 {
        margin-top: 0.5rem;
    }

    /* 页面容器修复 */
    .container {
        padding-left: 8px !important;
        padding-right: 8px !important;
    }

    /* 确保表格容器大小正确 */
    .table-container,
    .table-wrapper,
    .inline-block.min-w-full,
    .inline-block.min-w-full table {
        width: 100% !important;
        max-width: 100% !important;
        box-sizing: border-box !important;
        overflow-x: hidden !important;
    }

    /* 表格行适应屏幕宽度 */
    .table-modern.mobile-table tr {
        width: 100% !important;
        max-width: 100% !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        box-sizing: border-box !important;
    }

    /* 移动端卡片样式 */
    .storage-card {
        margin: 0 0 16px 0 !important;
        width: 100% !important;
    }

    /* 卡片头部样式 */
    .storage-card-header {
        padding: 10px 14px !important;
        border-bottom: 1px solid #eee !important;
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
    }

    /* 卡片主体样式 */
    .storage-card-body {
        padding: 12px 14px !important;
        display: flex !important;
        gap: 12px !important;
    }

    /* 卡片底部样式 */
    .storage-card-footer {
        padding: 8px 14px !important;
        border-top: 1px solid #e5e7eb !important;
        display: flex !important;
        justify-content: space-around !important;
        align-items: center !important;
        gap: 8px !important;
    }

    /* 卡片图片容器 */
    .storage-card-img-container {
        width: 60px !important;
        height: 60px !important;
        border-radius: 6px !important;
        border: 1px solid #eee !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        overflow: hidden !important;
        flex-shrink: 0 !important;
    }

    /* 卡片图片样式 */
    .storage-card-img {
        width: 100% !important;
        height: 100% !important;
        object-fit: contain !important;
        cursor: pointer !important;
    }

    /* 卡片信息容器 */
    .storage-card-info {
        flex-grow: 1 !important;
        display: flex !important;
        flex-direction: column !important;
        justify-content: center !important;
        gap: 4px !important;
    }

    /* 标签组样式 */
    .tag-group {
        display: flex !important;
        flex-wrap: wrap !important;
        gap: 6px !important;
        margin: 8px 0 !important;
    }

    /* 规格标签样式 */
    .storage-card .spec-tag {
        padding: 1px 6px !important;
        font-size: 0.7rem !important;
        border-radius: 4px !important;
        margin: 0 4px 4px 0 !important;
    }

    /* 操作按钮样式 */
    .storage-card-action-button {
        padding: 4px 8px !important;
        border-radius: 6px !important;
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
        background-color: transparent !important;
        font-size: 0.8rem !important;
        font-weight: 500 !important;
        transition: background-color 0.15s ease-out, color 0.15s ease-out !important;
    }
}

/* 暗黑模式样式 */
.dark-mode {
    --bg-primary: #121212;
    --bg-secondary: #1e1e1e;
    --bg-card: #252525;
    --bg-input: #2a2a2a;
    --text-primary: #f5f5f5;
    --text-secondary: #a0aec0;
    --border-color: #333333;
    --input-bg: #2a2a2a;
    --input-text: #e2e8f0;
    --button-primary-bg: #9333ea;
    --button-primary-hover: #7e22ce;
    --button-primary-text: #ffffff;
    --highlight-color: #9333ea;
    --accent-color: #8b5cf6;
    --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

body.dark-mode {
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.dark-mode .bg-white {
    background-color: var(--bg-secondary);
}

.dark-mode .bg-gray-50 {
    background-color: var(--bg-primary);
}

.dark-mode .bg-gray-100 {
    background-color: #1a1a1a;
}

.dark-mode .text-gray-700,
.dark-mode .text-gray-800,
.dark-mode .text-gray-900 {
    color: var(--text-primary);
}

.dark-mode .text-gray-500,
.dark-mode .text-gray-600 {
    color: var(--text-secondary);
}

.dark-mode .border-gray-200,
.dark-mode .border-gray-300 {
    border-color: var(--border-color);
}

.dark-mode input,
.dark-mode select,
.dark-mode textarea {
    background-color: var(--input-bg);
    color: var(--input-text);
    border-color: var(--border-color);
}

.dark-mode .shadow-md {
    box-shadow: var(--card-shadow);
}

.dark-mode .bg-purple-600 {
    background-color: #9333ea;
}

.dark-mode .hover\:bg-purple-700:hover {
    background-color: #7e22ce;
}

.dark-mode .text-purple-700 {
    color: #a855f7;
}

.dark-mode .text-purple-800 {
    color: #a855f7;
}

/* 表格美化 */
.dark-mode table thead {
    background-color: #1a1a1a;
}

.dark-mode table th {
    color: #94a3b8;
}

.dark-mode table tbody tr {
    border-color: #333;
    border-bottom: none;
    /* 移除表格行的底部边框 */
}

.dark-mode table tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.03);
}

/* 移除表格所有横线 */
.dark-mode .divide-y,
.dark-mode table,
.dark-mode tbody,
.dark-mode tr {
    border: none !important;
}

.dark-mode table tr:not(:last-child) {
    border-bottom: none !important;
}

.dark-mode .border-b,
.dark-mode .border-t {
    border-top: none !important;
    border-bottom: none !important;
}

/* 替代横线的视觉分隔 - 使用更微妙的行间距和悬停效果 */
.dark-mode table tbody tr {
    padding: 8px 0;
    transition: background-color 0.15s ease-in-out;
}

.dark-mode table tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

/* 表单美化 */
.dark-mode input:focus,
.dark-mode select:focus,
.dark-mode textarea:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
}

/* 按钮美化 */
.dark-mode button[type="submit"],
.dark-mode .bg-purple-600 {
    background-color: var(--button-primary-bg);
    transition: all 0.2s ease;
}

.dark-mode button[type="submit"]:hover,
.dark-mode .bg-purple-600:hover {
    background-color: var(--button-primary-hover);
    transform: translateY(-1px);
}

/* 卡片边框和阴影美化 */
.dark-mode .rounded-lg {
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.dark-mode .shadow-md {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
}

/* 模态框美化 */
.dark-mode #storageModal .bg-white {
    background-color: var(--bg-card);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

/* 标签样式调整 */
.dark-mode .spec-tag {
    opacity: 0.85;
}

/* 暗黑模式下读/写速度标签的样式增强 */
.dark-mode .spec-tag.freq {
    background-color: rgba(126, 34, 206, 0.25) !important;
    color: #c4b5fd !important;
    border: 1px solid rgba(126, 34, 206, 0.4) !important;
    font-weight: 600 !important;
    text-shadow: 0 0 1px rgba(0, 0, 0, 0.3) !important;
}

/* 暗黑模式下的特殊元素 */
.dark-mode #darkModeToggle {
    background-color: #fbbf24;
    color: #92400e;
}

.dark-mode #darkModeToggle:hover {
    background-color: #f59e0b;
}

/* 智能识别输入区域暗夜模式样式 */
.dark-mode .border-gray-200 {
    border-color: #333 !important;
}

.dark-mode .bg-purple-50 {
    background-color: rgba(147, 51, 234, 0.08) !important;
    border-color: rgba(147, 51, 234, 0.2) !important;
}

.dark-mode #smartInput {
    background-color: #2a2a2a;
    color: #e2e8f0;
    border-color: #4a5568;
}

.dark-mode #autoFillBtn {
    background-color: #9333ea;
}

.dark-mode #autoFillBtn:hover {
    background-color: #7e22ce;
}

.dark-mode .text-purple-600 {
    color: #a855f7 !important;
}

/* 暗黑模式过渡效果 */
body {
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* 重置按钮深色模式样式 */
.dark-mode #resetFilterBtn {
    background-color: #4B5563;
    color: #F3F4F6;
    border: 1px solid #6B7280;
}

.dark-mode #resetFilterBtn:hover {
    background-color: #6B7280;
    color: #FFFFFF;
}

/* 移动端卡片暗夜模式样式 */
.dark-mode .storage-card {
    background-color: var(--bg-secondary) !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3) !important;
    border: 1px solid rgba(255, 255, 255, 0.05) !important;
}

/* 卡片头部暗色样式 */
.dark-mode .storage-card-header {
    background-color: rgba(0, 0, 0, 0.2) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05) !important;
}

/* 卡片内容区域暗色样式 */
.dark-mode .storage-card-body {
    background-color: var(--bg-secondary) !important;
    color: var(--text-primary) !important;
}

/* 卡片底部暗色样式 */
.dark-mode .storage-card-footer {
    background-color: rgba(0, 0, 0, 0.1) !important;
    border-top: 1px solid rgba(255, 255, 255, 0.05) !important;
}

/* 卡片内文字颜色 */
.dark-mode .storage-card h2,
.dark-mode .storage-card h3,
.dark-mode .storage-card p,
.dark-mode .storage-card div {
    color: var(--text-primary) !important;
}

.dark-mode .storage-card .text-gray-600,
.dark-mode .storage-card .text-gray-500 {
    color: var(--text-secondary) !important;
}

/* 卡片内按钮样式 */
.dark-mode .storage-card button {
    background-color: transparent !important;
}

.dark-mode .storage-card button:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
}

/* 品牌标签暗夜模式调整 */
.dark-mode .ssd-storage {
    background-color: rgba(5, 150, 105, 0.2) !important;
    color: #34d399 !important;
    border-color: rgba(5, 150, 105, 0.3) !important;
}

.dark-mode .hdd-storage {
    background-color: rgba(79, 70, 229, 0.2) !important;
    color: #818cf8 !important;
    border-color: rgba(79, 70, 229, 0.3) !important;
}

.dark-mode .nvme-storage {
    background-color: rgba(217, 119, 6, 0.2) !important;
    color: #fbbf24 !important;
    border-color: rgba(217, 119, 6, 0.3) !important;
}

/* 针对移动端专有样式补充 */
@media (max-width: 640px) {
    .dark-mode .storage-card-outer-container {
        margin-bottom: 15px !important;
    }

    /* 移动端暗夜模式下的文本颜色调整 */
    .dark-mode .storage-card-outer-container span[style*="font-weight: bold"] {
        color: var(--text-primary) !important;
    }

    /* 移动端暗夜模式下的标签调整 */
    .dark-mode .storage-card-outer-container .tag-group span {
        background-color: rgba(75, 85, 99, 0.2) !important;
        border: 1px solid rgba(75, 85, 99, 0.3) !important;
    }

    /* 移动端暗夜模式下的按钮点击效果 */
    .dark-mode .storage-card-outer-container button:active {
        background-color: rgba(255, 255, 255, 0.15) !important;
    }

    /* 移动端图片容器暗夜模式样式 */
    .dark-mode .storage-card-img-container {
        background-color: #1a1a1a !important;
        border-color: #333 !important;
    }
}