CREATE DATABASE IF NOT EXISTS delivery_system;
USE delivery_system;

CREATE TABLE IF NOT EXISTS users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    role VARCHAR(20) DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 登录记录表
CREATE TABLE IF NOT EXISTS login_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    username VARCHAR(50) NOT NULL,
    login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(50),
    user_agent TEXT,
    login_status VARCHAR(20),
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Insert default admin user if not exists
-- 注意：这里使用哈希密码，对应的原始密码是'admin123'
INSERT INTO users (username, password, role)
SELECT 'admin', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin'
WHERE NOT EXISTS (SELECT 1 FROM users WHERE username = 'admin');

-- 上面的哈希密码对应的原始密码是'admin123'
-- 如果admin用户已存在，确保它有admin角色和正确密码
UPDATE users SET role = 'admin', password = '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi' WHERE username = 'admin';

-- 笔记分类表
CREATE TABLE IF NOT EXISTS note_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    color VARCHAR(7) DEFAULT '#3B82F6',
    icon VARCHAR(50) DEFAULT 'fas fa-folder',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 笔记表
CREATE TABLE IF NOT EXISTS notes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    content LONGTEXT,
    category_id INT,
    author_id INT NOT NULL,
    is_public BOOLEAN DEFAULT TRUE,
    is_pinned BOOLEAN DEFAULT FALSE,
    view_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES note_categories(id) ON DELETE SET NULL,
    FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE,
    FULLTEXT(title, content)
);

-- 笔记标签表
CREATE TABLE IF NOT EXISTS note_tags (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL UNIQUE,
    color VARCHAR(7) DEFAULT '#6B7280',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 笔记标签关联表
CREATE TABLE IF NOT EXISTS note_tag_relations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    note_id INT NOT NULL,
    tag_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (note_id) REFERENCES notes(id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES note_tags(id) ON DELETE CASCADE,
    UNIQUE KEY unique_note_tag (note_id, tag_id)
);

-- 笔记访问记录表（用于统计和分析）
CREATE TABLE IF NOT EXISTS note_access_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    note_id INT NOT NULL,
    user_id INT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    access_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (note_id) REFERENCES notes(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- 插入默认分类
INSERT INTO note_categories (name, description, color, icon) VALUES
('配置信息', '系统配置和设置相关的笔记', '#3B82F6', 'fas fa-cog'),
('知识库', '技术知识和经验总结', '#10B981', 'fas fa-book'),
('文档', '项目文档和说明', '#F59E0B', 'fas fa-file-alt'),
('问题解决', '问题排查和解决方案', '#EF4444', 'fas fa-tools'),
('其他', '其他类型的笔记', '#6B7280', 'fas fa-folder')
ON DUPLICATE KEY UPDATE name=VALUES(name);

-- 插入默认标签
INSERT INTO note_tags (name, color) VALUES
('重要', '#EF4444'),
('配置', '#3B82F6'),
('教程', '#10B981'),
('问题', '#F59E0B'),
('解决方案', '#8B5CF6'),
('文档', '#6B7280')
ON DUPLICATE KEY UPDATE name=VALUES(name);

CREATE TABLE IF NOT EXISTS stores (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS addresses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    address VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS delivery_records (
    id INT PRIMARY KEY AUTO_INCREMENT,
    store_id INT NOT NULL,
    address_id INT NOT NULL,
    image_url VARCHAR(255),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (store_id) REFERENCES stores(id),
    FOREIGN KEY (address_id) REFERENCES addresses(id)
);

CREATE TABLE IF NOT EXISTS parts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    record_id INT NOT NULL,
    type VARCHAR(50) NOT NULL COMMENT '配件类型',
    model VARCHAR(255) NOT NULL COMMENT '型号',
    price DECIMAL(10,2) NOT NULL COMMENT '价格',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (record_id) REFERENCES delivery_records(id) ON DELETE CASCADE
) COMMENT='配件明细表';

CREATE INDEX idx_record_id ON parts(record_id);
CREATE INDEX idx_type ON parts(type);
CREATE INDEX idx_created_at ON parts(created_at);

CREATE TABLE IF NOT EXISTS `store_feedback` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `content` TEXT,
    `image_path` VARCHAR(255),
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 价格记录主表
CREATE TABLE IF NOT EXISTS price_records (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 价格记录明细表
CREATE TABLE IF NOT EXISTS price_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    record_id INT NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    operation_type VARCHAR(50) NOT NULL,
    description TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (record_id) REFERENCES price_records(id) ON DELETE CASCADE
);

-- 原始表格数据表
CREATE TABLE IF NOT EXISTS raw_table_data (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    total_amount INT NOT NULL,
    raw_data TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 创建索引以提高查询性能
CREATE INDEX idx_price_records_user_id ON price_records(user_id);
CREATE INDEX idx_price_records_created_at ON price_records(created_at);
CREATE INDEX idx_price_items_record_id ON price_items(record_id);
CREATE INDEX idx_raw_table_data_user_id ON raw_table_data(user_id);
CREATE INDEX idx_raw_table_data_created_at ON raw_table_data(created_at);


