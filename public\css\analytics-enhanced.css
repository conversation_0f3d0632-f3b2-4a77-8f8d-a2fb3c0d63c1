:root {
    --bg-primary: #f9fafb;
    --bg-secondary: #ffffff;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --border-color: #e5e7eb;
    --card-shadow: 0 4px 15px rgba(0,0,0,0.1);
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

[data-theme="dark"] {
    --bg-primary: #111827;
    --bg-secondary: #1f2937;
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --border-color: #374151;
    --card-shadow: 0 4px 15px rgba(0,0,0,0.3);
    --gradient-primary: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
}

body {
    background-color: var(--bg-primary);
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.chart-container {
    position: relative;
    height: 400px;
    width: 100%;
}

.stat-card {
    background: var(--gradient-primary);
    color: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: var(--card-shadow);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.loading-spinner {
    border: 4px solid var(--border-color);
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.section-card {
    background: var(--bg-secondary);
    border-radius: 15px;
    padding: 20px;
    box-shadow: var(--card-shadow);
    margin-bottom: 20px;
    border: 1px solid var(--border-color);
}

.theme-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--card-shadow);
}

.theme-toggle:hover {
    transform: scale(1.1);
}

.user-badge {
    background: var(--gradient-primary);
    color: white;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.admin-badge {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.error-message {
    background: #fee2e2;
    border: 1px solid #fecaca;
    color: #dc2626;
    padding: 12px;
    border-radius: 8px;
    margin: 10px 0;
}

[data-theme="dark"] .error-message {
    background: #7f1d1d;
    border-color: #991b1b;
    color: #fca5a5;
}

.success-message {
    background: #d1fae5;
    border: 1px solid #a7f3d0;
    color: #065f46;
    padding: 12px;
    border-radius: 8px;
    margin: 10px 0;
}

[data-theme="dark"] .success-message {
    background: #064e3b;
    border-color: #047857;
    color: #6ee7b7;
}

.time-slider-container {
    margin: 20px 0;
    padding: 20px;
    background: var(--bg-secondary);
    border-radius: 10px;
    border: 1px solid var(--border-color);
}

.time-slider {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: var(--border-color);
    outline: none;
    -webkit-appearance: none;
}

.time-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #3b82f6;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.time-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #3b82f6;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.input-field {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 8px 12px;
    border-radius: 8px;
    outline: none;
    transition: all 0.3s ease;
}

.input-field:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 表格深色主题适配 */
.data-table {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
}

.data-table th {
    background: var(--bg-primary);
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-color);
}

.data-table td {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-color);
}

.data-table tr:hover td {
    background: var(--bg-primary);
}

.stat-card-small {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 16px;
    transition: all 0.3s ease;
}

.stat-card-small:hover {
    transform: translateY(-2px);
    box-shadow: var(--card-shadow);
}

.stat-card-small .stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--text-primary);
}

.stat-card-small .stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: 4px;
}

/* 用户头像适配 */
.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 500;
    font-size: 14px;
}

/* 角色标签适配 */
.role-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.role-admin {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.role-user {
    background: rgba(34, 197, 94, 0.1);
    color: #22c55e;
    border: 1px solid rgba(34, 197, 94, 0.2);
}

[data-theme="dark"] .role-admin {
    background: rgba(239, 68, 68, 0.2);
    color: #fca5a5;
}

[data-theme="dark"] .role-user {
    background: rgba(34, 197, 94, 0.2);
    color: #86efac;
}

/* 访问拒绝页面样式 */
.access-denied {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    color: white;
}

.access-denied-content {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
    max-width: 500px;
    margin: 20px;
}

.access-denied-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.access-denied-title {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 15px;
}

.access-denied-message {
    font-size: 1.1rem;
    margin-bottom: 25px;
    opacity: 0.9;
    line-height: 1.5;
}

.access-denied-countdown {
    font-size: 0.9rem;
    opacity: 0.7;
    margin-top: 20px;
}

.access-denied-buttons {
    margin-top: 25px;
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.access-denied-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.access-denied-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.access-denied-btn.primary {
    background: rgba(59, 130, 246, 0.8);
    border-color: rgba(59, 130, 246, 1);
}
