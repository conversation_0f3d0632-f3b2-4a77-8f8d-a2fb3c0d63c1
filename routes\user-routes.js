const express = require('express');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const router = express.Router();

// 导入数据库连接
const db = require('../config/db');

// 导入中间件
const { checkAdminRole, checkReadPermission } = require('../middleware/auth');

// 用户注册
router.post('/register', async (req, res) => {
    try {
        const { username, password } = req.body;

        // 验证用户名和密码
        if (!username || !password) {
            return res.status(400).json({ error: '用户名和密码不能为空' });
        }

        // 验证用户名格式
        if (username.length < 6 || username.includes(' ')) {
            return res.status(400).json({ error: '用户名至少需要6个字符且不能包含空格' });
        }

        // 验证密码格式
        if (password.length < 6) {
            return res.status(400).json({ error: '密码至少需要6个字符' });
        }

        const hasLetter = /[a-zA-Z]/.test(password);
        const hasNumber = /[0-9]/.test(password);
        
        if (!hasLetter || !hasNumber) {
            return res.status(400).json({ error: '密码必须同时包含字母和数字' });
        }

        // 检查用户名是否已存在
        const [existingUsers] = await db.query('SELECT * FROM users WHERE username = ?', [username]);
        if (existingUsers.length > 0) {
            return res.status(400).json({ error: '用户名已存在' });
        }

        // 加密密码
        const hashedPassword = await bcrypt.hash(password, 10);

        // 插入新用户
        const [result] = await db.query(
            'INSERT INTO users (username, password, role) VALUES (?, ?, ?)',
            [username, hashedPassword, 'user'] // 默认为普通用户
        );

        const userId = result.insertId;

        // 创建JWT令牌
        const token = jwt.sign(
            { id: userId, username: username, role: 'user' },
            process.env.JWT_SECRET || 'your_jwt_secret',
            { expiresIn: '8h' }
        );

        // 记录注册后的自动登录
        try {
            const ip = req.headers['x-forwarded-for'] || req.socket.remoteAddress;
            const userAgent = req.headers['user-agent'];
            await db.query(
                'INSERT INTO login_logs (user_id, username, login_time, ip_address, user_agent, login_status) VALUES (?, ?, NOW(), ?, ?, ?)',
                [userId, username, ip, userAgent, 'success_register']
            );
        } catch (logErr) {
            console.error('记录注册日志失败:', logErr);
            // 这里我们选择不中断流程，即使日志记录失败
        }

        res.status(201).json({
            message: '用户注册成功',
            userId: userId,
            token: token,
            username: username,
            user: {
                id: userId,
                username: username,
                role: 'user'
            }
        });
    } catch (error) {
        console.error('用户注册失败:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});

// 用户登录
router.post('/login', async (req, res) => {
    try {
        const { username, password } = req.body;
        
        console.log(`[Login Attempt] Received username: ${username}`); // Debug log
        console.log(`[Login Debug] Request body:`, req.body); // 添加请求体调试日志

        // 验证用户名和密码
        if (!username || !password) {
            return res.status(400).json({ error: '用户名和密码不能为空' });
        }

        // 获取客户端IP地址和UserAgent
        const ip = req.headers['x-forwarded-for'] || req.socket.remoteAddress;
        const userAgent = req.headers['user-agent'];
        let loginStatus = 'failed';
        let userId = null;

        // 查询用户
        const [users] = await db.query('SELECT * FROM users WHERE username = ?', [username]);
        if (users.length === 0) {
            console.log('[Login Failed] User not found.'); // Debug log
            
            // 记录失败的登录尝试 (用户名不存在)
            try {
                await db.query(
                    'INSERT INTO login_logs (user_id, username, ip_address, user_agent, login_status) VALUES (?, ?, ?, ?, ?)',
                    [0, username, ip, userAgent, 'failed_username']
                );
            } catch (logErr) {
                console.error('记录登录日志失败:', logErr);
                // 继续处理，不影响登录流程
            }
            
            return res.status(401).json({ error: '用户名或密码错误' });
        }

        const user = users[0];
        userId = user.id;
        
        // 验证密码
        const isPasswordValid = await bcrypt.compare(password, user.password);
        if (!isPasswordValid) {
            console.log('[Login Failed] Password does not match.'); // Debug log
            
            // 记录失败的登录尝试 (密码错误)
            try {
                await db.query(
                    'INSERT INTO login_logs (user_id, username, ip_address, user_agent, login_status) VALUES (?, ?, ?, ?, ?)',
                    [userId, username, ip, userAgent, 'failed_password']
                );
            } catch (logErr) {
                console.error('记录登录日志失败:', logErr);
                // 继续处理，不影响登录流程
            }
            
            return res.status(401).json({ error: '用户名或密码错误' });
        }

        // 创建JWT令牌
        const token = jwt.sign(
            { id: user.id, username: user.username, role: user.role },
            process.env.JWT_SECRET || 'your_jwt_secret',
            { expiresIn: '24h' }
        );

        // 记录登录日志
        try {
            await db.query(
                'INSERT INTO login_logs (user_id, username, login_time, ip_address, user_agent, login_status) VALUES (?, ?, NOW(), ?, ?, ?)',
                [user.id, user.username, ip, userAgent, 'success']
            );
        } catch (logErr) {
            console.error('记录登录日志失败:', logErr);
            // 继续处理，不影响登录流程
        }

        console.log('[Login Success] User authenticated successfully.'); // Debug log
        console.log(`[Login Success] User role: ${user.role}`); // 打印用户角色
        
        // 构建响应对象
        const response = {
            message: '登录成功',
            token,
            username: user.username,
            user: {
                id: user.id,
                username: user.username,
                role: user.role
            }
        };
        
        // 打印响应对象，用于调试
        console.log('[Login Response]', response);
        
        res.json(response);
    } catch (error) {
        console.error('用户登录失败:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});

// 获取用户信息（验证token）
router.get('/validate-token', async (req, res) => {
    try {
        const token = req.headers.authorization?.split(' ')[1];
        
        console.log('[Validate Token] 收到验证请求，token:', token ? `${token.substring(0, 10)}...` : '无token');
        
        if (!token) {
            console.log('[Validate Token] 未提供token');
            return res.status(401).json({ error: '未提供令牌' });
        }
        
        try {
            const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your_jwt_secret');
            console.log('[Validate Token] Token验证成功，用户信息:', decoded);
            
            // 从数据库获取最新的用户信息
            const [users] = await db.query('SELECT id, username, role FROM users WHERE id = ?', [decoded.id]);
            
            if (users.length === 0) {
                console.log('[Validate Token] 用户不存在:', decoded.id);
                return res.status(401).json({ error: '用户不存在' });
            }
            
            const user = users[0];
            console.log('[Validate Token] 数据库中的用户信息:', user);
            
            // 返回用户信息
            res.json({
                valid: true,
                user: {
                    id: user.id,
                    username: user.username,
                    role: user.role
                }
            });
        } catch (error) {
            console.error('[Validate Token] Token验证失败:', error);
            return res.status(401).json({ error: '令牌无效或已过期' });
        }
    } catch (error) {
        console.error('[Validate Token] 服务器错误:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});

// 获取登录日志
router.get('/login-logs', checkReadPermission, async (req, res) => {
    try {
        console.log('获取登录日志请求参数:', req.query);

        // 解析查询参数
        const limit = parseInt(req.query.limit) || 20;
        const offset = parseInt(req.query.offset) || 0;
        const username = req.query.username || '';
        const status = req.query.status || '';
        const startDate = req.query.startDate || '';
        const endDate = req.query.endDate || '';
        const search = req.query.search || '';

        // 构建查询条件
        let whereConditions = [];
        let params = [];

        // 用户名筛选
        if (username && username.trim()) {
            whereConditions.push('username LIKE ?');
            params.push(`%${username.trim()}%`);
        }

        // 登录状态筛选
        if (status && status.trim()) {
            whereConditions.push('login_status = ?');
            params.push(status.trim());
        }

        // 日期范围筛选
        if (startDate && startDate.trim()) {
            whereConditions.push('DATE(login_time) >= ?');
            params.push(startDate.trim());
        }

        if (endDate && endDate.trim()) {
            whereConditions.push('DATE(login_time) <= ?');
            params.push(endDate.trim());
        }

        // 通用搜索（兼容旧版本）
        if (search && search.trim() && !username) {
            whereConditions.push('(username LIKE ? OR ip_address LIKE ?)');
            params.push(`%${search.trim()}%`, `%${search.trim()}%`);
        }

        // 构建WHERE子句
        const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';

        console.log('构建的查询条件:', whereClause);
        console.log('查询参数:', params);

        // 获取总记录数
        const [countResult] = await db.query(`SELECT COUNT(*) as total FROM login_logs ${whereClause}`, params);
        const total = countResult[0].total;

        // 获取分页数据
        const [logs] = await db.query(
            `SELECT * FROM login_logs ${whereClause} ORDER BY login_time DESC LIMIT ? OFFSET ?`,
            [...params, limit, offset]
        );

        console.log(`查询结果: 总记录数=${total}, 当前页记录数=${logs.length}`);

        res.json({
            data: logs,
            total,
            limit,
            offset,
            pages: Math.ceil(total / limit)
        });
    } catch (error) {
        console.error('获取登录日志失败:', error);
        res.status(500).json({ error: '服务器错误', details: error.message });
    }
});

// 获取用户列表 (仅限管理员)
router.get('/users', checkAdminRole, async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const offset = (page - 1) * limit;
        const search = req.query.search || '';
        const role = req.query.role || '';
        
        // 构建查询条件
        let whereClause = '';
        let params = [];
        
        if (search) {
            whereClause = 'WHERE username LIKE ?';
            params.push(`%${search}%`);
        }
        
        if (role && role !== 'all') {
            whereClause = whereClause ? `${whereClause} AND role = ?` : 'WHERE role = ?';
            params.push(role);
        }
        
        // 获取总记录数
        const [countResult] = await db.query(`SELECT COUNT(*) as total FROM users ${whereClause}`, params);
        const total = countResult[0].total;
        
        // 获取分页数据 (不返回密码字段，移除updated_at字段)
        const [users] = await db.query(
            `SELECT id, username, role, created_at FROM users ${whereClause} ORDER BY id DESC LIMIT ? OFFSET ?`, 
            [...params, limit, offset]
        );
        
        res.json({
            data: users,
            total,
            page,
            limit,
            pages: Math.ceil(total / limit)
        });
    } catch (error) {
        console.error('获取用户列表失败:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});

// 获取单个用户 (仅限管理员)
router.get('/users/:id', checkAdminRole, async (req, res) => {
    try {
        const [users] = await db.query(
            'SELECT id, username, role, created_at FROM users WHERE id = ?', 
            [req.params.id]
        );
        
        if (users.length === 0) {
            return res.status(404).json({ error: '用户不存在' });
        }
        
        res.json(users[0]);
    } catch (error) {
        console.error('获取用户失败:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});

// 创建用户 (仅限管理员)
router.post('/users', checkAdminRole, async (req, res) => {
    try {
        const { username, password, role } = req.body;
        
        // 验证必填字段
        if (!username || !password || !role) {
            return res.status(400).json({ error: '用户名、密码和角色不能为空' });
        }
        
        // 验证用户名格式
        if (username.length < 6 || username.includes(' ')) {
            return res.status(400).json({ error: '用户名至少需要6个字符且不能包含空格' });
        }
        
        // 验证密码格式
        if (password.length < 6) {
            return res.status(400).json({ error: '密码至少需要6个字符' });
        }
        
        const hasLetter = /[a-zA-Z]/.test(password);
        const hasNumber = /[0-9]/.test(password);
        
        if (!hasLetter || !hasNumber) {
            return res.status(400).json({ error: '密码必须同时包含字母和数字' });
        }
        
        // 验证角色
        if (role !== 'admin' && role !== 'user') {
            return res.status(400).json({ error: '角色必须是 admin 或 user' });
        }
        
        // 检查用户名是否已存在
        const [existingUsers] = await db.query('SELECT * FROM users WHERE username = ?', [username]);
        if (existingUsers.length > 0) {
            return res.status(400).json({ error: '用户名已存在' });
        }
        
        // 加密密码
        const hashedPassword = await bcrypt.hash(password, 10);
        
        // 插入新用户
        const [result] = await db.query(
            'INSERT INTO users (username, password, role, created_at) VALUES (?, ?, ?, NOW())',
            [username, hashedPassword, role]
        );
        
        res.status(201).json({
            message: '用户创建成功',
            userId: result.insertId
        });
    } catch (error) {
        console.error('创建用户失败:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});

// 更新用户 (仅限管理员)
router.put('/users/:id', checkAdminRole, async (req, res) => {
    try {
        const { username, password, role } = req.body;
        const userId = req.params.id;
        
        // 检查用户是否存在
        const [existingUsers] = await db.query('SELECT * FROM users WHERE id = ?', [userId]);
        if (existingUsers.length === 0) {
            return res.status(404).json({ error: '用户不存在' });
        }
        
        // 验证用户名
        if (username) {
            if (username.length < 6 || username.includes(' ')) {
                return res.status(400).json({ error: '用户名至少需要6个字符且不能包含空格' });
            }
            
            // 检查用户名是否已被其他用户使用
            const [usernameUsers] = await db.query('SELECT * FROM users WHERE username = ? AND id != ?', [username, userId]);
            if (usernameUsers.length > 0) {
                return res.status(400).json({ error: '用户名已存在' });
            }
        }
        
        // 验证密码
        let hashedPassword = null;
        if (password) {
            if (password.length < 6) {
                return res.status(400).json({ error: '密码至少需要6个字符' });
            }
            
            const hasLetter = /[a-zA-Z]/.test(password);
            const hasNumber = /[0-9]/.test(password);
            
            if (!hasLetter || !hasNumber) {
                return res.status(400).json({ error: '密码必须同时包含字母和数字' });
            }
            
            hashedPassword = await bcrypt.hash(password, 10);
        }
        
        // 验证角色
        if (role && role !== 'admin' && role !== 'user') {
            return res.status(400).json({ error: '角色必须是 admin 或 user' });
        }
        
        // 构建更新SQL
        let updateSql = 'UPDATE users SET ';
        const updateParams = [];
        const updateFields = [];
        
        if (username) {
            updateFields.push('username = ?');
            updateParams.push(username);
        }
        
        if (hashedPassword) {
            updateFields.push('password = ?');
            updateParams.push(hashedPassword);
        }
        
        if (role) {
            updateFields.push('role = ?');
            updateParams.push(role);
        }
        
        if (updateFields.length === 0) {
            return res.status(400).json({ error: '没有提供要更新的字段' });
        }
        
        updateSql += updateFields.join(', ');
        updateSql += ' WHERE id = ?';
        updateParams.push(userId);
        
        // 执行更新
        await db.query(updateSql, updateParams);
        
        res.json({
            message: '用户更新成功'
        });
    } catch (error) {
        console.error('更新用户失败:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});

// 删除用户 (仅限管理员)
router.delete('/users/:id', checkAdminRole, async (req, res) => {
    try {
        const userId = req.params.id;
        
        // 检查用户是否存在
        const [existingUsers] = await db.query('SELECT * FROM users WHERE id = ?', [userId]);
        if (existingUsers.length === 0) {
            return res.status(404).json({ error: '用户不存在' });
        }
        
        // 不允许删除自己
        if (req.user.id === parseInt(userId)) {
            return res.status(400).json({ error: '不能删除当前登录的用户' });
        }
        
        // 执行删除
        await db.query('DELETE FROM users WHERE id = ?', [userId]);
        
        res.json({
            message: '用户删除成功'
        });
    } catch (error) {
        console.error('删除用户失败:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});

// 检查用户角色 (调试API)
router.get('/check-role', async (req, res) => {
    try {
        if (req.user) {
            res.json({
                role: req.user.role,
                isAdmin: req.user.role === 'admin'
            });
        } else {
            res.json({
                role: 'guest',
                isAdmin: false
            });
        }
    } catch (error) {
        console.error('检查用户角色失败:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});

module.exports = router; 