/**
 * 收入管理系统数据库优化脚本
 * 添加索引以提高查询性能
 */

const db = require('../config/db');

async function optimizeRevenueDatabase() {
    try {
        console.log('开始优化收入管理系统数据库...');
        
        // 检查并创建索引
        const indexes = [
            {
                name: 'idx_revenue_shop_name',
                table: 'revenue_records',
                columns: 'shop_name',
                description: '店铺名称索引，用于店铺筛选'
            },
            {
                name: 'idx_revenue_order_number',
                table: 'revenue_records',
                columns: 'order_number',
                description: '订单号唯一索引，用于快速查找和重复检查'
            },
            {
                name: 'idx_revenue_user_id',
                table: 'revenue_records',
                columns: 'user_id',
                description: '用户ID索引，用于权限控制查询'
            },
            {
                name: 'idx_revenue_created_at',
                table: 'revenue_records',
                columns: 'created_at',
                description: '创建时间索引，用于时间范围筛选'
            },
            {
                name: 'idx_revenue_updated_at',
                table: 'revenue_records',
                columns: 'updated_at',
                description: '更新时间索引，用于时间范围筛选'
            },
            {
                name: 'idx_revenue_total_revenue',
                table: 'revenue_records',
                columns: 'total_revenue',
                description: '收入金额索引，用于统计查询'
            },
            {
                name: 'idx_revenue_composite',
                table: 'revenue_records',
                columns: 'user_id, created_at',
                description: '复合索引，用于用户权限和时间筛选的组合查询'
            }
        ];
        
        for (const index of indexes) {
            try {
                // 检查索引是否已存在
                const [existingIndexes] = await db.query(`
                    SELECT COUNT(*) as count 
                    FROM information_schema.statistics 
                    WHERE table_schema = DATABASE() 
                    AND table_name = ? 
                    AND index_name = ?
                `, [index.table, index.name]);
                
                if (existingIndexes[0].count > 0) {
                    console.log(`索引 ${index.name} 已存在，跳过创建`);
                    continue;
                }
                
                // 创建索引
                const createIndexSQL = `CREATE INDEX ${index.name} ON ${index.table} (${index.columns})`;
                await db.query(createIndexSQL);
                console.log(`✓ 创建索引: ${index.name} - ${index.description}`);
                
            } catch (error) {
                console.error(`创建索引 ${index.name} 失败:`, error.message);
            }
        }
        
        // 分析表以更新统计信息
        console.log('分析表以更新统计信息...');
        await db.query('ANALYZE TABLE revenue_records');
        console.log('✓ 表分析完成');
        
        // 显示表的索引信息
        console.log('\n当前表索引信息:');
        const [indexInfo] = await db.query(`
            SELECT 
                INDEX_NAME as '索引名称',
                COLUMN_NAME as '列名',
                NON_UNIQUE as '非唯一',
                CARDINALITY as '基数'
            FROM information_schema.statistics 
            WHERE table_schema = DATABASE() 
            AND table_name = 'revenue_records'
            ORDER BY INDEX_NAME, SEQ_IN_INDEX
        `);
        
        console.table(indexInfo);
        
        // 显示表大小信息
        const [tableInfo] = await db.query(`
            SELECT 
                table_name as '表名',
                table_rows as '行数',
                ROUND(data_length / 1024 / 1024, 2) as '数据大小(MB)',
                ROUND(index_length / 1024 / 1024, 2) as '索引大小(MB)',
                ROUND((data_length + index_length) / 1024 / 1024, 2) as '总大小(MB)'
            FROM information_schema.tables 
            WHERE table_schema = DATABASE() 
            AND table_name = 'revenue_records'
        `);
        
        console.log('\n表大小信息:');
        console.table(tableInfo);
        
        console.log('\n数据库优化完成！');
        
    } catch (error) {
        console.error('数据库优化失败:', error);
        throw error;
    }
}

// 如果直接运行此文件，则执行优化
if (require.main === module) {
    optimizeRevenueDatabase()
        .then(() => {
            console.log('数据库优化成功完成');
            process.exit(0);
        })
        .catch(error => {
            console.error('数据库优化失败:', error);
            process.exit(1);
        });
}

module.exports = { optimizeRevenueDatabase };
