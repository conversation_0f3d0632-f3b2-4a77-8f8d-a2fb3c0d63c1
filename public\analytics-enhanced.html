<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据分析 - 价格计算器系统</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/analytics-enhanced.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-zoom"></script>
</head>
<body>
    <!-- 主题切换按钮 -->
    <button class="theme-toggle" id="themeToggle" title="切换主题">
        <i class="fas fa-moon" id="themeIcon"></i>
    </button>

    <div class="container mx-auto px-4 py-8">
        <!-- 页面头部 -->
        <header class="mb-8">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold flex items-center">
                        <i class="fas fa-chart-bar mr-3 text-blue-500"></i>
                        数据分析中心
                    </h1>
                    <p class="text-gray-500 mt-2">价格计算器系统数据统计与分析</p>
                </div>
                <div class="flex items-center space-x-4">
                    <!-- 用户信息显示 -->
                    <div id="userInfo" class="hidden">
                        <span class="user-badge" id="userBadge">
                            <i class="fas fa-user mr-1"></i>
                            <span id="userName">加载中...</span>
                        </span>
                    </div>
                    <!-- 返回按钮 -->
                    <a href="/price.html" class="btn-primary">
                        <i class="fas fa-arrow-left mr-2"></i>返回价格计算器
                    </a>
                </div>
            </div>
        </header>

        <!-- 筛选控制区域 -->
        <div class="section-card">
            <h3 class="text-lg font-semibold mb-4">
                <i class="fas fa-filter mr-2 text-blue-500"></i>数据筛选
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- 时间范围选择 -->
                <div>
                    <label for="timeRange" class="block text-sm font-medium mb-2">时间范围</label>
                    <select id="timeRange" class="input-field w-full">
                        <option value="7d">最近7天</option>
                        <option value="30d" selected>最近30天</option>
                        <option value="90d">最近90天</option>
                        <option value="all">全部时间</option>
                    </select>
                </div>

                <!-- 用户选择 (仅管理员可见) -->
                <div id="userSelectContainer" class="hidden">
                    <label for="userSelect" class="block text-sm font-medium mb-2">用户选择</label>
                    <select id="userSelect" class="input-field w-full">
                        <option value="">当前用户</option>
                    </select>
                </div>

                <!-- 刷新按钮 -->
                <div class="flex items-end">
                    <button id="refreshBtn" class="btn-primary">
                        <i class="fas fa-sync-alt mr-2"></i>刷新数据
                    </button>
                </div>

                <!-- 清除缓存按钮 (仅管理员可见) -->
                <div id="adminControls" class="hidden flex items-end">
                    <button id="clearCacheBtn" class="btn-primary" style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);">
                        <i class="fas fa-trash mr-2"></i>清除缓存
                    </button>
                </div>
            </div>

            <!-- 时间滑轨控制 -->
            <div class="time-slider-container">
                <div class="flex justify-between items-center mb-4">
                    <h4 class="text-md font-medium">时间范围滑轨</h4>
                    <div class="flex items-center space-x-4">
                        <span id="startDate" class="text-sm text-gray-500"></span>
                        <span class="text-sm text-gray-500">至</span>
                        <span id="endDate" class="text-sm text-gray-500"></span>
                    </div>
                </div>
                <input type="range" id="timeSlider" class="time-slider" min="0" max="90" value="30" step="1">
                <div class="flex justify-between text-xs text-gray-500 mt-2">
                    <span>今天</span>
                    <span>30天前</span>
                    <span>90天前</span>
                </div>
            </div>
        </div>

        <!-- 统计卡片区域 -->
        <div id="statsCards" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
            <!-- 统计卡片将通过JavaScript动态生成 -->
        </div>

        <!-- 图表区域 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 活跃度趋势图 -->
            <div class="section-card">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold">
                        <i class="fas fa-chart-line mr-2 text-blue-500"></i>活跃度趋势
                    </h3>
                    <div class="flex items-center space-x-2">
                        <button id="refreshChart" class="btn-primary text-xs px-2 py-1" title="刷新图表">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                        <button id="toggleAnimation" class="btn-primary text-xs px-2 py-1" title="禁用动画">
                            <i class="fas fa-pause"></i>
                        </button>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="activityTrendChart"></canvas>
                </div>
            </div>

            <!-- 操作类型分布 -->
            <div class="section-card">
                <h3 class="text-lg font-semibold mb-4">
                    <i class="fas fa-chart-pie mr-2 text-purple-500"></i>操作类型分布
                </h3>
                <div class="chart-container">
                    <canvas id="operationTypeChart"></canvas>
                </div>
            </div>

            <!-- 金额分布图 -->
            <div class="section-card">
                <h3 class="text-lg font-semibold mb-4">
                    <i class="fas fa-chart-bar mr-2 text-green-500"></i>金额分布
                </h3>
                <div class="chart-container">
                    <canvas id="amountDistributionChart"></canvas>
                </div>
            </div>

            <!-- 时间分布图 -->
            <div class="section-card">
                <h3 class="text-lg font-semibold mb-4">
                    <i class="fas fa-clock mr-2 text-orange-500"></i>操作时间分布
                </h3>
                <div class="chart-container">
                    <canvas id="timeDistributionChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 管理员专用区域 -->
        <div id="adminSection" class="hidden mt-8">
            <div class="section-card">
                <h3 class="text-lg font-semibold mb-4">
                    <i class="fas fa-users mr-2 text-red-500"></i>用户概览 (管理员专用)
                </h3>
                <div id="userOverviewTable" class="overflow-x-auto">
                    <!-- 用户概览表格将通过JavaScript生成 -->
                </div>
            </div>
        </div>

        <!-- 加载指示器 -->
        <div id="loadingIndicator" class="hidden text-center py-8">
            <div class="loading-spinner"></div>
            <p class="mt-4">正在加载数据...</p>
        </div>

        <!-- 错误消息区域 -->
        <div id="errorContainer"></div>
    </div>
    <script src="/js/analytics-enhanced.js"></script>

</body>
</html>