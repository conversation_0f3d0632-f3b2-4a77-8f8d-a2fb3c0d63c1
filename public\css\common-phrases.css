 /* 暗色模式样式 */
 .dark {
     color-scheme: dark;
 }

 .dark body {
     background-color: #1a1a1a;
     color: #e0e0e0;
 }

 .dark .bg-white {
     background-color: #2a2a2a;
 }

 .dark .text-gray-700 {
     color: #d0d0d0;
 }

 .dark .text-gray-600 {
     color: #b0b0b0;
 }

 .dark .text-gray-800 {
     color: #e0e0e0;
 }

 .dark .border-gray-300 {
     border-color: #444;
 }

 .dark .shadow-md {
     box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
 }

 .dark .bg-gray-50 {
     background-color: #1a1a1a;
 }

 .dark .bg-gray-100 {
     background-color: #2a2a2a;
 }

 /* 动画效果 */
 .fade-in {
     animation: fadeIn 0.3s ease-in-out;
 }

 @keyframes fadeIn {
     from {
         opacity: 0;
         transform: translateY(10px);
     }

     to {
         opacity: 1;
         transform: translateY(0);
     }
 }

 /* 允许文本选择复制 */
 .text-sm.text-gray-900.dark\:text-gray-100.whitespace-pre-wrap,
 .selectable-text {
     user-select: text !important;
     -webkit-user-select: text !important;
     -moz-user-select: text !important;
     -ms-user-select: text !important;
     cursor: text;
 }

 /* 增加话术文本区域的可见度，使其更容易选择 */
 .text-gray-900.dark\:text-gray-100.whitespace-pre-wrap,
 .selectable-text {
     padding: 6px;
     border-radius: 4px;
     position: relative;
     z-index: 10;
 }

 /* 鼠标悬停时的样式提示 */
 .text-gray-900.dark\:text-gray-100.whitespace-pre-wrap:hover,
 .selectable-text:hover {
     background-color: rgba(0, 0, 0, 0.03);
 }

 .dark .text-gray-900.dark\:text-gray-100.whitespace-pre-wrap:hover,
 .dark .selectable-text:hover {
     background-color: rgba(255, 255, 255, 0.03);
 }

 /* 移动端优化 */
 @media (max-width: 640px) {
     .action-buttons button {
         width: 36px;
         height: 36px;
         display: flex;
         align-items: center;
         justify-content: center;
         margin-bottom: 4px;
     }

     .phrase-card {
         padding: 10px;
     }

     .text-sm.text-gray-900.dark\:text-gray-100.whitespace-pre-wrap,
     .selectable-text {
         padding: 8px;
         font-size: 14px;
         line-height: 1.5;
     }

     /* 增大触摸区域 */
     .action-buttons button,
     .action-buttons a {
         min-height: 44px;
         min-width: 44px;
         display: flex;
         align-items: center;
         justify-content: center;
     }

     /* 调整图片预览大小 */
     .max-h-32 {
         max-height: 120px;
     }

     /* 修复移动端上的长按选择问题 */
     .selectable-text {
         -webkit-touch-callout: default;
         touch-action: auto;
     }
 }