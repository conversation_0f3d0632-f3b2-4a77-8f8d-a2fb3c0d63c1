/**
 * 拼音搜索工具包装器
 * 为js-pinyin库提供更简单的接口
 */

// 创建全局拼音实例
const pinyinInstance = new Pinyin();

// 定义全局PinyinHelper对象
const PinyinHelper = {
    /**
     * 获取字符串的完整拼音
     * @param {string} str 中文字符串
     * @return {string} 拼音字符串
     */
    getFullPinyin: function(str) {
        return pinyinInstance.getFullChars(str);
    },
    
    /**
     * 获取字符串的拼音首字母
     * @param {string} str 中文字符串
     * @return {string} 拼音首字母
     */
    getPinyinInitials: function(str) {
        return pinyinInstance.getCamelChars(str);
    }
};
