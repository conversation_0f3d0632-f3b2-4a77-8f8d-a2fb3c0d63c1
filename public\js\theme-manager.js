/**
 * 主题管理器 - 统一的主题切换系统
 * 支持亮色/深色主题切换、状态持久化、平滑动画
 */

class ThemeManager {
    constructor() {
        this.themes = {
            light: {
                name: 'light',
                displayName: '浅色主题',
                icon: 'fas fa-sun',
                colors: {
                    primary: '#3B82F6',
                    secondary: '#6B7280',
                    success: '#10B981',
                    warning: '#F59E0B',
                    error: '#EF4444',
                    background: '#FFFFFF',
                    surface: '#F9FAFB',
                    text: '#111827'
                }
            },
            dark: {
                name: 'dark',
                displayName: '深色主题',
                icon: 'fas fa-moon',
                colors: {
                    primary: '#60A5FA',
                    secondary: '#9CA3AF',
                    success: '#34D399',
                    warning: '#FBBF24',
                    error: '#F87171',
                    background: '#111827',
                    surface: '#1F2937',
                    text: '#F9FAFB'
                }
            }
        };
        
        this.currentTheme = 'light';
        this.transitionDuration = 300; // ms
        this.storageKey = 'preferred-theme';
        
        this.init();
    }
    
    /**
     * 初始化主题管理器
     */
    init() {
        console.log('初始化主题管理器...');

        // 加载保存的主题偏好
        this.loadThemePreference();

        // 监听系统主题变化
        this.watchSystemTheme();

        // 设置初始主题
        this.applyTheme(this.currentTheme, false);

        // 设置主题切换按钮（延迟执行以确保DOM已加载）
        setTimeout(() => {
            this.setupThemeToggle();
        }, 100);

        // 监听页面可见性变化
        this.handleVisibilityChange();

        console.log('主题管理器初始化完成，当前主题:', this.currentTheme);
    }
    
    /**
     * 加载主题偏好设置
     */
    loadThemePreference() {
        // 1. 优先使用用户保存的偏好
        const savedTheme = localStorage.getItem(this.storageKey);
        if (savedTheme && this.themes[savedTheme]) {
            this.currentTheme = savedTheme;
            return;
        }
        
        // 2. 其次使用系统偏好
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            this.currentTheme = 'dark';
        } else {
            this.currentTheme = 'light';
        }
        
        // 3. 保存检测到的偏好
        this.saveThemePreference();
    }
    
    /**
     * 保存主题偏好设置
     */
    saveThemePreference() {
        localStorage.setItem(this.storageKey, this.currentTheme);
        
        // 同时保存到sessionStorage以便跨标签页同步
        sessionStorage.setItem(this.storageKey, this.currentTheme);
    }
    
    /**
     * 监听系统主题变化
     */
    watchSystemTheme() {
        if (!window.matchMedia) return;
        
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        
        mediaQuery.addEventListener('change', (e) => {
            // 只有在用户没有手动设置主题时才跟随系统
            if (!localStorage.getItem(this.storageKey)) {
                const newTheme = e.matches ? 'dark' : 'light';
                this.setTheme(newTheme);
            }
        });
    }
    
    /**
     * 设置主题切换按钮
     */
    setupThemeToggle() {
        const toggleButtons = document.querySelectorAll('[data-theme-toggle]');

        console.log('找到主题切换按钮数量:', toggleButtons.length);

        if (toggleButtons.length === 0) {
            console.warn('未找到主题切换按钮，将在500ms后重试');
            setTimeout(() => {
                this.setupThemeToggle();
            }, 500);
            return;
        }

        toggleButtons.forEach((button, index) => {
            console.log(`设置第${index + 1}个主题切换按钮`);

            // 创建绑定的事件处理函数
            const handleClick = (e) => {
                e.preventDefault();
                console.log('主题切换按钮被点击');
                this.toggleTheme();
            };

            // 移除可能存在的旧事件监听器
            if (button._themeToggleHandler) {
                button.removeEventListener('click', button._themeToggleHandler);
            }

            // 保存事件处理函数引用并添加监听器
            button._themeToggleHandler = handleClick;
            button.addEventListener('click', handleClick);

            // 设置初始图标
            this.updateToggleButton(button);
        });

        console.log('主题切换按钮设置完成');
    }
    
    /**
     * 更新主题切换按钮
     */
    updateToggleButton(button) {
        const currentThemeConfig = this.themes[this.currentTheme];
        const nextTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        const nextThemeConfig = this.themes[nextTheme];
        
        // 更新图标
        button.innerHTML = `<i class="${nextThemeConfig.icon}"></i>`;
        
        // 更新提示文本
        button.title = `切换到${nextThemeConfig.displayName}`;
        button.setAttribute('aria-label', `切换到${nextThemeConfig.displayName}`);
        
        // 添加动画类
        button.classList.add('theme-toggle-btn');
    }
    
    /**
     * 应用主题
     */
    applyTheme(themeName, animate = true) {
        if (!this.themes[themeName]) {
            console.warn('未知主题:', themeName);
            return;
        }
        
        const html = document.documentElement;
        const body = document.body;
        
        // 添加过渡动画
        if (animate) {
            this.addTransition();
        }
        
        // 移除所有主题类
        Object.keys(this.themes).forEach(theme => {
            html.classList.remove(theme);
        });
        
        // 添加新主题类
        html.classList.add(themeName);
        
        // 设置CSS自定义属性
        const themeConfig = this.themes[themeName];
        Object.entries(themeConfig.colors).forEach(([key, value]) => {
            html.style.setProperty(`--color-${key}`, value);
        });
        
        // 更新meta标签
        this.updateMetaTheme(themeConfig);
        
        // 更新所有切换按钮
        document.querySelectorAll('[data-theme-toggle]').forEach(button => {
            this.updateToggleButton(button);
        });
        
        // 触发主题变化事件
        this.dispatchThemeChangeEvent(themeName);
        
        // 移除过渡动画
        if (animate) {
            setTimeout(() => {
                this.removeTransition();
            }, this.transitionDuration);
        }
        
        console.log('主题已应用:', themeName);
    }
    
    /**
     * 添加过渡动画
     */
    addTransition() {
        const style = document.createElement('style');
        style.id = 'theme-transition';
        style.textContent = `
            *, *::before, *::after {
                transition: background-color ${this.transitionDuration}ms ease,
                           color ${this.transitionDuration}ms ease,
                           border-color ${this.transitionDuration}ms ease,
                           box-shadow ${this.transitionDuration}ms ease !important;
            }
        `;
        document.head.appendChild(style);
    }
    
    /**
     * 移除过渡动画
     */
    removeTransition() {
        const style = document.getElementById('theme-transition');
        if (style) {
            style.remove();
        }
    }
    
    /**
     * 更新meta主题标签
     */
    updateMetaTheme(themeConfig) {
        // 更新theme-color meta标签
        let themeColorMeta = document.querySelector('meta[name="theme-color"]');
        if (!themeColorMeta) {
            themeColorMeta = document.createElement('meta');
            themeColorMeta.name = 'theme-color';
            document.head.appendChild(themeColorMeta);
        }
        themeColorMeta.content = themeConfig.colors.primary;
        
        // 更新msapplication-navbutton-color meta标签
        let navButtonMeta = document.querySelector('meta[name="msapplication-navbutton-color"]');
        if (!navButtonMeta) {
            navButtonMeta = document.createElement('meta');
            navButtonMeta.name = 'msapplication-navbutton-color';
            document.head.appendChild(navButtonMeta);
        }
        navButtonMeta.content = themeConfig.colors.primary;
    }
    
    /**
     * 触发主题变化事件
     */
    dispatchThemeChangeEvent(themeName) {
        const event = new CustomEvent('themechange', {
            detail: {
                theme: themeName,
                themeConfig: this.themes[themeName]
            }
        });
        
        document.dispatchEvent(event);
        window.dispatchEvent(event);
    }
    
    /**
     * 切换主题
     */
    toggleTheme() {
        console.log('切换主题，当前主题:', this.currentTheme);
        const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        console.log('切换到新主题:', newTheme);

        this.setTheme(newTheme);

        // 显示切换提示
        const themeConfig = this.themes[newTheme];
        this.showThemeChangeNotification(`已切换到${themeConfig.displayName}`, newTheme);
    }
    
    /**
     * 设置主题
     */
    setTheme(themeName) {
        console.log('设置主题:', themeName, '当前主题:', this.currentTheme);

        if (!this.themes[themeName]) {
            console.error('未知主题:', themeName);
            return;
        }

        if (themeName === this.currentTheme) {
            console.log('主题未改变，跳过设置');
            return;
        }

        this.currentTheme = themeName;
        this.saveThemePreference();
        this.applyTheme(themeName);

        console.log('主题设置完成:', themeName);
    }
    
    /**
     * 获取当前主题
     */
    getCurrentTheme() {
        return this.currentTheme;
    }
    
    /**
     * 获取主题配置
     */
    getThemeConfig(themeName = null) {
        const theme = themeName || this.currentTheme;
        return this.themes[theme];
    }
    
    /**
     * 显示主题切换通知
     */
    showThemeChangeNotification(message, themeName = null) {
        // 如果传入的是消息而不是主题名，使用当前主题
        const actualThemeName = themeName || this.currentTheme;
        const themeConfig = this.themes[actualThemeName];

        // 如果themeConfig不存在，使用默认配置
        if (!themeConfig) {
            console.warn('Invalid theme name:', actualThemeName);
            return;
        }
        
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `
            fixed top-4 right-4 z-50 px-4 py-3 rounded-lg shadow-lg
            bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700
            transform translate-x-full transition-transform duration-300
            flex items-center space-x-3
        `;
        
        notification.innerHTML = `
            <i class="${themeConfig.icon} text-blue-600 dark:text-blue-400"></i>
            <span class="text-gray-900 dark:text-gray-100">${message}</span>
            <button onclick="this.parentElement.remove()" class="text-gray-500 hover:text-gray-700 dark:hover:text-gray-300">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        document.body.appendChild(notification);
        
        // 显示动画
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);
        
        // 自动隐藏
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.parentElement.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
    
    /**
     * 处理页面可见性变化
     */
    handleVisibilityChange() {
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                // 页面重新可见时，检查是否有其他标签页更改了主题
                const currentSavedTheme = localStorage.getItem(this.storageKey);
                if (currentSavedTheme && currentSavedTheme !== this.currentTheme) {
                    this.setTheme(currentSavedTheme);
                }
            }
        });
    }
    
    /**
     * 重置主题到系统默认
     */
    resetToSystemTheme() {
        localStorage.removeItem(this.storageKey);
        sessionStorage.removeItem(this.storageKey);
        
        const systemTheme = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
        this.setTheme(systemTheme);
        
        this.showThemeChangeNotification(systemTheme);
    }
}

// 创建全局主题管理器实例
window.themeManager = new ThemeManager();

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ThemeManager;
}
