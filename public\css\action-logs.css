/* 操作日志页面样式 */
*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}
/* 基础变量 - 默认主题 */
:root {
    --primary-color: #3b82f6;
    --primary-hover: #2563eb;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    --border-radius: 0.5rem;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --transition: all 0.2s ease-in-out;
}

/* 蓝色主题 */
[data-theme="blue"] {
    --primary-color: #1e40af;
    --primary-hover: #1d4ed8;
    --success-color: #0891b2;
    --info-color: #0284c7;
    --gray-50: #eff6ff;
    --gray-100: #dbeafe;
    --gray-200: #bfdbfe;
    --gray-300: #93c5fd;
    --shadow-sm: 0 1px 2px 0 rgb(30 64 175 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(30 64 175 / 0.1), 0 2px 4px -2px rgb(30 64 175 / 0.1);
}

/* 绿色主题 */
[data-theme="green"] {
    --primary-color: #059669;
    --primary-hover: #047857;
    --success-color: #10b981;
    --info-color: #14b8a6;
    --gray-50: #ecfdf5;
    --gray-100: #d1fae5;
    --gray-200: #a7f3d0;
    --gray-300: #6ee7b7;
    --shadow-sm: 0 1px 2px 0 rgb(5 150 105 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(5 150 105 / 0.1), 0 2px 4px -2px rgb(5 150 105 / 0.1);
}

/* 紫色主题 */
[data-theme="purple"] {
    --primary-color: #7c3aed;
    --primary-hover: #6d28d9;
    --success-color: #8b5cf6;
    --info-color: #a855f7;
    --gray-50: #faf5ff;
    --gray-100: #f3e8ff;
    --gray-200: #e9d5ff;
    --gray-300: #d8b4fe;
    --shadow-sm: 0 1px 2px 0 rgb(124 58 237 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(124 58 237 / 0.1), 0 2px 4px -2px rgb(124 58 237 / 0.1);
}

/* 暗色主题 */
[data-theme="dark"] {
    --primary-color: #60a5fa;
    --primary-hover: #3b82f6;
    --success-color: #34d399;
    --warning-color: #fbbf24;
    --danger-color: #f87171;
    --info-color: #38bdf8;
    --gray-50: #0f172a;
    --gray-100: #1e293b;
    --gray-200: #334155;
    --gray-300: #475569;
    --gray-400: #64748b;
    --gray-500: #94a3b8;
    --gray-600: #cbd5e1;
    --gray-700: #e2e8f0;
    --gray-800: #f1f5f9;
    --gray-900: #ffffff;
    --border-radius: 0.5rem;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.5);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.6), 0 2px 4px -2px rgb(0 0 0 / 0.5);
    --transition: all 0.2s ease-in-out;
}

/* 暗色主题下的页面容器 */
[data-theme="dark"] .action-logs-container {
    background: linear-gradient(135deg, var(--gray-50) 0%, #0c1220 100%);
}

/* 暗色主题下的页面头部 */
[data-theme="dark"] .page-header {
    background: rgba(30, 41, 59, 0.8);
    border-bottom-color: var(--gray-200);
    backdrop-filter: blur(10px);
}

/* 暗色主题下的卡片容器 */
[data-theme="dark"] .filters-section,
[data-theme="dark"] .table-container {
    background: rgba(30, 41, 59, 0.6);
    border-color: var(--gray-200);
    backdrop-filter: blur(10px);
}

/* 暗色主题下的表格头部 */
[data-theme="dark"] .logs-table th {
    background: var(--gray-100);
    color: var(--gray-700);
}

/* 暗色主题下的表格行悬停效果 */
[data-theme="dark"] .logs-table tbody tr:hover {
    background: rgba(51, 65, 85, 0.3);
}

/* 暗色主题下的分页 */
[data-theme="dark"] .pagination {
    background: rgba(30, 41, 59, 0.6);
    border-top-color: var(--gray-200);
}

/* 移除暗色主题 */

/* 页面容器 */
.action-logs-container {
    min-height: 100vh;
    background-color: var(--gray-50);
    transition: var(--transition);
    position: relative;
    overflow-x: hidden;
}

/* 移除暗黑背景效果 */

/* 移除动态背景效果 */

/* 页面头部 */
.page-header {
    background: white;
    border-bottom: 1px solid var(--gray-200);
    padding: 1.5rem 0;
    margin-bottom: 2rem;
    transition: var(--transition);
    position: relative;
    z-index: 10;
}

/* 移除暗色主题页面头部样式 */

.page-title {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--gray-900);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.page-title i {
    color: var(--primary-color);
}

/* 筛选器区域 */
.filters-section {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    transition: var(--transition);
    position: relative;
    z-index: 10;
}

/* 移除暗色主题筛选器样式 */

.filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
}

.filter-input,
.filter-select {
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    background: white;
    color: var(--gray-900);
    font-size: 0.875rem;
    transition: var(--transition);
}

.filter-input:focus,
.filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

/* 移除暗色主题输入框样式 */

/* 按钮样式 */
.btn {
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-hover);
}

/* 移除暗色主题按钮样式 */

.btn-secondary {
    background: var(--gray-200);
    color: var(--gray-700);
}

.btn-secondary:hover {
    background: var(--gray-300);
}

/* 移除暗色主题次要按钮样式 */

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background: #dc2626;
}

/* 移除暗色主题危险按钮样式 */

/* 操作按钮组 */
.action-buttons {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

/* 表格样式 */
.table-container {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    transition: var(--transition);
    position: relative;
    z-index: 10;
}

/* 移除暗色主题表格容器样式 */

.logs-table {
    width: 100%;
    border-collapse: collapse;
}

.logs-table th {
    background: var(--gray-50);
    padding: 0.75rem;
    text-align: left;
    font-weight: 600;
    color: var(--gray-700);
    border-bottom: 1px solid var(--gray-200);
    font-size: 0.875rem;
}

/* 移除暗色主题表格头部样式 */

.logs-table td {
    padding: 0.75rem;
    border-bottom: 1px solid var(--gray-200);
    color: var(--gray-900);
    font-size: 0.875rem;
}

.logs-table tbody tr:hover {
    background: var(--gray-50);
}

/* 移除暗色主题表格行样式 */

/* 操作类型标签 */
.action-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.action-badge.create {
    background: rgb(16 185 129 / 0.1);
    color: var(--success-color);
}

.action-badge.read {
    background: rgb(6 182 212 / 0.1);
    color: var(--info-color);
}

.action-badge.update {
    background: rgb(245 158 11 / 0.1);
    color: var(--warning-color);
}

.action-badge.delete {
    background: rgb(239 68 68 / 0.1);
    color: var(--danger-color);
}

/* 详情展开 */
.details-toggle {
    background: none;
    border: none;
    color: var(--primary-color);
    cursor: pointer;
    font-size: 0.75rem;
    padding: 0.25rem;
    border-radius: 0.25rem;
    transition: var(--transition);
}

.details-toggle:hover {
    background: rgb(59 130 246 / 0.1);
}

.details-content {
    background: var(--gray-50);
    padding: 0.75rem;
    margin-top: 0.5rem;
    border-radius: 0.25rem;
    font-family: monospace;
    font-size: 0.75rem;
    color: var(--gray-600);
    white-space: pre-wrap;
    max-height: 200px;
    overflow-y: auto;
}

/* 暗色主题下的详情内容 */
[data-theme="dark"] .details-content {
    background: var(--gray-100);
    color: var(--gray-500);
}

/* 暗色主题下的模态框 */
[data-theme="dark"] .modal-content {
    background: var(--gray-50);
    color: var(--gray-900);
}

[data-theme="dark"] .modal-content h3 {
    color: var(--gray-900);
}

[data-theme="dark"] .modal-content p {
    color: var(--gray-600);
}

/* 暗色主题下的表单元素 */
[data-theme="dark"] .form-group label {
    color: var(--gray-700);
}

[data-theme="dark"] .form-group input {
    background: var(--gray-100);
    border-color: var(--gray-200);
    color: var(--gray-900);
}

[data-theme="dark"] .form-group input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(96 165 250 / 0.1);
}

/* 暗色主题下的筛选器输入框 */
[data-theme="dark"] .filter-input,
[data-theme="dark"] .filter-select {
    background: var(--gray-100);
    border-color: var(--gray-200);
    color: var(--gray-900);
}

[data-theme="dark"] .filter-input:focus,
[data-theme="dark"] .filter-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(96 165 250 / 0.1);
}

/* 暗色主题下的按钮样式调整 */
[data-theme="dark"] .btn-secondary {
    background: var(--gray-200);
    color: var(--gray-800);
}

[data-theme="dark"] .btn-secondary:hover {
    background: var(--gray-300);
    color: var(--gray-900);
}

/* 暗色主题下的通知样式 */
[data-theme="dark"] .notification {
    backdrop-filter: blur(10px);
}

[data-theme="dark"] .notification.success {
    background: var(--success-color);
}

[data-theme="dark"] .notification.error {
    background: var(--danger-color);
}

[data-theme="dark"] .notification.info {
    background: var(--info-color);
}

/* 分页 */
.pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: white;
    border-top: 1px solid var(--gray-200);
    transition: var(--transition);
    position: relative;
    z-index: 10;
}

/* 移除暗色主题分页样式 */

.pagination-info {
    color: var(--gray-600);
    font-size: 0.875rem;
}

.pagination-controls {
    display: flex;
    gap: 0.5rem;
}

/* 加载状态 */
.loading {
    text-align: center;
    padding: 2rem;
    color: var(--gray-500);
}

.loading i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--gray-500);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--gray-400);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .filters-grid {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .table-container {
        overflow-x: auto;
    }
    
    .logs-table {
        min-width: 800px;
    }
    
    .pagination {
        flex-direction: column;
        gap: 1rem;
    }
}

/* 主题切换按钮 */
.theme-toggle {
    position: relative;
    overflow: hidden;
    min-width: 120px;
    justify-content: center;
}

.theme-toggle i {
    transition: var(--transition);
    margin-right: 0.5rem;
}

.theme-toggle:hover i {
    transform: scale(1.1) rotate(15deg);
}

.theme-toggle:active {
    transform: scale(0.95);
}

/* 主题切换动画 */
.theme-transition {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease !important;
}

.theme-transition *,
.theme-transition *::before,
.theme-transition *::after {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease !important;
}

/* 主题切换时的闪烁效果 */
@keyframes themeSwitch {
    0% { opacity: 1; }
    50% { opacity: 0.8; }
    100% { opacity: 1; }
}

.theme-switching {
    animation: themeSwitch 0.3s ease-in-out;
}

/* 主题状态指示器 */
.theme-indicator {
    position: fixed;
    bottom: 1rem;
    right: 1rem;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
    color: var(--gray-600);
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-sm);
    z-index: 1001;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    opacity: 0;
    transform: translateY(20px);
    transition: var(--transition);
    pointer-events: none;
}

/* 暗色主题下的指示器 */
[data-theme="dark"] .theme-indicator {
    background: rgba(31, 41, 55, 0.9);
    border-color: var(--gray-200);
    color: var(--gray-600);
}

.theme-indicator.show {
    opacity: 1;
    transform: translateY(0);
}

.theme-indicator i {
    color: var(--primary-color);
}

/* 通知样式 */
.notification {
    position: fixed;
    top: 1rem;
    right: 5rem;
    z-index: 1002;
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    color: white;
    font-size: 0.875rem;
    box-shadow: var(--shadow-md);
    transform: translateX(100%);
    transition: transform 0.3s ease-in-out;
    backdrop-filter: blur(10px);
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    background: var(--success-color);
}

.notification.error {
    background: var(--danger-color);
}

.notification.info {
    background: var(--info-color);
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    max-width: 400px;
    width: 90%;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
}

.modal-content h3 {
    margin: 0 0 1rem 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
}

.modal-content p {
    margin: 0 0 1.5rem 0;
    color: var(--gray-600);
}

/* 移除暗色主题模态框样式 */

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--gray-700);
}

/* 移除暗色主题表单标签样式 */

.form-group input {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    background: white;
    color: var(--gray-900);
    transition: var(--transition);
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

/* 移除暗色主题表单输入框样式 */

.modal-actions {
    display: flex;
    gap: 0.75rem;
    justify-content: flex-end;
}

/* 容器样式 */
.container {
    max-width: 1200px;
    margin: 0 auto;
}

.mx-auto {
    margin-left: auto;
    margin-right: auto;
}

.px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
}

/* Flex 工具类 */
.flex {
    display: flex;
}

.items-center {
    align-items: center;
}

.justify-between {
    justify-content: space-between;
}

.gap-4 {
    gap: 1rem;
}

/* 文本工具类 */
.text-sm {
    font-size: 0.875rem;
}

.text-lg {
    font-size: 1.125rem;
}

.font-semibold {
    font-weight: 600;
}

.font-mono {
    font-family: monospace;
}

.text-gray-600 {
    color: var(--gray-600);
}

.text-gray-800 {
    color: var(--gray-800);
}

.mb-4 {
    margin-bottom: 1rem;
}

/* 网格工具类 */
.grid {
    display: grid;
}

.grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
}

@media (min-width: 768px) {
    .md\\:grid-cols-4 {
        grid-template-columns: repeat(4, minmax(0, 1fr));
    }
}
