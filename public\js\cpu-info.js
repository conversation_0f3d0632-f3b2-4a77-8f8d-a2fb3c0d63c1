// 使用main.js中定义的supportWebP变量
// CPU页面初始化WebP支持相关功能

// 定义一个变量追踪当前是否有活跃的预览
let activeViewer = null;

// 定义openImageFullscreen函数在全局作用域
function openImageFullscreen(src) {
    if (!src) return;
    console.log('[DEBUG] Opening image in fullscreen:', src);
    
    // 如果已经有活跃的预览，先关闭它
    if (activeViewer) {
        try {
            activeViewer.destroy();
            activeViewer = null;
            // 找到并移除可能存在的容器
            const oldContainer = document.querySelector('.viewer-container');
            if (oldContainer && oldContainer.parentNode) {
                oldContainer.parentNode.removeChild(oldContainer);
            }
        } catch (e) {
            console.error('Error destroying existing viewer:', e);
        }
    }
    
    // 创建一个临时的图片容器
    const container = document.createElement('div');
    container.className = 'viewer-container';
    container.style.display = 'none';
    document.body.appendChild(container);

    // 创建图片元素
    const img = document.createElement('img');
    img.src = src;
    container.appendChild(img);

    // 初始化 Viewer
    const viewer = new Viewer(img, {
        backdrop: true,          // 启用背景遮罩
        button: true,           // 显示关闭按钮
        navbar: false,          // 隐藏底部导航栏（只有一张图片时不需要）
        title: false,           // 不显示标题
        toolbar: {              // 自定义工具栏
            zoomIn: true,       // 放大按钮
            zoomOut: true,      // 缩小按钮
            oneToOne: true,     // 1:1 尺寸按钮
            reset: true,        // 重置按钮
            prev: false,        // 上一张（隐藏，因为只有一张图片）
            play: false,        // 播放按钮（隐藏）
            next: false,        // 下一张（隐藏）
            rotateLeft: true,   // 向左旋转
            rotateRight: true,  // 向右旋转
            flipHorizontal: true, // 水平翻转
            flipVertical: true,  // 垂直翻转
        },
        viewed() {
            // 图片加载完成后自动打开查看器
            if (window.innerWidth < 640) {
                viewer.zoomTo(0.8);  // 移动设备使用较小的初始缩放比例
            } else {
                viewer.zoomTo(1);    // 桌面设备使用正常的缩放比例
            }
        },
        hidden() {
            // 查看器关闭后移除临时元素
            viewer.destroy();
            activeViewer = null;
            if (container && container.parentNode) {
                container.parentNode.removeChild(container);
            }
        },
        maxZoomRatio: 5,        // 最大缩放比例
        minZoomRatio: 0.1,      // 最小缩放比例
        transition: true,       // 启用过渡效果
        keyboard: true,         // 启用键盘支持
    });

    // 保存活跃的预览引用
    activeViewer = viewer;

    // 显示查看器
    viewer.show();
}

// 立即将函数暴露到全局作用域
window.openImageFullscreen = openImageFullscreen;

document.addEventListener('DOMContentLoaded', function() {
    // DOM元素
    const cpuForm = document.getElementById('cpuForm');
    const brand = document.getElementById('brand');
    const model = document.getElementById('model');
    const series = document.getElementById('series');
    
    // 尝试获取socket字段，支持多种可能的ID名称
    let socket = document.getElementById('socket');
    if (!socket) {
        socket = document.getElementById('socket_type');
        console.log('[DEBUG] Using socket_type field instead of socket');
    }
    
    // 如果仍然没有找到socket字段，创建一个临时的隐藏字段
    if (!socket && cpuForm) {
        console.warn('[WARNING] Socket field not found! Creating fallback field.');
        const socketFallback = document.createElement('input');
        socketFallback.type = 'hidden';
        socketFallback.id = 'socket';
        socketFallback.value = 'Unknown';
        cpuForm.appendChild(socketFallback);
        socket = socketFallback;
    }
    
    const coreCount = document.getElementById('coreCount');
    const threadCount = document.getElementById('threadCount');
    const baseClock = document.getElementById('baseClock');
    const boostClock = document.getElementById('boostClock');
    const tdp = document.getElementById('tdp');
    const l3Cache = document.getElementById('l3Cache');
    const processNode = document.getElementById('processNode');
    const releaseDate = document.getElementById('releaseDate');
    const integratedGraphics = document.getElementById('integratedGraphics');
    const price = document.getElementById('price');
    const cpuImage = document.getElementById('cpuImage');
    const imagePreview = document.getElementById('imagePreview');
    const imagePreviewContainer = document.getElementById('imagePreviewContainer');
    const removeImageBtn = document.getElementById('removeImageBtn');
    const notes = document.getElementById('notes');
    const cpuTableBody = document.getElementById('cpuTableBody');
    const cpuSearch = document.getElementById('cpuSearch');
    const brandFilter = document.getElementById('brandFilter');
    const totalCount = document.getElementById('totalCount');
    const prevPage = document.getElementById('prevPage');
    const nextPage = document.getElementById('nextPage');
    const pageInfo = document.getElementById('pageInfo');
    const cpuModal = document.getElementById('cpuModal');
    const closeModal = document.getElementById('closeModal');
    const closeModalBtn = document.getElementById('closeModalBtn');
    const cpuDetails = document.getElementById('cpuDetails');
    const editBtn = document.getElementById('editBtn');
    const deleteBtn = document.getElementById('deleteBtn');
    const resetFilterBtn = document.getElementById('resetFilterBtn');
    const smartInput = document.getElementById('smartInput');
    const autoFillBtn = document.getElementById('autoFillBtn');


    // 全局变量
    let currentPage = 1;
    const pageSize = 10;
    let totalRecords = 0;
    let cpus = [];
    let imageFile = null;
    let currentCpuId = null;
    let isEditing = false;
    let isDarkMode = false; // 默认为亮色模式
    // 最大显示页码按钮数
    const maxPageButtons = 5;
    // 用户角色
    let isAdminUser = false;

    // 在全局变量下添加API端点常量
    const API_ENDPOINTS = {
        CPUS: '/api/cpus',
        CPU: (id) => {
            const endpoint = `/api/cpus/${id}`;
            console.log('构建删除API端点:', endpoint);
            return endpoint;
        }
    };

    // 初始化
    init();

    // 初始化函数
    function init() {
        console.log('初始化CPU信息管理页面...');
        
        // 继续设置其他功能
        setupEventListeners();
        initDarkMode();
        
        // 检查用户权限
        isAdmin().then(adminStatus => {
            isAdminUser = adminStatus;
            console.log('用户权限检查完成，管理员状态:', isAdminUser);
        }).catch(error => {
            console.error('权限检查失败:', error);
            isAdminUser = false;
        });
        
        // 加载CPU数据
        loadCpus();
        
        // 如果浏览器不支持WebP，显示提示信息
        if (typeof supportWebP !== 'undefined' && !supportWebP) {
            const uploadArea = document.querySelector('#cpuImage');
            if (uploadArea) {
                const infoEl = document.createElement('p');
                infoEl.className = 'text-xs text-orange-500 mt-1';
                infoEl.innerHTML = '<i class="fas fa-exclamation-triangle mr-1"></i>您的浏览器可能不支持WebP格式，但服务器仍会转换图片以优化加载速度';
                uploadArea.parentNode.appendChild(infoEl);
            }
        }
    }

    // 初始化暗夜模式
    function initDarkMode() {
        // 检查本地存储中的暗夜模式设置
        isDarkMode = localStorage.getItem('darkMode') === 'true'; // 只有当明确设置为true时才使用暗夜模式
        if (isDarkMode) {
            document.body.classList.add('dark-mode');
            updateDarkModeIcon(true);
        } else {
            document.body.classList.remove('dark-mode');
            updateDarkModeIcon(false);
        }
    }
    
    // 更新暗夜模式图标
    function updateDarkModeIcon(isDark) {
        const darkModeToggle = document.getElementById('darkModeToggle');
        if (darkModeToggle) {
            if (isDark) {
                darkModeToggle.innerHTML = '<i class="fas fa-sun"></i>';
                darkModeToggle.classList.remove('bg-gray-200', 'hover:bg-gray-300', 'text-gray-700');
                darkModeToggle.classList.add('bg-yellow-400', 'hover:bg-yellow-500', 'text-yellow-900');
                darkModeToggle.title = '切换至亮色模式';
                darkModeToggle.style.boxShadow = '0 0 10px rgba(251, 191, 36, 0.5)';
            } else {
                darkModeToggle.innerHTML = '<i class="fas fa-moon"></i>';
                darkModeToggle.classList.remove('bg-yellow-400', 'hover:bg-yellow-500', 'text-yellow-900');
                darkModeToggle.classList.add('bg-gray-200', 'hover:bg-gray-300', 'text-gray-700');
                darkModeToggle.title = '切换至暗夜模式';
                darkModeToggle.style.boxShadow = 'none';
            }
        }
    }
    
    // 切换暗夜模式
    function toggleDarkMode() {
        isDarkMode = !isDarkMode;
        localStorage.setItem('darkMode', isDarkMode);
        
        if (isDarkMode) {
            document.body.classList.add('dark-mode');
        } else {
            document.body.classList.remove('dark-mode');
        }
        
        updateDarkModeIcon(isDarkMode);
        
        // 在暗夜模式切换后刷新数据显示，以应用新的样式
        loadCpus();  // 重新加载数据，更新所有显示样式，增强深色模式下的对比度
    }

    // 设置事件监听器
    function setupEventListeners() {
        // 暗夜模式切换
        const darkModeToggle = document.getElementById('darkModeToggle');
        if (darkModeToggle) {
            darkModeToggle.addEventListener('click', toggleDarkMode);
        }
        
        // 表单提交
        if (cpuForm) {
        cpuForm.addEventListener('submit', handleFormSubmit);
        }

        // 智能解析
        if (autoFillBtn) {
        autoFillBtn.addEventListener('click', parseCpuInfo);
        }

        // 图片上传
        if (cpuImage) {
        cpuImage.addEventListener('change', handleImageUpload);
        }

        // 移除图片
        if (removeImageBtn) {
        removeImageBtn.addEventListener('click', removeImage);
        }

        // 搜索框
        if (cpuSearch) {
        cpuSearch.addEventListener('input', debounce(() => {
            currentPage = 1;
            loadCpus();
        }, 300));
        }

        // 品牌过滤器
        if (brandFilter) {
        brandFilter.addEventListener('change', () => {
            currentPage = 1;
            loadCpus();
        });
        }

        // 重置过滤器
        if (resetFilterBtn) {
        resetFilterBtn.addEventListener('click', () => {
                if (cpuSearch) cpuSearch.value = '';
                if (brandFilter) brandFilter.value = 'all';
            currentPage = 1;
            loadCpus();
        });
        }

        // 分页控制
        if (prevPage) {
            prevPage.addEventListener('click', () => {
                if (currentPage > 1) {
                    changePage(currentPage - 1);
                }
            });
        }

        if (nextPage) {
            nextPage.addEventListener('click', () => {
                const totalPages = Math.ceil(totalRecords / pageSize) || 1;
                if (currentPage < totalPages) {
                    changePage(currentPage + 1);
                }
            });
        }
        
        // 首页和尾页
        const firstPageBtn = document.getElementById('firstPage');
        if (firstPageBtn) {
            firstPageBtn.addEventListener('click', () => changePage(1));
        }

        const lastPageBtn = document.getElementById('lastPage');
        if (lastPageBtn) {
            lastPageBtn.addEventListener('click', () => {
                const totalPages = Math.ceil(totalRecords / pageSize) || 1;
                changePage(totalPages);
            });
        }

        // 页码跳转
        const goToPageBtn = document.getElementById('goToPage');
        if (goToPageBtn) {
            goToPageBtn.addEventListener('click', () => {
                const pageJumpInput = document.getElementById('pageJump');
                if (pageJumpInput) {
                    let pageNum = parseInt(pageJumpInput.value);
                    const totalPages = Math.ceil(totalRecords / pageSize) || 1;
                    
                    if (isNaN(pageNum) || pageNum < 1) {
                        pageNum = 1;
                    } else if (pageNum > totalPages) {
                        pageNum = totalPages;
                    }
                    
                    changePage(pageNum);
                    pageJumpInput.value = '';
                }
            });
        }
    }
    
    // 辅助函数：防抖动
    function debounce(func, wait) {
        let timeout;
        return function() {
            const context = this;
            const args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), wait);
        };
    }
    
    // 辅助函数：带有认证的fetch请求
    function fetchWithAuth(url, options = {}) {
        const token = localStorage.getItem('token');
        const headers = options.headers || {};
        
        if (token) {
            headers['Authorization'] = `Bearer ${token}`;
        }
        
        return fetch(url, {
            ...options,
            headers: {
                ...headers
            }
        });
    }

    // 加载CPU数据
    function loadCpus() {
        console.log('加载CPU数据...');
        showTableLoadingState();

        // 构建查询参数
        const params = new URLSearchParams();
        params.append('page', currentPage);
        params.append('limit', pageSize);
        
        // 添加搜索条件
        if (cpuSearch && cpuSearch.value.trim()) {
            params.append('search', cpuSearch.value.trim());
        }
        
        // 添加品牌过滤
        if (brandFilter && brandFilter.value !== 'all') {
            params.append('brand', brandFilter.value);
        }
        
        // 获取认证令牌
        const token = localStorage.getItem('token');
        const headers = {
            'Content-Type': 'application/json'
        };
        
        // 如果有令牌，添加到请求头
        if (token) {
            headers['Authorization'] = `Bearer ${token}`;
        }
        
        // 使用带认证的fetch
        fetch(`${API_ENDPOINTS.CPUS}?${params.toString()}`, {
            method: 'GET',
            headers: headers
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error('网络响应错误');
                }
                return response.json();
            })
            .then(data => {
                console.log('加载数据成功:', data);
                
                // 更新全局变量
                cpus = data.data || [];
                totalRecords = data.total || 0;
                
                // 渲染表格
                renderCpuTable(cpus);
                
                // 更新分页信息
                updatePagination();
                
                // 显示总记录数
                const totalRecordsEl = document.getElementById('totalRecords');
                if (totalRecordsEl) {
                    totalRecordsEl.textContent = totalRecords;
                }
                
                hideLoadingState();
            })
            .catch(error => {
                console.error('获取CPU数据失败:', error);
                if (typeof showErrorMessage === 'function') {
                    showErrorMessage(`获取数据失败: ${error.message}`);
                } else {
                    console.error(`获取数据失败: ${error.message}`);
                }
                hideLoadingState();
            });
    }

    // 渲染CPU表格
    function renderCpuTable(cpus) {
        if (!cpuTableBody) return;
        
        if (cpus.length === 0) {
            cpuTableBody.innerHTML = '<tr><td colspan="7" class="text-center py-4">暂无数据</td></tr>';
            return;
        }

        // 检测是否为移动设备，如果是则使用卡片视图
        const isMobile = window.innerWidth < 640;
        if (isMobile) {
            renderMobileCards(cpus);
            return;
        }

        cpuTableBody.innerHTML = '';
        
        // 添加变量记录上次点击的行
        let lastClickedRow = null;
        
        cpus.forEach(cpu => {
                const row = document.createElement('tr');
            row.className = 'hover:bg-blue-50 dark:hover:bg-gray-700 transition-colors border-b border-gray-200 relative';
            
            // 添加行点击事件，高亮显示当前选中的行
            row.addEventListener('click', function() {
                // 如果有上次选中的行，移除其高亮样式
                if (lastClickedRow && lastClickedRow !== this) {
                    lastClickedRow.classList.remove('current-row');
                }
                
                // 为当前行添加或移除高亮样式（切换效果）
                this.classList.toggle('current-row');
                
                // 更新上次点击的行
                lastClickedRow = this.classList.contains('current-row') ? this : null;
            });
            
            // 图片单元格
            const imgCell = document.createElement('td');
            imgCell.className = 'px-3 py-2';
            
            const img = document.createElement('img');
            img.src = cpu.image_url || '/images/default-cpu.png';
            img.className = 'w-12 h-12 object-contain mx-auto cursor-pointer hover:opacity-90 transition-opacity';
            img.alt = `${cpu.brand} ${cpu.model}`;
            // 添加点击事件，显示大图
            img.onclick = function() {
                openImageFullscreen(cpu.image_url || '/images/default-cpu.png');
            };
            imgCell.appendChild(img);
            
            // 型号单元格
                const modelCell = document.createElement('td');
            modelCell.className = 'px-3 py-2';
            modelCell.textContent = cpu.model || '未知型号';
            
            // 品牌单元格
                const brandCell = document.createElement('td');
            brandCell.className = 'px-3 py-2';
            brandCell.textContent = cpu.brand || '未知品牌';
            
            // 接口单元格
                const socketCell = document.createElement('td');
            socketCell.className = 'px-3 py-2';
            socketCell.textContent = cpu.socket || '未知';
            
            // 核心/线程单元格
            const coreCell = document.createElement('td');
            coreCell.className = 'px-3 py-2';
            coreCell.textContent = (cpu.core_count ? cpu.core_count : '?') + '核/' + 
                                  (cpu.thread_count ? cpu.thread_count : '?') + '线程';
            
            // 频率单元格
                const clockCell = document.createElement('td');
            clockCell.className = 'px-3 py-2';
            clockCell.textContent = cpu.base_clock ? cpu.base_clock + 'GHz' : '未知';
            
            // 操作单元格
            const actionCell = document.createElement('td');
            actionCell.className = 'px-3 py-2 text-right';

            // 查看按钮
            const viewBtn = document.createElement('button');
            viewBtn.className = 'btn-view text-blue-500 hover:text-blue-700 mr-2';
            viewBtn.innerHTML = '<i class="fas fa-eye"></i>';
            viewBtn.title = '查看详情';
            viewBtn.dataset.id = cpu.id;
            viewBtn.addEventListener('click', () => viewCpu(cpu.id));

            // 编辑按钮
            const editBtn = document.createElement('button');
            editBtn.className = 'btn-edit text-green-500 hover:text-green-700 mr-2';
            editBtn.innerHTML = '<i class="fas fa-edit"></i>';
            editBtn.title = '编辑';
            editBtn.dataset.id = cpu.id;
            editBtn.dataset.action = 'edit';
            editBtn.addEventListener('click', () => editCpu(cpu.id));

            // 删除按钮
            const deleteBtn = document.createElement('button');
            deleteBtn.className = 'btn-delete text-red-500 hover:text-red-700';
            deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
            deleteBtn.title = '删除';
            deleteBtn.dataset.id = cpu.id;
            deleteBtn.dataset.action = 'delete';
            deleteBtn.addEventListener('click', () => confirmDelete(cpu.id));
            
            // 添加所有操作按钮到单元格
            actionCell.appendChild(viewBtn);
            actionCell.appendChild(editBtn);
            actionCell.appendChild(deleteBtn);

            // 将所有单元格添加到行
            row.appendChild(imgCell);
            row.appendChild(modelCell);
            row.appendChild(brandCell);
            row.appendChild(socketCell);
            row.appendChild(coreCell);
            row.appendChild(clockCell);
                row.appendChild(actionCell);

            // 将行添加到表格
                cpuTableBody.appendChild(row);
            });
        }
        
    // 渲染移动端卡片布局
    function renderMobileCards(cpus) {
        if (!cpuTableBody) return;
        
        // 清空表格内容
        cpuTableBody.innerHTML = '';
        
        // 调整表格容器样式以适应卡片布局
        const tableElement = cpuTableBody.closest('table');
        if (tableElement) {
            tableElement.style.display = 'block';
            tableElement.style.width = '100%';
            tableElement.style.maxWidth = '100%';
            tableElement.style.borderCollapse = 'collapse';
            tableElement.style.borderSpacing = '0';
            
            // 隐藏表头
            const theadElement = tableElement.querySelector('thead');
            if (theadElement) {
                theadElement.style.display = 'none';
            }
        }
        
        // 调整表格主体样式
        cpuTableBody.style.display = 'block';
        cpuTableBody.style.width = '100%';
        cpuTableBody.style.maxWidth = '100%';
        
        // 遍历CPU数据并创建卡片
        cpus.forEach((cpu, index) => {
            // 创建卡片外层容器
            const cardOuterContainer = document.createElement('div');
            cardOuterContainer.className = 'cpu-card-outer-container';
            cardOuterContainer.style.width = '100%';
            cardOuterContainer.style.maxWidth = '100vw';
            cardOuterContainer.style.boxSizing = 'border-box';
            cardOuterContainer.style.padding = '0 4px';
            cardOuterContainer.style.marginBottom = '12px';
            cardOuterContainer.style.position = 'relative';
            cardOuterContainer.style.animation = `fadeIn 0.3s ease-in-out ${index * 0.05}s both`;
            
            // 创建卡片主体
            const card = document.createElement('div');
            card.className = 'cpu-card';
            card.style.width = '100%';
            card.style.borderRadius = '12px';
            card.style.overflow = 'hidden';
            card.style.backgroundColor = isDarkMode ? 'var(--bg-secondary)' : 'white';
            card.style.boxShadow = isDarkMode ? '0 5px 15px rgba(0,0,0,0.3)' : '0 5px 15px rgba(0,0,0,0.07)';
            card.style.display = 'flex';
            card.style.flexDirection = 'column';
            card.style.minWidth = '0';
            card.style.transition = 'transform 0.2s ease, box-shadow 0.2s ease';
            card.style.border = isDarkMode ? '1px solid rgba(255, 255, 255, 0.05)' : 'none';
            
            // 添加触摸反馈效果
            card.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.99)';
                this.style.boxShadow = isDarkMode ? '0 2px 8px rgba(0,0,0,0.4)' : '0 2px 8px rgba(0,0,0,0.05)';
            });
            
            card.addEventListener('touchend', function() {
                this.style.transform = 'scale(1)';
                this.style.boxShadow = isDarkMode ? '0 5px 15px rgba(0,0,0,0.3)' : '0 5px 15px rgba(0,0,0,0.07)';
            });
            
            // 设置主题颜色
            let themeColor, lightThemeColor, borderThemeColor, headerBgColor;
            
            // 根据CPU品牌设置不同的主题色
            if (cpu.brand === 'AMD') {
                themeColor = isDarkMode ? '#f87171' : '#c70025'; // AMD红色
                lightThemeColor = isDarkMode ? 'rgba(248, 113, 113, 0.1)' : 'rgba(199, 0, 37, 0.05)';
                borderThemeColor = isDarkMode ? 'rgba(248, 113, 113, 0.2)' : 'rgba(199, 0, 37, 0.1)';
                headerBgColor = isDarkMode ? 'rgba(248, 113, 113, 0.05)' : '#fff5f5';
            } else if (cpu.brand === 'Intel') {
                themeColor = isDarkMode ? '#60a5fa' : '#0071c5'; // Intel蓝色
                lightThemeColor = isDarkMode ? 'rgba(96, 165, 250, 0.1)' : 'rgba(0, 113, 197, 0.05)';
                borderThemeColor = isDarkMode ? 'rgba(96, 165, 250, 0.2)' : 'rgba(0, 113, 197, 0.1)';
                headerBgColor = isDarkMode ? 'rgba(96, 165, 250, 0.05)' : '#f0f7ff';
            } else {
                themeColor = isDarkMode ? '#a3a3a3' : '#4b5563'; // 其他灰色
                lightThemeColor = isDarkMode ? 'rgba(163, 163, 163, 0.1)' : 'rgba(75, 85, 99, 0.05)';
                borderThemeColor = isDarkMode ? 'rgba(163, 163, 163, 0.2)' : 'rgba(75, 85, 99, 0.1)';
                headerBgColor = isDarkMode ? 'rgba(163, 163, 163, 0.05)' : '#f9fafb';
            }
            
            // 创建卡片头部
            const cardHeader = document.createElement('div');
            cardHeader.style.padding = '12px 16px';
            cardHeader.style.backgroundColor = headerBgColor;
            cardHeader.style.borderBottom = `1px solid ${borderThemeColor}`;
            cardHeader.style.display = 'flex';
            cardHeader.style.alignItems = 'center';
            
            // 创建图片容器
            const imgContainer = document.createElement('div');
            imgContainer.className = 'flex-shrink-0 mr-3';
            
            const imgElement = document.createElement('img');
            imgElement.src = cpu.image_url || '/images/default-cpu.png';
            imgElement.alt = cpu.model || 'CPU图片';
            imgElement.className = 'h-12 w-12 object-contain rounded-md shadow-sm cursor-pointer';
            imgElement.style.border = `1px solid ${borderThemeColor}`;
            
            // 添加图片点击预览功能
            imgElement.onclick = function() {
                openImageFullscreen(cpu.image_url || '/images/default-cpu.png');
            };
            
            // 图片加载失败时使用默认图片
            imgElement.onerror = function() {
                this.onerror = null;
                this.src = '/images/default-cpu.png';
            };
            
            imgContainer.appendChild(imgElement);
            
            // 创建标题容器
            const titleContainer = document.createElement('div');
            titleContainer.className = 'flex-1 overflow-hidden';
            titleContainer.style.minWidth = '0';
            
            // 创建型号文本
            const modelText = document.createElement('div');
            modelText.style.fontSize = '1rem';
            modelText.style.fontWeight = '600';
            modelText.style.color = isDarkMode ? 'var(--text-primary)' : '#2d3748';
            modelText.style.marginBottom = '4px';
            modelText.style.lineHeight = '1.35';
            modelText.style.wordBreak = 'break-word';
            modelText.textContent = cpu.model || '未知型号';
            
            // 创建标签容器
            const brandContainer = document.createElement('div');
            brandContainer.className = 'flex items-center text-xs flex-wrap';
            brandContainer.style.gap = '6px';
            
            // 添加品牌标签
            const brandBadge = createBadge(cpu.brand || '未知', themeColor, lightThemeColor, borderThemeColor, getCpuBrandIcon(cpu.brand));
            brandContainer.appendChild(brandBadge);
            
            // 如果有接口信息，添加接口标签
            if (cpu.socket) {
                const socketBadge = createBadge(cpu.socket, '#4A5568', 'rgba(226, 232, 240, 0.5)', 'rgba(203, 213, 225, 0.5)', 'fas fa-plug');
                brandContainer.appendChild(socketBadge);
            }
            
            // 添加各元素到标题容器
            titleContainer.appendChild(modelText);
            titleContainer.appendChild(brandContainer);
            
            // 添加到卡片头部
            cardHeader.appendChild(imgContainer);
            cardHeader.appendChild(titleContainer);
            
            // 创建卡片主体
            const cardBody = document.createElement('div');
            cardBody.style.padding = '12px 16px';
            cardBody.style.backgroundColor = isDarkMode ? 'var(--bg-secondary)' : 'white';
            
            // 创建规格亮点区域
            const specsHighlight = document.createElement('div');
            specsHighlight.style.display = 'flex';
            specsHighlight.style.justifyContent = 'space-around';
            specsHighlight.style.alignItems = 'stretch';
            specsHighlight.style.marginBottom = '12px';
            specsHighlight.style.backgroundColor = lightThemeColor;
            specsHighlight.style.padding = '12px 8px';
            specsHighlight.style.borderRadius = '8px';
            
            // 添加核心数亮点
            let hasHighlight = false;
            if (cpu.core_count) {
                specsHighlight.appendChild(createHighlightItem('核心数', cpu.core_count, 'fas fa-microchip', themeColor));
                hasHighlight = true;
            }
            
            // 添加线程数亮点
            if (cpu.thread_count) {
                specsHighlight.appendChild(createHighlightItem('线程数', cpu.thread_count, 'fas fa-network-wired', themeColor));
                hasHighlight = true;
            }
            
            // 添加基础频率亮点
            if (cpu.base_clock) {
                specsHighlight.appendChild(createHighlightItem('基础频率', cpu.base_clock + 'GHz', 'fas fa-tachometer-alt', themeColor));
                hasHighlight = true;
            }
            
            // 只有当有亮点数据时才添加到卡片主体
            if (hasHighlight) {
                cardBody.appendChild(specsHighlight);
            }
            
            // 创建附加信息网格
            const infoGrid = document.createElement('div');
            infoGrid.style.display = 'grid';
            infoGrid.style.gridTemplateColumns = 'repeat(auto-fit, minmax(100px, 1fr))';
            infoGrid.style.gap = '10px';
            infoGrid.style.marginBottom = '12px';
            
            // 添加加速频率信息
            if (cpu.boost_clock) {
                infoGrid.appendChild(createInfoItem('加速频率', `${cpu.boost_clock}GHz`, 'fas fa-bolt', themeColor));
            }
            
            // 添加TDP信息
            if (cpu.tdp) {
                infoGrid.appendChild(createInfoItem('功耗', `${cpu.tdp}W`, 'fas fa-fire', themeColor));
            }
            
            // 添加L3缓存信息
            if (cpu.l3_cache) {
                infoGrid.appendChild(createInfoItem('L3缓存', `${cpu.l3_cache}MB`, 'fas fa-database', themeColor));
            }
            
            // 只有当有信息时才添加到卡片主体
            if (infoGrid.children.length > 0) {
                cardBody.appendChild(infoGrid);
            }
            
            // 创建标签组
            const tagGroup = document.createElement('div');
            tagGroup.className = 'flex flex-wrap gap-2';
            
            // 添加制程工艺标签
            if (cpu.process_node) {
                const processColor = isDarkMode ? '#4ade80' : '#10b981';
                const processBgColor = isDarkMode ? 'rgba(74, 222, 128, 0.1)' : 'rgba(16, 185, 129, 0.1)';
                tagGroup.appendChild(createSpecTag(cpu.process_node, 'fas fa-microchip', processColor, processBgColor));
            }
            
            // 添加发布日期标签
            if (cpu.release_date) {
                const dateColor = isDarkMode ? '#a78bfa' : '#8b5cf6';
                const dateBgColor = isDarkMode ? 'rgba(167, 139, 250, 0.1)' : 'rgba(139, 92, 246, 0.1)';
                const dateStr = new Date(cpu.release_date).toLocaleDateString();
                tagGroup.appendChild(createSpecTag(dateStr, 'fas fa-calendar', dateColor, dateBgColor));
            }
            
            // 添加集成显卡标签
            if (cpu.integrated_graphics && cpu.integrated_graphics !== '无') {
                const graphicsColor = isDarkMode ? '#f472b6' : '#ec4899';
                const graphicsBgColor = isDarkMode ? 'rgba(244, 114, 182, 0.1)' : 'rgba(236, 72, 153, 0.1)';
                tagGroup.appendChild(createSpecTag('集显: ' + cpu.integrated_graphics, 'fas fa-tv', graphicsColor, graphicsBgColor));
            }
            
            // 只有当有标签时才添加到卡片主体
            if (tagGroup.children.length > 0) {
                cardBody.appendChild(tagGroup);
            }
            
            // 创建卡片底部
            const cardFooter = document.createElement('div');
            cardFooter.style.padding = '12px 16px';
            cardFooter.style.backgroundColor = isDarkMode ? 'rgba(0,0,0,0.1)' : '#fdfdff';
            cardFooter.style.borderTop = `1px solid ${borderThemeColor}`;
            cardFooter.style.display = 'flex';
            cardFooter.style.justifyContent = 'space-around';
            cardFooter.style.gap = '12px';
            
            // 创建操作按钮
            const viewButton = createActionButton('<i class="fas fa-eye"></i>', '查看详情', lightThemeColor, themeColor, () => viewCpu(cpu.id));
            viewButton.className = 'btn-view';
            viewButton.title = '查看详情';
            
            const editButton = createActionButton('<i class="fas fa-edit"></i>', '编辑', 'rgba(16, 185, 129, 0.08)', '#10b981', () => editCpu(cpu.id));
            editButton.className = 'btn-edit';
            editButton.dataset.action = 'edit';
            
            const deleteButton = createActionButton('<i class="fas fa-trash"></i>', '删除', 'rgba(239, 68, 68, 0.08)', '#ef4444', () => confirmDelete(cpu.id));
            deleteButton.className = 'btn-delete';
            deleteButton.dataset.action = 'delete';
            
            // 添加所有操作按钮到底部
            cardFooter.appendChild(viewButton);
            cardFooter.appendChild(editButton);
            cardFooter.appendChild(deleteButton);
            
            // 组装卡片各部分
            card.appendChild(cardHeader);
            card.appendChild(cardBody);
            card.appendChild(cardFooter);
            cardOuterContainer.appendChild(card);
            cpuTableBody.appendChild(cardOuterContainer);
        });
    }
    
    // 创建信息项
    function createInfoItem(label, value, iconClass, themeColor) {
        const item = document.createElement('div');
        item.style.display = 'flex';
        item.style.flexDirection = 'column';
        item.style.alignItems = 'center';
        item.style.padding = '6px 4px';
        item.style.textAlign = 'center';

        const iconEl = document.createElement('div');
        iconEl.innerHTML = `<i class="${iconClass}" style="color:${themeColor}; font-size: 0.9rem;"></i>`;
        iconEl.style.marginBottom = '4px';
        
        const valueEl = document.createElement('div');
        valueEl.className = 'text-xs font-medium';
        valueEl.textContent = value;
        valueEl.style.color = isDarkMode ? 'var(--text-primary)' : '#4A5568';
        valueEl.style.marginBottom = '2px';
        
        const labelEl = document.createElement('div');
        labelEl.className = 'text-xxs text-gray-500';
        labelEl.textContent = label;
        labelEl.style.textTransform = 'uppercase';

        item.appendChild(iconEl);
        item.appendChild(valueEl);
        item.appendChild(labelEl);
        return item;
    }
    
    // 创建亮点项
    function createHighlightItem(label, value, iconClass, themeColor) {
        const item = document.createElement('div');
        item.className = 'text-center flex-1 px-1';
        item.style.minWidth = '70px';

        const iconEl = document.createElement('div');
        iconEl.className = 'mb-1.5';
        iconEl.innerHTML = `<i class="${iconClass}" style="color:${themeColor}; font-size: 1.2rem;"></i>`;
        
        const valueEl = document.createElement('div');
        valueEl.className = 'font-semibold';
        valueEl.style.fontSize = '1.1rem';
        valueEl.style.color = themeColor;
        valueEl.style.lineHeight = '1.2';
        valueEl.textContent = value;
        
        const labelEl = document.createElement('div');
        labelEl.className = 'text-xs text-gray-500 mt-0.5';
        labelEl.textContent = label;

        item.appendChild(iconEl);
        item.appendChild(valueEl);
        item.appendChild(labelEl);
        return item;
    }
    
    // 创建规格标签
    function createSpecTag(text, iconClass, color, bgColor) {
        const tag = document.createElement('span');
        tag.className = 'inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium';
        tag.style.backgroundColor = bgColor;
        tag.style.color = color;
        tag.style.border = `1px solid ${color}26`;
        tag.innerHTML = `<i class="${iconClass} mr-1.5"></i> ${text}`;
        return tag;
    }
    
    // 创建徽章
    function createBadge(text, textColor, bgColor, borderColor, iconClass = null) {
        const badge = document.createElement('span');
        badge.className = 'inline-flex items-center';
        badge.style.padding = '3px 8px';
        badge.style.backgroundColor = bgColor;
        badge.style.color = textColor;
        badge.style.border = `1px solid ${borderColor}`;
        badge.style.fontWeight = '500';
        badge.style.borderRadius = '6px';
        badge.style.fontSize = '0.7rem';
        if (iconClass) {
            badge.innerHTML = `<i class="${iconClass} mr-1.5"></i> ${text}`;
        } else {
            badge.textContent = text;
        }
        return badge;
    }
    
    // 创建操作按钮
    function createActionButton(innerHTML, title, bgColor, textColor, onClick) {
        const button = document.createElement('button');
        button.style.width = '40px';
        button.style.height = '40px';
        button.style.borderRadius = '50%';
        button.style.display = 'flex';
        button.style.alignItems = 'center';
        button.style.justifyContent = 'center';
        button.style.backgroundColor = bgColor;
        button.style.color = textColor;
        button.style.transition = 'all 0.15s ease-out';
        button.style.boxShadow = '0 1px 3px rgba(0,0,0,0.05)';
        button.innerHTML = innerHTML;
        button.title = title;
        button.addEventListener('click', onClick);
        button.addEventListener('touchstart', function() { 
            this.style.transform = 'scale(0.92)'; 
            this.style.boxShadow = '0 0 0 rgba(0,0,0,0.05)';
        });
        button.addEventListener('touchend', function() { 
            this.style.transform = 'scale(1)'; 
            this.style.boxShadow = '0 1px 3px rgba(0,0,0,0.05)';
        });
        return button;
    }
    
    // 获取CPU品牌图标
    function getCpuBrandIcon(brandName) {
        if (!brandName) return 'fas fa-microchip';
        const lowerBrand = brandName.toLowerCase();
        if (lowerBrand.includes('amd')) return 'fas fa-fire';
        if (lowerBrand.includes('intel')) return 'fas fa-microchip';
        return 'fas fa-microchip';
    }

    // 显示表格加载状态
    function showTableLoadingState() {
        if (cpuTableBody) {
            cpuTableBody.innerHTML = `<tr><td colspan="7" class="px-3 py-4 text-center text-gray-500"><i class="fas fa-spinner fa-spin mr-2"></i>加载中...</td></tr>`;
        }
    }
    
    // 显示加载状态
    function showLoadingState() {
        // 显示加载指示器
        const loadingIndicator = document.getElementById('loadingIndicator');
        if (loadingIndicator) {
            loadingIndicator.classList.remove('hidden');
        }
        
        // 禁用提交按钮
        const submitBtn = document.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.classList.add('opacity-50', 'cursor-not-allowed');
            submitBtn.innerHTML = '<i class="fas fa-circle-notch fa-spin mr-2"></i>处理中...';
        }
    }
    
    // 隐藏加载状态
    function hideLoadingState() {
        // 隐藏加载指示器
        const loadingIndicator = document.getElementById('loadingIndicator');
        if (loadingIndicator) {
            loadingIndicator.classList.add('hidden');
        }
        
        // 恢复提交按钮
        const submitBtn = document.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = false;
            submitBtn.classList.remove('opacity-50', 'cursor-not-allowed');
            submitBtn.innerHTML = '保存';
        }
    }
    
    // 更新分页信息
    function updatePagination() {
        const totalPages = Math.ceil(totalRecords / pageSize) || 1;
        
        // 更新当前页和总页数显示
        const currentPageDisplay = document.getElementById('currentPageDisplay');
        const totalPagesDisplay = document.getElementById('totalPagesDisplay');
        
        if (currentPageDisplay) {
        currentPageDisplay.textContent = currentPage;
        }
        
        if (totalPagesDisplay) {
            totalPagesDisplay.textContent = totalPages;
        }
        
        // 更新页码按钮
        if (prevPage) {
            prevPage.disabled = currentPage <= 1;
        }
        
        if (nextPage) {
            nextPage.disabled = currentPage >= totalPages;
        }
        
        // 更新首页和尾页按钮
        const firstPageBtn = document.getElementById('firstPage');
        if (firstPageBtn) {
            firstPageBtn.disabled = currentPage <= 1;
        }
        
        const lastPageBtn = document.getElementById('lastPage');
        if (lastPageBtn) {
            lastPageBtn.disabled = currentPage >= totalPages;
        }
        
        // 生成页码按钮
        const pageNumbers = document.getElementById('pageNumbers');
        if (pageNumbers) {
            pageNumbers.innerHTML = '';
            
            // 确定要显示的页码范围
            let startPage = Math.max(1, currentPage - Math.floor(maxPageButtons / 2));
            let endPage = Math.min(totalPages, startPage + maxPageButtons - 1);
            
            // 调整起始页，确保显示足够的按钮
                startPage = Math.max(1, endPage - maxPageButtons + 1);
            
            // 创建页码按钮
            for (let i = startPage; i <= endPage; i++) {
                const pageButton = document.createElement('button');
                pageButton.className = `px-2 py-1 border ${i === currentPage ? 'bg-blue-600 text-white' : 'bg-white text-gray-700'} rounded-md text-sm`;
                pageButton.textContent = i;
                pageButton.addEventListener('click', () => changePage(i));
                pageNumbers.appendChild(pageButton);
            }
        }
    }

    // 切换页面
    function changePage(page) {
        currentPage = page;
        loadCpus();
    }

    // 查看CPU详情
    function viewCpu(id) {
        console.log('查看CPU详情:', id);
        
        // 获取CPU详情数据
        fetchWithAuth(`${API_ENDPOINTS.CPU(id)}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('获取CPU详情失败');
                }
                return response.json();
            })
            .then(cpu => {
                // 找到模态框元素
                const modal = document.getElementById('cpuModal');
                const detailsContainer = document.getElementById('cpuDetails');
                
                if (!modal || !detailsContainer) {
                    console.error('未找到模态框元素');
                    return;
                }
                
                // 清空原有内容并填充新数据
                detailsContainer.innerHTML = `
                    <div class="text-center mb-4">
                        <h1 class="text-xl font-bold">${cpu.model || '未知型号'}</h1>
                        <div class="cpu-badge ${cpu.brand === 'AMD' ? 'amd-cpu' : 'intel-cpu'} inline-block mt-1">${cpu.brand || '未知品牌'}</div>
                    </div>
                    
                    <div class="flex justify-center mb-6">
                        <img src="${cpu.image_url || '/images/default-cpu.png'}" alt="${cpu.brand} ${cpu.model}" 
                            class="w-32 h-32 object-contain rounded-lg border border-gray-200 cursor-pointer hover:opacity-90 transition-opacity" 
                            onclick="openImageFullscreen('${cpu.image_url || '/images/default-cpu.png'}')">
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                        <div class="border rounded-lg overflow-hidden">
                            <div class="flex items-center bg-gray-50 p-3">
                                <i class="fas fa-microchip text-blue-600 mr-2"></i>
                                <span class="font-semibold text-gray-700">基本规格</span>
                            </div>
                            <div class="p-3 grid grid-cols-2 gap-2">
                                <div>
                                    <span class="text-gray-500">系列</span>
                                    <p class="font-semibold">${cpu.series || '未知'}</p>
                                </div>
                                <div>
                                    <span class="text-gray-500">接口</span>
                                    <p class="font-semibold">${cpu.socket || '未知'}</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="border rounded-lg overflow-hidden">
                            <div class="flex items-center bg-gray-50 p-3">
                                <i class="fas fa-tachometer-alt text-orange-500 mr-2"></i>
                                <span class="font-semibold text-gray-700">性能参数</span>
                            </div>
                            <div class="p-3 grid grid-cols-2 gap-2">
                                <div>
                                    <span class="text-gray-500">基础频率</span>
                                    <p class="font-semibold">${cpu.base_clock ? cpu.base_clock + ' GHz' : '未知'}</p>
                                </div>
                                <div>
                                    <span class="text-gray-500">加速频率</span>
                                    <p class="font-semibold">${cpu.boost_clock ? cpu.boost_clock + ' GHz' : '未知'}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                        <div class="border rounded-lg overflow-hidden">
                            <div class="flex items-center bg-gray-50 p-3">
                                <i class="fas fa-layer-group text-green-600 mr-2"></i>
                                <span class="font-semibold text-gray-700">核心架构</span>
                            </div>
                            <div class="p-3 grid grid-cols-2 gap-2">
                                <div>
                                    <span class="text-gray-500">核心数</span>
                                    <p class="font-semibold">${cpu.core_count || '未知'}</p>
                                </div>
                                <div>
                                    <span class="text-gray-500">线程数</span>
                                    <p class="font-semibold">${cpu.thread_count || '未知'}</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="border rounded-lg overflow-hidden">
                            <div class="flex items-center bg-gray-50 p-3">
                                <i class="fas fa-memory text-indigo-500 mr-2"></i>
                                <span class="font-semibold text-gray-700">缓存与功耗</span>
                            </div>
                            <div class="p-3 grid grid-cols-2 gap-2">
                                <div>
                                    <span class="text-gray-500">L3缓存</span>
                                    <p class="font-semibold">${cpu.l3_cache ? cpu.l3_cache + ' MB' : '未知'}</p>
                                </div>
                                <div>
                                    <span class="text-gray-500">功耗</span>
                                    <p class="font-semibold">${cpu.tdp ? cpu.tdp + ' W' : '未知'}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="border rounded-lg overflow-hidden">
                            <div class="flex items-center bg-gray-50 p-3">
                                <i class="fas fa-microchip text-purple-600 mr-2"></i>
                                <span class="font-semibold text-gray-700">技术参数</span>
                            </div>
                            <div class="p-3 grid grid-cols-2 gap-2">
                                <div>
                                    <span class="text-gray-500">制程工艺</span>
                                    <p class="font-semibold">${cpu.process_node || '未知'}</p>
                                </div>
                                <div>
                                    <span class="text-gray-500">发布日期</span>
                                    <p class="font-semibold">${cpu.release_date ? new Date(cpu.release_date).toLocaleDateString() : '未知'}</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="border rounded-lg overflow-hidden">
                            <div class="flex items-center bg-gray-50 p-3">
                                <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                                <span class="font-semibold text-gray-700">其他信息</span>
                            </div>
                            <div class="p-3">
                                <div class="mb-2">
                                    <span class="text-gray-500">集成显卡</span>
                                    <p class="font-semibold">${cpu.integrated_graphics || '无'}</p>
                                </div>
                                <div class="mb-2">
                                    <span class="text-gray-500">价格</span>
                                    <p class="font-semibold">${cpu.price && typeof cpu.price === 'number' ? '¥' + cpu.price.toFixed(2) : (cpu.price || '未知')}</p>
                                </div>
                                <div>
                                    <span class="text-gray-500">备注</span>
                                    <p class="font-semibold">${cpu.notes || '无'}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                
                // 设置编辑和删除按钮的事件处理
                const editBtn = document.getElementById('editBtn');
                const deleteBtn = document.getElementById('deleteBtn');
                const closeModalBtn = document.getElementById('closeModalBtn');
                const closeModal = document.getElementById('closeModal');
                
                // 检查用户权限并相应设置按钮
                isAdmin().then(isAdminUser => {
                    if (editBtn) {
                        if (isAdminUser) {
                            // 管理员可以编辑
                            editBtn.style.display = 'inline-flex';
                            editBtn.onclick = () => {
                                modal.classList.add('hidden');
                                editCpu(id);
                            };
                        } else {
                            // 普通用户隐藏编辑按钮
                            editBtn.style.display = 'none';
                        }
                    }
                    
                    if (deleteBtn) {
                        if (isAdminUser) {
                            // 管理员可以删除
                            deleteBtn.style.display = 'inline-flex';
                            deleteBtn.onclick = () => {
                                modal.classList.add('hidden');
                                confirmDelete(id);
                            };
                        } else {
                            // 普通用户隐藏删除按钮
                            deleteBtn.style.display = 'none';
                        }
                    }
                });
                
                if (closeModalBtn) {
                    closeModalBtn.onclick = () => modal.classList.add('hidden');
                }
                
                if (closeModal) {
                    closeModal.onclick = () => modal.classList.add('hidden');
                }
                
                // 显示模态框
                modal.classList.remove('hidden');
            })
            .catch(error => {
                console.error('获取CPU详情失败:', error);
                if (typeof showErrorMessage === 'function') {
                    showErrorMessage(`获取CPU详情失败: ${error.message}`);
                } else {
                    alert(`获取CPU详情失败: ${error.message}`);
                }
            });
    }

    // 编辑CPU
    function editCpu(id) {
        console.log('编辑CPU:', id);
        
        // 检查用户权限
        isAdmin().then(isAdminUser => {
            if (!isAdminUser) {
                showErrorMessage('权限不足，只有管理员可以编辑数据');
                return;
            }
            
            // 获取CPU详情数据
            fetchWithAuth(`${API_ENDPOINTS.CPU(id)}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('获取CPU详情失败');
                    }
                    return response.json();
                })
                .then(cpu => {
                    // 将CPU数据填充到表单中
                    document.getElementById('brand').value = cpu.brand || '';
                    document.getElementById('model').value = cpu.model || '';
                    document.getElementById('series').value = cpu.series || '';
                    document.getElementById('socket').value = cpu.socket || '';
                    document.getElementById('coreCount').value = cpu.core_count || '';
                    document.getElementById('threadCount').value = cpu.thread_count || '';
                    document.getElementById('baseClock').value = cpu.base_clock || '';
                    document.getElementById('boostClock').value = cpu.boost_clock || '';
                    document.getElementById('tdp').value = cpu.tdp || '';
                    document.getElementById('l3Cache').value = cpu.l3_cache || '';
                    document.getElementById('processNode').value = cpu.process_node || '';
                    document.getElementById('releaseDate').value = cpu.release_date ? cpu.release_date.split('T')[0] : '';
                    document.getElementById('integratedGraphics').value = cpu.integrated_graphics || '';
                    document.getElementById('price').value = cpu.price || '';
                    document.getElementById('notes').value = cpu.notes || '';
                    
                    // 处理图片预览
                    const imagePreviewContainer = document.getElementById('imagePreviewContainer');
                    const imagePreview = document.getElementById('imagePreview');
                    
                    if (cpu.image_url && imagePreviewContainer && imagePreview) {
                        imagePreview.src = cpu.image_url;
                        imagePreviewContainer.classList.remove('hidden');
                    }
                    
                    // 设置表单模式为"编辑"
                    const cpuForm = document.getElementById('cpuForm');
                    if (cpuForm) {
                        // 存储当前编辑的CPU ID
                        cpuForm.dataset.editId = id;
                        
                        // 更改提交按钮文本
                        const submitButton = cpuForm.querySelector('button[type="submit"]');
                        if (submitButton) {
                            submitButton.innerHTML = '<i class="fas fa-save mr-2"></i> 更新CPU信息';
                        }
                        
                        // 滚动到表单顶部
                        cpuForm.scrollIntoView({ behavior: 'smooth' });
                    }
                })
                .catch(error => {
                    console.error('获取CPU详情失败:', error);
                    if (typeof showErrorMessage === 'function') {
                        showErrorMessage(`获取CPU详情失败: ${error.message}`);
                    } else {
                        alert(`获取CPU详情失败: ${error.message}`);
                    }
                });
        });
    }

    // 确认删除
    function confirmDelete(id) {
        // 实现删除确认功能
        console.log('确认删除CPU:', id);
        
        // 检查用户权限
        isAdmin().then(isAdminUser => {
            if (!isAdminUser) {
                showErrorMessage('权限不足，只有管理员可以删除数据');
                return;
            }
            
            if (confirm(`确定要删除ID为 ${id} 的CPU吗？此操作不可恢复。`)) {
                deleteCpu(id);
            }
        });
    }
    
    // 执行删除操作
    function deleteCpu(id) {
        // 获取token
        const token = localStorage.getItem('token');
        if (!token) {
            console.error('未找到认证令牌，无法执行删除操作');
            if (typeof showToast === 'function') {
                showToast('请先登录', 'error');
            }
            return;
        }
        
        console.log('执行删除操作，ID:', id);
        console.log('使用的认证令牌:', token);
        
        // 直接使用fetch，明确设置Authorization头
        fetch(`${API_ENDPOINTS.CPU(id)}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            }
        })
        .then(response => {
            console.log('删除请求响应状态:', response.status);
            
            if (response.status === 204) {
                console.log(`CPU ${id} 已成功删除`);
                // 显示成功消息
                if (typeof showToast === 'function') {
                    showToast('删除成功', 'success');
                } else {
                    console.log('删除成功');
                }
                // 重新加载数据
                loadCpus();
            } else if (response.status === 403) {
                console.error('权限不足，无法删除');
                if (typeof showToast === 'function') {
                    showToast('权限不足，只有管理员可以删除数据', 'error');
                } else {
                    console.error('权限不足，只有管理员可以删除数据');
                }
            } else {
                return response.json().then(data => {
                    throw new Error(data.message || '删除失败');
                });
            }
        })
        .catch(error => {
            console.error('删除CPU时出错:', error);
            if (typeof showToast === 'function') {
                showToast(`删除失败: ${error.message}`, 'error');
            } else {
                console.error(`删除失败: ${error.message}`);
                    }
                });
            }
    
    // 处理表单提交
    function handleFormSubmit(e) {
        e.preventDefault();
        console.log('提交CPU表单');
        
        // 检查用户权限
        isAdmin().then(isAdminUser => {
            if (!isAdminUser) {
                showErrorMessage('权限不足，只有管理员可以添加或修改数据');
                return;
            }
        
        // 检查是否为编辑模式
        const isEditMode = e.target.dataset.editId ? true : false;
        const editId = e.target.dataset.editId;
        
        // 创建FormData对象用于提交表单（支持文件上传）
        const formData = new FormData();
        
        // 添加文本字段
        formData.append('brand', document.getElementById('brand').value);
        formData.append('model', document.getElementById('model').value);
        formData.append('series', document.getElementById('series').value);
        formData.append('socket', document.getElementById('socket').value);
        
        // 添加数字字段，处理空值
        const coreCount = document.getElementById('coreCount').value;
        if (coreCount) formData.append('core_count', coreCount);
        
        const threadCount = document.getElementById('threadCount').value;
        if (threadCount) formData.append('thread_count', threadCount);
        
        const baseClock = document.getElementById('baseClock').value;
        if (baseClock) formData.append('base_clock', baseClock);
        
        const boostClock = document.getElementById('boostClock').value;
        if (boostClock) formData.append('boost_clock', boostClock);
        
        const tdp = document.getElementById('tdp').value;
        if (tdp) formData.append('tdp', tdp);
        
        const l3Cache = document.getElementById('l3Cache').value;
        if (l3Cache) formData.append('l3_cache', l3Cache);
        
        const price = document.getElementById('price').value;
        if (price) formData.append('price', price);
        
        // 添加其他字段
        formData.append('process_node', document.getElementById('processNode').value);
        formData.append('release_date', document.getElementById('releaseDate').value);
        formData.append('integrated_graphics', document.getElementById('integratedGraphics').value);
        formData.append('notes', document.getElementById('notes').value);
        
        // 验证必填字段
        if (!formData.get('brand') || !formData.get('model')) {
            showErrorMessage('品牌和型号为必填项');
            return;
        }
        
        // 处理图片上传
        const cpuImage = document.getElementById('cpuImage');
        if (cpuImage && cpuImage.files && cpuImage.files.length > 0) {
            formData.append('image', cpuImage.files[0]);
            console.log('添加图片到表单:', cpuImage.files[0].name);
        }
        
        // 显示进度条并禁用提交按钮
        showUploadProgress();
        disableSubmitButton(true, isEditMode);

        // 确定API端点和HTTP方法
        const url = isEditMode ? `${API_ENDPOINTS.CPU(editId)}` : API_ENDPOINTS.CPUS;
        const method = isEditMode ? 'PUT' : 'POST';

        // 使用XMLHttpRequest来支持上传进度
        uploadWithProgress(url, method, formData)
        .then(data => {
            console.log(isEditMode ? '更新成功:' : '添加成功:', data);

            // 显示成功消息
            showToast(isEditMode ? 'CPU信息已成功更新' : 'CPU信息已成功添加', 'success');

            // 重置表单
            resetForm();

            // 重新加载CPU列表
            loadCpus();
        })
        .catch(error => {
            console.error(isEditMode ? '更新失败:' : '添加失败:', error);
            showErrorMessage(error.message);
        })
        .finally(() => {
            // 隐藏进度条并恢复提交按钮
            hideUploadProgress();
            disableSubmitButton(false, isEditMode);
        });
    });
    }
    
    // 重置表单
    function resetForm() {
        const cpuForm = document.getElementById('cpuForm');
        if (cpuForm) {
            cpuForm.reset();
            
            // 清除编辑模式
            delete cpuForm.dataset.editId;
            
            // 恢复提交按钮文本
            const submitButton = cpuForm.querySelector('button[type="submit"]');
            if (submitButton) {
                submitButton.innerHTML = '<i class="fas fa-save mr-2"></i> 保存CPU信息';
            }
            
            // 隐藏图片预览
            const imagePreviewContainer = document.getElementById('imagePreviewContainer');
            if (imagePreviewContainer) {
                imagePreviewContainer.classList.add('hidden');
            }
        }
    }

    // 处理图片上传
    function handleImageUpload(e) {
        console.log('上传CPU图片');
        
        const file = e.target.files[0];
        if (!file) return;
        
        // 验证文件类型
        const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!validTypes.includes(file.type)) {
            if (typeof showErrorMessage === 'function') {
                showErrorMessage('请选择有效的图片文件（JPG, PNG, GIF, WEBP）');
            } else {
                alert('请选择有效的图片文件（JPG, PNG, GIF, WEBP）');
            }
            e.target.value = ''; // 清除文件选择
            return;
        }
        
        // 验证文件大小（最大2MB）
        if (file.size > 2 * 1024 * 1024) {
            if (typeof showErrorMessage === 'function') {
                showErrorMessage('图片文件大小不能超过2MB');
            } else {
                alert('图片文件大小不能超过2MB');
            }
            e.target.value = ''; // 清除文件选择
            return;
        }
        
        // 显示图片预览
        const reader = new FileReader();
        reader.onload = function(event) {
            const imagePreviewContainer = document.getElementById('imagePreviewContainer');
            const imagePreview = document.getElementById('imagePreview');
            
            if (imagePreviewContainer && imagePreview) {
                imagePreview.src = event.target.result;
                imagePreviewContainer.classList.remove('hidden');
                
                // 添加点击预览功能
                imagePreview.classList.add('cursor-pointer');
                imagePreview.onclick = function() {
                    openImageFullscreen(event.target.result);
                };
            }
        };
        reader.readAsDataURL(file);
    }
    
    // 移除图片
    function removeImage() {
        console.log('移除CPU图片');
        
        // 清空文件输入
        const cpuImage = document.getElementById('cpuImage');
        if (cpuImage) {
            cpuImage.value = '';
        }
        
        // 隐藏预览
        const imagePreviewContainer = document.getElementById('imagePreviewContainer');
        if (imagePreviewContainer) {
            imagePreviewContainer.classList.add('hidden');
        }
        
        // 清除图片URL（如果是编辑模式）
        const cpuForm = document.getElementById('cpuForm');
        if (cpuForm && cpuForm.dataset.editId) {
            // 在编辑模式下，可以添加标记表示已移除图片
            cpuForm.dataset.removeImage = 'true';
        }
    }
    
    // 解析CPU信息
    function parseCpuInfo() {
        // 实现解析CPU信息功能
        console.log('解析CPU信息');
    }

    // 显示错误消息
    function showErrorMessage(message) {
        console.error(message);
        // 如果页面上有提示区域，可以显示错误消息
        const errorAlert = document.getElementById('errorAlert');
        if (errorAlert) {
            errorAlert.textContent = message;
            errorAlert.classList.remove('hidden');
        setTimeout(() => {
                errorAlert.classList.add('hidden');
            }, 5000);
        } else {
            // 如果没有错误提示区域，使用Toast
            showToast(message, 'error');
        }
    }
    
    // 显示Toast消息提示
    function showToast(message, type = 'info') {
        // 如果已有Toast，先移除
        const existingToast = document.getElementById('toast-notification');
        if (existingToast) {
            existingToast.remove();
        }
        
        // 创建Toast元素
        const toast = document.createElement('div');
        toast.id = 'toast-notification';
        
        // 设置样式和内容
        let bgColor, textColor, icon;
        switch (type) {
            case 'success':
                bgColor = 'bg-green-500';
                textColor = 'text-white';
                icon = '<i class="fas fa-check-circle mr-2"></i>';
                break;
            case 'error':
                bgColor = 'bg-red-500';
                textColor = 'text-white';
                icon = '<i class="fas fa-exclamation-circle mr-2"></i>';
                break;
            case 'warning':
                bgColor = 'bg-yellow-500';
                textColor = 'text-gray-800';
                icon = '<i class="fas fa-exclamation-triangle mr-2"></i>';
                break;
            default:
                bgColor = 'bg-blue-500';
                textColor = 'text-white';
                icon = '<i class="fas fa-info-circle mr-2"></i>';
        }
        
        // 设置Toast样式
        toast.className = `fixed top-4 left-1/2 transform -translate-x-1/2 z-50 ${bgColor} ${textColor} px-6 py-3 rounded-lg shadow-lg flex items-center opacity-0 transition-opacity duration-300`;
        toast.innerHTML = `${icon}${message}`;
        
        // 添加到页面
        document.body.appendChild(toast);
        
        // 显示动画
        setTimeout(() => {
            toast.classList.add('opacity-100');
        }, 10);
        
        // 自动隐藏
        setTimeout(() => {
            toast.classList.remove('opacity-100');
            toast.classList.add('opacity-0');
            setTimeout(() => {
                toast.remove();
            }, 300);
        }, 3000);
    }
});

// 初始化权限控制
function initPermissions() {
    console.log('初始化权限控制...');
    
    // 如果没有权限控制模块，发出警告
    if (typeof setupPermissionBasedUI !== 'function') {
        console.warn('警告：权限控制模块未加载！请确保已引入permission-helper.js');
        return;
    }
    
    // 执行权限控制初始化
    setupPermissionBasedUI().then(() => {
        // 检查编辑按钮
        const editButtons = document.querySelectorAll('.btn-edit, [data-action="edit"]');
        const deleteButtons = document.querySelectorAll('.btn-delete, [data-action="delete"]');
        
        // 根据用户角色处理按钮
        isAdmin().then(isAdminUser => {
            if (!isAdminUser) {
                // 禁用表单
                const form = document.querySelector('form');
                if (form) {
                    const inputs = form.querySelectorAll('input, select, textarea');
                    inputs.forEach(input => {
                        input.setAttribute('disabled', 'disabled');
                    });
                    
                    const submitBtn = form.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        submitBtn.setAttribute('disabled', 'disabled');
                        submitBtn.title = '只有管理员可以添加或修改数据';
                    }
                }
                
                // 隐藏或禁用编辑、删除按钮，但保留查看详情按钮
                editButtons.forEach(btn => {
                    btn.style.display = 'none';
                });
                
                deleteButtons.forEach(btn => {
                    btn.style.display = 'none';
                });
                
                // 确保查看详情按钮可见
                const viewButtons = document.querySelectorAll('.btn-view, [title="查看详情"]');
                viewButtons.forEach(btn => {
                    btn.style.display = 'inline-flex';
                    btn.style.visibility = 'visible';
                });
                
                // 为所有删除操作添加权限验证拦截器
                interceptDeleteOperations();
        } else {
                // 添加管理员标识
                const header = document.querySelector('h1, h2');
                if (header) {
                    const adminBadge = document.createElement('span');
                    adminBadge.className = 'bg-green-500 text-white text-xs px-2 py-1 rounded ml-2';
                    adminBadge.innerText = '管理员';
                    header.appendChild(adminBadge);
                }
            }
        });
    });
}

// 拦截所有删除操作的请求
function interceptDeleteOperations() {
    // 保存原始的fetch函数
    const originalFetch = window.fetch;
    
    // 重写fetch函数以拦截删除请求
    window.fetch = async function(url, options = {}) {
        // 检查是否为删除操作
        if (options && options.method === 'DELETE') {
            console.log('拦截到删除操作请求:', url);
            
            // 检查用户权限
            const isAdminUser = await isAdmin();
            if (!isAdminUser) {
                console.warn('权限不足，普通用户无法执行删除操作');
                // 返回权限错误响应
                return Promise.resolve(new Response(JSON.stringify({
                    error: '权限不足',
                    message: '只有管理员可以删除数据'
                }), {
                    status: 403,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }));
            }
            
            // 确保删除请求包含Authorization头
            const token = localStorage.getItem('token');
            if (token) {
                // 初始化headers对象（如果不存在）
                options.headers = options.headers || {};
                
                // 如果options.headers是Headers对象，将其转换为普通对象
                if (options.headers instanceof Headers) {
                    const plainHeaders = {};
                    options.headers.forEach((value, key) => {
                        plainHeaders[key] = value;
                    });
                    options.headers = plainHeaders;
                }
                
                // 添加Authorization头
                options.headers['Authorization'] = `Bearer ${token}`;
            } else {
                console.warn('未找到token，可能导致后端权限验证失败');
            }
        }
        
        // 对于非删除请求或管理员的删除请求，正常执行
        return originalFetch.apply(this, [url, options]);
    };
    
    // 添加全局点击事件拦截
    document.addEventListener('click', async function(event) {
        // 查找可能触发删除操作的元素
        const deleteButton = event.target.closest('.btn-delete, [data-action="delete"]');
        if (deleteButton) {
            const isAdminUser = await isAdmin();
            if (!isAdminUser) {
                // 阻止事件传播
                event.preventDefault();
                event.stopPropagation();
                console.warn('权限不足，普通用户无法执行删除操作');
                
                // 可选：显示提示消息（使用toast或alert）
                if (typeof showToast === 'function') {
                    showToast('只有管理员可以删除数据', 'error');
                } else {
                    console.error('权限不足，只有管理员可以删除数据');
                }
                
                return false;
            }
        }
    }, true);
}

// 页面加载完成后执行权限初始化
document.addEventListener('DOMContentLoaded', initPermissions);
