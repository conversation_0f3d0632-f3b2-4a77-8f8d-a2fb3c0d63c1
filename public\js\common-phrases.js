// 定义一个全局变量追踪当前是否有活跃的预览
let activeViewer = null;

document.addEventListener('DOMContentLoaded', function() {
    // DOM元素
    const phraseForm = document.getElementById('phraseForm');
    const phraseList = document.getElementById('phraseList');
    const editModal = document.getElementById('editModal');
    const editForm = document.getElementById('editForm');
    const deleteBtn = document.getElementById('deleteBtn');
    const resetBtn = document.getElementById('resetBtn');
    const closeEditModal = document.getElementById('closeEditModal');
    const filterCategory = document.getElementById('filterCategory');
    const filterType = document.getElementById('filterType');
    const searchTerm = document.getElementById('searchTerm');
    const phraseCount = document.getElementById('phraseCount');
    const previewModal = document.getElementById('previewModal');
    const previewContent = document.getElementById('previewContent');
    const closePreviewModal = document.getElementById('closePreviewModal');
    const resetFiltersBtn = document.getElementById('resetFiltersBtn');
    const themeToggle = document.getElementById('themeToggle');
    const themeIcon = document.getElementById('themeIcon');

    // 话术数据
    let phrases = [];
    let categories = [];
    let currentEditId = null;
    
    // 主题切换功能
    const initTheme = () => {
        // 检查本地存储的主题偏好
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme === 'dark' || (!savedTheme && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            document.documentElement.classList.add('dark');
            themeIcon.classList.remove('fa-sun');
            themeIcon.classList.add('fa-moon');
        } else {
            document.documentElement.classList.remove('dark');
            themeIcon.classList.remove('fa-moon');
            themeIcon.classList.add('fa-sun');
        }
    };
    
    // 初始化主题
    initTheme();
    
    // 主题切换事件
    themeToggle.addEventListener('click', () => {
        const isDark = document.documentElement.classList.toggle('dark');
        if (isDark) {
            themeIcon.classList.remove('fa-sun');
            themeIcon.classList.add('fa-moon');
            localStorage.setItem('theme', 'dark');
        } else {
            themeIcon.classList.remove('fa-moon');
            themeIcon.classList.add('fa-sun');
            localStorage.setItem('theme', 'light');
        }
    });
    
    // 获取话术数据
    const fetchPhrases = async () => {
        try {
            phraseList.innerHTML = `
                <div class="text-center text-gray-500 dark:text-gray-400 py-8">
                    <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                    <p>正在加载话术...</p>
                </div>
            `;
            
            const response = await fetchWithAuth('/api/common-phrases');
            if (!response || !response.ok) throw new Error('获取话术失败');
            
            phrases = await response.json();
            renderPhrases();
            updatePhraseCount();
            
            // 获取分类列表
            await fetchCategories();
        } catch (error) {
            console.error('获取话术失败:', error);
            showToast('获取话术失败', 'error');
            
            phraseList.innerHTML = `
                <div class="text-center text-gray-500 dark:text-gray-400 py-8">
                    <i class="fas fa-exclamation-triangle text-2xl mb-2 text-yellow-500"></i>
                    <p>加载话术失败，请刷新页面重试</p>
                </div>
            `;
        }
    };
    
    // 获取分类列表
    const fetchCategories = async () => {
        try {
            const response = await fetchWithAuth('/api/common-phrases/meta/categories');
            if (!response || !response.ok) throw new Error('获取分类失败');
            
            categories = await response.json();
            
            // 填充分类下拉列表
            const categoryList = document.getElementById('categoryList');
            const editCategoryList = document.getElementById('editCategoryList');
            categoryList.innerHTML = '';
            editCategoryList.innerHTML = '';
            filterCategory.innerHTML = '<option value="">所有分类</option>';
            
            categories.forEach(category => {
                categoryList.innerHTML += `<option value="${category}">`;
                editCategoryList.innerHTML += `<option value="${category}">`;
                filterCategory.innerHTML += `<option value="${category}">${category}</option>`;
            });
        } catch (error) {
            console.error('获取分类失败:', error);
        }
    };
    
    // 渲染话术列表
    const renderPhrases = () => {
        if (phrases.length === 0) {
            phraseList.innerHTML = `
                <div class="text-center text-gray-500 dark:text-gray-400 py-8">
                    <i class="fas fa-comment-slash text-2xl mb-2"></i>
                    <p>暂无话术，请添加新的话术</p>
                </div>
            `;
            return;
        }
        
        // 应用筛选
        const categoryFilter = filterCategory.value;
        const typeFilter = filterType.value;
        const search = searchTerm.value.toLowerCase();
        
        const filteredPhrases = phrases.filter(phrase => {
            const matchCategory = !categoryFilter || phrase.category === categoryFilter;
            const matchType = !typeFilter || phrase.type === typeFilter;

            // 全字段搜索：搜索内容、分类、标签
            const matchSearch = !search ||
                phrase.content.toLowerCase().includes(search) ||
                (phrase.category && phrase.category.toLowerCase().includes(search)) ||
                (phrase.tags && phrase.tags.toLowerCase().includes(search));

            return matchCategory && matchType && matchSearch;
        });
        
        if (filteredPhrases.length === 0) {
            phraseList.innerHTML = `
                <div class="text-center text-gray-500 dark:text-gray-400 py-8">
                    <i class="fas fa-filter text-2xl mb-2"></i>
                    <p>没有符合筛选条件的话术</p>
                </div>
            `;
            return;
        }
        
        phraseList.innerHTML = '';
        
        filteredPhrases.forEach(phrase => {
            const card = document.createElement('div');
            card.className = 'bg-gray-50 dark:bg-gray-700 p-3 rounded-lg border border-gray-200 dark:border-gray-600 transition-all hover:shadow-md fade-in';
            
            // 设置话术类型图标
            let typeIcon = 'fa-comment';
            let typeText = '文本';
            
            switch (phrase.type) {
                case 'link':
                    typeIcon = 'fa-link';
                    typeText = '链接';
                    break;
                case 'image':
                    typeIcon = 'fa-image';
                    typeText = '图片';
                    break;
                case 'file':
                    typeIcon = 'fa-file';
                    typeText = '文件';
                    break;
            }
            
            // 设置预览按钮（只对图片和文件显示）
            let previewButton = '';
            if ((phrase.type === 'image' || phrase.type === 'file') && phrase.file_url) {
                previewButton = `
                    <button class="preview-btn text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300" data-id="${phrase.id}">
                        <i class="fas fa-eye"></i>
                    </button>
                `;
            }
            
            // 构建图片预览HTML
            let imagePreviewHtml = '';
            if (phrase.type === 'image' && phrase.file_url) {
                imagePreviewHtml = `
                    <div class="mt-3 mb-2 group relative">
                        <img src="${phrase.file_url}" alt="图片预览" class="max-w-full max-h-32 rounded border border-gray-200 dark:border-gray-700 cursor-pointer hover:opacity-90 transition-opacity image-preview" onclick="openImageFullscreen('${phrase.file_url}')">
                        <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity flex space-x-1">
                            <button onclick="openImageFullscreen('${phrase.file_url}')" class="bg-blue-600 hover:bg-blue-700 text-white rounded-full p-2 z-20" title="预览大图">
                                <i class="fas fa-search-plus"></i>
                            </button>
                            <a href="${phrase.file_url}" download class="bg-indigo-600 hover:bg-indigo-700 text-white rounded-full p-2 z-20 inline-block" title="下载图片">
                                <i class="fas fa-download"></i>
                            </a>
                        </div>
                    </div>
                `;
            }
            
            // 格式化创建时间
            const createdDate = new Date(phrase.created_at);
            const formattedDate = createdDate.toLocaleDateString('zh-CN') + ' ' + 
                                 createdDate.toLocaleTimeString('zh-CN', {hour: '2-digit', minute:'2-digit'});
            
            card.innerHTML = `
                <div class="phrase-card">
                    <div class="flex flex-col sm:flex-row justify-between">
                        <div class="flex items-start space-x-2 mb-2 sm:mb-0 flex-1">
                            <div class="text-indigo-500 mt-1 hidden sm:block">
                                <i class="fas ${typeIcon}"></i>
                            </div>
                            <div class="flex-1">
                                <div class="text-sm text-gray-900 dark:text-gray-100 whitespace-pre-wrap selectable-text" title="点击可选择文本，双击可全选">${phrase.content}</div>
                                ${imagePreviewHtml}
                                <div class="flex flex-wrap mt-2 gap-1">
                                    ${phrase.category ? `
                                    <span class="px-2 py-0.5 text-xs bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200 rounded-full">
                                        <i class="fas fa-folder-open text-xs mr-1"></i> ${phrase.category}
                                    </span>` : ''}
                                    <span class="px-2 py-0.5 text-xs bg-gray-100 dark:bg-gray-600 text-gray-800 dark:text-gray-300 rounded-full">
                                        <i class="fas ${typeIcon} text-xs mr-1"></i> ${typeText}
                                    </span>
                                    ${phrase.tags ? phrase.tags.split(',').map(tag => `
                                    <span class="px-2 py-0.5 text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full">
                                        <i class="fas fa-tag text-xs mr-1"></i> ${tag.trim()}
                                    </span>`).join('') : ''}
                                    <span class="px-2 py-0.5 text-xs bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded-full">
                                        <i class="fas fa-chart-line text-xs mr-1"></i> 使用 ${phrase.usage_count} 次
                                    </span>
                                </div>
                                <div class="text-xs text-gray-500 dark:text-gray-400 mt-2">
                                    <i class="far fa-clock mr-1"></i> ${formattedDate}
                                </div>
                            </div>
                        </div>
                        <div class="flex sm:flex-col flex-row-reverse justify-start sm:space-y-2 sm:ml-2 mt-2 sm:mt-0 space-x-2 space-x-reverse sm:space-x-0 action-buttons">
                            ${previewButton}
                            <button class="use-btn text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700" data-id="${phrase.id}" title="${phrase.type === 'image' ? '复制图片' : '复制到剪贴板'}">
                                <i class="fas fa-clipboard"></i>
                            </button>
                            <button class="edit-btn text-indigo-600 dark:text-indigo-400 hover:text-indigo-800 dark:hover:text-indigo-300 p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700" data-id="${phrase.id}" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="delete-btn text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700" data-id="${phrase.id}" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
            
            // 编辑按钮事件
            card.querySelector('.edit-btn').addEventListener('click', () => {
                openEditModal(phrase.id);
            });
            
            // 复制到剪贴板按钮事件
            card.querySelector('.use-btn').addEventListener('click', async () => {
                incrementUsage(phrase.id);
                
                // 如果是图片类型，则复制图片
                if (phrase.type === 'image' && phrase.file_url) {
                    const useBtn = card.querySelector('.use-btn');
                    const originalIcon = useBtn.innerHTML;
                    const originalTitle = useBtn.getAttribute('title');
                    
                    // 显示加载状态
                    useBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                    useBtn.setAttribute('title', '正在处理图片...');
                    
                    // 显示处理中提示
                    showToast('正在准备图片，请稍候...', 'info');
                    
                    // 调用复制图片函数
                    const success = await copyImageToClipboard(phrase.file_url);
                    
                    // 恢复按钮状态并给予反馈
                    if (success) {
                        useBtn.innerHTML = '<i class="fas fa-check text-green-500"></i>';
                        useBtn.setAttribute('title', '图片已复制!');
                        showToast('图片已成功复制到剪贴板', 'success');
                        setTimeout(() => {
                            useBtn.innerHTML = originalIcon;
                            useBtn.setAttribute('title', originalTitle);
                        }, 1500);
                    } else {
                        useBtn.innerHTML = '<i class="fas fa-times text-red-500"></i>';
                        useBtn.setAttribute('title', '复制失败，请尝试预览后复制');
                        showToast('复制图片失败，请尝试预览后复制或下载', 'error');
                        setTimeout(() => {
                            useBtn.innerHTML = originalIcon;
                            useBtn.setAttribute('title', originalTitle);
                        }, 1500);
                    }
                    return;
                }
                
                // 对于文本内容，使用原有的复制方式
                const textArea = document.createElement("textarea");
                textArea.style.position = 'fixed';
                textArea.style.opacity = '0';
                textArea.style.pointerEvents = 'none';
                textArea.style.left = '-9999px';
                textArea.style.top = '0px';
                textArea.value = phrase.content;
                document.body.appendChild(textArea);
                
                textArea.focus();
                textArea.select();
                
                try {
                    const successful = document.execCommand('copy');
                    
                    // 添加复制成功的视觉反馈
                    const useBtn = card.querySelector('.use-btn');
                    const originalIcon = useBtn.innerHTML;
                    useBtn.innerHTML = '<i class="fas fa-check text-green-500"></i>';
                    setTimeout(() => {
                        useBtn.innerHTML = originalIcon;
                    }, 1000);
                    
                    const msg = successful ? '已复制话术到剪贴板' : '复制失败，请手动复制';
                    showToast(msg, successful ? 'success' : 'error');
                } catch (err) {
                    // 如果execCommand失败，尝试使用navigator.clipboard
                    navigator.clipboard.writeText(phrase.content)
                        .then(() => {
                            showToast('已复制话术到剪贴板', 'success');
                            // 添加复制成功的视觉反馈
                            const useBtn = card.querySelector('.use-btn');
                            const originalIcon = useBtn.innerHTML;
                            useBtn.innerHTML = '<i class="fas fa-check text-green-500"></i>';
                            setTimeout(() => {
                                useBtn.innerHTML = originalIcon;
                            }, 1000);
                        })
                        .catch(err => {
                            console.error('复制失败:', err);
                            showToast('复制失败，请手动复制', 'error');
                        });
                } finally {
                    // 移除临时文本区域
                    document.body.removeChild(textArea);
                }
            });
            
            // 删除按钮事件
            card.querySelector('.delete-btn').addEventListener('click', () => {
                if (confirm('确定要删除这条话术吗？此操作不可恢复。')) {
                    deletePhrase(phrase.id);
                }
            });
            
            // 预览按钮事件（如果存在）
            const previewBtn = card.querySelector('.preview-btn');
            if (previewBtn) {
                previewBtn.addEventListener('click', () => {
                    openPreviewModal(phrase);
                });
            }
            
            phraseList.appendChild(card);
        });
    };
    
    // 增加使用次数
    const incrementUsage = async (id) => {
        try {
            const response = await fetchWithAuth(`/api/common-phrases/${id}/use`, {
                method: 'POST'
            });
            
            if (!response || !response.ok) throw new Error('更新使用次数失败');
            
            // 更新本地数据
            const phrase = phrases.find(p => p.id === id);
            if (phrase) {
                phrase.usage_count++;
                renderPhrases();
            }
        } catch (error) {
            console.error('增加使用次数失败:', error);
        }
    };
    
    // 打开编辑弹窗
    const openEditModal = async (id) => {
        try {
            currentEditId = id;
            const phrase = phrases.find(p => p.id === id);
            
            if (!phrase) {
                showToast('找不到话术信息', 'error');
                return;
            }
            
            document.getElementById('editId').value = phrase.id;
            document.getElementById('editContent').value = phrase.content;
            document.getElementById('editType').value = phrase.type;
            document.getElementById('editCategory').value = phrase.category || '';
            document.getElementById('editTags').value = phrase.tags || '';
            
            // 显示当前文件信息
            const currentFileDisplay = document.getElementById('currentFileDisplay');
            if (phrase.file_url) {
                const fileName = phrase.file_url.split('/').pop();
                
                if (phrase.type === 'image') {
                    // 如果是图片，显示图片预览
                    currentFileDisplay.innerHTML = `
                        <div>
                            <div class="flex items-center space-x-2 mb-2">
                                <i class="fas fa-image text-indigo-500"></i>
                                <a href="${phrase.file_url}" target="_blank" class="text-indigo-600 dark:text-indigo-400 hover:underline truncate max-w-xs">${fileName}</a>
                            </div>
                            <div class="mt-2 relative group">
                                <img src="${phrase.file_url}" alt="图片预览" class="max-w-full max-h-40 rounded border border-gray-300 dark:border-gray-600 cursor-pointer hover:opacity-90 transition-opacity" onclick="openImageFullscreen('${phrase.file_url}')">
                                <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                    <button onclick="openImageFullscreen('${phrase.file_url}')" class="bg-blue-600 hover:bg-blue-700 text-white rounded-full p-2" title="预览大图">
                                        <i class="fas fa-search-plus"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;
                } else {
                    // 如果是其他类型文件
                    currentFileDisplay.innerHTML = `
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-file text-indigo-500"></i>
                            <a href="${phrase.file_url}" target="_blank" class="text-indigo-600 dark:text-indigo-400 hover:underline truncate max-w-xs">${fileName}</a>
                        </div>
                    `;
                }
            } else {
                currentFileDisplay.innerHTML = '<span class="text-gray-500 dark:text-gray-400">无文件</span>';
            }
            
            // 显示弹窗
            editModal.classList.remove('hidden');
        } catch (error) {
            console.error('打开编辑弹窗失败:', error);
            showToast('加载话术详情失败', 'error');
        }
    };
    
    // 打开预览弹窗
    const openPreviewModal = (phrase) => {
        if (!phrase.file_url) {
            showToast('没有可预览的文件', 'warning');
            return;
        }
        
        previewContent.innerHTML = '';
        
        if (phrase.type === 'image') {
            // 显示图片
            previewContent.innerHTML = `
                <div class="flex flex-col items-center">
                    <div class="relative w-full">
                        <img src="${phrase.file_url}" alt="预览图片" class="max-w-full max-h-[60vh] object-contain mx-auto">
                    </div>
                    <div class="mt-4 flex flex-wrap justify-center gap-3 w-full">
                        <button id="copyImageBtn" class="px-4 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center min-h-[44px] flex-1">
                            <i class="fas fa-clipboard mr-1"></i> 复制图片
                        </button>
                        <a href="${phrase.file_url}" download class="px-4 py-3 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 flex items-center justify-center min-h-[44px] flex-1">
                            <i class="fas fa-download mr-1"></i> 下载图片
                        </a>
                    </div>
                </div>
            `;
            
            // 添加复制图片的点击事件
            setTimeout(() => {
                const copyImageBtn = document.getElementById('copyImageBtn');
                if (copyImageBtn) {
                    copyImageBtn.addEventListener('click', async (e) => {
                        // 阻止事件传播和默认行为
                        if (e) {
                            e.stopPropagation();
                            e.preventDefault();
                        }
                        
                        console.log('预览模式: 复制图片按钮被点击');
                        
                        // 显示加载状态
                        const originalText = copyImageBtn.innerHTML;
                        const originalBgClass = copyImageBtn.className;
                        copyImageBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> 处理中...';
                        copyImageBtn.setAttribute('disabled', 'disabled');
                        copyImageBtn.className = copyImageBtn.className.replace('bg-green-600', 'bg-gray-500').replace('hover:bg-green-700', '');
                        
                        // 显示处理中提示
                        showToast('正在准备图片，请稍候...', 'info');
                        
                        // 调用复制图片函数
                        const success = await copyImageToClipboard(phrase.file_url);
                        
                        if (success) {
                            copyImageBtn.innerHTML = '<i class="fas fa-check mr-1"></i> 已复制';
                            copyImageBtn.className = copyImageBtn.className.replace('bg-gray-500', 'bg-green-700');
                            showToast('图片已成功复制到剪贴板', 'success');
                            
                            setTimeout(() => {
                                copyImageBtn.innerHTML = originalText;
                                copyImageBtn.className = originalBgClass;
                                copyImageBtn.removeAttribute('disabled');
                            }, 1500);
                        } else {
                            copyImageBtn.innerHTML = '<i class="fas fa-times mr-1"></i> 复制失败';
                            showToast('复制图片失败，请尝试下载后使用', 'error');
                            
                            setTimeout(() => {
                                copyImageBtn.innerHTML = originalText;
                                copyImageBtn.className = originalBgClass;
                                copyImageBtn.removeAttribute('disabled');
                            }, 1500);
                        }
                    });
                }
            }, 100);
        } else {
            // 显示文件下载链接
            const fileName = phrase.file_url.split('/').pop();
            previewContent.innerHTML = `
                <div class="bg-white dark:bg-gray-800 p-6 rounded-lg text-center">
                    <i class="fas fa-file-alt text-5xl text-indigo-500 mb-4"></i>
                    <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">${fileName}</h3>
                    <p class="text-gray-600 dark:text-gray-400 mb-4">此文件不支持直接预览</p>
                    <a href="${phrase.file_url}" download class="inline-block px-4 py-3 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 min-h-[44px] flex items-center justify-center">
                        <i class="fas fa-download mr-1"></i> 下载文件
                    </a>
                </div>
            `;
        }
        
        previewModal.classList.remove('hidden');
    };
    
    // 复制图片到剪贴板 - 增强移动端兼容性
    const copyImageToClipboard = async (imgUrl) => {
        try {
            console.log('正在复制图片:', imgUrl);
            
            // 确保使用正确的URL（处理相对路径）
            const fullUrl = new URL(imgUrl, window.location.origin).href;
            console.log('完整URL:', fullUrl);
            
            // 获取图片数据
            const response = await fetchWithAuth(fullUrl, {
                mode: 'cors',
                cache: 'no-cache',
                headers: {
                    'Cache-Control': 'no-cache'
                }
            });
            
            if (!response || !response.ok) {
                console.error('获取图片失败:', response.status, response.statusText);
                showToast('获取图片失败，请稍后重试', 'error');
                return false;
            }
            
            const blob = await response.blob();
            console.log('获取到图片Blob:', blob.type, blob.size, '字节');
            
            // 确认图片MIME类型
            if (!blob.type.startsWith('image/')) {
                console.error('不是有效的图片类型:', blob.type);
                showToast('无效的图片格式', 'error');
                return false;
            }
            
            // 检测是否在移动端
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            
            // 尝试使用现代Clipboard API (首选方法)
            if (!isMobile && window.ClipboardItem && navigator.clipboard && navigator.clipboard.write) {
                try {
                    // 创建ClipboardItem对象
                    const item = new ClipboardItem({
                        [blob.type]: blob
                    });
                    
                    console.log('准备写入剪贴板:', item);
                    
                    // 写入剪贴板
                    await navigator.clipboard.write([item]);
                    
                    console.log('图片已成功写入剪贴板 (API方式)');
                    showToast('图片已复制到剪贴板', 'success');
                    return true;
                } catch (err) {
                    console.warn('使用ClipboardItem API复制失败，尝试备选方法:', err);
                    // 继续尝试其他方法
                }
            } 
            
            // 移动设备特殊处理
            if (isMobile) {
                // 创建下载链接
                const url = URL.createObjectURL(blob);
                showToast('移动端暂不支持直接复制图片，已提供下载选项', 'info');
                
                // 创建下载链接
                const link = document.createElement('a');
                link.href = url;
                link.download = imgUrl.split('/').pop() || 'image.png';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                // 释放URL
                setTimeout(() => URL.revokeObjectURL(url), 100);
                return false;
            }
            
            // 备选方法：使用canvas和document.execCommand
            console.log('尝试使用Canvas方法复制图片');
            
            try {
                // 创建图片元素
                const img = new Image();
                
                // 加载图片前禁用CORS
                img.crossOrigin = 'anonymous';
                
                // 等待图片加载完成
                await new Promise((resolve, reject) => {
                    img.onload = resolve;
                    img.onerror = reject;
                    img.src = fullUrl;
                });
                
                // 创建canvas并绘制图片
                const canvas = document.createElement('canvas');
                canvas.width = img.width;
                canvas.height = img.height;
                
                // 绘制图片到canvas
                const ctx = canvas.getContext('2d');
                ctx.drawImage(img, 0, 0);
                
                // 将canvas内容转为Data URL
                let dataURL = '';
                try {
                    dataURL = canvas.toDataURL('image/png');
                } catch (canvasErr) {
                    console.error('Canvas转换失败 (可能是跨域问题):', canvasErr);
                    throw new Error('无法处理图片数据');
                }
                
                // 创建临时可见元素
                const tempContainer = document.createElement('div');
                tempContainer.style.position = 'fixed';
                tempContainer.style.left = '-9999px';
                tempContainer.style.top = '-9999px';
                tempContainer.innerHTML = `<img src="${dataURL}" alt="复制的图片">`;
                document.body.appendChild(tempContainer);
                
                // 选择临时元素
                const range = document.createRange();
                range.selectNode(tempContainer.firstChild);
                window.getSelection().removeAllRanges();
                window.getSelection().addRange(range);
                
                // 执行复制命令
                const successful = document.execCommand('copy');
                window.getSelection().removeAllRanges();
                
                // 移除临时元素
                document.body.removeChild(tempContainer);
                
                if (successful) {
                    console.log('图片已成功复制到剪贴板 (Canvas方法)');
                    showToast('图片已复制到剪贴板', 'success');
                    return true;
                } else {
                    throw new Error('execCommand复制失败');
                }
            } catch (fallbackErr) {
                console.error('备选复制方法失败:', fallbackErr);
                
                // 如果都失败了，提供下载选项
                showToast('无法直接复制图片，请使用下载选项', 'warning');
                
                // 创建下载链接
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = imgUrl.split('/').pop() || 'image.png';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                setTimeout(() => URL.revokeObjectURL(url), 100);
                
                return false;
            }
        } catch (error) {
            console.error('复制图片过程中发生错误:', error);
            showToast('图片处理失败', 'error');
            return false;
        }
    };
    
    // 更新话术数量显示
    const updatePhraseCount = () => {
        phraseCount.textContent = `${phrases.length}个话术`;
    };
    
    // 表单提交处理（添加新话术）
    phraseForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        try {
            const formData = new FormData(phraseForm);
            
            // 内容和文件检查
            const content = formData.get('content').trim();
            const fileInput = document.getElementById('file');
            const hasFile = fileInput.files && fileInput.files.length > 0;
            
            // 至少需要内容或文件其中之一
            if (!content && !hasFile) {
                showToast('请输入话术内容或上传文件/图片', 'error');
                return;
            }
            
            // 自动设置类型（系统会在后端最终决定）
            let detectedType = 'text';
            
            // 根据文件判断类型
            if (hasFile) {
                const file = fileInput.files[0];
                if (file.type.startsWith('image/')) {
                    detectedType = 'image';
                    formData.set('type', 'image'); // 设置为图片类型
                } else {
                    detectedType = 'file';
                    formData.set('type', 'file'); // 设置为文件类型
                }
            } 
            // 根据内容判断链接
            else if (content.match(/^https?:\/\//i)) {
                detectedType = 'link';
                formData.set('type', 'link'); // 设置为链接类型
            }
            
            // 发送请求
            const response = await fetchWithAuth('/api/common-phrases', {
                method: 'POST',
                body: formData
            });
            
            if (!response || !response.ok) throw new Error('添加话术失败');
            
            const result = await response.json();
            
            // 重新获取话术列表
            await fetchPhrases();
            
            // 重置表单
            phraseForm.reset();
            
            // 显示添加成功通知，包含自动识别的类型信息
            let typeText = '';
            switch (detectedType) {
                case 'text': typeText = '文本'; break;
                case 'link': typeText = '链接'; break;
                case 'image': typeText = '图片(已转换为WebP)'; break;
                case 'file': typeText = '文件'; break;
            }
            showToast(`话术添加成功，自动识别为${typeText}类型`, 'success');
        } catch (error) {
            console.error('添加话术失败:', error);
            showToast('添加话术失败，请重试', 'error');
        }
    });
    
    // 编辑表单提交处理
    editForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        try {
            const id = document.getElementById('editId').value;
            const content = document.getElementById('editContent').value.trim();
            const type = document.getElementById('editType').value;
            const category = document.getElementById('editCategory').value;
            const tags = document.getElementById('editTags').value;
            const fileInput = document.getElementById('editFile');
            const hasFile = fileInput.files && fileInput.files.length > 0;
            const hasExistingFile = document.getElementById('currentFileDisplay').innerHTML.includes('href=');
            
            // 至少需要内容或文件其中之一
            if (!content && !hasFile && !hasExistingFile) {
                showToast('请输入话术内容或上传文件/图片', 'error');
                return;
            }
            
            const formData = new FormData();
            formData.append('content', content);
            formData.append('type', type);
            formData.append('category', category);
            formData.append('tags', tags);
            
            if (fileInput.files[0]) {
                formData.append('file', fileInput.files[0]);
            }
            
            // 发送请求
            const response = await fetchWithAuth(`/api/common-phrases/${id}`, {
                method: 'PUT',
                body: formData
            });
            
            if (!response || !response.ok) throw new Error('更新话术失败');
            
            // 重新获取话术列表
            await fetchPhrases();
            
            // 关闭弹窗
            editModal.classList.add('hidden');
            showToast('话术更新成功', 'success');
        } catch (error) {
            console.error('更新话术失败:', error);
            showToast('更新话术失败，请重试', 'error');
        }
    });
    
    // 删除话术
    deleteBtn.addEventListener('click', async () => {
        try {
            if (!currentEditId) return;
            
            if (!confirm('确定要删除这条话术吗？此操作不可恢复。')) {
                return;
            }
            
            const response = await fetchWithAuth(`/api/common-phrases/${currentEditId}`, {
                method: 'DELETE'
            });
            
            if (!response || !response.ok) throw new Error('删除话术失败');
            
            // 重新获取话术列表
            await fetchPhrases();
            
            // 关闭弹窗
            editModal.classList.add('hidden');
            showToast('话术删除成功', 'success');
        } catch (error) {
            console.error('删除话术失败:', error);
            showToast('删除话术失败，请重试', 'error');
        }
    });
    
    // 重置按钮
    resetBtn.addEventListener('click', () => {
        phraseForm.reset();
    });
    
    // 关闭编辑弹窗
    closeEditModal.addEventListener('click', () => {
        editModal.classList.add('hidden');
    });
    
    // 关闭预览弹窗
    closePreviewModal.addEventListener('click', () => {
        previewModal.classList.add('hidden');
    });
    
    // 点击弹窗外部关闭
    window.addEventListener('click', (e) => {
        if (e.target === editModal) {
            editModal.classList.add('hidden');
        }
        if (e.target === previewModal) {
            previewModal.classList.add('hidden');
        }
    });
    
    // 添加移动端滑动关闭功能
    let touchStartY = 0;
    let touchEndY = 0;
    
    const handleTouchStart = (e) => {
        touchStartY = e.touches[0].clientY;
    };
    
    const handleTouchMove = (e) => {
        touchEndY = e.touches[0].clientY;
    };
    
    const handleTouchEnd = (e, modalElement) => {
        const minSwipeDistance = 100;
        const swipeDistance = touchEndY - touchStartY;
        
        if (swipeDistance > minSwipeDistance) {
            // 下滑关闭
            modalElement.classList.add('hidden');
        }
        
        // 重置
        touchStartY = 0;
        touchEndY = 0;
    };
    
    // 为编辑弹窗添加滑动关闭
    const editModalContent = editModal.querySelector('.bg-white');
    editModalContent.addEventListener('touchstart', handleTouchStart, { passive: true });
    editModalContent.addEventListener('touchmove', handleTouchMove, { passive: true });
    editModalContent.addEventListener('touchend', (e) => handleTouchEnd(e, editModal), { passive: true });
    
    // 为预览弹窗添加滑动关闭
    const previewModalContent = previewModal.querySelector('.bg-white');
    previewModalContent.addEventListener('touchstart', handleTouchStart, { passive: true });
    previewModalContent.addEventListener('touchmove', handleTouchMove, { passive: true });
    previewModalContent.addEventListener('touchend', (e) => handleTouchEnd(e, previewModal), { passive: true });
    
    // 监听筛选变化
    filterCategory.addEventListener('change', renderPhrases);
    filterType.addEventListener('change', renderPhrases);
    searchTerm.addEventListener('input', renderPhrases);

    // 重置筛选按钮事件
    resetFiltersBtn.addEventListener('click', () => {
        searchTerm.value = '';
        filterCategory.value = '';
        filterType.value = '';
        renderPhrases();
        showToast('筛选条件已重置', 'info');
    });
    
    // 文件上传相关元素
    const typeSelect = document.getElementById('type');
    const fileUploadContainer = document.getElementById('fileUploadContainer');
    const fileInfo = document.getElementById('fileInfo');
    const fileInput = document.getElementById('file');
    const addImageBtn = document.getElementById('addImageBtn');
    const addFileBtn = document.getElementById('addFileBtn');
    const fileButtonText = document.getElementById('fileButtonText');
    const selectedFileName = document.getElementById('selectedFileName');
    
    // 默认显示文件上传区域
    fileUploadContainer.style.display = 'block';
    
    // 添加图片按钮点击事件
    addImageBtn.addEventListener('click', () => {
        typeSelect.value = 'image'; // 设置类型为图片
        fileInput.accept = 'image/*'; // 只接受图片文件
        fileButtonText.textContent = '选择图片';
        fileInfo.textContent = '图片将自动转换为WebP格式以优化加载速度';
        fileInput.click(); // 触发文件选择
    });
    
    // 添加文件按钮点击事件
    addFileBtn.addEventListener('click', () => {
        typeSelect.value = 'file'; // 设置类型为文件
        fileInput.accept = '*/*'; // 接受所有类型文件
        fileButtonText.textContent = '选择文件';
        fileInfo.textContent = '支持任何类型文件，最大10MB';
        fileInput.click(); // 触发文件选择
    });
    
    // 文件选择变化事件
    const imagePreviewContainer = document.getElementById('imagePreviewContainer');
    const imagePreview = document.getElementById('imagePreview');
    
    fileInput.addEventListener('change', (e) => {
        if (fileInput.files.length > 0) {
            const file = fileInput.files[0];
            // 显示已选择的文件名
            selectedFileName.classList.remove('hidden');
            selectedFileName.querySelector('span').textContent = file.name;
            
            // 根据文件类型自动设置话术类型
            if (file.type.startsWith('image/')) {
                typeSelect.value = 'image';
                
                // 显示图片预览
                const reader = new FileReader();
                reader.onload = function(e) {
                    imagePreview.src = e.target.result;
                    imagePreviewContainer.classList.remove('hidden');

                    // 添加点击预览功能
                    imagePreview.classList.add('cursor-pointer');
                    imagePreview.onclick = function() {
                        openImageFullscreen(e.target.result);
                    };
                }
                reader.readAsDataURL(file);
            } else {
                typeSelect.value = 'file';
                imagePreviewContainer.classList.add('hidden');
            }
        } else {
            selectedFileName.classList.add('hidden');
            imagePreviewContainer.classList.add('hidden');
        }
    });
    
    // 监听话术类型变化
    typeSelect.addEventListener('change', () => {
        if (typeSelect.value === 'image') {
            fileInfo.textContent = '图片将自动转换为WebP格式以优化加载速度';
            fileButtonText.textContent = '选择图片';
            fileInput.accept = 'image/*';
        } else if (typeSelect.value === 'file') {
            fileInfo.textContent = '支持任何类型文件，最大10MB';
            fileButtonText.textContent = '选择文件';
            fileInput.accept = '*/*';
        }
    });
    
    // 显示通知提示
    function showToast(message, type = 'success') {
        const toast = document.getElementById('toast');
        const toastMessage = document.getElementById('toastMessage');
        const toastIcon = document.getElementById('toastIcon');
        
        toastMessage.textContent = message;
        
        // 设置图标和颜色
        toast.className = 'fixed bottom-4 right-4 px-4 py-2 rounded-md text-white transform transition-all duration-300 z-50';
        
        switch (type) {
            case 'success':
                toast.classList.add('bg-green-600');
                toastIcon.className = 'fas fa-check-circle mr-2';
                break;
            case 'error':
                toast.classList.add('bg-red-600');
                toastIcon.className = 'fas fa-times-circle mr-2';
                break;
            case 'warning':
                toast.classList.add('bg-yellow-500');
                toastIcon.className = 'fas fa-exclamation-circle mr-2';
                break;
            case 'info':
                toast.classList.add('bg-blue-500');
                toastIcon.className = 'fas fa-info-circle mr-2';
                break;
        }
        
        // 显示通知
        toast.classList.remove('translate-y-20', 'opacity-0');
        
        // 3秒后隐藏
        setTimeout(() => {
            toast.classList.add('translate-y-20', 'opacity-0');
        }, 3000);
    }
    
    // 处理编辑窗口中的图片和文件按钮
    const editAddImageBtn = document.getElementById('editAddImageBtn');
    const editAddFileBtn = document.getElementById('editAddFileBtn');
    const editFileInput = document.getElementById('editFile');
    const editFileButtonText = document.getElementById('editFileButtonText');
    const editSelectedFileName = document.getElementById('editSelectedFileName');
    const editTypeSelect = document.getElementById('editType');
    
    // 添加图片按钮点击事件（编辑窗口）
    editAddImageBtn.addEventListener('click', () => {
        editTypeSelect.value = 'image'; // 设置类型为图片
        editFileInput.accept = 'image/*'; // 只接受图片文件
        editFileButtonText.textContent = '选择图片';
        editFileInput.click(); // 触发文件选择
    });
    
    // 添加文件按钮点击事件（编辑窗口）
    editAddFileBtn.addEventListener('click', () => {
        editTypeSelect.value = 'file'; // 设置类型为文件
        editFileInput.accept = '*/*'; // 接受所有类型文件
        editFileButtonText.textContent = '选择文件';
        editFileInput.click(); // 触发文件选择
    });
    
    // 文件选择变化事件（编辑窗口）
    const editImagePreviewContainer = document.getElementById('editImagePreviewContainer');
    const editImagePreview = document.getElementById('editImagePreview');
    
    editFileInput.addEventListener('change', (e) => {
        if (editFileInput.files.length > 0) {
            const file = editFileInput.files[0];
            // 显示已选择的文件名
            editSelectedFileName.classList.remove('hidden');
            editSelectedFileName.querySelector('span').textContent = file.name;
            
            // 根据文件类型自动设置话术类型
            if (file.type.startsWith('image/')) {
                editTypeSelect.value = 'image';
                
                // 显示图片预览
                const reader = new FileReader();
                reader.onload = function(e) {
                    editImagePreview.src = e.target.result;
                    editImagePreviewContainer.classList.remove('hidden');

                    // 添加点击预览功能
                    editImagePreview.classList.add('cursor-pointer');
                    editImagePreview.onclick = function() {
                        openImageFullscreen(e.target.result);
                    };
                }
                reader.readAsDataURL(file);
            } else {
                editTypeSelect.value = 'file';
                editImagePreviewContainer.classList.add('hidden');
            }
        } else {
            editSelectedFileName.classList.add('hidden');
            editImagePreviewContainer.classList.add('hidden');
        }
    });
    
    // 删除话术
    const deletePhrase = async (id) => {
        try {
            const response = await fetchWithAuth(`/api/common-phrases/${id}`, {
                method: 'DELETE'
            });
            
            if (!response || !response.ok) throw new Error('删除话术失败');
            
            // 重新获取话术列表
            await fetchPhrases();
            showToast('话术删除成功', 'success');
        } catch (error) {
            console.error('删除话术失败:', error);
            showToast('删除话术失败，请重试', 'error');
        }
    };
    
    // 初始化
    const init = () => {
        // 清理localStorage中的复制话术数据
        localStorage.removeItem('copiedPhrase');
        
        // 获取话术列表
        fetchPhrases();
    };
    
    // 执行初始化
    init();
    
    // 添加全局事件委托，处理话术文本的双击全选
    document.addEventListener('dblclick', function(e) {
        if (e.target && e.target.classList.contains('selectable-text')) {
            // 创建文本选区
            const range = document.createRange();
            range.selectNodeContents(e.target);
            const selection = window.getSelection();
            selection.removeAllRanges();
            selection.addRange(range);
            
            // 显示提示
            showToast('已选择全部文本，可按Ctrl+C复制', 'info');
        }
    });
    
    // 添加文本区域点击事件，阻止事件冒泡确保文本可选
    document.addEventListener('click', function(e) {
        if (e.target && e.target.classList.contains('selectable-text')) {
            e.stopPropagation();
        }
    }, true);
});

// 图片全屏预览功能（参考CPU页面实现）
function openImageFullscreen(src) {
    if (!src) return;
    console.log('[DEBUG] Opening image in fullscreen:', src);

    // 如果已经有活跃的预览，先关闭它
    if (activeViewer) {
        try {
            activeViewer.destroy();
            activeViewer = null;
            // 找到并移除可能存在的容器
            const oldContainer = document.querySelector('.viewer-container');
            if (oldContainer && oldContainer.parentNode) {
                oldContainer.parentNode.removeChild(oldContainer);
            }
        } catch (e) {
            console.error('Error destroying existing viewer:', e);
        }
    }

    try {
        // 创建临时容器和图片元素
        const container = document.createElement('div');
        container.className = 'viewer-container';
        container.style.display = 'none';

        const img = document.createElement('img');
        img.src = src;
        img.alt = '图片预览';

        container.appendChild(img);
        document.body.appendChild(container);

        // 初始化 Viewer
        const viewer = new Viewer(img, {
            backdrop: true,          // 启用背景遮罩
            button: true,           // 显示关闭按钮
            navbar: false,          // 隐藏底部导航栏（只有一张图片时不需要）
            title: false,           // 不显示标题
            toolbar: {              // 自定义工具栏
                zoomIn: true,       // 放大按钮
                zoomOut: true,      // 缩小按钮
                oneToOne: true,     // 1:1 尺寸按钮
                reset: true,        // 重置按钮
                prev: false,        // 上一张（隐藏，因为只有一张图片）
                play: false,        // 播放按钮（隐藏）
                next: false,        // 下一张（隐藏）
                rotateLeft: true,   // 向左旋转
                rotateRight: true,  // 向右旋转
                flipHorizontal: true, // 水平翻转
                flipVertical: true,  // 垂直翻转
            },
            viewed() {
                // 图片加载完成后自动打开查看器
                if (window.innerWidth < 640) {
                    viewer.zoomTo(0.8);  // 移动设备使用较小的初始缩放比例
                } else {
                    viewer.zoomTo(1);    // 桌面设备使用正常的缩放比例
                }
            },
            hidden() {
                // 查看器关闭后移除临时元素
                viewer.destroy();
                document.body.removeChild(container);
                activeViewer = null; // 清除活跃预览实例引用
            },
            maxZoomRatio: 5,        // 最大缩放比例
            minZoomRatio: 0.1,      // 最小缩放比例
            transition: true,       // 启用过渡效果
            keyboard: true,         // 启用键盘支持
        });

        // 保存当前预览实例
        activeViewer = viewer;

        // 显示查看器
        viewer.show();
    } catch (error) {
        console.error('图片预览出错:', error);
        alert('图片预览功能发生错误，请刷新页面后重试');
    }
}