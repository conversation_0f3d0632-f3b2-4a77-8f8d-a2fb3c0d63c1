#!/usr/bin/env node

/**
 * 价格系统数据库迁移脚本启动器
 * 用于在部署或更新时运行数据库迁移
 */

const path = require('path');

// 设置项目根目录
process.chdir(path.join(__dirname, '..'));

// 引入迁移模块
const { migratePriceSchema } = require('../db/price-schema-migration');

console.log('='.repeat(50));
console.log('价格系统数据库迁移');
console.log('='.repeat(50));

// 执行迁移
migratePriceSchema()
    .then((success) => {
        if (success) {
            console.log('\n✅ 迁移成功完成！');
            console.log('现在价格记录和原始表格数据都会关联到具体用户。');
            process.exit(0);
        } else {
            console.log('\n❌ 迁移失败！');
            console.log('请检查数据库连接和权限设置。');
            process.exit(1);
        }
    })
    .catch(error => {
        console.error('\n💥 迁移过程中发生严重错误:');
        console.error(error);
        process.exit(1);
    });
