const express = require('express');
const router = express.Router();
const db = require('../config/db');
const XLSX = require('xlsx');
const path = require('path');
const fs = require('fs');

// 创建Excel模板记录表
const createExcelTemplateTable = async () => {
    try {
        await db.query(`
            CREATE TABLE IF NOT EXISTS excel_template_records (
                id INT PRIMARY KEY AUTO_INCREMENT,
                template_name VARCHAR(100) NOT NULL COMMENT '模板名称',
                remarks TEXT COMMENT '备注信息',
                data_json LONGTEXT COMMENT '表格数据JSON',
                file_path VARCHAR(255) COMMENT '保存的文件路径',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                created_by VARCHAR(50) COMMENT '创建人'
            )
        `);
        console.log('Excel模板记录表创建成功');
    } catch (error) {
        console.error('创建Excel模板记录表失败:', error);
    }
};

// 初始化表
createExcelTemplateTable();

// 读取Excel模板
router.get('/template', async (req, res) => {
    try {
        const templatePath = path.join(__dirname, '..', 'demo.xlsx');
        
        if (!fs.existsSync(templatePath)) {
            return res.status(404).json({
                success: false,
                message: '模板文件不存在'
            });
        }

        // 读取Excel文件
        const workbook = XLSX.readFile(templatePath);
        const sheetName = workbook.SheetNames[0]; // 获取第一个工作表
        const worksheet = workbook.Sheets[sheetName];
        
        // 转换为JSON格式
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
            header: 1, // 使用数组格式
            defval: '' // 空单元格默认值
        });

        res.json({
            success: true,
            data: {
                sheetName: sheetName,
                data: jsonData,
                range: worksheet['!ref'] // 数据范围
            }
        });

    } catch (error) {
        console.error('读取Excel模板失败:', error);
        res.status(500).json({
            success: false,
            message: '读取模板失败: ' + error.message
        });
    }
});

// 保存Excel数据
router.post('/save', async (req, res) => {
    try {
        const { templateName, remarks, data, sheetName } = req.body;
        
        // 获取用户信息
        let createdBy = 'anonymous';
        if (req.user && req.user.username) {
            createdBy = req.user.username;
        }

        // 验证必填字段
        if (!templateName || !remarks || !data) {
            return res.status(400).json({
                success: false,
                message: '模板名称、备注和数据不能为空'
            });
        }

        // 创建新的Excel文件
        const workbook = XLSX.utils.book_new();
        const worksheet = XLSX.utils.aoa_to_sheet(data);
        XLSX.utils.book_append_sheet(workbook, worksheet, sheetName || 'Sheet1');

        // 生成文件名
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const fileName = `${templateName}_${timestamp}.xlsx`;
        const filePath = path.join(__dirname, '..', 'uploads', 'excel_templates', fileName);
        
        // 确保目录存在
        const uploadDir = path.dirname(filePath);
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
        }

        // 保存Excel文件
        XLSX.writeFile(workbook, filePath);

        // 保存记录到数据库
        const [result] = await db.query(`
            INSERT INTO excel_template_records 
            (template_name, remarks, data_json, file_path, created_by)
            VALUES (?, ?, ?, ?, ?)
        `, [
            templateName,
            remarks,
            JSON.stringify(data),
            fileName,
            createdBy
        ]);

        res.json({
            success: true,
            message: '保存成功',
            recordId: result.insertId,
            fileName: fileName
        });

    } catch (error) {
        console.error('保存Excel数据失败:', error);
        res.status(500).json({
            success: false,
            message: '保存失败: ' + error.message
        });
    }
});

// 获取保存的记录列表
router.get('/records', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const offset = (page - 1) * limit;
        const search = req.query.search || '';

        // 构建搜索条件
        let whereClause = '';
        let queryParams = [];
        let countParams = [];

        if (search.trim()) {
            whereClause = 'WHERE template_name LIKE ? OR remarks LIKE ? OR created_by LIKE ?';
            const searchPattern = `%${search}%`;
            queryParams = [searchPattern, searchPattern, searchPattern];
            countParams = [searchPattern, searchPattern, searchPattern];
        }

        // 获取记录
        const [records] = await db.query(`
            SELECT id, template_name, remarks, file_path, created_by, created_at, updated_at
            FROM excel_template_records
            ${whereClause}
            ORDER BY created_at DESC
            LIMIT ? OFFSET ?
        `, [...queryParams, limit, offset]);

        // 获取总数
        const [countResult] = await db.query(`
            SELECT COUNT(*) as total FROM excel_template_records
            ${whereClause}
        `, countParams);

        res.json({
            success: true,
            data: records,
            pagination: {
                page: page,
                limit: limit,
                total: countResult[0].total,
                totalPages: Math.ceil(countResult[0].total / limit)
            }
        });

    } catch (error) {
        console.error('获取记录列表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取记录列表失败: ' + error.message
        });
    }
});

// 获取特定记录的详细数据
router.get('/records/:id', async (req, res) => {
    try {
        const { id } = req.params;

        const [records] = await db.query(`
            SELECT * FROM excel_template_records WHERE id = ?
        `, [id]);

        if (records.length === 0) {
            return res.status(404).json({
                success: false,
                message: '记录不存在'
            });
        }

        const record = records[0];
        
        // 解析JSON数据
        let parsedData = null;
        if (record.data_json) {
            try {
                parsedData = JSON.parse(record.data_json);
            } catch (e) {
                console.error('解析JSON数据失败:', e);
            }
        }

        res.json({
            success: true,
            data: {
                ...record,
                data_json: parsedData
            }
        });

    } catch (error) {
        console.error('获取记录详情失败:', error);
        res.status(500).json({
            success: false,
            message: '获取记录详情失败: ' + error.message
        });
    }
});

// 删除记录
router.delete('/records/:id', async (req, res) => {
    try {
        const { id } = req.params;

        // 先获取记录信息
        const [records] = await db.query(`
            SELECT file_path FROM excel_template_records WHERE id = ?
        `, [id]);

        if (records.length === 0) {
            return res.status(404).json({
                success: false,
                message: '记录不存在'
            });
        }

        // 删除文件
        const filePath = path.join(__dirname, '..', 'uploads', 'excel_templates', records[0].file_path);
        if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
        }

        // 删除数据库记录
        const [result] = await db.query('DELETE FROM excel_template_records WHERE id = ?', [id]);

        if (result.affectedRows === 0) {
            return res.status(404).json({
                success: false,
                message: '记录不存在'
            });
        }

        res.json({
            success: true,
            message: '删除成功'
        });

    } catch (error) {
        console.error('删除记录失败:', error);
        res.status(500).json({
            success: false,
            message: '删除记录失败: ' + error.message
        });
    }
});

// 下载Excel文件
router.get('/download/:id', async (req, res) => {
    try {
        const { id } = req.params;

        const [records] = await db.query(`
            SELECT template_name, file_path FROM excel_template_records WHERE id = ?
        `, [id]);

        if (records.length === 0) {
            return res.status(404).json({
                success: false,
                message: '记录不存在'
            });
        }

        const record = records[0];
        const filePath = path.join(__dirname, '..', 'uploads', 'excel_templates', record.file_path);

        if (!fs.existsSync(filePath)) {
            return res.status(404).json({
                success: false,
                message: '文件不存在'
            });
        }

        // 设置下载头
        res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(record.template_name)}.xlsx"`);
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');

        // 发送文件
        res.sendFile(filePath);

    } catch (error) {
        console.error('下载文件失败:', error);
        res.status(500).json({
            success: false,
            message: '下载文件失败: ' + error.message
        });
    }
});

module.exports = router;
