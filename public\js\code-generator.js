document.addEventListener('DOMContentLoaded', function() {
    // DOM 元素
    const addFieldBtn = document.getElementById('addFieldBtn');
    const fieldsContainer = document.getElementById('fieldsContainer');
    const previewBtn = document.getElementById('previewBtn');
    const generateBtn = document.getElementById('generateBtn');
    const resetBtn = document.getElementById('resetBtn');
    const resultModal = document.getElementById('resultModal');
    const closeResultBtn = document.getElementById('closeResultBtn');
    const viewModuleBtn = document.getElementById('viewModuleBtn');
    
    // 标签页切换
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');
    
    let generatedModuleName = '';
    
    // 初始化事件监听器
    initEventListeners();
    
    function initEventListeners() {
        // 添加字段按钮
        addFieldBtn.addEventListener('click', addField);
        
        // 预览代码按钮
        previewBtn.addEventListener('click', previewCode);
        
        // 生成模块按钮
        generateBtn.addEventListener('click', generateModule);
        
        // 重置按钮
        resetBtn.addEventListener('click', resetForm);
        
        // 标签页切换
        tabButtons.forEach(button => {
            button.addEventListener('click', () => switchTab(button.dataset.tab));
        });
        
        // 模态框关闭
        closeResultBtn.addEventListener('click', closeModal);
        viewModuleBtn.addEventListener('click', viewGeneratedModule);
        
        // 字段删除事件委托
        fieldsContainer.addEventListener('click', function(e) {
            if (e.target.closest('.remove-field-btn')) {
                removeField(e.target.closest('.field-row'));
            }
        });
        
        // 英文名称自动生成
        document.getElementById('chineseName').addEventListener('input', function() {
            const chineseName = this.value;
            const englishName = document.getElementById('englishName');
            const tableName = document.getElementById('tableName');
            
            if (chineseName && !englishName.value) {
                // 简单的中文到英文映射（可以扩展）
                const mapping = {
                    '声卡': 'soundcard',
                    '网卡': 'networkcard',
                    '光驱': 'optical',
                    '键盘': 'keyboard',
                    '鼠标': 'mouse',
                    '音响': 'speaker',
                    '耳机': 'headphone',
                    '摄像头': 'camera',
                    '打印机': 'printer',
                    '扫描仪': 'scanner'
                };
                
                const suggested = mapping[chineseName] || chineseName.toLowerCase().replace(/[^a-z0-9]/g, '');
                englishName.value = suggested;
                tableName.value = suggested + 's';
            }
        });
    }
    
    function addField() {
        const fieldRow = document.createElement('div');
        fieldRow.className = 'field-row';
        fieldRow.innerHTML = `
            <div class="grid grid-cols-1 md:grid-cols-5 gap-3">
                <div>
                    <label class="block text-xs font-medium text-gray-600 mb-1">字段名</label>
                    <input type="text" class="field-name w-full px-2 py-1 border border-gray-300 rounded text-sm" placeholder="如：model" required>
                </div>
                <div>
                    <label class="block text-xs font-medium text-gray-600 mb-1">字段类型</label>
                    <select class="field-type w-full px-2 py-1 border border-gray-300 rounded text-sm">
                        <option value="string">字符串</option>
                        <option value="integer">整数</option>
                        <option value="float">浮点数</option>
                        <option value="text">长文本</option>
                        <option value="date">日期</option>
                    </select>
                </div>
                <div>
                    <label class="block text-xs font-medium text-gray-600 mb-1">显示名称</label>
                    <input type="text" class="field-label w-full px-2 py-1 border border-gray-300 rounded text-sm" placeholder="如：型号" required>
                </div>
                <div>
                    <label class="block text-xs font-medium text-gray-600 mb-1">必填</label>
                    <select class="field-required w-full px-2 py-1 border border-gray-300 rounded text-sm">
                        <option value="false">否</option>
                        <option value="true">是</option>
                    </select>
                </div>
                <div class="flex items-end">
                    <button type="button" class="remove-field-btn bg-red-500 hover:bg-red-600 text-white px-2 py-1 rounded text-sm">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
        fieldsContainer.appendChild(fieldRow);
    }
    
    function removeField(fieldRow) {
        if (fieldsContainer.children.length > 1) {
            fieldRow.remove();
        } else {
            alert('至少需要保留一个字段');
        }
    }
    
    function switchTab(tabName) {
        // 更新按钮状态
        tabButtons.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.tab === tabName);
        });
        
        // 更新内容显示
        tabContents.forEach(content => {
            content.classList.toggle('active', content.id === tabName + 'Tab');
        });
    }
    
    function getFormData() {
        const chineseName = document.getElementById('chineseName').value.trim();
        const englishName = document.getElementById('englishName').value.trim();
        const tableName = document.getElementById('tableName').value.trim();
        const iconClass = document.getElementById('iconClass').value.trim();
        const pageDescription = document.getElementById('pageDescription').value.trim();
        
        if (!chineseName || !englishName || !tableName || !iconClass || !pageDescription) {
            throw new Error('请填写所有基本信息字段');
        }
        
        const fields = [];
        const fieldRows = fieldsContainer.querySelectorAll('.field-row');
        
        fieldRows.forEach(row => {
            const fieldName = row.querySelector('.field-name').value.trim();
            const fieldType = row.querySelector('.field-type').value;
            const fieldLabel = row.querySelector('.field-label').value.trim();
            const fieldRequired = row.querySelector('.field-required').value === 'true';

            if (!fieldName || !fieldLabel) {
                throw new Error('请填写所有字段信息');
            }

            fields.push({
                name: fieldName,
                type: fieldType,
                label: fieldLabel,
                required: fieldRequired
            });
        });
        
        if (fields.length === 0) {
            throw new Error('至少需要定义一个字段');
        }
        
        return {
            chineseName,
            englishName,
            tableName,
            iconClass,
            pageDescription,
            fields
        };
    }
    
    function previewCode() {
        try {
            const formData = getFormData();
            
            // 生成SQL代码
            const sqlCode = generateSQLCode(formData);
            document.getElementById('sqlCode').textContent = sqlCode;
            
            // 生成路由代码
            const routeCode = generateRouteCode(formData);
            document.getElementById('routeCode').textContent = routeCode;
            
            // 生成HTML代码（部分）
            const htmlCode = generateHTMLCode(formData);
            document.getElementById('htmlCode').textContent = htmlCode;
            
            // 生成JavaScript代码（部分）
            const jsCode = generateJSCode(formData);
            document.getElementById('jsCode').textContent = jsCode;
            
            // 重新高亮代码
            Prism.highlightAll();
            
            showToast('代码预览已生成', 'success');
        } catch (error) {
            showToast(error.message, 'error');
        }
    }
    
    function generateSQLCode(data) {
        let sql = `-- ${data.chineseName}表结构\n`;
        sql += `CREATE TABLE IF NOT EXISTS ${data.tableName} (\n`;
        sql += `    id INT AUTO_INCREMENT PRIMARY KEY,\n`;
        
        data.fields.forEach(field => {
            let sqlType;
            switch (field.type) {
                case 'string':
                    sqlType = 'VARCHAR(255)';
                    break;
                case 'integer':
                    sqlType = 'INT';
                    break;
                case 'float':
                    sqlType = 'DECIMAL(10,2)';
                    break;
                case 'text':
                    sqlType = 'TEXT';
                    break;
                default:
                    sqlType = 'VARCHAR(255)';
            }
            sql += `    ${field.name} ${sqlType} COMMENT '${field.label}',\n`;
        });
        
        sql += `    price DECIMAL(10,2) COMMENT '价格',\n`;
        sql += `    notes TEXT COMMENT '备注',\n`;
        sql += `    image_url VARCHAR(255) COMMENT '图片URL',\n`;
        sql += `    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n`;
        sql += `    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n`;
        sql += `);`;
        
        return sql;
    }
    
    function generateRouteCode(data) {
        let code = `const express = require('express');\n`;
        code += `const createResourceRouter = require('./resource-handler');\n\n`;
        code += `// ${data.chineseName} 资源配置\n`;
        code += `const ${data.englishName}Config = {\n`;
        code += `    resourceName: '${data.englishName}',\n`;
        code += `    tableName: '${data.tableName}',\n`;
        code += `    uploadDir: '${data.englishName}',\n`;
        code += `    defaultImage: 'images/default-${data.englishName}.png',\n`;
        code += `    searchFields: [${data.fields.slice(0, 3).map(f => `'${f.name}'`).join(', ')}],\n`;
        code += `    fieldMap: {\n`;
        
        data.fields.forEach(field => {
            code += `        ${field.name}: { type: '${field.type}' },\n`;
        });
        code += `        price: { type: 'float' },\n`;
        code += `        notes: { type: 'string' }\n`;
        code += `    }\n`;
        code += `};\n\n`;
        code += `// 创建 ${data.chineseName} 路由\n`;
        code += `const router = createResourceRouter(${data.englishName}Config);\n\n`;
        code += `module.exports = router;`;
        
        return code;
    }
    
    function generateHTMLCode(data) {
        let code = `<!-- ${data.chineseName}信息管理页面头部 -->\n`;
        code += `<header class="mb-8">\n`;
        code += `    <h1 class="text-3xl font-bold text-blue-700 flex items-center">\n`;
        code += `        <i class="${data.iconClass} mr-3"></i> ${data.chineseName}信息管理\n`;
        code += `    </h1>\n`;
        code += `    <p class="text-gray-600 mt-2">${data.pageDescription}</p>\n`;
        code += `</header>\n\n`;
        code += `<!-- 表单字段示例 -->\n`;
        
        data.fields.slice(0, 4).forEach(field => {
            code += `<div>\n`;
            code += `    <label class="block text-sm font-medium text-gray-700 mb-1">${field.label}</label>\n`;
            const inputType = field.type === 'integer' || field.type === 'float' ? 'number' : 'text';
            code += `    <input type="${inputType}" id="${field.name}" class="w-full px-3 py-2 border border-gray-300 rounded-md" placeholder="请输入${field.label}">\n`;
            code += `</div>\n`;
        });
        
        return code;
    }
    
    function generateJSCode(data) {
        let code = `// ${data.chineseName}管理JavaScript代码片段\n\n`;
        code += `// API端点常量\n`;
        code += `const API_ENDPOINTS = {\n`;
        code += `    ${data.englishName.toUpperCase()}S: '/api/${data.tableName}',\n`;
        code += `    ${data.englishName.toUpperCase()}: (id) => \`/api/${data.tableName}/\${id}\`\n`;
        code += `};\n\n`;
        code += `// 加载${data.chineseName}数据\n`;
        code += `function load${data.chineseName}s() {\n`;
        code += `    fetch(API_ENDPOINTS.${data.englishName.toUpperCase()}S)\n`;
        code += `        .then(response => response.json())\n`;
        code += `        .then(data => {\n`;
        code += `            console.log('${data.chineseName}数据:', data);\n`;
        code += `            // 处理数据显示逻辑\n`;
        code += `        })\n`;
        code += `        .catch(error => {\n`;
        code += `            console.error('加载${data.chineseName}数据失败:', error);\n`;
        code += `        });\n`;
        code += `}\n\n`;
        code += `// 表单数据收集\n`;
        code += `function getFormData() {\n`;
        code += `    return {\n`;
        
        data.fields.forEach(field => {
            code += `        ${field.name}: document.getElementById('${field.name}').value,\n`;
        });
        code += `        price: document.getElementById('price').value,\n`;
        code += `        notes: document.getElementById('notes').value\n`;
        code += `    };\n`;
        code += `}`;
        
        return code;
    }
    
    function generateModule() {
        try {
            const formData = getFormData();
            generatedModuleName = formData.englishName;
            
            showModal();
            
            // 发送生成请求到后端
            fetch('/api/code-generator/generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: JSON.stringify(formData)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('生成失败');
                }
                return response.json();
            })
            .then(result => {
                showSuccess(result.message);
            })
            .catch(error => {
                showError(error.message);
            });
            
        } catch (error) {
            showToast(error.message, 'error');
        }
    }
    
    function showModal() {
        resultModal.classList.remove('hidden');
        resultModal.classList.add('flex');
        document.getElementById('resultIcon').innerHTML = '<i class="fas fa-spinner fa-spin text-blue-500"></i>';
        document.getElementById('resultTitle').textContent = '正在生成模块...';
        document.getElementById('resultMessage').textContent = '请稍候，正在生成代码文件...';
        document.getElementById('resultActions').classList.add('hidden');
    }
    
    function showSuccess(message) {
        document.getElementById('resultIcon').innerHTML = '<i class="fas fa-check-circle text-green-500"></i>';
        document.getElementById('resultTitle').textContent = '生成成功！';
        document.getElementById('resultMessage').textContent = message;
        document.getElementById('resultActions').classList.remove('hidden');
    }
    
    function showError(message) {
        document.getElementById('resultIcon').innerHTML = '<i class="fas fa-exclamation-circle text-red-500"></i>';
        document.getElementById('resultTitle').textContent = '生成失败';
        document.getElementById('resultMessage').textContent = message;
        document.getElementById('resultActions').classList.remove('hidden');
        document.getElementById('viewModuleBtn').style.display = 'none';
    }
    
    function closeModal() {
        resultModal.classList.add('hidden');
        resultModal.classList.remove('flex');
    }
    
    function viewGeneratedModule() {
        if (generatedModuleName) {
            window.location.href = `/${generatedModuleName}-info.html`;
        }
    }
    
    function resetForm() {
        if (confirm('确定要重置表单吗？这将清除所有已填写的内容。')) {
            document.getElementById('generatorForm').reset();
            
            // 清空字段容器，只保留第一个字段
            const firstField = fieldsContainer.querySelector('.field-row');
            fieldsContainer.innerHTML = '';
            fieldsContainer.appendChild(firstField);
            
            // 重置代码预览
            document.getElementById('sqlCode').textContent = '-- 请先配置字段并点击预览代码';
            document.getElementById('routeCode').textContent = '// 请先配置字段并点击预览代码';
            document.getElementById('htmlCode').textContent = '<!-- 请先配置字段并点击预览代码 -->';
            document.getElementById('jsCode').textContent = '// 请先配置字段并点击预览代码';
            
            showToast('表单已重置', 'info');
        }
    }
    
    function showToast(message, type = 'info') {
        // 简单的提示实现
        const toast = document.createElement('div');
        toast.className = `fixed top-4 right-4 px-4 py-2 rounded-md text-white z-50 ${
            type === 'success' ? 'bg-green-500' : 
            type === 'error' ? 'bg-red-500' : 
            type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500'
        }`;
        toast.textContent = message;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, 3000);
    }
});
