.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.editable-table {
    border-collapse: collapse;
    width: 100%;
    font-size: 14px;
}

.editable-table th,
.editable-table td {
    border: 1px solid #d1d5db;
    padding: 8px;
    text-align: left;
    min-width: 100px;
}

.editable-table th {
    background-color: #f3f4f6;
    font-weight: bold;
    position: sticky;
    top: 0;
    z-index: 10;
}

.editable-table input {
    width: 100%;
    border: none;
    background: transparent;
    padding: 4px;
    font-size: 14px;
}

.editable-table input:focus {
    outline: 2px solid #3b82f6;
    background-color: #eff6ff;
}

.table-container {
    max-height: 70vh;
    overflow: auto;
    border: 1px solid #d1d5db;
    border-radius: 8px;
}

.paste-area {
    min-height: 120px;
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    background-color: #f9fafb;
    margin-bottom: 20px;
}

.paste-area:hover {
    border-color: #3b82f6;
    background-color: #eff6ff;
}

.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 10% auto;
    padding: 20px;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: black;
}