const express = require('express');
const router = express.Router();
const db = require('../config/db');
const multer = require('multer');
const sharp = require('sharp');
const path = require('path');
const fs = require('fs');

// 确保上传目录存在
const fanUploadDir = './public/uploads/fan';
if (!fs.existsSync(fanUploadDir)) {
    fs.mkdirSync(fanUploadDir, { recursive: true });
}

// Multer配置
const fanUpload = multer({
    storage: multer.diskStorage({
        destination: (req, file, cb) => cb(null, fanUploadDir),
        filename: (req, file, cb) => {
            const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
            const ext = path.extname(file.originalname);
            cb(null, 'fan-' + uniqueSuffix + ext);
        }
    }),
    fileFilter: (req, file, cb) => {
        if (file.mimetype.startsWith('image/')) cb(null, true);
        else cb(new Error('只允许上传图片文件!'), false);
    },
    limits: { fileSize: 10 * 1024 * 1024 }
});

const processImage = async (file) => {
    const webpFilename = `${path.basename(file.filename, path.extname(file.filename))}.webp`;
    const webpPath = path.join(fanUploadDir, webpFilename);
    // 只转换格式，不调整尺寸，使用无损压缩
    await sharp(file.path).webp({ quality: 100, lossless: true }).toFile(webpPath);
    fs.unlinkSync(file.path);
    return `/uploads/fan/${webpFilename}`;
};

// GET all fans
router.get('/', async (req, res) => {
    try {
        const { page = 1, limit = 10, search = '', brand = '', size = '' } = req.query;
        const offset = (page - 1) * limit;

        let whereClauses = [];
        let queryParams = [];

        if (search) {
            whereClauses.push('(model LIKE ? OR brand LIKE ?)');
            queryParams.push(`%${search}%`, `%${search}%`);
        }
        if (brand) {
            whereClauses.push('brand = ?');
            queryParams.push(brand);
        }
        if (size) {
            whereClauses.push('size = ?');
            queryParams.push(size);
        }

        const whereCondition = whereClauses.length ? `WHERE ${whereClauses.join(' AND ')}` : '';
        
        const countQuery = `SELECT COUNT(*) as total FROM fans ${whereCondition}`;
        const dataQuery = `SELECT * FROM fans ${whereCondition} ORDER BY created_at DESC LIMIT ? OFFSET ?`;

        const [totalRows] = await db.query(countQuery, queryParams);
        const total = totalRows[0].total;
        
        queryParams.push(parseInt(limit), parseInt(offset));
        const [fans] = await db.query(dataQuery, queryParams);

        res.json({
            fans,
            total,
            page: parseInt(page),
            limit: parseInt(limit),
            totalPages: Math.ceil(total / limit)
        });
    } catch (error) {
        console.error('获取风扇列表失败:', error);
        res.status(500).json({ message: '服务器内部错误' });
    }
});

// GET single fan
router.get('/:id', async (req, res) => {
    try {
        const [fan] = await db.query('SELECT * FROM fans WHERE id = ?', [req.params.id]);
        if (fan.length === 0) {
            return res.status(404).json({ message: '未找到指定的风扇' });
        }
        res.json(fan[0]);
    } catch (error) {
        console.error(`获取风扇(id: ${req.params.id})失败:`, error);
        res.status(500).json({ message: '服务器内部错误' });
    }
});

// POST new fan
router.post('/', fanUpload.single('image'), async (req, res) => {
    try {
        // 直接根据数据库表结构构建数据对象
        const fanData = {
            brand: req.body.brand || null,
            model: req.body.model || null,
            size: req.body.size || null,
            thickness: req.body.thickness || null,
            speed_min: req.body.speed_min || null,
            speed_max: req.body.speed_max || req.body.rpm || null, // 优先使用 speed_max
            airflow: req.body.airflow || null,
            staticPressure: req.body.staticPressure || null,
            noiseLevel: req.body.noiseLevel || null,
            pwm: req.body.pwm !== undefined ? req.body.pwm : null,
            connector: req.body.connector || null,
            bearing_type: req.body.bearing_type || null,
            rgbLighting: req.body.rgbLighting !== undefined ? req.body.rgbLighting : null,
            warranty: req.body.warranty || null,
            color: req.body.color || null,
            price: req.body.price || null,
            notes: req.body.notes || null,
            image_url: '/images/default-fan.png'
        };

        if (req.file) {
            fanData.image_url = await processImage(req.file);
        }

        const [result] = await db.query('INSERT INTO fans SET ?', fanData);
        const [newFan] = await db.query('SELECT * FROM fans WHERE id = ?', [result.insertId]);
        res.status(201).json(newFan[0]);
    } catch (error) {
        console.error('创建风扇失败:', error);
        res.status(500).json({ message: '创建失败', error: error.message });
    }
});

// PUT update fan
router.put('/:id', fanUpload.single('image'), async (req, res) => {
    try {
        const { id } = req.params;
        const fanData = {};

        // 定义与数据库完全匹配的字段列表
        const fields = [
            'brand', 'model', 'size', 'thickness', 'speed_min', 'speed_max', 'airflow', 
            'staticPressure', 'noiseLevel', 'pwm', 'connector', 'bearing_type', 
            'rgbLighting', 'warranty', 'color', 'price', 'notes'
        ];
        
        // 遍历字段并添加到更新对象
        fields.forEach(field => {
            if (req.body[field] !== undefined) {
                fanData[field] = req.body[field];
            }
        });

        // 处理前端传来的 rpm 字段，映射到 speed_max
        if (req.body.rpm !== undefined) {
            // 检查是否为范围格式 (如 "800-1500")
            const rpmRange = req.body.rpm.match(/(\d+)(?:-(\d+))?/);
            if (rpmRange && rpmRange[2]) {
                fanData.speed_min = rpmRange[1];
                fanData.speed_max = rpmRange[2];
            } else if (rpmRange) {
                fanData.speed_max = rpmRange[1];
            } else {
                fanData.speed_max = req.body.rpm;
            }
        }

        if (req.file) {
            const [existing] = await db.query('SELECT image_url FROM fans WHERE id = ?', [id]);
            if (existing.length > 0 && existing[0].image_url && !existing[0].image_url.includes('default-fan.png')) {
                const oldImagePath = path.join(__dirname, '..', 'public', existing[0].image_url);
                if (fs.existsSync(oldImagePath)) fs.unlinkSync(oldImagePath);
            }
            fanData.image_url = await processImage(req.file);
        }

        if (Object.keys(fanData).length > 0) {
            await db.query('UPDATE fans SET ? WHERE id = ?', [fanData, id]);
        }
        
        const [updatedFan] = await db.query('SELECT * FROM fans WHERE id = ?', [id]);
        res.json(updatedFan[0]);
    } catch (error) {
        console.error(`更新风扇(id: ${req.params.id})失败:`, error);
        res.status(500).json({ message: '更新失败', error: error.message });
    }
});

// DELETE fan
router.delete('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const [fan] = await db.query('SELECT image_url FROM fans WHERE id = ?', [id]);
        if (fan.length > 0 && fan[0].image_url && !fan[0].image_url.includes('default-fan.png')) {
            const imagePath = path.join(__dirname, '..', 'public', fan[0].image_url);
            if (fs.existsSync(imagePath)) fs.unlinkSync(imagePath);
        }

        const [result] = await db.query('DELETE FROM fans WHERE id = ?', [id]);
        if (result.affectedRows === 0) {
            return res.status(404).json({ message: '未找到要删除的风扇' });
        }
        res.status(204).send();
    } catch (error) {
        console.error(`删除风扇(id: ${req.params.id})失败:`, error);
        res.status(500).json({ message: '删除失败', error: error.message });
    }
});

module.exports = router; 