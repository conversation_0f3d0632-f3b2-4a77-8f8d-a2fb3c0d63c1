<!DOCTYPE html>
<html lang="zh-CN">
<head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>登录日志 - 电脑配件综合管理系统</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <script>
            tailwind.config = {
                darkMode: 'class',
                theme: {
                    extend: {}
                }
            }
        </script>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        <script src="/js/main.js"></script>
        <script src="/js/theme-manager.js"></script>
        <style>
                .status-success {
                        color: #10B981;
                }
                .status-failed_username, .status-failed_password, .status-system_error {
                        color: #EF4444;
                }
                
                /* 主题切换按钮样式 */
                .theme-toggle-btn {
                    transition: all 0.3s ease;
                }
                
                .theme-toggle-btn:hover {
                    transform: scale(1.1);
                }
                
                /* 暗色主题下的状态颜色调整 */
                .dark .status-success {
                    color: #34D399;
                }
                
                .dark .status-failed_username, 
                .dark .status-failed_password, 
                .dark .status-system_error {
                    color: #F87171;
                }
        </style>
</head>

<body class="bg-gray-50 dark:bg-gray-900 min-h-screen transition-colors duration-300">
        <!-- 导航栏 -->
        <nav class="bg-indigo-600 dark:bg-gray-800 text-white p-4 shadow-md transition-colors duration-300">
                <div class="container mx-auto flex justify-between items-center">
                        <a href="index.html" class="text-2xl font-bold flex items-center">
                                <i class="fas fa-truck mr-2"></i> 电脑配件综合管理系统
                        </a>
                        <div class="flex items-center space-x-4">
                                <a href="index.html" class="hover:text-indigo-200 dark:hover:text-gray-300">
                                        <i class="fas fa-home mr-1"></i> 首页
                                </a>
                                
                                <!-- 主题切换按钮 -->
                                <button data-theme-toggle 
                                        class="theme-toggle-btn p-2 rounded-lg hover:bg-indigo-500 dark:hover:bg-gray-700 transition-colors"
                                        title="切换主题">
                                    <i class="fas fa-moon"></i>
                                </button>
                        </div>
                </div>
        </nav>

        <!-- 主要内容 -->
        <main class="container mx-auto p-4">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6 transition-colors duration-300">
                        <h2 class="text-xl font-bold text-gray-800 dark:text-white mb-4">登录日志</h2>
                        
                        <!-- 筛选条件 -->
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                                <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">用户名</label>
                                        <input type="text" id="usernameFilter" class="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md px-3 py-2 transition-colors duration-300">
                                </div>
                                <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">登录状态</label>
                                        <select id="statusFilter" class="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md px-3 py-2 transition-colors duration-300">
                                                <option value="">全部</option>
                                                <option value="success">成功</option>
                                                <option value="failed_username">用户名不存在</option>
                                                <option value="failed_password">密码错误</option>
                                                <option value="system_error">系统错误</option>
                                        </select>
                                </div>
                                <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">开始日期</label>
                                        <input type="date" id="startDateFilter" class="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md px-3 py-2 transition-colors duration-300">
                                </div>
                                <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">结束日期</label>
                                        <input type="date" id="endDateFilter" class="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md px-3 py-2 transition-colors duration-300">
                                </div>
                        </div>
                        
                        <div class="flex justify-end mb-4 space-x-2">
                                <button id="resetBtn" class="bg-gray-500 dark:bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-600 dark:hover:bg-gray-700 transition-colors duration-300">
                                        <i class="fas fa-undo mr-2"></i> 重置
                                </button>
                                <button id="searchBtn" class="bg-indigo-600 dark:bg-indigo-500 text-white px-4 py-2 rounded-md hover:bg-indigo-700 dark:hover:bg-indigo-600 transition-colors duration-300">
                                        <i class="fas fa-search mr-2"></i> 搜索
                                </button>
                        </div>
                        
                        <!-- 数据表格 -->
                        <div class="overflow-x-auto">
                                <table class="min-w-full bg-white dark:bg-gray-800 transition-colors duration-300">
                                        <thead class="bg-gray-100 dark:bg-gray-700 transition-colors duration-300">
                                                <tr>
                                                        <th class="py-2 px-4 border-b dark:border-gray-600 text-left text-gray-800 dark:text-white">ID</th>
                                                        <th class="py-2 px-4 border-b dark:border-gray-600 text-left text-gray-800 dark:text-white">用户ID</th>
                                                        <th class="py-2 px-4 border-b dark:border-gray-600 text-left text-gray-800 dark:text-white">用户名</th>
                                                        <th class="py-2 px-4 border-b dark:border-gray-600 text-left text-gray-800 dark:text-white">登录时间</th>
                                                        <th class="py-2 px-4 border-b dark:border-gray-600 text-left text-gray-800 dark:text-white">IP地址</th>
                                                        <th class="py-2 px-4 border-b dark:border-gray-600 text-left text-gray-800 dark:text-white">浏览器/设备</th>
                                                        <th class="py-2 px-4 border-b dark:border-gray-600 text-left text-gray-800 dark:text-white">登录状态</th>
                                                </tr>
                                        </thead>
                                        <tbody id="logsTableBody">
                                                <!-- 日志数据将通过JavaScript加载 -->
                                                <tr>
                                                        <td colspan="7" class="py-4 text-center text-gray-600 dark:text-gray-400">加载中...</td>
                                                </tr>
                                        </tbody>
                                </table>
                        </div>
                        
                        <!-- 分页信息 -->
                        <div class="mt-4 flex justify-between items-center">
                                <div id="pageInfo" class="text-sm text-gray-600 dark:text-gray-400">
                                        显示 <span id="pageStart">0</span> - <span id="pageEnd">0</span> 条，共 <span id="totalCount">0</span> 条
                                </div>
                                <div class="flex space-x-2">
                                        <button id="prevPage" class="px-3 py-1 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-300">
                                                <i class="fas fa-chevron-left"></i> 上一页
                                        </button>
                                        <button id="nextPage" class="px-3 py-1 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-300">
                                                下一页 <i class="fas fa-chevron-right"></i>
                                        </button>
                                </div>
                        </div>
                </div>
        </main>

        <script>
                // 全局变量
                let currentPage = 0;
                let pageSize = 20;
                let totalRecords = 0;
                
                // DOM 元素
                const logsTableBody = document.getElementById('logsTableBody');
                const pageStart = document.getElementById('pageStart');
                const pageEnd = document.getElementById('pageEnd');
                const totalCount = document.getElementById('totalCount');
                const prevPageBtn = document.getElementById('prevPage');
                const nextPageBtn = document.getElementById('nextPage');
                const searchBtn = document.getElementById('searchBtn');
                const resetBtn = document.getElementById('resetBtn');
                
                // 初始化主题管理器
                let themeManager;
                document.addEventListener('DOMContentLoaded', () => {
                    themeManager = new ThemeManager();
                });
                
                // 检查用户认证
                function checkAuth() {
                        const token = localStorage.getItem('token');
                        
                        if (!token) {
                                alert('请先登录后访问');
                                window.location.href = 'login.html';
                                return false;
                        }
                        
                        return true;
                }
                
                // 加载登录日志数据
                async function loadLoginLogs() {
                        if (!checkAuth()) return;

                        const token = localStorage.getItem('token');
                        const usernameFilter = document.getElementById('usernameFilter').value.trim();
                        const statusFilter = document.getElementById('statusFilter').value.trim();
                        const startDateFilter = document.getElementById('startDateFilter').value.trim();
                        const endDateFilter = document.getElementById('endDateFilter').value.trim();

                        try {
                                logsTableBody.innerHTML = '<tr><td colspan="7" class="py-4 text-center text-gray-600 dark:text-gray-400">加载中...</td></tr>';

                                // 构建查询参数
                                const params = new URLSearchParams({
                                        limit: pageSize,
                                        offset: currentPage * pageSize
                                });

                                // 添加筛选参数
                                if (usernameFilter) {
                                        params.append('username', usernameFilter);
                                }
                                if (statusFilter) {
                                        params.append('status', statusFilter);
                                }
                                if (startDateFilter) {
                                        params.append('startDate', startDateFilter);
                                }
                                if (endDateFilter) {
                                        params.append('endDate', endDateFilter);
                                }

                                console.log('请求登录日志数据，参数:', params.toString());

                                const response = await fetch(`/api/login-logs?${params.toString()}`, {
                                        headers: {
                                                'Authorization': `Bearer ${token}`
                                        }
                                });

                                if (!response.ok) {
                                        const errorText = await response.text();
                                        console.error('API响应错误:', response.status, errorText);
                                        throw new Error(`网络请求失败: ${response.status} ${response.statusText}`);
                                }

                                const data = await response.json();
                                console.log('获取登录日志数据:', data);

                                // 渲染表格数据
                                renderLogs(data.data || []);

                                // 更新分页信息
                                totalRecords = data.total || 0;
                                const start = totalRecords > 0 ? currentPage * pageSize + 1 : 0;
                                const end = Math.min(start + pageSize - 1, totalRecords);

                                pageStart.textContent = start;
                                pageEnd.textContent = end;
                                totalCount.textContent = totalRecords;

                                // 更新分页按钮状态
                                prevPageBtn.disabled = currentPage === 0;
                                prevPageBtn.classList.toggle('opacity-50', currentPage === 0);

                                const maxPage = Math.ceil(totalRecords / pageSize) - 1;
                                nextPageBtn.disabled = currentPage >= maxPage;
                                nextPageBtn.classList.toggle('opacity-50', currentPage >= maxPage);

                        } catch (error) {
                                console.error('获取登录日志失败:', error);
                                logsTableBody.innerHTML = `
                                        <tr>
                                                <td colspan="7" class="py-4 text-center text-red-500 dark:text-red-400">加载数据失败: ${error.message}</td>
                                        </tr>
                                `;
                        }
                }

                // 渲染日志数据
                function renderLogs(logs) {
                        if (!logs || logs.length === 0) {
                                logsTableBody.innerHTML = '<tr><td colspan="7" class="py-4 text-center text-gray-600 dark:text-gray-400">暂无登录记录</td></tr>';
                                return;
                        }

                        let html = '';

                        logs.forEach(log => {
                                // 处理状态显示
                                let statusText = '';
                                let statusClass = '';

                                switch(log.login_status) {
                                        case 'success':
                                                statusText = '成功';
                                                statusClass = 'status-success';
                                                break;
                                        case 'failed_username':
                                                statusText = '用户名不存在';
                                                statusClass = 'status-failed_username';
                                                break;
                                        case 'failed_password':
                                                statusText = '密码错误';
                                                statusClass = 'status-failed_password';
                                                break;
                                        case 'system_error':
                                                statusText = '系统错误';
                                                statusClass = 'status-system_error';
                                                break;
                                        default:
                                                statusText = log.login_status || '未知';
                                }

                                // 处理浏览器/设备信息
                                const userAgent = log.user_agent || '';
                                const shortUserAgent = userAgent.length > 50 ? userAgent.substring(0, 50) + '...' : userAgent;

                                // 处理日期格式
                                let formattedTime = '';
                                try {
                                        if (log.login_time) {
                                                const dateOptions = {
                                                        year: 'numeric',
                                                        month: '2-digit',
                                                        day: '2-digit',
                                                        hour: '2-digit',
                                                        minute: '2-digit',
                                                        second: '2-digit'
                                                };
                                                formattedTime = new Date(log.login_time).toLocaleString('zh-CN', dateOptions);
                                        } else {
                                                formattedTime = '-';
                                        }
                                } catch (e) {
                                        console.error('日期处理错误:', e);
                                        formattedTime = log.login_time || '-';
                                }

                                html += `
                                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                                                <td class="py-2 px-4 border-b dark:border-gray-600 text-gray-800 dark:text-gray-200">${log.id || '-'}</td>
                                                <td class="py-2 px-4 border-b dark:border-gray-600 text-gray-800 dark:text-gray-200">${log.user_id || '-'}</td>
                                                <td class="py-2 px-4 border-b dark:border-gray-600 text-gray-800 dark:text-gray-200">${log.username || '-'}</td>
                                                <td class="py-2 px-4 border-b dark:border-gray-600 text-gray-800 dark:text-gray-200">${formattedTime}</td>
                                                <td class="py-2 px-4 border-b dark:border-gray-600 text-gray-800 dark:text-gray-200">${log.ip_address || '-'}</td>
                                                <td class="py-2 px-4 border-b dark:border-gray-600 text-gray-800 dark:text-gray-200" title="${userAgent}">${shortUserAgent}</td>
                                                <td class="py-2 px-4 border-b dark:border-gray-600 ${statusClass}">${statusText}</td>
                                        </tr>
                                `;
                        });

                        logsTableBody.innerHTML = html;
                }

                // 重置筛选条件
                function resetFilters() {
                        // 清空所有筛选输入框
                        document.getElementById('usernameFilter').value = '';
                        document.getElementById('statusFilter').value = '';
                        document.getElementById('startDateFilter').value = '';
                        document.getElementById('endDateFilter').value = '';

                        // 重置到第一页
                        currentPage = 0;

                        // 重新加载数据
                        loadLoginLogs();

                        console.log('筛选条件已重置');
                }

                // 页面加载完成后的事件绑定
                document.addEventListener('DOMContentLoaded', () => {
                        console.log('登录日志页面加载完成');

                        // 首次加载
                        loadLoginLogs();

                        // 搜索按钮事件
                        searchBtn.addEventListener('click', () => {
                                currentPage = 0; // 重置到第一页
                                loadLoginLogs();
                        });

                        // 重置按钮事件
                        resetBtn.addEventListener('click', resetFilters);

                        // 输入框回车键搜索事件
                        const filterInputs = [
                                document.getElementById('usernameFilter'),
                                document.getElementById('startDateFilter'),
                                document.getElementById('endDateFilter')
                        ];

                        filterInputs.forEach(input => {
                                input.addEventListener('keypress', (e) => {
                                        if (e.key === 'Enter') {
                                                currentPage = 0;
                                                loadLoginLogs();
                                        }
                                });
                        });

                        // 下拉框变化时自动搜索
                        document.getElementById('statusFilter').addEventListener('change', () => {
                                currentPage = 0;
                                loadLoginLogs();
                        });

                        // 分页按钮事件
                        prevPageBtn.addEventListener('click', () => {
                                if (currentPage > 0) {
                                        currentPage--;
                                        loadLoginLogs();
                                }
                        });

                        nextPageBtn.addEventListener('click', () => {
                                const maxPage = Math.ceil(totalRecords / pageSize) - 1;
                                if (currentPage < maxPage) {
                                        currentPage++;
                                        loadLoginLogs();
                                }
                        });
                });
        </script>
</body>
</html>
