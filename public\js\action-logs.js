/**
 * 操作日志页面JavaScript
 */

// 全局变量
let currentPage = 1;
let pageSize = 20;
let totalRecords = 0;
let totalPages = 1;
let isLoading = false;

// DOM元素
let elements = {};

// 初始化页面
document.addEventListener('DOMContentLoaded', async () => {
    console.log('操作日志页面初始化开始...');
    
    // 获取DOM元素
    initializeElements();
    
    // 检查权限
    if (!await checkPermissions()) {
        return;
    }
    
    // 初始化主题
    initializeTheme();

    // 绑定事件监听器
    bindEventListeners();
    
    // 加载初始数据
    await loadActionLogs();
    
    console.log('操作日志页面初始化完成');
});

/**
 * 获取DOM元素
 */
function initializeElements() {
    elements = {
        // 筛选器
        usernameFilter: document.getElementById('usernameFilter'),
        actionTypeFilter: document.getElementById('actionTypeFilter'),
        targetTableFilter: document.getElementById('targetTableFilter'),
        startDateFilter: document.getElementById('startDateFilter'),
        endDateFilter: document.getElementById('endDateFilter'),
        globalSearch: document.getElementById('globalSearch'),
        
        // 按钮
        searchBtn: document.getElementById('searchBtn'),
        clearFiltersBtn: document.getElementById('clearFiltersBtn'),
        refreshBtn: document.getElementById('refreshBtn'),
        exportBtn: document.getElementById('exportBtn'),
        cleanupBtn: document.getElementById('cleanupBtn'),
        
        // 表格和分页
        logsTableBody: document.getElementById('logsTableBody'),
        pageStart: document.getElementById('pageStart'),
        pageEnd: document.getElementById('pageEnd'),
        totalCount: document.getElementById('totalCount'),
        currentPageSpan: document.getElementById('currentPage'),
        totalPagesSpan: document.getElementById('totalPages'),
        prevPageBtn: document.getElementById('prevPageBtn'),
        nextPageBtn: document.getElementById('nextPageBtn'),
        
        // 用户信息
        currentUser: document.getElementById('currentUser'),

        // 主题切换
        themeToggle: document.getElementById('themeToggle'),
        themeText: document.getElementById('themeText'),
        themeIndicator: document.getElementById('themeIndicator'),
        themeIndicatorText: document.getElementById('themeIndicatorText'),
        
        // 统计信息
        statsSection: document.getElementById('statsSection'),
        statsContent: document.getElementById('statsContent'),
        
        // 模态框
        cleanupModal: document.getElementById('cleanupModal'),
        cleanupDays: document.getElementById('cleanupDays'),
        confirmCleanup: document.getElementById('confirmCleanup'),
        cancelCleanup: document.getElementById('cancelCleanup')
    };
}

/**
 * 检查用户权限
 */
async function checkPermissions() {
    const token = localStorage.getItem('token');
    
    if (!token) {
        showNotification('请先登录', 'error');
        setTimeout(() => {
            window.location.href = '/login.html';
        }, 2000);
        return false;
    }
    
    try {
        const response = await fetch('/api/validate-token', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        if (!response.ok) {
            throw new Error('Token验证失败');
        }
        
        const data = await response.json();
        
        if (!data.valid || data.user.role !== 'admin') {
            showNotification('权限不足，只有管理员可以查看操作日志', 'error');
            setTimeout(() => {
                window.location.href = '/pc-components.html';
            }, 2000);
            return false;
        }
        
        // 显示用户信息
        elements.currentUser.textContent = `${data.user.username} (管理员)`;
        
        return true;
        
    } catch (error) {
        console.error('权限检查失败:', error);
        showNotification('权限检查失败，请重新登录', 'error');
        setTimeout(() => {
            window.location.href = '/login.html';
        }, 2000);
        return false;
    }
}

/**
 * 主题管理
 */
const themes = [
    { name: 'default', label: '默认主题', icon: 'fas fa-palette' },
    { name: 'blue', label: '蓝色主题', icon: 'fas fa-water' },
    { name: 'green', label: '绿色主题', icon: 'fas fa-leaf' },
    { name: 'purple', label: '紫色主题', icon: 'fas fa-gem' },
    { name: 'dark', label: '暗色主题', icon: 'fas fa-moon' }
];

let currentThemeIndex = 0;

/**
 * 初始化主题
 */
function initializeTheme() {
    const savedTheme = localStorage.getItem('actionLogsTheme') || 'default';
    const themeIndex = themes.findIndex(theme => theme.name === savedTheme);
    currentThemeIndex = themeIndex >= 0 ? themeIndex : 0;

    applyTheme(themes[currentThemeIndex]);
}

/**
 * 应用主题
 */
function applyTheme(theme, animate = false) {
    if (animate) {
        // 添加切换动画类
        document.body.classList.add('theme-transition', 'theme-switching');
    }

    // 设置主题属性
    if (theme.name === 'default') {
        document.documentElement.removeAttribute('data-theme');
    } else {
        document.documentElement.setAttribute('data-theme', theme.name);
    }

    // 更新按钮文本和图标
    if (elements.themeText) {
        elements.themeText.textContent = theme.label;
    }

    if (elements.themeToggle) {
        const icon = elements.themeToggle.querySelector('i');
        if (icon) {
            // 添加图标切换动画
            icon.style.transform = 'scale(0.8) rotate(180deg)';
            setTimeout(() => {
                icon.className = theme.icon;
                icon.style.transform = 'scale(1) rotate(0deg)';
            }, 150);
        }
    }

    // 更新主题指示器
    if (elements.themeIndicatorText) {
        elements.themeIndicatorText.textContent = theme.label;
    }

    // 保存主题设置
    localStorage.setItem('actionLogsTheme', theme.name);

    if (animate) {
        // 移除动画类
        setTimeout(() => {
            document.body.classList.remove('theme-transition', 'theme-switching');
        }, 300);
    }
}

/**
 * 显示主题指示器
 */
function showThemeIndicator() {
    if (elements.themeIndicator) {
        elements.themeIndicator.classList.add('show');

        // 3秒后自动隐藏
        setTimeout(() => {
            elements.themeIndicator.classList.remove('show');
        }, 3000);
    }
}

/**
 * 切换主题
 */
function toggleTheme() {
    // 防止快速连续点击
    if (elements.themeToggle.disabled) return;

    elements.themeToggle.disabled = true;

    currentThemeIndex = (currentThemeIndex + 1) % themes.length;
    const newTheme = themes[currentThemeIndex];

    applyTheme(newTheme, true);

    // 根据主题类型显示不同的通知
    const notificationType = newTheme.name === 'dark' ? 'info' : 'success';
    showNotification(`已切换到${newTheme.label}`, notificationType);
    showThemeIndicator();

    // 重新启用按钮
    setTimeout(() => {
        elements.themeToggle.disabled = false;
    }, 400);
}

/**
 * 快速切换到暗色主题
 */
function toggleToDarkTheme() {
    if (elements.themeToggle.disabled) return;

    elements.themeToggle.disabled = true;

    const darkThemeIndex = themes.findIndex(theme => theme.name === 'dark');
    const currentTheme = themes[currentThemeIndex];

    if (currentTheme.name === 'dark') {
        // 如果当前是暗色主题，切换回默认主题
        currentThemeIndex = 0;
        showNotification('已切换回默认主题', 'success');
    } else {
        // 切换到暗色主题
        currentThemeIndex = darkThemeIndex;
        showNotification('已切换到暗色主题', 'info');
    }

    applyTheme(themes[currentThemeIndex], true);
    showThemeIndicator();

    // 重新启用按钮
    setTimeout(() => {
        elements.themeToggle.disabled = false;
    }, 400);
}

/**
 * 绑定事件监听器
 */
function bindEventListeners() {
    // 主题切换
    elements.themeToggle?.addEventListener('click', toggleTheme);

    // 双击快速切换到暗色主题
    elements.themeToggle?.addEventListener('dblclick', (e) => {
        e.preventDefault();
        toggleToDarkTheme();
    });

    // 键盘快捷键
    document.addEventListener('keydown', (e) => {
        if (e.ctrlKey && e.key === 't') {
            e.preventDefault();
            toggleTheme();
        }
        // Ctrl+Shift+D 快速切换到暗色主题
        if (e.ctrlKey && e.shiftKey && e.key === 'D') {
            e.preventDefault();
            toggleToDarkTheme();
        }
    });
    
    // 搜索按钮
    elements.searchBtn?.addEventListener('click', () => {
        currentPage = 1;
        loadActionLogs();
    });
    
    // 清空筛选
    elements.clearFiltersBtn?.addEventListener('click', clearFilters);
    
    // 刷新按钮
    elements.refreshBtn?.addEventListener('click', () => {
        loadActionLogs();
    });
    
    // 导出按钮
    elements.exportBtn?.addEventListener('click', exportLogs);
    
    // 清理按钮
    elements.cleanupBtn?.addEventListener('click', showCleanupModal);
    
    // 分页按钮
    elements.prevPageBtn?.addEventListener('click', () => {
        if (currentPage > 1) {
            currentPage--;
            loadActionLogs();
        }
    });
    
    elements.nextPageBtn?.addEventListener('click', () => {
        if (currentPage < totalPages) {
            currentPage++;
            loadActionLogs();
        }
    });
    
    // 回车键搜索
    const searchInputs = [
        elements.usernameFilter,
        elements.globalSearch
    ];
    
    searchInputs.forEach(input => {
        input?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                currentPage = 1;
                loadActionLogs();
            }
        });
    });
    
    // 清理模态框事件
    elements.confirmCleanup?.addEventListener('click', performCleanup);
    elements.cancelCleanup?.addEventListener('click', hideCleanupModal);
}

/**
 * 加载操作日志
 */
async function loadActionLogs() {
    if (isLoading) return;
    
    isLoading = true;
    showLoading();
    
    try {
        const params = new URLSearchParams({
            page: currentPage,
            limit: pageSize
        });
        
        // 添加筛选参数
        if (elements.usernameFilter?.value.trim()) {
            params.append('username', elements.usernameFilter.value.trim());
        }
        
        if (elements.actionTypeFilter?.value && elements.actionTypeFilter.value !== 'all') {
            params.append('actionType', elements.actionTypeFilter.value);
        }
        
        if (elements.targetTableFilter?.value && elements.targetTableFilter.value !== 'all') {
            params.append('targetTable', elements.targetTableFilter.value);
        }
        
        if (elements.startDateFilter?.value) {
            params.append('startDate', elements.startDateFilter.value);
        }
        
        if (elements.endDateFilter?.value) {
            params.append('endDate', elements.endDateFilter.value);
        }
        
        if (elements.globalSearch?.value.trim()) {
            params.append('search', elements.globalSearch.value.trim());
        }
        
        const token = localStorage.getItem('token');
        const response = await fetch(`/api/action-logs?${params.toString()}`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result = await response.json();
        
        if (!result.success) {
            throw new Error(result.message || '获取数据失败');
        }
        
        // 更新数据
        totalRecords = result.pagination.total;
        totalPages = result.pagination.pages;
        
        // 渲染表格
        renderLogsTable(result.data);
        
        // 更新分页信息
        updatePaginationInfo();
        
    } catch (error) {
        console.error('加载操作日志失败:', error);
        showNotification('加载数据失败: ' + error.message, 'error');
        showEmptyState('加载失败');
    } finally {
        isLoading = false;
    }
}

/**
 * 渲染日志表格
 */
function renderLogsTable(logs) {
    if (!elements.logsTableBody) return;
    
    if (logs.length === 0) {
        showEmptyState('暂无数据');
        return;
    }
    
    const tbody = elements.logsTableBody;
    tbody.innerHTML = '';
    
    logs.forEach(log => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${log.id}</td>
            <td>
                <div class="flex items-center gap-2">
                    <i class="fas fa-user text-gray-400"></i>
                    ${escapeHtml(log.username)}
                </div>
            </td>
            <td>
                <span class="action-badge ${log.action_type}">
                    ${getActionTypeText(log.action_type)}
                </span>
            </td>
            <td>
                <span class="font-mono text-sm">
                    ${escapeHtml(log.target_table || '-')}
                </span>
            </td>
            <td>
                <span class="font-mono text-sm">
                    ${escapeHtml(log.target_id || '-')}
                </span>
            </td>
            <td>
                <span class="font-mono text-sm">
                    ${escapeHtml(log.ip_address || '-')}
                </span>
            </td>
            <td>
                <span class="text-sm">
                    ${formatDateTime(log.created_at)}
                </span>
            </td>
            <td>
                ${log.details ? `
                    <button class="details-toggle" onclick="toggleDetails(this, ${log.id})">
                        <i class="fas fa-eye"></i>
                        查看详情
                    </button>
                    <div id="details-${log.id}" class="details-content" style="display: none;">
                        ${escapeHtml(JSON.stringify(log.details, null, 2))}
                    </div>
                ` : '-'}
            </td>
        `;
        tbody.appendChild(row);
    });
}

/**
 * 显示加载状态
 */
function showLoading() {
    if (elements.logsTableBody) {
        elements.logsTableBody.innerHTML = `
            <tr>
                <td colspan="8" class="loading">
                    <i class="fas fa-spinner"></i>
                    正在加载数据...
                </td>
            </tr>
        `;
    }
}

/**
 * 显示空状态
 */
function showEmptyState(message = '暂无数据') {
    if (elements.logsTableBody) {
        elements.logsTableBody.innerHTML = `
            <tr>
                <td colspan="8" class="empty-state">
                    <i class="fas fa-inbox"></i>
                    <div>${message}</div>
                </td>
            </tr>
        `;
    }
}

/**
 * 更新分页信息
 */
function updatePaginationInfo() {
    const start = totalRecords === 0 ? 0 : (currentPage - 1) * pageSize + 1;
    const end = Math.min(currentPage * pageSize, totalRecords);
    
    if (elements.pageStart) elements.pageStart.textContent = start;
    if (elements.pageEnd) elements.pageEnd.textContent = end;
    if (elements.totalCount) elements.totalCount.textContent = totalRecords;
    if (elements.currentPageSpan) elements.currentPageSpan.textContent = currentPage;
    if (elements.totalPagesSpan) elements.totalPagesSpan.textContent = totalPages;
    
    // 更新按钮状态
    if (elements.prevPageBtn) {
        elements.prevPageBtn.disabled = currentPage <= 1;
    }
    if (elements.nextPageBtn) {
        elements.nextPageBtn.disabled = currentPage >= totalPages;
    }
}

/**
 * 清空筛选条件
 */
function clearFilters() {
    if (elements.usernameFilter) elements.usernameFilter.value = '';
    if (elements.actionTypeFilter) elements.actionTypeFilter.value = 'all';
    if (elements.targetTableFilter) elements.targetTableFilter.value = 'all';
    if (elements.startDateFilter) elements.startDateFilter.value = '';
    if (elements.endDateFilter) elements.endDateFilter.value = '';
    if (elements.globalSearch) elements.globalSearch.value = '';
    
    currentPage = 1;
    loadActionLogs();
    
    showNotification('已清空筛选条件', 'success');
}

/**
 * 切换详情显示
 */
function toggleDetails(button, logId) {
    const detailsDiv = document.getElementById(`details-${logId}`);
    const icon = button.querySelector('i');
    
    if (detailsDiv.style.display === 'none') {
        detailsDiv.style.display = 'block';
        icon.className = 'fas fa-eye-slash';
        button.innerHTML = '<i class="fas fa-eye-slash"></i> 隐藏详情';
    } else {
        detailsDiv.style.display = 'none';
        icon.className = 'fas fa-eye';
        button.innerHTML = '<i class="fas fa-eye"></i> 查看详情';
    }
}

/**
 * 显示通知
 */
function showNotification(message, type = 'info') {
    const container = document.getElementById('notificationContainer');
    if (!container) return;
    
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    container.appendChild(notification);
    
    // 显示动画
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
    
    // 自动隐藏
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            container.removeChild(notification);
        }, 300);
    }, 3000);
}

/**
 * 工具函数
 */

// HTML转义
function escapeHtml(text) {
    if (text === null || text === undefined) return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 格式化日期时间
function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

// 获取操作类型文本
function getActionTypeText(actionType) {
    const typeMap = {
        'create': '创建',
        'read': '读取',
        'update': '更新',
        'delete': '删除'
    };
    return typeMap[actionType] || actionType;
}

// 导出日志
async function exportLogs() {
    showNotification('导出功能开发中...', 'info');
}

// 显示清理模态框
function showCleanupModal() {
    if (elements.cleanupModal) {
        elements.cleanupModal.style.display = 'block';
    }
}

// 隐藏清理模态框
function hideCleanupModal() {
    if (elements.cleanupModal) {
        elements.cleanupModal.style.display = 'none';
    }
}

// 执行清理
async function performCleanup() {
    const days = parseInt(elements.cleanupDays?.value) || 30;
    
    try {
        const token = localStorage.getItem('token');
        const response = await fetch('/api/action-logs/cleanup', {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ days })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showNotification(result.message, 'success');
            hideCleanupModal();
            loadActionLogs();
        } else {
            throw new Error(result.message);
        }
        
    } catch (error) {
        console.error('清理日志失败:', error);
        showNotification('清理失败: ' + error.message, 'error');
    }
}
