<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>硬盘信息管理 - 电脑配件综合管理系统</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/viewerjs/1.11.6/viewer.min.css">
    <link rel="stylesheet" href="/css/storage-info.css"></link>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/viewerjs/1.11.6/viewer.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="/js/js-pinyin.js"></script>
    <script src="/js/pinyin-wrapper.js"></script>
    <script src="/js/main.js"></script>
    <script src="/js/permission-helper.js"></script>
</head>

<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-2 sm:px-4 py-4 sm:py-8">
        <header class="mb-4 sm:mb-8">
            <h1 class="text-xl sm:text-3xl font-bold text-purple-700 flex items-center">
                <i class="fas fa-hdd mr-2 sm:mr-3"></i> 硬盘信息管理
            </h1>
            <div class="flex justify-between items-center">
                <p class="text-gray-600 mt-1 sm:mt-2 text-sm sm:text-base">记录和管理硬盘配置信息</p>
                <div class="mt-2 flex space-x-2">
                    <button id="darkModeToggle" class="w-10 h-10 rounded-full bg-gray-200 hover:bg-gray-300 flex items-center justify-center text-gray-700 shadow-sm transition-all duration-300" title="切换暗夜模式">
                        <i class="fas fa-moon"></i>
                    </button>
                    <a href="/pc-components.html"
                        class="inline-block bg-purple-600 text-white py-1 sm:py-2 px-3 sm:px-4 rounded-md hover:bg-purple-700 text-sm sm:text-base">
                        <i class="fas fa-arrow-left mr-1"></i> 返回配件列表
                    </a>
                    <a href="/"
                        class="inline-block bg-purple-600 text-white py-1 sm:py-2 px-3 sm:px-4 rounded-md hover:bg-purple-700 text-sm sm:text-base">
                        <i class="fas fa-home mr-1"></i> 首页
                    </a>
                </div>
            </div>
        </header>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-4 sm:gap-6">
            <!-- 左侧表单区域 -->
            <div class="lg:col-span-1 bg-white p-3 sm:p-6 rounded-lg shadow-md">
                <h2 class="text-lg sm:text-xl font-semibold text-gray-800 mb-3 sm:mb-4 flex items-center">
                    <i class="fas fa-plus-circle mr-1 sm:mr-2 text-purple-500"></i> 添加硬盘信息
                </h2>

                <form id="storageForm" class="space-y-3 sm:space-y-4">
                    <!-- 智能识别输入 -->
                    <div class="bg-purple-50 p-3 rounded-lg border border-purple-200">
                        <div class="flex gap-2 mobile:flex-col">
                            <input type="text" id="smartInput"
                                placeholder="输入硬盘参数（示例：雷克沙 Thor雷神 1TB M.2 2280 PCIe 4.0 读取4200/写入2100 TLC）"
                                class="flex-1 px-3 py-2 border rounded-md text-sm focus:ring-2 focus:ring-purple-500">
                            <button type="button" id="autoFillBtn"
                                class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 text-sm transition-colors">
                                <i class="fas fa-search mr-2"></i>智能解析
                            </button>
                        </div>
                        <p class="mt-2 text-xs text-purple-600">支持格式：品牌 型号 容量 类型 规格 接口 读/写速度 TBW 价格</p>
                        <p class="mt-1 text-xs text-purple-700"><i class="fas fa-info-circle mr-1"></i> 支持多种格式，如"硬盘型号：Thor雷神、品牌：雷克沙、容量：1TB"等</p>
                    </div>
                    <!-- 1.硬盘核心信息 -->
                    <div class="border border-gray-200 rounded-md p-3">
                        <h3 class="text-md font-medium text-gray-700 mb-2">硬盘核心信息</h3>

                        <div class="space-y-3">
                            <div>
                                <label for="model" class="block text-sm font-medium text-gray-700 mb-1">硬盘型号
                                    <span class="text-red-500">*</span></label>
                                <input type="text" id="model"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500 text-sm sm:text-base"
                                    placeholder="如: Samsung 970 EVO Plus" required>
                            </div>

                            <div>
                                <label for="brand" class="block text-sm font-medium text-gray-700 mb-1">品牌 <span
                                        class="text-red-500">*</span></label>
                                <select id="brand"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500 text-sm sm:text-base"
                                    required>
                                    <option value="all" />所有品牌
                                    <option value="雷克沙" />雷克沙
                                    <option value="威刚" />威刚
                                    <option value="三星" />三星
                                    <option value="西部数据" />西部数据
                                    <option value="金士顿" />金士顿
                                    <option value="宏碁" />宏碁
                                    <option value="微星" />微星
                                    <option value="紫光" />紫光
                                    <option value="希捷" />希捷
                                    <option value="东芝" />东芝
                                    <option value="致态" />致态
                                    <option value="梵想" />梵想
                                    <option value="其他" />其他
                                </select>
                            </div>

                            <div>
                                <label for="type" class="block text-sm font-medium text-gray-700 mb-1">类型 <span
                                        class="text-red-500">*</span></label>
                                <select id="type"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500 text-sm sm:text-base"
                                    required>
                                    <option value="">选择类型</option>
                                    <option value="SSD">SSD (固态硬盘)</option>
                                    <option value="HDD">HDD (机械硬盘)</option>
                                    <option value="M.2 NVMe">M.2 NVMe</option>
                                    <option value="M.2 SATA">M.2 SATA</option>
                                </select>
                            </div>

                            <div>
                                <label for="capacity" class="block text-sm font-medium text-gray-700 mb-1">容量 (GB) <span
                                        class="text-red-500">*</span></label>
                                <input type="number" id="capacity"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500 text-sm sm:text-base"
                                    placeholder="如: 1000" required>
                            </div>
                        </div>
                    </div>

                    <!-- 2.硬盘规格信息 -->
                    <div class="border border-gray-200 rounded-md p-3">
                        <h3 class="text-md font-medium text-gray-700 mb-2">硬盘规格信息</h3>

                        <div class="space-y-3">
                            <div>
                                <label for="formFactor" class="block text-sm font-medium text-gray-700 mb-1">规格</label>
                                <select id="formFactor"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500 text-sm sm:text-base">
                                    <option value="">选择规格</option>
                                    <option value="2.5寸">2.5寸</option>
                                    <option value="3.5寸">3.5寸</option>
                                    <option value="M.2 2280">M.2 2280</option>
                                    <option value="M.2 2242">M.2 2242</option>
                                    <option value="M.2 22110">M.2 22110</option>
                                    <option value="其他">其他</option>
                                </select>
                            </div>

                            <div>
                                <label for="interface" class="block text-sm font-medium text-gray-700 mb-1">接口类型</label>
                                <input type="text" id="interface"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500 text-sm sm:text-base"
                                    placeholder="如: PCIe 4.0">
                                <!-- <select id="interface"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500 text-sm sm:text-base">
                                    <option value="">选择接口类型</option>
                                    <option value="SATA III">SATA III</option>
                                    <option value="SATA II">SATA II</option>
                                    <option value="PCIe 3.0">PCIe 3.0</option>
                                    <option value="PCIe 4.0">PCIe 4.0</option>
                                    <option value="PCIe 5.0">PCIe 5.0</option>
                                    <option value="USB 3.0">USB 3.0</option>
                                    <option value="USB 3.1">USB 3.1</option>
                                    <option value="其他">其他</option>
                                </select> -->
                            </div>

                            <div>
                                <label for="readSpeed" class="block text-sm font-medium text-gray-700 mb-1">读取速度
                                    (MB/s)</label>
                                <input type="number" id="readSpeed"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500 text-sm sm:text-base"
                                    placeholder="如: 3500">
                            </div>

                            <div>
                                <label for="writeSpeed" class="block text-sm font-medium text-gray-700 mb-1">写入速度
                                    (MB/s)</label>
                                <input type="number" id="writeSpeed"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500 text-sm sm:text-base"
                                    placeholder="如: 3300">
                            </div>

                            <div>
                                <label for="cacheSize" class="block text-sm font-medium text-gray-700 mb-1">缓存 (MB)</label>
                                <input type="number" id="cacheSize"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500 text-sm sm:text-base"
                                    placeholder="如: 64">
                            </div>
                        </div>
                    </div>

                    <!-- 3.耐久性和性能 -->
                    <div class="border border-gray-200 rounded-md p-3">
                        <h3 class="text-md font-medium text-gray-700 mb-2">耐久性和性能</h3>

                        <div class="space-y-3">
                            <div>
                                <label for="tbw" class="block text-sm font-medium text-gray-700 mb-1">总写入量 (TB)</label>
                                <input type="number" id="tbw"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500 text-sm sm:text-base"
                                    placeholder="如: 600">
                            </div>

                            <div>
                                <label for="mtbf" class="block text-sm font-medium text-gray-700 mb-1">颗粒类型</label>
                                <input type="text" id="mtbf"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500 text-sm sm:text-base"
                                    placeholder="如: TLC/QLC/SLC">
                            </div>
                        </div>
                    </div>

                    <!-- 4.价格与备注 -->
                    <div class="border border-gray-200 rounded-md p-3">
                        <h3 class="text-md font-medium text-gray-700 mb-2">价格与备注</h3>

                        <div class="space-y-3">
                            <div>
                                <label for="price" class="block text-sm font-medium text-gray-700 mb-1">价格</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 sm:text-sm">¥</span>
                                    </div>
                                    <input type="number" id="price"
                                        class="w-full pl-7 px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500 text-sm sm:text-base"
                                        placeholder="0.00" min="0" step="0.01">
                                </div>
                            </div>

                            <div>
                                <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">备注</label>
                                <textarea id="notes" rows="3"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500 text-sm sm:text-base"
                                    placeholder="其他需要备注的信息..."></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- 5.图片上传 -->
                    <div class="border border-gray-200 rounded-md p-3">
                        <h3 class="text-md font-medium text-gray-700 mb-2">图片上传</h3>

                        <div
                            class="mt-1 flex justify-center px-4 sm:px-6 pt-3 sm:pt-5 pb-4 sm:pb-6 border-2 border-gray-300 border-dashed rounded-md">
                            <div class="space-y-1 text-center">
                                <div class="flex text-sm text-gray-600 items-center justify-center flex-wrap">
                                    <label for="storageImage"
                                        class="relative cursor-pointer bg-white rounded-md font-medium text-purple-600 hover:text-purple-500 focus-within:outline-none">
                                        <span>上传图片</span>
                                        <input id="storageImage" name="storageImage" type="file" accept="image/*"
                                            class="sr-only">
                                    </label>
                                    <p class="pl-1 mobile-hidden">或将图片拖拽到此处</p>
                                </div>
                                <p class="text-xs text-gray-500">PNG, JPG, GIF 格式，大小不超过5MB（将自动转换为WebP格式以优化加载速度）</p>
                            </div>
                        </div>

                        <!-- 上传进度条 -->
                        <div id="uploadProgressContainer" class="mt-2 hidden">
                            <div class="bg-gray-200 dark:bg-gray-700 rounded-full h-3 mb-2 overflow-hidden">
                                <div id="uploadProgressBar" class="bg-gradient-to-r from-yellow-500 to-yellow-600 dark:from-yellow-400 dark:to-yellow-500 h-3 rounded-full transition-all duration-500 ease-out relative" style="width: 0%">
                                    <!-- 进度条光泽效果 -->
                                    <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-20 animate-pulse"></div>
                                </div>
                            </div>
                            <div class="flex justify-between items-center text-xs">
                                <span id="uploadProgressText" class="text-gray-600 dark:text-gray-400 flex items-center">
                                    <i id="uploadProgressIcon" class="fas fa-upload mr-1"></i>
                                    准备上传...
                                </span>
                                <span id="uploadProgressPercent" class="text-yellow-600 dark:text-yellow-400 font-medium">0%</span>
                            </div>
                        </div>

                        <div id="imagePreviewContainer" class="mt-2 hidden">
                            <div class="relative inline-block">
                                <img id="imagePreview" src="#" alt="预览图"
                                    class="h-24 sm:h-32 rounded-md shadow image-preview">
                                <button type="button" id="removeImageBtn"
                                    class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 sm:w-6 sm:h-6 flex items-center justify-center hover:bg-red-600">
                                    <i class="fas fa-times text-xs"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div>
                        <button type="submit"
                            class="w-full bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-md">
                            <i class="fas fa-save mr-1"></i> 保存硬盘信息
                        </button>
                    </div>
                </form>
            </div>

            <!-- 右侧列表区域 -->
            <div class="lg:col-span-3">

                <!-- 搜索和过滤 -->
                <div class="bg-white p-3 sm:p-6 rounded-lg shadow-md mb-4 sm:mb-6">
                    <h2 class="text-xl font-semibold text-purple-800 mb-2 flex items-center">
                        <i class="fas fa-hdd mr-2 sm:mr-3"></i> 硬盘列表
                    </h2>
                    <div class="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
                        <div class="w-full sm:w-auto sm:flex-1">
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-search text-gray-400"></i>
                                </div>
                                <input type="text" id="storageSearch" placeholder="搜索硬盘型号、品牌等..."
                                    class="w-full py-2 pl-10 pr-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                            </div>
                        </div>

                        <div class="flex flex-wrap mt-2 sm:mt-0 gap-2 w-full sm:w-auto">
                            <select id="brandFilter"
                                class="w-[calc(50%-0.25rem)] sm:w-auto sm:flex-none border border-gray-300 rounded-md shadow-sm py-2 pl-2 pr-6 text-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                                <option value="all">所有品牌</option>
                                <option value="雷克沙">雷克沙</option>
                                <option value="威刚">威刚</option>
                                <option value="三星">三星</option>
                                <option value="西部数据">西部数据</option>
                                <option value="金士顿">金士顿</option>
                                <option value="宏碁">宏碁</option>
                                <option value="微星">微星</option>
                                <option value="紫光">紫光</option>
                                <option value="希捷">希捷</option>
                                <option value="东芝">东芝</option>
                                <option value="致态">致态</option>
                                <option value="梵想">梵想</option>
                                <option value="其他">其他</option>
                            </select>

                            <select id="typeFilter"
                                class="w-[calc(50%-0.25rem)] sm:w-auto sm:flex-none border border-gray-300 rounded-md shadow-sm py-2 pl-2 pr-6 text-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                                <option value="all">所有类型</option>
                                <option value="SSD">SSD</option>
                                <option value="HDD">HDD</option>
                                <option value="M.2 NVMe">M.2 NVMe</option>
                                <option value="M.2 SATA">M.2 SATA</option>
                            </select>

                            <button id="resetFilterBtn"
                                class="w-full mt-2 sm:w-auto sm:mt-0 flex-none bg-gray-200 hover:bg-gray-300 text-gray-700 py-2 px-3 sm:px-4 rounded-md text-sm">
                                <i class="fas fa-sync-alt mr-1"></i> 重置
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 硬盘列表 -->
                <div class="bg-white rounded-lg shadow-md">
                    <div class="overflow-x-auto sm:mx-0">
                        <div class="inline-block min-w-full align-middle">
                            <table class="min-w-full divide-y divide-gray-200 table-compact sm:table-auto">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col"
                                            class="px-2 py-2 sm:px-4 sm:py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell">
                                            图片
                                        </th>
                                        <th scope="col"
                                            class="px-2 py-2 sm:px-4 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider model-column">
                                        硬盘型号
                                    </th>
                                    <th scope="col"
                                            class="px-2 py-2 sm:px-4 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell">
                                        品牌
                                    </th>
                                    <th scope="col"
                                            class="px-2 py-2 sm:px-4 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider capacity-column">
                                            容量/类型
                                    </th>
                                    <th scope="col"
                                            class="px-2 py-2 sm:px-4 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell">
                                        颗粒
                                    </th>
                                    <th scope="col"
                                            class="px-2 py-2 sm:px-4 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell">
                                        读/写速度
                                    </th>
                                    <th scope="col"
                                            class="px-2 py-2 sm:px-4 sm:py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider action-column">
                                        操作
                                    </th>
                                </tr>
                            </thead>
                            <tbody id="storageTableBody" class="bg-white divide-y divide-gray-200">
                                <!-- 数据将通过JavaScript填充 -->
                                <tr>
                                        <td colspan="7" class="px-4 py-4 text-center text-sm text-gray-500">
                                        加载中...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        </div>
                    </div>

                    <!-- 分页控件 -->
                    <div class="mt-4 px-4 py-4 border-t border-gray-200 sm:px-6 flex flex-wrap justify-between items-center pagination-mobile">
                        <div class="text-sm text-gray-700 mb-2 sm:mb-0" id="totalCount">
                            共 0 条记录
                        </div>
                        <div class="flex items-center space-x-1 sm:space-x-2 page-controls-mobile">
                            <button id="firstPage" title="首页"
                                class="px-2 py-1 border border-gray-300 rounded-md text-sm bg-white text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                <i class="fas fa-angle-double-left"></i>
                            </button>
                            <button id="prevPage" title="上一页"
                                class="px-2 py-1 border border-gray-300 rounded-md text-sm bg-white text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            
                            <div id="pageNumbers" class="hidden sm:flex space-x-1">
                                <!-- 页码将通过JavaScript填充 -->
                            </div>
                            
                            <span id="pageInfo" class="px-2 py-1 text-sm whitespace-nowrap">第 <span id="currentPageDisplay">1</span> / <span id="totalPagesDisplay">1</span> 页</span>
                            
                            <button id="nextPage" title="下一页"
                                class="px-2 py-1 border border-gray-300 rounded-md text-sm bg-white text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                            <button id="lastPage" title="尾页"
                                class="px-2 py-1 border border-gray-300 rounded-md text-sm bg-white text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                <i class="fas fa-angle-double-right"></i>
                            </button>
                            
                            <div class="hidden sm:flex items-center ml-2">
                                <span class="text-sm">跳转到</span>
                                <input type="number" id="pageJump" min="1" class="w-12 ml-1 px-2 py-1 border border-gray-300 rounded-md text-sm">
                                <button id="goToPage" class="ml-1 px-2 py-1 bg-purple-600 text-white text-sm rounded-md hover:bg-purple-700">
                                    确定
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 硬盘详情模态框 -->
        <div id="storageModal"
            class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
            <div class="bg-white rounded-lg shadow-xl p-4 sm:p-6 max-w-4xl w-full max-h-[90vh] overflow-y-auto">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg sm:text-xl font-semibold text-gray-800">硬盘详情</h3>
                    <button id="closeModal" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div id="storageDetails" class="space-y-4">
                    <!-- 详情会动态填充 -->
                </div>

                <div class="mt-6 flex justify-end space-x-2">
                    <button id="editBtn"
                        class="px-3 py-1 sm:px-4 sm:py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 text-sm sm:text-base">
                        <i class="fas fa-edit mr-1"></i> 编辑
                    </button>
                    <button id="deleteBtn"
                        class="px-3 py-1 sm:px-4 sm:py-2 bg-red-600 text-white rounded-md hover:bg-red-700 text-sm sm:text-base">
                        <i class="fas fa-trash mr-1"></i> 删除
                    </button>
                    <button id="closeModalBtn"
                        class="px-3 py-1 sm:px-4 sm:py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 text-sm sm:text-base">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/upload-progress.js"></script>
    <script src="/js/storage-info.js"></script>
</body>

</html>