
// 检测浏览器是否支持WebP
let supportWebP = false;
function checkWebPSupport() {
  return new Promise(resolve => {
    const webP = new Image();
    webP.onload = webP.onerror = function() {
      supportWebP = webP.height === 2;
      resolve(supportWebP);
    };
    webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
  });
}

// 页面加载时检测WebP支持
document.addEventListener('DOMContentLoaded', function() {
  checkWebPSupport();
});

// 创建一个简单的脚本来测试PSU表单提交
document.addEventListener('DOMContentLoaded', function() {
    // 找到表单
    const psuForm = document.getElementById('psuForm');
    
    if (!psuForm) {
        console.error('找不到PSU表单');
        return;
    }
    
    // 在控制台上显示表单提交
    console.log('已找到PSU表单，添加提交处理程序');
    
    // 添加表单提交事件监听器
    psuForm.addEventListener('submit', function(event) {
        event.preventDefault();
        
        console.log('表单提交被拦截');
        
        // 创建FormData对象
        const formData = new FormData(psuForm);
        
        // 打印表单数据
        console.log('表单数据:');
        for (const [key, value] of formData.entries()) {
            console.log(`${key}: ${value instanceof File ? '文件: '+value.name : value}`);
        }
        
        // 进行表单提交前的验证
        const brand = formData.get('brand');
        const model = formData.get('model');
        const wattage = formData.get('wattage');
        
        if (!brand || !model || !wattage) {
            console.error('必填字段缺失');
            alert('请填写必填字段: 品牌、型号和功率');
            return;
        }
        
        // 创建xhr请求
        const xhr = new XMLHttpRequest();
        xhr.open('POST', '/api/psus', true);
        
        xhr.onload = function() {
            console.log('收到响应:', {
                status: xhr.status,
                statusText: xhr.statusText,
                responseText: xhr.responseText
            });
            
            if (xhr.status >= 200 && xhr.status < 300) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    console.log('解析的JSON响应:', response);
                    alert('提交成功! ID: ' + response.id);
                } catch (e) {
                    console.error('解析响应出错:', e);
                    alert('提交成功，但解析响应失败');
                }
            } else {
                console.error('请求失败:', xhr.status, xhr.statusText);
                alert('提交失败: ' + xhr.statusText);
            }
        };
        
        xhr.onerror = function() {
            console.error('请求出错');
            alert('网络错误，请求失败');
        };
        
        console.log('发送表单数据...');
        xhr.send(formData);
    });
    
    // 添加调试按钮
    const debugBtn = document.createElement('button');
    debugBtn.type = 'button';
    debugBtn.className = 'px-3 py-1 sm:px-4 sm:py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 mr-2';
    debugBtn.innerHTML = '<i class="fas fa-bug mr-1"></i> 调试信息';
    debugBtn.onclick = function() {
        // 获取表单字段信息并打印
        const formData = new FormData(psuForm);
        const formValues = {};
        for (const [key, value] of formData.entries()) {
            formValues[key] = value instanceof File ? `文件: ${value.name || '无'}` : value;
        }
        console.log('当前表单值:', formValues);
        alert('调试信息已打印到控制台');
    };
    
    // 将按钮添加到表单底部
    const submitButton = psuForm.querySelector('button[type="submit"]');
    if (submitButton && submitButton.parentNode) {
        submitButton.parentNode.insertBefore(debugBtn, submitButton);
    }
    
    console.log('调试脚本加载完成');
}); 