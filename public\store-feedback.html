<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商铺问题反馈 - 电脑配件综合管理系统</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        // 配置Tailwind CSS支持暗色模式
        tailwind.config = {
            darkMode: 'class',
        }
    </script>
    <script src="/js/main.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- 添加加载状态指示器 -->
    <link rel="stylesheet" href="/css/store-feedback.css">

    <!-- 先加载核心库的脚本，添加加载完成事件 -->
     <script src="/js/store.js"></script>

    <!-- 按顺序加载依赖库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"
        onload="window.librariesLoaded.main = true; checkAllLibrariesLoaded();"
        onerror="handleLibraryError('Chart.js')"></script>
    <script src="https://cdn.jsdelivr.net/npm/wordcloud@1.2.2/src/wordcloud2.min.js"
        onload="window.librariesLoaded.main = true; checkAllLibrariesLoaded();"
        onerror="handleLibraryError('WordCloud')"></script>

    <!-- 拼音库 -->
    <script src="/js/js-pinyin.js"
        onload="console.log('js-pinyin loaded successfully'); window.librariesLoaded.pinyin = true; checkAllLibrariesLoaded();"
        onerror="handleLibraryError('js-pinyin')"></script>
    <script src="/js/pinyin-util.js"
        onload="console.log('pinyin-util loaded successfully'); window.librariesLoaded.pinyin = true; checkAllLibrariesLoaded();"
        onerror="handleLibraryError('拼音库')"></script>
</head>

<body class="bg-gray-50 dark:bg-gray-900 min-h-screen transition-colors duration-300">
    <!-- 加载状态指示器 -->
    <div id="loading-overlay">
        <div class="spinner mb-4"></div>
        <p id="loading-message" class="text-gray-700 dark:text-gray-300">正在加载页面，请稍候...</p>
    </div>

    <div class="container mx-auto px-2 sm:px-4 py-4 sm:py-8">
        <header class="mb-4 sm:mb-8">
            <div class="flex justify-between items-start">
                <div class="flex-1">
                    <h1 class="text-xl sm:text-3xl font-bold text-indigo-700 dark:text-indigo-300 flex items-center">
                        <i class="fas fa-comment-alt mr-2 sm:mr-3"></i> 商铺问题反馈系统
                    </h1>
                    <p class="text-gray-600 dark:text-gray-300 mt-1 sm:mt-2 text-sm sm:text-base">记录和管理商铺反馈信息</p>
                </div>

                <!-- 主题切换按钮 -->
                <div class="flex items-center space-x-2">
                    <button id="themeToggle"
                            class="p-2 rounded-lg bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200"
                            title="切换主题"
                            onclick="
                                const html = document.documentElement;
                                const icon = document.getElementById('themeIcon');
                                if (html.classList.contains('dark')) {
                                    html.classList.remove('dark');
                                    localStorage.setItem('theme', 'light');
                                    if (icon) icon.className = 'fas fa-moon text-lg';
                                    console.log('切换到浅色主题');
                                } else {
                                    html.classList.add('dark');
                                    localStorage.setItem('theme', 'dark');
                                    if (icon) icon.className = 'fas fa-sun text-lg';
                                    console.log('切换到暗色主题');
                                }
                                return false;
                            ">
                        <i id="themeIcon" class="fas fa-moon text-lg"></i>
                    </button>
                </div>
            </div>

            <div class="mt-2">
                <a href="/"
                    class="inline-block bg-indigo-600 dark:bg-indigo-500 text-white py-1 sm:py-2 px-3 sm:px-4 rounded-md hover:bg-indigo-700 dark:hover:bg-indigo-600 text-sm sm:text-base transition-colors duration-200">
                    <i class="fas fa-home mr-1"></i> 返回首页
                </a>
            </div>
        </header>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-4 sm:gap-6">
            <!-- 左侧表单区域 -->
            <div class="lg:col-span-1 bg-white dark:bg-gray-800 p-3 sm:p-6 rounded-lg shadow-md transition-colors duration-300">
                <h2 class="text-lg sm:text-xl font-semibold text-gray-800 dark:text-gray-200 mb-3 sm:mb-4 flex items-center">
                    <i class="fas fa-plus-circle mr-1 sm:mr-2 text-indigo-500 dark:text-indigo-400"></i> 添加反馈
                </h2>

                <form id="feedbackForm" class="space-y-3 sm:space-y-4">
                    <div>
                        <label for="storeSearch" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">商铺名称</label>
                        <div class="relative">
                            <input type="text" id="storeSearch"
                                class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors duration-200"
                                placeholder="搜索店铺(支持拼音首字母)">
                            <div id="storeSearchResults"
                                class="absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 shadow-lg rounded-md border border-gray-200 dark:border-gray-600 max-h-60 overflow-auto hidden">
                            </div>
                        </div>
                    </div>

                    <div>
                        <label for="storeLocation" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">商铺地点</label>
                        <select id="storeLocation"
                            class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors duration-200">
                            <option value="">选择地点</option>
                        </select>
                    </div>

                    <div>
                        <label for="feedbackContent" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">反馈问题</label>
                        <textarea id="feedbackContent" name="feedbackContent" rows="4"
                            class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors duration-200"
                            placeholder="请输入反馈问题的详细描述..."></textarea>
                    </div>

                    <div>
                        <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">备注</label>
                        <textarea id="notes" name="notes" rows="2"
                            class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors duration-200"
                            placeholder="可选的备注信息..."></textarea>
                    </div>

                    <div>
                        <label for="feedbackImage" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">上传图片</label>
                        <div
                            class="mt-1 flex justify-center px-4 sm:px-6 pt-3 sm:pt-5 pb-4 sm:pb-6 border-2 border-gray-300 dark:border-gray-600 border-dashed rounded-md transition-colors duration-200">
                            <div class="space-y-1 text-center">
                                <div class="flex text-sm text-gray-600 dark:text-gray-400 items-center justify-center flex-wrap">
                                    <label for="feedbackImage"
                                        class="relative cursor-pointer bg-white dark:bg-gray-700 rounded-md font-medium text-indigo-600 dark:text-indigo-400 hover:text-indigo-500 dark:hover:text-indigo-300 focus-within:outline-none transition-colors duration-200">
                                        <span>上传图片</span>
                                        <input id="feedbackImage" name="feedbackImage" type="file" accept="image/*"
                                            class="sr-only">
                                    </label>
                                    <p class="pl-1 mobile-hidden">或将图片拖拽到此处</p>
                                </div>
                                <p class="text-xs text-gray-500 dark:text-gray-400">PNG, JPG, GIF 格式，大小不超过10MB</p>
                            </div>
                        </div>
                        <div id="imagePreviewContainer" class="mt-2 hidden">
                            <div class="relative inline-block">
                                <img id="imagePreview" src="#" alt="预览图"
                                    class="h-24 sm:h-32 rounded-md shadow image-preview">
                                <button type="button" id="removeImageBtn"
                                    class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 sm:w-6 sm:h-6 flex items-center justify-center hover:bg-red-600">
                                    <i class="fas fa-times text-xs"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="flex space-x-2 sm:space-x-3 mobile-stack">
                        <button type="submit"
                            class="flex-1 bg-indigo-600 dark:bg-indigo-500 text-white py-1 sm:py-2 px-2 sm:px-4 rounded-md hover:bg-indigo-700 dark:hover:bg-indigo-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 text-sm sm:text-base transition-colors duration-200">
                            <i class="fas fa-save mr-1 sm:mr-2"></i> 提交反馈
                        </button>
                        <button type="reset"
                            class="flex-1 bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-gray-200 py-1 sm:py-2 px-2 sm:px-4 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 text-sm sm:text-base transition-colors duration-200">
                            <i class="fas fa-undo mr-1 sm:mr-2"></i> 重置
                        </button>
                    </div>
                </form>
            </div>

            <!-- 右侧列表和分析区域 -->
            <div class="lg:col-span-3 space-y-4 sm:space-y-6">
                <!-- 反馈列表 -->
                <div class="bg-white dark:bg-gray-800 p-3 sm:p-6 rounded-lg shadow-md transition-colors duration-300">
                    <h2
                        class="text-lg sm:text-xl font-semibold text-gray-800 dark:text-gray-200 mb-3 sm:mb-4 flex items-center justify-between">
                        <div>
                            <i class="fas fa-list mr-1 sm:mr-2 text-indigo-500 dark:text-indigo-400"></i> 反馈列表
                        </div>
                        <div class="flex space-x-2">
                            <input id="feedbackSearch" type="text" placeholder="搜索反馈..."
                                class="px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors duration-200">
                            <select id="statusFilter" class="px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors duration-200">
                                <option value="all">全部状态</option>
                                <option value="pending">待处理</option>
                                <option value="in-progress">处理中</option>
                                <option value="resolved">已解决</option>
                            </select>
                        </div>
                    </h2>

                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th scope="col"
                                        class="w-1/6 px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        商铺名称</th>
                                    <th scope="col"
                                        class="w-1/6 px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        图片</th>
                                    <th scope="col"
                                        class="w-1/6 px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        操作</th>
                                </tr>
                            </thead>
                            <tbody id="feedbackTableBody" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                <!-- 数据将通过JavaScript动态填充 -->
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-4 flex justify-between items-center">
                        <div>
                            <span id="totalCount" class="text-sm text-gray-600 dark:text-gray-400">共 0 条记录</span>
                        </div>
                        <div class="flex space-x-1">
                            <button id="prevPage"
                                class="px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm disabled:opacity-50 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors duration-200">上一页</button>
                            <span id="pageInfo" class="px-2 py-1 text-sm text-gray-700 dark:text-gray-300">第 1 页</span>
                            <button id="nextPage"
                                class="px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm disabled:opacity-50 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors duration-200">下一页</button>
                        </div>
                    </div>
                </div>

                <!-- 问题分析区域 -->
                <div class="bg-white dark:bg-gray-800 p-3 sm:p-6 rounded-lg shadow-md transition-colors duration-300">
                    <h2 class="text-lg sm:text-xl font-semibold text-gray-800 dark:text-gray-200 mb-3 sm:mb-4 flex items-center">
                        <i class="fas fa-chart-pie mr-1 sm:mr-2 text-indigo-500 dark:text-indigo-400"></i> 问题分析
                    </h2>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- 第一行：词云和状态分布 -->
                        <div class="col-span-1 md:col-span-2 grid grid-cols-1 lg:grid-cols-3 gap-4">
                            <!-- 词云分析 -->
                            <div class="lg:col-span-2 bg-gray-50 dark:bg-gray-700 p-3 rounded-lg transition-colors duration-300">
                                <h3 class="text-md font-medium text-gray-700 dark:text-gray-300 mb-2">反馈问题词云</h3>
                                <div id="wordCloudContainer" class="h-64">
                                    <div class="chart-error text-red-500 text-center hidden">词云渲染失败</div>
                                </div>
                            </div>

                            <!-- 问题状态分布 -->
                            <div class="lg:col-span-1 bg-gray-50 dark:bg-gray-700 p-3 rounded-lg transition-colors duration-300">
                                <h3 class="text-md font-medium text-gray-700 dark:text-gray-300 mb-2">问题状态分布</h3>
                                <div class="h-64 flex items-center justify-center">
                                    <canvas id="feedbackTypeChart" class="w-full h-full"></canvas>
                                    <div class="chart-error text-red-500 text-center hidden">状态分布图渲染失败</div>
                                </div>
                            </div>
                        </div>

                        <!-- 第二行：时间分布和地点分布 -->
                        <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg transition-colors duration-300">
                            <h3 class="text-md font-medium text-gray-700 dark:text-gray-300 mb-2">反馈时间分布</h3>
                            <div class="h-64 flex items-center justify-center">
                                <canvas id="feedbackTimeChart" class="w-full h-full"></canvas>
                                <div class="chart-error text-red-500 text-center hidden">时间分布图渲染失败</div>
                            </div>
                        </div>

                        <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg transition-colors duration-300">
                            <h3 class="text-md font-medium text-gray-700 dark:text-gray-300 mb-2">反馈地点分布</h3>
                            <div class="h-64 flex items-center justify-center location-chart-container">
                                <canvas id="locationChart" class="w-full h-full"></canvas>
                                <div class="chart-error text-red-500 text-center hidden">地点分布图渲染失败</div>
                            </div>
                        </div>

                        <!-- 第三行：问题分类和平均解决时间 -->
                        <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg transition-colors duration-300">
                            <h3 class="text-md font-medium text-gray-700 dark:text-gray-300 mb-2">问题分类统计</h3>
                            <div class="h-64 flex items-center justify-center category-chart-container">
                                <canvas id="categoryChart" class="w-full h-full"></canvas>
                                <div class="chart-error text-red-500 text-center hidden">问题分类图渲染失败</div>
                            </div>
                        </div>

                        <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg flex flex-col transition-colors duration-300">
                            <h3 class="text-md font-medium text-gray-700 dark:text-gray-300 mb-2">问题解决效率</h3>
                            <div class="flex-1 flex items-center justify-center">
                                <div class="text-center">
                                    <div id="resolutionTimeValue" class="text-4xl font-bold text-indigo-600 dark:text-indigo-400">0</div>
                                    <div id="resolutionTimeUnit" class="text-gray-500 dark:text-gray-400">小时</div>
                                    <div class="mt-2 text-sm text-gray-600 dark:text-gray-400">平均解决时间</div>
                                </div>
                            </div>
                        </div>

                        <!-- 第四行：热门商铺排名 -->
                        <div class="col-span-1 md:col-span-2 bg-gray-50 dark:bg-gray-700 p-3 rounded-lg transition-colors duration-300">
                            <h3 class="text-md font-medium text-gray-700 dark:text-gray-300 mb-2">热门商铺排名</h3>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
                                    <thead class="bg-gray-100 dark:bg-gray-600">
                                        <tr>
                                            <th scope="col"
                                                class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                                排名</th>
                                            <th scope="col"
                                                class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                                商铺名称</th>
                                            <th scope="col"
                                                class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                                反馈数量</th>
                                        </tr>
                                    </thead>
                                    <tbody id="storeRankingBody" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600">
                                        <!-- 数据将通过JavaScript动态填充 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 添加图片查看模态框 -->
        <div id="imageModal" class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 hidden">
            <div class="relative">
                <button id="closeImageModal"
                    class="absolute top-2 right-2 text-white bg-gray-800 dark:bg-gray-700 rounded-full w-8 h-8 flex items-center justify-center hover:bg-gray-700 dark:hover:bg-gray-600 transition-colors duration-200">
                    <i class="fas fa-times"></i>
                </button>
                <img id="modalImage" src="#" alt="图片预览" class="max-w-[90vw] max-h-[90vh] rounded-lg">
            </div>
        </div>

        <!-- 反馈详情模态框 -->
        <div id="feedbackModal"
            class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-4 sm:p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto transition-colors duration-300">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg sm:text-xl font-semibold text-gray-800 dark:text-gray-200">反馈详情</h3>
                    <button id="closeModal" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div id="feedbackDetails" class="space-y-4">
                    <!-- 详情会动态填充 -->
                </div>

                <div class="mt-6 flex justify-end space-x-2">
                    <button id="updateStatusBtn"
                        class="px-3 py-1 sm:px-4 sm:py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 text-sm sm:text-base">
                        更新状态
                    </button>
                    <button id="closeModalBtn"
                        class="px-3 py-1 sm:px-4 sm:py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 text-sm sm:text-base">
                        关闭
                    </button>
                </div>
            </div>
        </div>

        <!-- 更新状态模态框 -->
        <div id="statusModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
            <div class="bg-white rounded-lg shadow-xl p-4 sm:p-6 max-w-md w-full">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-800">更新反馈状态</h3>
                    <button id="closeStatusModal" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div class="space-y-4">
                    <div>
                        <label for="feedbackStatus" class="block text-sm font-medium text-gray-700 mb-1">状态</label>
                        <select id="feedbackStatus"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                            <option value="pending">待处理</option>
                            <option value="in-progress">处理中</option>
                            <option value="resolved">已解决</option>
                        </select>
                    </div>
                </div>

                <div class="mt-6 flex justify-end space-x-2">
                    <button id="saveStatusBtn"
                        class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
                        保存
                    </button>
                    <button id="cancelStatusBtn"
                        class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300">
                        取消
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加错误处理脚本 -->
    <script>
        // 全局错误处理
        window.addEventListener('error', function (event) {
            console.error('页面加载错误:', event.error || event.message);
            // 确保加载提示不会永远显示
            setTimeout(() => {
                const overlay = document.getElementById('loading-overlay');
                if (overlay && overlay.style.display !== 'none') {
                    overlay.style.display = 'none';
                }
            }, 3000);
        });

        // 页面加载监听器
        window.addEventListener('load', function () {
            console.log('页面资源加载完成');

            // 调试拼音工具是否正确加载
            if (typeof pinyinUtil !== 'undefined') {
                console.log('pinyinUtil 已加载，可用方法:', Object.keys(pinyinUtil));
                try {
                    console.log('测试 pinyinUtil:', pinyinUtil.getFirstLetters('测试'));
                } catch (e) {
                    console.error('测试 pinyinUtil 失败:', e);
                }
            } else {
                console.error('pinyinUtil 未加载');
            }

            // 确保3秒后无论如何都关闭加载提示
            setTimeout(() => {
                const overlay = document.getElementById('loading-overlay');
                if (overlay) {
                    overlay.style.display = 'none';
                }
            }, 3000);
        });
    </script>

    <!-- 主题切换功能 - 简化版 -->
    <script>
        // 在页面加载前立即应用保存的主题，避免闪烁
        (function() {
            const savedTheme = localStorage.getItem('theme');
            const systemPrefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;

            if (savedTheme === 'dark' || (!savedTheme && systemPrefersDark)) {
                document.documentElement.classList.add('dark');
            }
        })();

        // DOM加载完成后设置正确的图标
        document.addEventListener('DOMContentLoaded', function() {
            const themeIcon = document.getElementById('themeIcon');
            if (themeIcon) {
                const isDark = document.documentElement.classList.contains('dark');
                themeIcon.className = isDark ? 'fas fa-sun text-lg' : 'fas fa-moon text-lg';
                console.log('主题图标已初始化:', isDark ? '太阳' : '月亮');
            }
        });

        // 调试函数
        window.debugTheme = function() {
            console.log('=== 主题调试信息 ===');
            console.log('themeToggle元素:', document.getElementById('themeToggle'));
            console.log('themeIcon元素:', document.getElementById('themeIcon'));
            console.log('当前主题类:', document.documentElement.classList.contains('dark') ? 'dark' : 'light');
            console.log('localStorage主题:', localStorage.getItem('theme'));
            console.log('HTML类列表:', document.documentElement.classList.toString());
        };
    </script>

    <!-- 业务逻辑 -->
    <script src="/js/store-feedback.js"></script>
</body>

</html>