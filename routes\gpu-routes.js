const express = require('express');
const createResourceRouter = require('./resource-handler');

// GPU 资源配置
const gpuConfig = {
    resourceName: 'gpu',
    tableName: 'gpus',
    uploadDir: 'gpu',
    defaultImage: 'images/default-gpu.png',
    searchFields: ['brand', 'model', 'chipset'],
    fieldMap: {
        brand: { type: 'string' },
        model: { type: 'string' },
        chipset: { type: 'string' },
        memory_size: { type: 'string' },
        memory_type: { type: 'string' },
        memory_bus: { type: 'string' },
        core_clock: { type: 'float' },
        boost_clock: { type: 'float' },
        tdp: { type: 'integer' },
        power_connectors: { type: 'string' },
        display_ports: { type: 'string' },
        hdmi_ports: { type: 'string' },
        dimensions: { type: 'string' },
        recommended_psu: { type: 'string' },
        price: { type: 'float' },
        notes: { type: 'string' }
    }
};

// 创建 GPU 路由
const router = createResourceRouter(gpuConfig);

module.exports = router; 