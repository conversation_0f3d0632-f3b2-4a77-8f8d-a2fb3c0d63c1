const db = require('../config/db');

async function addStatusColumn() {
    try {
        console.log('开始添加status字段到revenue_records表...');
        
        // 检查字段是否已存在
        const [columns] = await db.query(`
            SELECT COLUMN_NAME 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'revenue_records' 
            AND COLUMN_NAME = 'status'
        `);
        
        if (columns.length > 0) {
            console.log('status字段已存在，跳过添加');
            return;
        }
        
        // 添加status字段
        await db.query(`
            ALTER TABLE revenue_records 
            ADD COLUMN status ENUM('未做单', '已做单') NOT NULL DEFAULT '未做单' 
            COMMENT '订单状态：未做单、已做单'
            AFTER total_revenue
        `);
        
        console.log('成功添加status字段到revenue_records表');
        
        // 为现有记录设置默认状态
        const [result] = await db.query(`
            UPDATE revenue_records 
            SET status = '未做单' 
            WHERE status IS NULL OR status = ''
        `);
        
        console.log(`更新了 ${result.affectedRows} 条现有记录的状态为"未做单"`);
        
        console.log('status字段添加完成！');
        
    } catch (error) {
        console.error('添加status字段失败:', error);
        throw error;
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    addStatusColumn()
        .then(() => {
            console.log('脚本执行完成');
            process.exit(0);
        })
        .catch((error) => {
            console.error('脚本执行失败:', error);
            process.exit(1);
        });
}

module.exports = addStatusColumn;
