const express = require('express');
const router = express.Router();
const db = require('../config/db');
const multer = require('multer');
const sharp = require('sharp');
const path = require('path');
const fs = require('fs');

// 确保上传目录存在
const coolerUploadDir = './public/uploads/cooler';
if (!fs.existsSync(coolerUploadDir)) {
    fs.mkdirSync(coolerUploadDir, { recursive: true });
}

const coolerUpload = multer({
    storage: multer.diskStorage({
        destination: (req, file, cb) => cb(null, coolerUploadDir),
        filename: (req, file, cb) => {
            const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
            const ext = path.extname(file.originalname);
            cb(null, 'cooler-' + uniqueSuffix + ext);
        }
    }),
    fileFilter: (req, file, cb) => {
        if (file.mimetype.startsWith('image/')) {
            cb(null, true);
        } else {
            cb(new Error('只允许上传图片文件!'), false);
        }
    },
    limits: { fileSize: 10 * 1024 * 1024 }
});

const processImage = async (file) => {
    const webpFilename = `${path.basename(file.filename, path.extname(file.filename))}.webp`;
    const webpPath = path.join(coolerUploadDir, webpFilename);
    // 只转换格式，不调整尺寸，使用无损压缩
    await sharp(file.path).webp({ quality: 100, lossless: true }).toFile(webpPath);
    fs.unlinkSync(file.path);
    return `/uploads/cooler/${webpFilename}`;
};

// GET /api/coolers - 获取所有散热器
router.get('/', async (req, res) => {
    try {
        const {
            page = 1,
            limit = 10,
            search = '',
            brand = '',
            type = '',
            tower = '',
            heatpipe = ''
        } = req.query;
        const offset = (page - 1) * limit;

        let whereClauses = [];
        let queryParams = [];

        // 搜索条件
        if (search) {
            whereClauses.push('(model LIKE ? OR brand LIKE ?)');
            queryParams.push(`%${search}%`, `%${search}%`);
        }

        // 品牌筛选
        if (brand) {
            whereClauses.push('brand = ?');
            queryParams.push(brand);
        }

        // 类型筛选
        if (type) {
            whereClauses.push('type = ?');
            queryParams.push(type);
        }

        // 塔数筛选
        if (tower) {
            whereClauses.push('tower_count = ?');
            queryParams.push(tower);
        }

        // 铜管数筛选
        if (heatpipe) {
            whereClauses.push('heatpipe_count = ?');
            queryParams.push(parseInt(heatpipe));
        }

        const whereCondition = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : '';

        const countQuery = `SELECT COUNT(*) as count FROM coolers ${whereCondition}`;
        const dataQuery = `SELECT * FROM coolers ${whereCondition} ORDER BY created_at DESC LIMIT ? OFFSET ?`;

        const countParams = [...queryParams];
        const dataParams = [...queryParams, parseInt(limit), parseInt(offset)];

        const [[countRows], [coolers]] = await Promise.all([
            db.query(countQuery, countParams),
            db.query(dataQuery, dataParams)
        ]);

        const totalRecords = countRows[0].count;
        res.json({
            coolers,
            totalRecords,
            totalPages: Math.ceil(totalRecords / limit),
            currentPage: parseInt(page)
        });
    } catch (error) {
        console.error('获取散热器列表失败:', error);
        res.status(500).json({ message: '服务器内部错误' });
    }
});

// GET /api/coolers/:id - 获取单个散热器
router.get('/:id', async (req, res) => {
    try {
        const [rows] = await db.query('SELECT * FROM coolers WHERE id = ?', [req.params.id]);
        if (rows.length === 0) {
            return res.status(404).json({ message: '未找到指定的散热器' });
        }
        res.json(rows[0]);
    } catch (error) {
        console.error(`获取散热器(id: ${req.params.id})失败:`, error);
        res.status(500).json({ message: '服务器内部错误' });
    }
});

// POST /api/coolers - 创建新散热器
router.post('/', coolerUpload.single('image'), async (req, res) => {
    const conn = await db.getConnection();
    try {
        await conn.beginTransaction();

        const fields = [
            'brand', 'model', 'type', 'tower_count', 'socket_support', 'tdp', 'heatpipe_count',
            'fan_count', 'fan_size', 'radiator_size', 'noise_level', 'height', 'rgb_lighting',
            'color', 'material', 'warranty', 'price', 'notes'
        ];
        const coolerData = {};
        fields.forEach(field => {
            coolerData[field] = req.body[field] || null;
        });

        if (req.file) {
            coolerData.image_url = await processImage(req.file);
        } else {
            coolerData.image_url = '/images/default-cooler.png';
        }

        const [result] = await conn.query('INSERT INTO coolers SET ?', coolerData);
        const [insertedData] = await conn.query('SELECT * FROM coolers WHERE id = ?', [result.insertId]);

        await conn.commit();
        res.status(201).json(insertedData[0]);
    } catch (error) {
        await conn.rollback();
        console.error('创建散热器失败:', error);
        res.status(500).json({ message: '创建失败', error: error.message });
    } finally {
        conn.release();
    }
});

// PUT /api/coolers/:id - 更新散热器
router.put('/:id', coolerUpload.single('image'), async (req, res) => {
    const { id } = req.params;
    const conn = await db.getConnection();
    try {
        await conn.beginTransaction();
        
        const fields = [
            'brand', 'model', 'type', 'tower_count', 'socket_support', 'tdp', 'heatpipe_count',
            'fan_count', 'fan_size', 'radiator_size', 'noise_level', 'height', 'rgb_lighting',
            'color', 'material', 'warranty', 'price', 'notes'
        ];
        const coolerData = {};
        fields.forEach(field => {
            if (req.body[field] !== undefined) {
                coolerData[field] = req.body[field] || null;
            }
        });

        if (req.file) {
            const [existing] = await conn.query('SELECT image_url FROM coolers WHERE id = ?', [id]);
            const oldImage = existing[0]?.image_url;
            if (oldImage && oldImage !== '/images/default-cooler.png') {
                const oldImagePath = path.join(__dirname, '..', 'public', oldImage);
                if(fs.existsSync(oldImagePath)) fs.unlinkSync(oldImagePath);
            }
            coolerData.image_url = await processImage(req.file);
        }

        if (Object.keys(coolerData).length > 0) {
            await conn.query('UPDATE coolers SET ? WHERE id = ?', [coolerData, id]);
        }
        
        const [updatedData] = await conn.query('SELECT * FROM coolers WHERE id = ?', [id]);

        await conn.commit();
        res.json(updatedData[0]);
    } catch (error) {
        await conn.rollback();
        console.error(`更新散热器(id: ${id})失败:`, error);
        res.status(500).json({ message: '更新失败', error: error.message });
    } finally {
        conn.release();
    }
});

// DELETE /api/coolers/:id - 删除散热器
router.delete('/:id', async (req, res) => {
    try {
        const [rows] = await db.query('SELECT image_url FROM coolers WHERE id = ?', [req.params.id]);
        const imageUrl = rows[0]?.image_url;
        if (imageUrl && imageUrl !== '/images/default-cooler.png') {
            const imagePath = path.join(__dirname, '..', 'public', imageUrl);
            if(fs.existsSync(imagePath)) fs.unlinkSync(imagePath);
        }

        const [result] = await db.query('DELETE FROM coolers WHERE id = ?', [req.params.id]);
        if (result.affectedRows === 0) {
            return res.status(404).json({ message: '未找到要删除的散热器' });
        }
        res.status(204).send();
    } catch (error) {
        console.error(`删除散热器(id: ${req.params.id})失败:`, error);
        res.status(500).json({ message: '删除失败', error: error.message });
    }
});

module.exports = router; 