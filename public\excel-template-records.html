<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel模板记录 - 电脑配件管理系统</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="/css/excel-template-records.css">
</head>
<body class="bg-gray-50">
<div class="container mx-auto px-4 py-8">
    <!-- 页面标题 -->
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-800">
            <i class="fas fa-file-excel mr-3"></i>Excel模板记录
        </h1>
        <div class="flex space-x-3">
            <button onclick="goToEditor()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-plus mr-2"></i>新建模板
            </button>
            <button onclick="goBack()" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-arrow-left mr-2"></i>返回
            </button>
        </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="bg-white rounded-lg shadow-md p-4 mb-6">
        <div class="flex flex-wrap gap-4 items-center">
            <div class="flex-1 min-w-64">
                <input type="text" id="searchInput" placeholder="搜索模板名称、备注或创建人..."
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            <button onclick="loadRecords()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-search mr-2"></i>搜索
            </button>
            <button onclick="refreshData()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-refresh mr-2"></i>刷新
            </button>
        </div>
    </div>

    <!-- 记录表格 -->
    <div class="bg-white rounded-lg shadow-md">
        <div class="p-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-800">模板记录列表</h2>
        </div>

        <div class="table-container">
            <table class="w-full">
                <thead class="bg-gray-50 sticky top-0">
                <tr>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        模板名称
                    </th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        备注信息
                    </th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建人
                    </th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        创建时间
                    </th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
                </thead>
                <tbody id="recordsTableBody" class="bg-white divide-y divide-gray-200">
                <!-- 数据将通过JavaScript动态加载 -->
                </tbody>
            </table>
        </div>

        <!-- 分页 -->
        <div class="px-4 py-3 border-t border-gray-200 flex items-center justify-between">
            <div class="text-sm text-gray-700">
                显示第 <span id="pageInfo">1-10</span> 条，共 <span id="totalCount">0</span> 条记录
            </div>
            <div class="flex space-x-2">
                <button id="prevBtn" onclick="changePage(-1)"
                        class="px-3 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 disabled:opacity-50">
                    上一页
                </button>
                <span id="currentPage" class="px-3 py-1 bg-blue-500 text-white rounded">1</span>
                <button id="nextBtn" onclick="changePage(1)"
                        class="px-3 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 disabled:opacity-50">
                    下一页
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 详情模态框 -->
<div id="detailModal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="closeModal()">&times;</span>
        <h2 class="text-2xl font-bold mb-4">模板详情</h2>
        <div id="modalContent">
            <!-- 详情内容将通过JavaScript动态加载 -->
        </div>
    </div>
</div>

<script src="/js/excel-template-records.js"></script>
</body>
</html>
