    /* 字段配置器样式 */
    .field-item {
        background: var(--color-surface);
        border: 2px solid rgba(0, 0, 0, 0.1);
        border-radius: var(--radius-lg);
        padding: var(--spacing-md);
        margin-bottom: var(--spacing-md);
        transition: all var(--transition-fast);
    }

    .dark .field-item {
        border-color: rgba(255, 255, 255, 0.2);
    }

    .field-item:hover {
        border-color: var(--color-primary);
        box-shadow: var(--shadow-md);
    }

    .field-item.dragging {
        opacity: 0.5;
        transform: rotate(5deg);
    }

    /* 生成进度条 */
    .progress-bar {
        width: 100%;
        height: 8px;
        background-color: rgba(0, 0, 0, 0.1);
        border-radius: var(--radius-md);
        overflow: hidden;
    }

    .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, var(--color-primary), var(--color-success));
        border-radius: var(--radius-md);
        transition: width var(--transition-normal);
    }

    /* 预览模态框 */
    .preview-modal {
        position: fixed;
        inset: 0;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 100;
        padding: var(--spacing-md);
    }

    .preview-content {
        background: var(--color-surface);
        border-radius: var(--radius-xl);
        max-width: 90vw;
        max-height: 90vh;
        overflow: auto;
        box-shadow: var(--shadow-xl);
    }