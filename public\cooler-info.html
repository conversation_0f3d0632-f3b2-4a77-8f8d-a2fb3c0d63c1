<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="">
    <title>散热器信息管理 - 电脑配件综合管理系统</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- ViewerJS库 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/viewerjs@1.11.6/dist/viewer.min.css">
    <link rel="stylesheet" href="/css/cooler-info.css">
    <script src="https://cdn.jsdelivr.net/npm/viewerjs@1.11.6/dist/viewer.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="/js/js-pinyin.js"></script>
    <script src="/js/pinyin-wrapper.js"></script>
    <script src="/js/main.js"></script>
    <!-- 权限控制 -->
    <script src="/js/permission-helper.js"></script>
    
</head>

<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-2 sm:px-4 py-4 sm:py-8">
        <header class="mb-4 sm:mb-8">
            <h1 class="text-xl sm:text-3xl font-bold text-cyan-700 flex items-center">
                <i class="fas fa-snowflake mr-2 sm:mr-3"></i> 散热器信息管理
            </h1>
            <div class="flex justify-between items-center">
                <p class="text-gray-600 mt-1 sm:mt-2 text-sm sm:text-base">记录和管理CPU散热器配置信息</p>
                <div class="mt-2 flex space-x-2">
                    <button id="darkModeToggle"
                        class="w-10 h-10 rounded-full bg-gray-200 hover:bg-gray-300 flex items-center justify-center text-gray-700 shadow-sm transition-all duration-300"
                        title="切换暗夜模式">
                        <i class="fas fa-moon"></i>
                    </button>
                    <a href="/pc-components.html"
                        class="inline-block bg-cyan-600 text-white py-1 sm:py-2 px-3 sm:px-4 rounded-md hover:bg-cyan-700 text-sm sm:text-base">
                        <i class="fas fa-arrow-left mr-1"></i> 返回配件列表
                    </a>
                    <a href="/"
                        class="inline-block bg-cyan-600 text-white py-1 sm:py-2 px-3 sm:px-4 rounded-md hover:bg-cyan-700 text-sm sm:text-base">
                        <i class="fas fa-home mr-1"></i> 首页
                    </a>
                </div>
            </div>
        </header>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-4 sm:gap-6">
            <!-- 左侧表单区域 -->
            <div class="lg:col-span-1 bg-white p-3 sm:p-6 rounded-lg shadow-md order-2 sm:order-1">
                <h2 class="text-lg sm:text-xl font-semibold text-gray-800 mb-3 sm:mb-4 flex items-center">
                    <i class="fas fa-plus-circle mr-1 sm:mr-2 text-cyan-500"></i> 添加散热器信息
                </h2>

                <form id="coolerForm" class="space-y-3 sm:space-y-4">
                    <!-- 智能识别输入 -->
                    <div class="border border-gray-200 rounded-md p-3 mb-3">
                        <h3 class="text-md font-medium text-gray-700 mb-2">智能识别输入</h3>
                        <div class="bg-cyan-50 p-3 rounded-lg border border-cyan-200">
                            <div class="flex flex-col sm:flex-row gap-2">
                                <input type="text" id="smartInput" placeholder="粘贴散热器参数信息"
                                    class="flex-1 px-3 py-2 border rounded-md text-sm focus:ring-2 focus:ring-cyan-500">
                                <button type="button" id="autoFillBtn"
                                    class="bg-cyan-600 text-white px-2 py-2 rounded-md hover:bg-cyan-700 text-sm transition-colors whitespace-nowrap flex items-center justify-center">
                                    <i class="fas fa-robot mr-1"></i>解析
                                </button>
                            </div>
                            <p class="mt-2 text-xs text-cyan-600">支持格式：品牌 型号 类型 散热功率 高度 噪音</p>
                        </div>
                    </div>

                    <!-- 1.散热器基本信息 -->
                    <div class="border border-gray-200 rounded-md p-3">
                        <h3 class="text-md font-medium text-gray-700 mb-2 flex items-center">
                            <i class="fas fa-info-circle mr-1 text-cyan-600"></i>基本信息
                        </h3>

                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                            <div class="col-span-1 sm:col-span-2">
                                <label for="model" class="block text-sm font-medium text-gray-700 mb-1">散热器型号 <span
                                        class="text-red-500">*</span></label>
                                <input type="text" id="model"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-cyan-500 focus:border-cyan-500 text-sm"
                                    placeholder="如: NH-D15" required>
                            </div>

                            <div>
                                <label for="brand" class="block text-sm font-medium text-gray-700 mb-1">品牌 <span
                                        class="text-red-500">*</span></label>
                                <select id="brand"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-cyan-500 focus:border-cyan-500 text-sm"
                                    required>
                                    <option value="">选择品牌</option>
                                    <option value="猫头鹰">猫头鹰 (Noctua)</option>
                                    <option value="九州风神">九州风神 (Deepcool)</option>
                                    <option value="航嘉">航嘉 (Huntkey)</option>
                                    <option value="酷冷至尊">酷冷至尊 (Cooler Master)</option>
                                    <option value="酷马">酷马 (Thermaltake)</option>
                                    <option value="海盗船">海盗船 (Corsair)</option>
                                    <option value="ID-COOLING">ID-COOLING</option>
                                    <option value="爱国者">爱国者 (Aigo)</option>
                                    <option value="超频三">超频三 (Pccooler)</option>
                                    <option value="安钛克">安钛克 (Antec)</option>
                                    <option value="利民">利民 (Thermalright)</option>
                                    <option value="快睿">快睿 (Scythe)</option>
                                    <option value="银欣">银欣 (SilverStone)</option>
                                    <option value="美商海盗船">美商海盗船 (Corsair)</option>
                                    <option value="AMD">AMD</option>
                                    <option value="英特尔">英特尔 (Intel)</option>
                                    <option value="半岛铁盒">半岛铁盒</option>
                                    <option value="微星">微星 (MSI)</option>
                                    <option value="AOC">AOC</option>
                                    <option value="瓦尔基里">瓦尔基里 (Valkyrie)</option>
                                    <option value="先马">先马 (Xiamen)</option>
                                    <option value="钛钽">钛钽 (Tantalum)</option>
                                    <option value="创氪星系">创氪星系 (Krypton Galaxy)</option>
                                    <option value="酷里奥">酷里奥 (Coolio)</option>
                                    <option value="乔思伯">乔思伯 (Jonsbo)</option>
                                    <option value="联力">联力 (Lian Li)</option>
                                    <option value="几何未来">几何未来 (Geometric Future)</option>
                                    <option value="其他">其他</option>
                                </select>
                            </div>

                            <div>
                                <label for="type" class="block text-sm font-medium text-gray-700 mb-1">类型 <span
                                        class="text-red-500">*</span></label>
                                <select id="type"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-cyan-500 focus:border-cyan-500 text-sm"
                                    required>
                                    <option value="">选择类型</option>
                                    <option value="风冷">风冷</option>
                                    <option value="水冷">水冷</option>
                                    <option value="AIO">AIO一体式水冷</option>
                                    <option value="半导体">半导体散热</option>
                                    <option value="被动散热">被动散热</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- 2.散热器规格 -->
                    <div class="border border-gray-200 rounded-md p-3">
                        <h3 class="text-md font-medium text-gray-700 mb-2 flex items-center">
                            <i class="fas fa-cogs mr-1 text-cyan-600"></i>规格信息
                        </h3>

                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                            <div class="col-span-1 sm:col-span-2">
                                <label for="socket_support"
                                    class="block text-sm font-medium text-gray-700 mb-1">支持的CPU接口</label>
                                <input type="text" id="socket_support"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-cyan-500 focus:border-cyan-500 text-sm"
                                    placeholder="如: LGA1700, AM4, AM5">
                            </div>

                            <div>
                                <label for="tdp" class="block text-sm font-medium text-gray-700 mb-1">散热功率 (W)</label>
                                <input type="number" id="tdp"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-cyan-500 focus:border-cyan-500 text-sm"
                                    placeholder="如: 180">
                            </div>

                            <div>
                                <label for="height" class="block text-sm font-medium text-gray-700 mb-1">散热器高度
                                    (mm)</label>
                                <input type="number" id="height"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-cyan-500 focus:border-cyan-500 text-sm"
                                    placeholder="如: 165">
                            </div>

                            <div>
                                <label for="fanCount" class="block text-sm font-medium text-gray-700 mb-1">风扇数量</label>
                                <input type="number" id="fanCount"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-cyan-500 focus:border-cyan-500 text-sm"
                                    placeholder="如: 2">
                            </div>

                            <div>
                                <label for="heatpipeCount"
                                    class="block text-sm font-medium text-gray-700 mb-1">铜管数量</label>
                                <input type="number" id="heatpipeCount"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-cyan-500 focus:border-cyan-500 text-sm"
                                    placeholder="如: 6">
                            </div>

                            <div>
                                <label for="towerCount" class="block text-sm font-medium text-gray-700 mb-1">塔数</label>
                                <select id="towerCount"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-cyan-500 focus:border-cyan-500 text-sm">
                                    <option value="">选择塔数</option>
                                    <option value="单塔">单塔</option>
                                    <option value="双塔">双塔</option>
                                </select>
                            </div>

                            <div>
                                <label for="fanSize" class="block text-sm font-medium text-gray-700 mb-1">风扇尺寸
                                    (mm)</label>
                                <input type="text" id="fanSize"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-cyan-500 focus:border-cyan-500 text-sm"
                                    placeholder="如: 120">
                            </div>

                            <div>
                                <label for="noiseLevel" class="block text-sm font-medium text-gray-700 mb-1">噪音级别
                                    (dBA)</label>
                                <input type="text" id="noiseLevel"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-cyan-500 focus:border-cyan-500 text-sm"
                                    placeholder="如: 24.6">
                            </div>

                            <div class="col-span-1 sm:col-span-2">
                                <label for="radiatorSize" class="block text-sm font-medium text-gray-700 mb-1">散热器尺寸
                                    (若为水冷，则为冷排尺寸)</label>
                                <input type="text" id="radiatorSize"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-cyan-500 focus:border-cyan-500 text-sm"
                                    placeholder="如: 280mm 或 165x150x162mm">
                            </div>
                        </div>
                    </div>

                    <!-- 3.其他特性 -->
                    <div class="border border-gray-200 rounded-md p-3">
                        <h3 class="text-md font-medium text-gray-700 mb-2 flex items-center">
                            <i class="fas fa-sliders-h mr-1 text-cyan-600"></i>其他特性
                        </h3>

                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                            <div>
                                <label for="rgbLighting"
                                    class="block text-sm font-medium text-gray-700 mb-1">RGB灯效</label>
                                <select id="rgbLighting"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-cyan-500 focus:border-cyan-500 text-sm">
                                    <option value="">选择</option>
                                    <option value="1">有</option>
                                    <option value="0">无</option>
                                </select>
                            </div>

                            <div>
                                <label for="color" class="block text-sm font-medium text-gray-700 mb-1">颜色</label>
                                <input type="text" id="color"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-cyan-500 focus:border-cyan-500 text-sm"
                                    placeholder="如: 黑色、白色、银色">
                            </div>

                            <div>
                                <label for="material" class="block text-sm font-medium text-gray-700 mb-1">材质</label>
                                <input type="text" id="material"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-cyan-500 focus:border-cyan-500 text-sm"
                                    placeholder="如: 铜底铝鳍片">
                            </div>

                            <div>
                                <label for="warranty" class="block text-sm font-medium text-gray-700 mb-1">保修期
                                    (年)</label>
                                <input type="number" id="warranty"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-cyan-500 focus:border-cyan-500 text-sm"
                                    placeholder="如: 6">
                            </div>

                            <div>
                                <label for="price" class="block text-sm font-medium text-gray-700 mb-1">价格</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 sm:text-sm">¥</span>
                                    </div>
                                    <input type="number" id="price"
                                        class="w-full pl-7 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-cyan-500 focus:border-cyan-500 text-sm"
                                        placeholder="0.00" min="0" step="0.01">
                                </div>
                            </div>

                            <div class="col-span-1 sm:col-span-2">
                                <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">备注</label>
                                <textarea id="notes" rows="3"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-cyan-500 focus:border-cyan-500 text-sm"
                                    placeholder="其他需要备注的信息..."></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- 4.图片上传 -->
                    <div class="border border-gray-200 rounded-md p-3">
                        <h3 class="text-md font-medium text-gray-700 mb-2 flex items-center">
                            <i class="fas fa-image mr-1 text-cyan-600"></i>图片上传
                        </h3>

                        <div
                            class="mt-1 flex justify-center px-4 pt-3 pb-4 border-2 border-gray-300 border-dashed rounded-md">
                            <div class="space-y-1 text-center">
                                <div class="flex text-sm text-gray-600 items-center justify-center flex-wrap">
                                    <label for="coolerImage"
                                        class="relative cursor-pointer bg-white rounded-md font-medium text-cyan-600 hover:text-cyan-500 focus-within:outline-none">
                                        <span>上传图片</span>
                                        <input id="coolerImage" name="coolerImage" type="file" accept="image/*"
                                            class="sr-only">
                                    </label>
                                </div>
                                <p class="text-xs text-gray-500">PNG, JPG, GIF 格式，大小不超过5MB</p>
                                <p class="text-xs text-gray-500 mt-1">不上传图片时将使用默认图片</p>
                            </div>
                        </div>
                        <!-- 上传进度条 -->
                        <div id="uploadProgressContainer" class="mt-2 hidden">
                            <div class="bg-gray-200 dark:bg-gray-700 rounded-full h-3 mb-2 overflow-hidden">
                                <div id="uploadProgressBar" class="bg-gradient-to-r from-cyan-500 to-cyan-600 dark:from-cyan-400 dark:to-cyan-500 h-3 rounded-full transition-all duration-500 ease-out relative" style="width: 0%">
                                    <!-- 进度条光泽效果 -->
                                    <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-20 animate-pulse"></div>
                                </div>
                            </div>
                            <div class="flex justify-between items-center text-xs">
                                <span id="uploadProgressText" class="text-gray-600 dark:text-gray-400 flex items-center">
                                    <i id="uploadProgressIcon" class="fas fa-upload mr-1"></i>
                                    准备上传...
                                </span>
                                <span id="uploadProgressPercent" class="text-cyan-600 dark:text-cyan-400 font-medium">0%</span>
                            </div>
                        </div>

                        <div id="imagePreviewContainer" class="mt-2 hidden">
                            <div class="relative inline-block">
                                <img id="imagePreview" src="#" alt="预览图" class="h-24 rounded-md shadow image-preview">
                                <button type="button" id="removeImageBtn"
                                    class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center hover:bg-red-600">
                                    <i class="fas fa-times text-xs"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div>
                        <button type="submit"
                            class="w-full bg-cyan-600 hover:bg-cyan-700 text-white py-2 px-4 rounded-md transition-colors flex items-center justify-center">
                            <i class="fas fa-save mr-2"></i> 保存散热器信息
                        </button>
                    </div>
                </form>
            </div>

            <!-- 右侧列表区域 -->
            <div class="lg:col-span-3 order-1 sm:order-2">
                <!-- 搜索和过滤 -->
                <div class="bg-white p-3 sm:p-6 rounded-lg shadow-md mb-4 sm:mb-6">
                    <h2 class="text-xl font-semibold text-cyan-800 mb-4 flex items-center">
                        <i class="fas fa-snowflake mr-2 sm:mr-3"></i> 散热器列表
                    </h2>

                    <!-- 搜索和过滤器 -->
                    <div class="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-4">
                        <!-- 搜索框 -->
                        <div class="w-full sm:w-2/5">
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-search text-gray-400"></i>
                                </div>
                                <input type="text" id="coolerSearch" placeholder="搜索散热器型号、品牌等..."
                                    class="w-full py-2 pl-10 pr-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-cyan-500 focus:border-cyan-500">
                            </div>
                        </div>

                        <!-- 过滤器 -->
                        <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-2 w-full">
                            <!-- 品牌筛选 -->
                            <select id="brandFilter"
                                class="border border-gray-300 rounded-md shadow-sm py-2 pl-3 pr-8 focus:outline-none focus:ring-cyan-500 focus:border-cyan-500 text-sm">
                                <option value="all">所有品牌</option>
                                <option value="猫头鹰">猫头鹰 (Noctua)</option>
                                <option value="九州风神">九州风神 (Deepcool)</option>
                                <option value="酷冷至尊">酷冷至尊 (Cooler Master)</option>
                                <option value="海盗船">海盗船 (Corsair)</option>
                                <option value="利民">利民 (Thermalright)</option>
                                <option value="ID-COOLING">ID-COOLING</option>
                                <option value="快睿">快睿 (Scythe)</option>
                                <option value="Arctic">Arctic</option>
                                <option value="航嘉">航嘉 (Huntkey)</option>
                                <option value="酷马">酷马 (Thermaltake)</option>
                                <option value="爱国者">爱国者 (Aigo)</option>
                                <option value="半岛铁盒">半岛铁盒</option>
                                <option value="微星">微星 (MSI)</option>
                                <option value="AOC">AOC</option>
                                <option value="瓦尔基里">瓦尔基里</option>
                                <option value="先马">先马</option>
                                <option value="钛钽">钛钽</option>
                                <option value="创氪星系">创氪星系</option>
                                <option value="酷里奥">酷里奥</option>
                                <option value="乔思伯">乔思伯</option>
                                <option value="联力">联力</option>
                                <option value="几何未来">几何未来</option>
                                <option value="超频三">超频三 (PcCooler)</option>
                                <option value="安钛克">安钛克 (Antec)</option>
                                <option value="银欣">银欣 (SilverStone)</option>
                                <option value="AMD">AMD</option>
                                <option value="Intel">Intel</option>
                                <option value="其他">其他品牌</option>
                            </select>

                            <!-- 类型筛选 -->
                            <select id="typeFilter"
                                class="border border-gray-300 rounded-md shadow-sm py-2 pl-3 pr-8 focus:outline-none focus:ring-cyan-500 focus:border-cyan-500 text-sm">
                                <option value="all">所有类型</option>
                                <option value="风冷">风冷</option>
                                <option value="AIO">水冷</option>
                                <option value="塔式">塔式</option>
                                <option value="下压式">下压式</option>
                            </select>

                            <!-- 塔数筛选 -->
                            <select id="towerFilter"
                                class="border border-gray-300 rounded-md shadow-sm py-2 pl-3 pr-8 focus:outline-none focus:ring-cyan-500 focus:border-cyan-500 text-sm">
                                <option value="all">所有塔数</option>
                                <option value="单塔">单塔</option>
                                <option value="双塔">双塔</option>
                                <option value="三塔">三塔</option>
                                <option value="四塔">四塔</option>
                            </select>

                            <!-- 铜管数筛选 -->
                            <select id="heatpipeFilter"
                                class="border border-gray-300 rounded-md shadow-sm py-2 pl-3 pr-8 focus:outline-none focus:ring-cyan-500 focus:border-cyan-500 text-sm">
                                <option value="all">所有铜管</option>
                                <option value="2">2根铜管</option>
                                <option value="3">3根铜管</option>
                                <option value="4">4根铜管</option>
                                <option value="5">5根铜管</option>
                                <option value="6">6根铜管</option>
                                <option value="7">7根铜管</option>
                                <option value="8">8根铜管</option>
                                <option value="9">9根铜管</option>
                                <option value="10">10根铜管</option>
                                <option value="12">12根铜管</option>
                            </select>



                            <!-- 重置按钮 -->
                            <button id="resetFilterBtn"
                                class="bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-3 rounded-md flex items-center justify-center text-sm">
                                <i class="fas fa-sync-alt mr-1"></i> 重置
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 散热器列表 -->
                <div class="bg-white rounded-lg shadow-md overflow-x-auto">
                    <div class="table-wrapper">
                        <table class="min-w-full divide-y divide-gray-200 table-compact sm:table-auto cooler-table">
                            <thead class="bg-gray-50 hidden sm:table-header-group">
                                <tr>
                                    <th scope="col"
                                        class="px-3 py-2 sm:px-4 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider model-column col-model">
                                        散热器型号
                                    </th>
                                    <th scope="col"
                                        class="px-3 py-2 sm:px-4 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell brand-column col-brand">
                                        品牌
                                    </th>
                                    <th scope="col"
                                        class="px-3 py-2 sm:px-4 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider type-column col-type">
                                        类型
                                    </th>
                                    <th scope="col"
                                        class="px-3 py-2 sm:px-4 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell">
                                        塔数
                                    </th>
                                    <th scope="col"
                                        class="px-3 py-2 sm:px-4 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell tdp-column col-tdp">
                                        TDP
                                    </th>
                                    <th scope="col"
                                        class="px-3 py-2 sm:px-4 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell color-column col-color">
                                        颜色
                                    </th>
                                    <th scope="col"
                                        class="px-3 py-2 sm:px-4 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell height-column col-height">
                                        高度
                                    </th>
                                    <th scope="col"
                                        class="px-3 py-2 sm:px-4 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell fan-column col-fan">
                                        风扇数
                                    </th>
                                    <th scope="col"
                                        class="px-3 py-2 sm:px-4 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell pipe-column col-pipe">
                                        铜管数
                                    </th>
                                    <th scope="col"
                                        class="px-3 py-2 sm:px-4 sm:py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider action-column col-action">
                                        操作
                                    </th>
                                </tr>
                            </thead>
                            <tbody id="coolerTableBody" class="bg-white divide-y divide-gray-200">
                                <!-- 数据将通过JavaScript填充 -->
                                <tr>
                                    <td colspan="10" class="px-4 py-4 text-center text-sm text-gray-500">
                                        <div class="flex items-center justify-center">
                                            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-cyan-500"
                                                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                                    stroke-width="4"></circle>
                                                <path class="opacity-75" fill="currentColor"
                                                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                                </path>
                                            </svg>
                                            <span>加载中...</span>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页控件 -->
                    <div
                        class="mt-4 px-4 py-4 border-t border-gray-200 sm:px-6 flex flex-wrap justify-between items-center pagination-mobile">
                        <div class="text-sm text-gray-700 mb-2 sm:mb-0" id="totalCount">
                            共 <span id="recordCount">0</span> 条记录
                        </div>
                        <div class="flex items-center space-x-1 sm:space-x-2 page-controls-mobile">
                            <button id="firstPage" title="首页"
                                class="px-2 py-1 border border-gray-300 rounded-md text-sm bg-white text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                <i class="fas fa-angle-double-left"></i>
                            </button>
                            <button id="prevPage" title="上一页"
                                class="px-2 py-1 border border-gray-300 rounded-md text-sm bg-white text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                <i class="fas fa-chevron-left"></i>
                            </button>

                            <div id="pageNumbers" class="hidden sm:flex space-x-1">
                                <!-- 页码将通过JavaScript填充 -->
                            </div>

                            <span id="pageInfo" class="px-2 py-1 text-sm whitespace-nowrap">第 <span
                                    id="currentPageDisplay">1</span> / <span id="totalPagesDisplay">1</span> 页</span>

                            <button id="nextPage" title="下一页"
                                class="px-2 py-1 border border-gray-300 rounded-md text-sm bg-white text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                            <button id="lastPage" title="尾页"
                                class="px-2 py-1 border border-gray-300 rounded-md text-sm bg-white text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                <i class="fas fa-angle-double-right"></i>
                            </button>

                            <div class="hidden sm:flex items-center ml-2">
                                <input type="number" id="pageJump" min="1"
                                    class="w-12 ml-1 px-2 py-1 border border-gray-300 rounded-md text-sm">
                                <button id="goToPage"
                                    class="ml-1 px-2 py-1 bg-cyan-600 text-white text-sm rounded-md hover:bg-cyan-700">
                                    确定
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 散热器详情模态框 -->
        <div id="coolerModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden"
            style="display:none;">
            <div
                class="bg-white rounded-lg shadow-xl p-4 sm:p-6 max-w-4xl w-full max-h-[90vh] overflow-y-auto relative">
                <div class="flex justify-between items-center mb-4">
                    <h3 id="detailTitle" class="text-lg sm:text-xl font-semibold text-gray-800">散热器详情</h3>
                    <button id="closeModal"
                        class="text-gray-500 hover:text-gray-700 p-2 text-2xl sm:text-xl absolute top-2 right-2">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div id="coolerDetails" class="space-y-4">
                    <!-- 详情会动态填充 -->
                </div>

                <div
                    class="mt-6 flex flex-wrap justify-center sm:justify-end space-x-0 space-y-2 sm:space-y-0 sm:space-x-2">
                    <button id="editBtn"
                        class="w-full sm:w-auto px-4 py-3 sm:py-2 bg-cyan-600 text-white rounded-md hover:bg-cyan-700 text-base font-medium">
                        <i class="fas fa-edit mr-2"></i> 编辑
                    </button>
                    <button id="deleteBtn"
                        class="w-full sm:w-auto px-4 py-3 sm:py-2 bg-red-600 text-white rounded-md hover:bg-red-700 text-base font-medium">
                        <i class="fas fa-trash mr-2"></i> 删除
                    </button>
                    <button id="closeModalBtn"
                        class="w-full sm:w-auto px-4 py-3 sm:py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 text-base font-medium">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="/js/cooler-info.js"></script>
</body>

</html>