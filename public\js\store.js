// 跟踪库加载状态
window.librariesLoaded = {
    main: false,
    pinyin: false
};

function checkAllLibrariesLoaded() {
    console.log('Library load status:', window.librariesLoaded);
    if (window.librariesLoaded.main && window.librariesLoaded.pinyin) {
        // All libraries loaded
        console.log('All libraries loaded successfully');
        console.log('Testing pinyinUtil:', window.pinyinUtil);
        if (window.pinyinUtil) {
            try {
                console.log('Test pinyinUtil methods:',
                    window.pinyinUtil.getFullPinyin('测试'),
                    window.pinyinUtil.getFirstLetters('测试')
                );
            } catch (e) {
                console.error('Error testing pinyinUtil:', e);
            }
        }

        // Hide loading overlay after all libraries are loaded
        setTimeout(hideLoadingOverlay, 500);
    }
}

function handleLibraryError(libraryName) {
    console.error(`Failed to load ${libraryName}`);
    // Still hide loading overlay after a timeout even if there's an error
    setTimeout(hideLoadingOverlay, 2000);
}

function showLoadingOverlay() {
    const overlay = document.createElement('div');
    overlay.id = 'loading-overlay';
    overlay.style.cssText = 'position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.7);z-index:9999;display:flex;justify-content:center;align-items:center;flex-direction:column;color:white;';

    const spinner = document.createElement('div');
    spinner.style.cssText = 'border:5px solid #f3f3f3;border-top:5px solid #3498db;border-radius:50%;width:50px;height:50px;animation:spin 2s linear infinite;margin-bottom:20px;';
    spinner.innerHTML = '<style>@keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}</style>';

    const message = document.createElement('div');
    message.innerText = '正在加载必要组件，请稍候...';

    overlay.appendChild(spinner);
    overlay.appendChild(message);
    document.documentElement.appendChild(overlay);

    // In case the loading takes too long, hide the overlay after a timeout
    setTimeout(hideLoadingOverlay, 5000);
}

function hideLoadingOverlay() {
    const overlay = document.getElementById('loading-overlay');
    if (overlay) {
        overlay.style.opacity = '0';
        overlay.style.transition = 'opacity 0.5s';
        setTimeout(() => {
            if (overlay.parentNode) {
                overlay.parentNode.removeChild(overlay);
            }
        }, 500);
    }
}

// Show loading overlay as soon as the page starts loading
showLoadingOverlay();