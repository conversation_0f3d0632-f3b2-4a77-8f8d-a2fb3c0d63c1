/* 渐变动画 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

/* 卡片悬浮效果 */
.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-5px);
}

/* 规格标签样式 */
.spec-tag {
    @apply px-2 py-0.5 rounded-md text-xs font-medium;
}

/* 规格标签颜色 - 更鲜明的配色方案 */
.spec-tag.size {
    @apply bg-blue-100 text-blue-800 border border-blue-200 font-semibold;
}

.spec-tag.rpm {
    @apply bg-amber-100 text-amber-800 border border-amber-200 font-semibold;
}

/* 深色模式下的表格样式优化 */
.dark .model-title {
    @apply text-slate-100;
}

.dark tbody tr {
    @apply border-slate-700 transition-colors duration-150;
}

.dark tbody tr:hover {
    @apply bg-slate-700/50;
}

.dark .spec-tag.size {
    @apply bg-blue-900/30 text-blue-300 border border-blue-800/40;
}

.dark .spec-tag.rpm {
    @apply bg-amber-900/30 text-amber-300 border border-amber-800/40;
}

/* 风扇品牌标签样式 */
.brand-badge {
    @apply px-2 py-1 rounded-md text-xs font-semibold;
}

/* 按钮点击效果 */
.btn-click {
    transform-origin: center;
    transition: transform 0.1s ease-out;
}

.btn-click:active {
    transform: scale(0.95);
}

/* 按钮处理中状态 */
.btn-click.processing {
    @apply opacity-90 cursor-wait bg-gradient-to-r from-primary-700 to-primary-600 shadow-inner;
}

/* 表格渐入效果 */
.row-animate:nth-child(1) {
    animation-delay: 0.05s;
}

.row-animate:nth-child(2) {
    animation-delay: 0.1s;
}

.row-animate:nth-child(3) {
    animation-delay: 0.15s;
}

.row-animate:nth-child(4) {
    animation-delay: 0.2s;
}

.row-animate:nth-child(5) {
    animation-delay: 0.25s;
}

.row-animate:nth-child(6) {
    animation-delay: 0.3s;
}

.row-animate:nth-child(7) {
    animation-delay: 0.35s;
}

.row-animate:nth-child(8) {
    animation-delay: 0.4s;
}

.row-animate:nth-child(9) {
    animation-delay: 0.45s;
}

.row-animate:nth-child(10) {
    animation-delay: 0.5s;
}
