const express = require('express');
const router = express.Router();
const db = require('../config/db');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const sharp = require('sharp');

// 配置multer用于硬盘图片上传
const storageImgStorage = multer.diskStorage({
    destination: function (req, file, cb) {
        const uploadDir = path.join(__dirname, '..', 'public', 'uploads', 'storage');
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const fileExtension = path.extname(file.originalname);
        cb(null, 'storage-' + uniqueSuffix + fileExtension);
    }
});

const storageUpload = multer({
    storage: storageImgStorage,
    limits: { fileSize: 5 * 1024 * 1024 }, // 5MB
    fileFilter: function (req, file, cb) {
        const filetypes = /jpeg|jpg|png|gif/;
        const mimetype = filetypes.test(file.mimetype);
        const extname = filetypes.test(path.extname(file.originalname).toLowerCase());

        if (mimetype && extname) {
            return cb(null, true);
        }
        cb(new Error('只允许上传JPG, PNG, GIF格式的图片'));
    }
});

// 辅助函数：安全地解析数字
const parseIntSafe = (value) => value === '' || value === undefined || value === null ? null : (Number.isNaN(parseInt(value)) ? null : parseInt(value));
const parseFloatSafe = (value) => value === '' || value === undefined || value === null ? null : (Number.isNaN(parseFloat(value)) ? null : parseFloat(value));

// 获取所有硬盘 (带分页和搜索)
router.get('/', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const offset = (page - 1) * limit;
        const search = req.query.search || '';
        const brand = req.query.brand || '';
        const type = req.query.type || '';
        const sortField = req.query.sortField || 'created_at';
        const sortOrder = req.query.sortOrder === 'asc' ? 'ASC' : 'DESC';

        let whereConditions = [];
        let params = [];

        // 处理搜索条件
        if (search) {
            whereConditions.push(`(brand LIKE ? OR model LIKE ? OR type LIKE ? OR interface LIKE ? OR form_factor LIKE ?)`);
            params = params.concat(Array(5).fill(`%${search}%`));
        }

        // 处理品牌筛选
        if (brand && brand !== 'all') {
            whereConditions.push(`brand = ?`);
            params.push(brand);
        }

        // 处理类型筛选
        if (type && type !== 'all') {
            whereConditions.push(`type = ?`);
            params.push(type);
        }

        // 组合WHERE子句
        let whereClause = '';
        if (whereConditions.length > 0) {
            whereClause = `WHERE ${whereConditions.join(' AND ')}`;
        }

        const countQuery = `SELECT COUNT(*) as total FROM storage ${whereClause}`;
        const [countResult] = await db.query(countQuery, params);
        const total = countResult[0].total;

        const dataQuery = `
            SELECT * FROM storage 
            ${whereClause} 
            ORDER BY ${sortField} ${sortOrder} 
            LIMIT ? OFFSET ?
        `;
        const [storageDevices] = await db.query(dataQuery, [...params, limit, offset]);

        res.json({
            storages: storageDevices,
            total,
            page,
            pages: Math.ceil(total / limit)
        });
    } catch (error) {
        console.error('获取硬盘列表失败:', error);
        res.status(500).json({ message: '获取硬盘列表失败', error: error.message });
    }
});

// 获取单个硬盘
router.get('/:id', async (req, res) => {
    try {
        const [storage] = await db.query('SELECT * FROM storage WHERE id = ?', [req.params.id]);
        if (storage.length > 0) {
            res.json(storage[0]);
        } else {
            res.status(404).json({ message: '未找到指定的硬盘' });
        }
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// 创建硬盘
router.post('/', storageUpload.single('image'), async (req, res) => {
    try {
        let imageUrl = null;
        if (req.file) {
            const tempPath = req.file.path;
            const outputFilename = path.basename(tempPath, path.extname(tempPath)) + '.webp';
            const outputPath = path.join(path.dirname(tempPath), outputFilename);

            await sharp(tempPath)
                .webp({ quality: 80 })
                .toFile(outputPath);
            
            fs.unlinkSync(tempPath); // 删除原图
            imageUrl = `/uploads/storage/${outputFilename}`;
        }
        
        const {
            brand, model, type, interface, form_factor, capacity, 
            read_speed, write_speed, cacheSize, tbw, mtbf,
            price, notes
        } = req.body;

        const storageData = {
            brand, 
            model, 
            type, 
            interface, 
            form_factor,
            capacity: parseIntSafe(capacity),
            read_speed: parseIntSafe(read_speed),
            write_speed: parseIntSafe(write_speed),
            cacheSize: parseIntSafe(cacheSize),
            tbw: parseIntSafe(tbw),
            mtbf,
            price: parseFloatSafe(price),
            notes,
            image_url: imageUrl
        };

        console.log('--- [后端调试] 即将写入数据库的数据 (创建): ---');
        console.log(storageData);

        const [result] = await db.query('INSERT INTO storage SET ?', storageData);
        res.status(201).json({ id: result.insertId, ...storageData });
    } catch (error) {
        console.error('创建硬盘失败:', error);
        res.status(500).json({ message: '创建硬盘失败', error: error.message });
    }
});

// 更新硬盘
router.put('/:id', storageUpload.single('image'), async (req, res) => {
    try {
        const { id } = req.params;
        const {
            brand, model, type, interface, form_factor, capacity, 
            read_speed, write_speed, cacheSize, tbw, mtbf,
            price, notes
        } = req.body;

        const storageData = {
            brand, 
            model, 
            type, 
            interface, 
            form_factor,
            capacity: parseIntSafe(capacity),
            read_speed: parseIntSafe(read_speed),
            write_speed: parseIntSafe(write_speed),
            cacheSize: parseIntSafe(cacheSize),
            tbw: parseIntSafe(tbw),
            mtbf,
            price: parseFloatSafe(price),
            notes
        };

        if (req.file) {
            const tempPath = req.file.path;
            const outputFilename = path.basename(tempPath, path.extname(tempPath)) + '.webp';
            const outputPath = path.join(path.dirname(tempPath), outputFilename);

            await sharp(tempPath)
                .webp({ quality: 100, lossless: true })
                .toFile(outputPath);
            
            fs.unlinkSync(tempPath);
            storageData.image_url = `/uploads/storage/${outputFilename}`;
        }
        
        console.log(`--- [后端调试] 即将写入数据库的数据 (更新 ID: ${id}): ---`);
        console.log(storageData);

        await db.query('UPDATE storage SET ? WHERE id = ?', [storageData, id]);
        res.json({ id, ...storageData });
    } catch (error) {
        console.error('更新硬盘失败:', error);
        res.status(500).json({ message: '更新硬盘失败', error: error.message });
    }
});

// 删除硬盘
router.delete('/:id', async (req, res) => {
    try {
        const [result] = await db.query('DELETE FROM storage WHERE id = ?', [req.params.id]);
        
        // 检查是否真的有记录被删除了
        if (result.affectedRows > 0) {
            // 返回一个成功的JSON响应
            res.json({ success: true, message: '硬盘删除成功' });
        } else {
            // 如果没有记录被删除 (例如ID不存在)，返回404
            res.status(404).json({ success: false, message: '未找到要删除的硬盘' });
        }
    } catch (error) {
        res.status(500).json({ success: false, message: error.message });
    }
});

module.exports = router; 