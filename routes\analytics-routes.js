// routes/analytics-routes.js - 数据分析API路由
const express = require('express');
const router = express.Router();
const { query, validationResult } = require('express-validator');
const db = require('../db');
const { verifyToken, checkAdminRole } = require('../middleware/auth');

// 缓存机制
const cache = new Map();
const CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存

// 生成缓存键
function generateCacheKey(endpoint, userId, params) {
    return `${endpoint}_${userId}_${JSON.stringify(params)}`;
}

// 获取缓存数据
function getCachedData(key) {
    const cached = cache.get(key);
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
        return cached.data;
    }
    cache.delete(key);
    return null;
}

// 设置缓存数据
function setCachedData(key, data) {
    cache.set(key, {
        data,
        timestamp: Date.now()
    });
}

// GET /api/analytics/user-stats - 用户统计数据
router.get('/user-stats', verifyToken, [
    query('timeRange').optional().isIn(['7d', '30d', '90d', 'all']).withMessage('时间范围无效'),
    query('userId').optional().isInt().withMessage('用户ID必须是整数')
], async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ success: false, message: '参数验证失败', errors: errors.array() });
    }

    try {
        const { timeRange = '30d', userId } = req.query;
        const isAdmin = req.user.role === 'admin';
        
        // 权限检查：普通用户只能查看自己的数据
        const targetUserId = isAdmin && userId ? parseInt(userId) : req.user.id;
        
        // 生成缓存键
        const cacheKey = generateCacheKey('user-stats', targetUserId, { timeRange });
        const cachedData = getCachedData(cacheKey);
        if (cachedData) {
            return res.json({ success: true, data: cachedData, cached: true });
        }

        // 构建时间条件
        let timeCondition = '';
        if (timeRange !== 'all') {
            const days = parseInt(timeRange.replace('d', ''));
            timeCondition = `AND created_at >= DATE_SUB(NOW(), INTERVAL ${days} DAY)`;
        }

        // 查询用户基本信息
        const [userInfo] = await db.query(`
            SELECT id, username, role, created_at 
            FROM users 
            WHERE id = ?
        `, [targetUserId]);

        if (userInfo.length === 0) {
            return res.status(404).json({ success: false, message: '用户不存在' });
        }

        // 查询价格记录统计
        const [priceStats] = await db.query(`
            SELECT
                COUNT(*) as record_count,
                COALESCE(SUM(total_amount), 0) as total_amount,
                COALESCE(AVG(total_amount), 0) as avg_amount,
                MIN(created_at) as first_record,
                MAX(created_at) as last_record
            FROM price_records
            WHERE user_id = ? ${timeCondition}
        `, [targetUserId]);

        // 查询今日总金额
        const [todayStats] = await db.query(`
            SELECT
                COALESCE(SUM(total_amount), 0) as today_total_amount,
                COUNT(*) as today_record_count
            FROM price_records
            WHERE user_id = ? AND DATE(created_at) = CURDATE()
        `, [targetUserId]);

        // 查询原始表格数据统计
        const [rawDataStats] = await db.query(`
            SELECT 
                COUNT(*) as conversion_count,
                COALESCE(SUM(total_amount), 0) as total_raw_amount
            FROM raw_table_data 
            WHERE user_id = ? ${timeCondition}
        `, [targetUserId]);

        // 查询价格记录明细统计
        const [itemStats] = await db.query(`
            SELECT
                COUNT(pi.id) as total_items,
                COUNT(DISTINCT pi.operation_type) as operation_types
            FROM price_items pi
            JOIN price_records pr ON pi.record_id = pr.id
            WHERE pr.user_id = ? ${timeCondition.replace('created_at', 'pr.created_at')}
        `, [targetUserId]);

        // 组装结果
        const result = {
            user: userInfo[0],
            timeRange,
            priceRecords: {
                count: priceStats[0].record_count,
                totalAmount: parseFloat(priceStats[0].total_amount),
                avgAmount: parseFloat(priceStats[0].avg_amount),
                firstRecord: priceStats[0].first_record,
                lastRecord: priceStats[0].last_record
            },
            todayStats: {
                totalAmount: parseFloat(todayStats[0].today_total_amount),
                recordCount: todayStats[0].today_record_count
            },
            rawDataConversions: {
                count: rawDataStats[0].conversion_count,
                totalAmount: parseInt(rawDataStats[0].total_raw_amount)
            },
            priceItems: {
                totalItems: itemStats[0].total_items,
                operationTypes: itemStats[0].operation_types
            }
        };

        // 缓存结果
        setCachedData(cacheKey, result);

        res.json({ success: true, data: result });
    } catch (error) {
        console.error('获取用户统计数据失败:', error);
        res.status(500).json({ success: false, message: '服务器错误', error: error.message });
    }
});

// GET /api/analytics/activity-trends - 活跃度趋势分析
router.get('/activity-trends', verifyToken, [
    query('timeRange').optional().isIn(['7d', '30d', '90d']).withMessage('时间范围无效'),
    query('userId').optional().isInt().withMessage('用户ID必须是整数')
], async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ success: false, message: '参数验证失败', errors: errors.array() });
    }

    try {
        const { timeRange = '30d', userId } = req.query;
        const isAdmin = req.user.role === 'admin';
        
        // 权限检查
        const targetUserId = isAdmin && userId ? parseInt(userId) : req.user.id;
        
        // 生成缓存键
        const cacheKey = generateCacheKey('activity-trends', targetUserId, { timeRange });
        const cachedData = getCachedData(cacheKey);
        if (cachedData) {
            return res.json({ success: true, data: cachedData, cached: true });
        }

        const days = parseInt(timeRange.replace('d', ''));

        // 查询每日价格记录创建数量
        const [dailyPriceRecords] = await db.query(`
            SELECT 
                DATE(created_at) as date,
                COUNT(*) as count,
                SUM(total_amount) as total_amount
            FROM price_records 
            WHERE user_id = ? 
            AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
            GROUP BY DATE(created_at)
            ORDER BY date ASC
        `, [targetUserId, days]);

        // 查询每日原始数据转换数量
        const [dailyRawData] = await db.query(`
            SELECT 
                DATE(created_at) as date,
                COUNT(*) as count
            FROM raw_table_data 
            WHERE user_id = ? 
            AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
            GROUP BY DATE(created_at)
            ORDER BY date ASC
        `, [targetUserId, days]);

        // 查询每小时活跃度分布
        const [hourlyActivity] = await db.query(`
            SELECT
                HOUR(created_at) as hour,
                COUNT(*) as total_operations
            FROM (
                SELECT created_at FROM price_records
                WHERE user_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
                UNION ALL
                SELECT created_at FROM raw_table_data
                WHERE user_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
            ) as all_operations
            GROUP BY HOUR(created_at)
            ORDER BY hour ASC
        `, [targetUserId, days, targetUserId, days]);

        const result = {
            timeRange,
            dailyPriceRecords,
            dailyRawData,
            hourlyActivity
        };

        // 缓存结果
        setCachedData(cacheKey, result);

        res.json({ success: true, data: result });
    } catch (error) {
        console.error('获取活跃度趋势失败:', error);
        res.status(500).json({ success: false, message: '服务器错误', error: error.message });
    }
});

// GET /api/analytics/operation-habits - 操作习惯分析
router.get('/operation-habits', verifyToken, [
    query('timeRange').optional().isIn(['7d', '30d', '90d', 'all']).withMessage('时间范围无效'),
    query('userId').optional().isInt().withMessage('用户ID必须是整数')
], async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ success: false, message: '参数验证失败', errors: errors.array() });
    }

    try {
        const { timeRange = '30d', userId } = req.query;
        const isAdmin = req.user.role === 'admin';
        
        // 权限检查
        const targetUserId = isAdmin && userId ? parseInt(userId) : req.user.id;
        
        // 生成缓存键
        const cacheKey = generateCacheKey('operation-habits', targetUserId, { timeRange });
        const cachedData = getCachedData(cacheKey);
        if (cachedData) {
            return res.json({ success: true, data: cachedData, cached: true });
        }

        // 构建时间条件
        let timeCondition = '';
        if (timeRange !== 'all') {
            const days = parseInt(timeRange.replace('d', ''));
            timeCondition = `AND created_at >= DATE_SUB(NOW(), INTERVAL ${days} DAY)`;
        }

        // 查询操作类型分布
        const [operationTypes] = await db.query(`
            SELECT
                operation_type,
                COUNT(*) as count,
                AVG(price) as avg_price
            FROM price_items pi
            JOIN price_records pr ON pi.record_id = pr.id
            WHERE pr.user_id = ? ${timeCondition.replace('created_at', 'pr.created_at')}
            GROUP BY operation_type
            ORDER BY count DESC
        `, [targetUserId]);

        // 查询金额分布
        const [amountDistribution] = await db.query(`
            SELECT 
                CASE 
                    WHEN total_amount < 100 THEN '0-100'
                    WHEN total_amount < 500 THEN '100-500'
                    WHEN total_amount < 1000 THEN '500-1000'
                    WHEN total_amount < 2000 THEN '1000-2000'
                    ELSE '2000+'
                END as amount_range,
                COUNT(*) as count
            FROM price_records 
            WHERE user_id = ? ${timeCondition}
            GROUP BY amount_range
            ORDER BY 
                CASE amount_range
                    WHEN '0-100' THEN 1
                    WHEN '100-500' THEN 2
                    WHEN '500-1000' THEN 3
                    WHEN '1000-2000' THEN 4
                    WHEN '2000+' THEN 5
                END
        `, [targetUserId]);

        // 查询工作日vs周末活跃度
        const [weekdayActivity] = await db.query(`
            SELECT 
                CASE 
                    WHEN DAYOFWEEK(created_at) IN (1, 7) THEN 'weekend'
                    ELSE 'weekday'
                END as day_type,
                COUNT(*) as operations
            FROM (
                SELECT created_at FROM price_records 
                WHERE user_id = ? ${timeCondition}
                UNION ALL
                SELECT created_at FROM raw_table_data 
                WHERE user_id = ? ${timeCondition}
            ) as all_operations
            GROUP BY day_type
        `, [targetUserId, targetUserId]);

        const result = {
            timeRange,
            operationTypes,
            amountDistribution,
            weekdayActivity
        };

        // 缓存结果
        setCachedData(cacheKey, result);

        res.json({ success: true, data: result });
    } catch (error) {
        console.error('获取操作习惯分析失败:', error);
        res.status(500).json({ success: false, message: '服务器错误', error: error.message });
    }
});

// GET /api/analytics/all-users-overview - 所有用户概览 (仅管理员)
router.get('/all-users-overview', checkAdminRole, [
    query('timeRange').optional().isIn(['7d', '30d', '90d', 'all']).withMessage('时间范围无效')
], async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ success: false, message: '参数验证失败', errors: errors.array() });
    }

    try {
        const { timeRange = '30d' } = req.query;

        // 生成缓存键
        const cacheKey = generateCacheKey('all-users-overview', 'admin', { timeRange });
        const cachedData = getCachedData(cacheKey);
        if (cachedData) {
            return res.json({ success: true, data: cachedData, cached: true });
        }

        // 构建时间条件
        let timeCondition = '';
        if (timeRange !== 'all') {
            const days = parseInt(timeRange.replace('d', ''));
            timeCondition = `AND pr.created_at >= DATE_SUB(NOW(), INTERVAL ${days} DAY)`;
        }

        // 查询所有用户的统计数据
        const [userStats] = await db.query(`
            SELECT
                u.id,
                u.username,
                u.role,
                u.created_at as user_created_at,
                COALESCE(pr_stats.price_record_count, 0) as price_record_count,
                COALESCE(pr_stats.total_amount, 0) as total_amount,
                COALESCE(rtd_stats.raw_data_count, 0) as raw_data_count,
                COALESCE(pi_stats.price_item_count, 0) as price_item_count,
                pr_stats.last_price_record,
                rtd_stats.last_raw_data
            FROM users u
            LEFT JOIN (
                SELECT
                    user_id,
                    COUNT(*) as price_record_count,
                    SUM(total_amount) as total_amount,
                    MAX(created_at) as last_price_record
                FROM price_records
                WHERE 1=1 ${timeCondition.replace('pr.', '')}
                GROUP BY user_id
            ) pr_stats ON u.id = pr_stats.user_id
            LEFT JOIN (
                SELECT
                    user_id,
                    COUNT(*) as raw_data_count,
                    MAX(created_at) as last_raw_data
                FROM raw_table_data
                WHERE 1=1 ${timeCondition.replace('pr.', '')}
                GROUP BY user_id
            ) rtd_stats ON u.id = rtd_stats.user_id
            LEFT JOIN (
                SELECT
                    pr.user_id,
                    COUNT(pi.id) as price_item_count
                FROM price_items pi
                JOIN price_records pr ON pi.record_id = pr.id
                WHERE 1=1 ${timeCondition.replace('pr.', 'pr.')}
                GROUP BY pr.user_id
            ) pi_stats ON u.id = pi_stats.user_id
            ORDER BY COALESCE(pr_stats.total_amount, 0) DESC, COALESCE(pr_stats.price_record_count, 0) DESC
        `);

        // 查询系统总体统计
        const [systemStats] = await db.query(`
            SELECT
                (SELECT COUNT(*) FROM users) as total_users,
                (SELECT COUNT(*) FROM price_records WHERE 1=1 ${timeCondition.replace('pr.', '')}) as total_price_records,
                (SELECT COUNT(*) FROM raw_table_data WHERE 1=1 ${timeCondition.replace('pr.', '')}) as total_raw_data,
                (SELECT COALESCE(SUM(total_amount), 0) FROM price_records WHERE 1=1 ${timeCondition.replace('pr.', '')}) as total_system_amount
        `);

        // 查询最活跃用户
        const [topUsers] = await db.query(`
            SELECT
                u.username,
                COUNT(*) as total_operations
            FROM (
                SELECT user_id FROM price_records WHERE 1=1 ${timeCondition.replace('pr.', '')}
                UNION ALL
                SELECT user_id FROM raw_table_data WHERE 1=1 ${timeCondition.replace('pr.', '')}
            ) as operations
            JOIN users u ON operations.user_id = u.id
            GROUP BY u.id, u.username
            ORDER BY total_operations DESC
            LIMIT 10
        `);

        const result = {
            timeRange,
            userStats,
            systemStats: systemStats[0],
            topUsers
        };

        // 缓存结果
        setCachedData(cacheKey, result);

        res.json({ success: true, data: result });
    } catch (error) {
        console.error('获取用户概览失败:', error);
        res.status(500).json({ success: false, message: '服务器错误', error: error.message });
    }
});

// GET /api/analytics/user-comparison - 用户对比分析 (仅管理员)
router.get('/user-comparison', checkAdminRole, [
    query('userIds').isString().withMessage('用户ID列表必须是字符串'),
    query('timeRange').optional().isIn(['7d', '30d', '90d', 'all']).withMessage('时间范围无效')
], async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ success: false, message: '参数验证失败', errors: errors.array() });
    }

    try {
        const { userIds, timeRange = '30d' } = req.query;
        const userIdArray = userIds.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));

        if (userIdArray.length === 0 || userIdArray.length > 10) {
            return res.status(400).json({ success: false, message: '用户ID数量必须在1-10之间' });
        }

        // 生成缓存键
        const cacheKey = generateCacheKey('user-comparison', 'admin', { userIds: userIdArray, timeRange });
        const cachedData = getCachedData(cacheKey);
        if (cachedData) {
            return res.json({ success: true, data: cachedData, cached: true });
        }

        // 构建时间条件
        let timeCondition = '';
        if (timeRange !== 'all') {
            const days = parseInt(timeRange.replace('d', ''));
            timeCondition = `AND created_at >= DATE_SUB(NOW(), INTERVAL ${days} DAY)`;
        }

        const placeholders = userIdArray.map(() => '?').join(',');

        // 查询用户对比数据
        const [comparisonData] = await db.query(`
            SELECT
                u.id,
                u.username,
                u.role,
                COALESCE(pr_stats.record_count, 0) as price_record_count,
                COALESCE(pr_stats.total_amount, 0) as total_amount,
                COALESCE(pr_stats.avg_amount, 0) as avg_amount,
                COALESCE(rtd_stats.conversion_count, 0) as raw_data_count,
                COALESCE(pi_stats.item_count, 0) as price_item_count
            FROM users u
            LEFT JOIN (
                SELECT
                    user_id,
                    COUNT(*) as record_count,
                    SUM(total_amount) as total_amount,
                    AVG(total_amount) as avg_amount
                FROM price_records
                WHERE user_id IN (${placeholders}) ${timeCondition}
                GROUP BY user_id
            ) pr_stats ON u.id = pr_stats.user_id
            LEFT JOIN (
                SELECT
                    user_id,
                    COUNT(*) as conversion_count
                FROM raw_table_data
                WHERE user_id IN (${placeholders}) ${timeCondition}
                GROUP BY user_id
            ) rtd_stats ON u.id = rtd_stats.user_id
            LEFT JOIN (
                SELECT
                    pr.user_id,
                    COUNT(pi.id) as item_count
                FROM price_items pi
                JOIN price_records pr ON pi.record_id = pr.id
                WHERE pr.user_id IN (${placeholders}) ${timeCondition.replace('created_at', 'pr.created_at')}
                GROUP BY pr.user_id
            ) pi_stats ON u.id = pi_stats.user_id
            WHERE u.id IN (${placeholders})
            ORDER BY total_amount DESC
        `, [...userIdArray, ...userIdArray, ...userIdArray, ...userIdArray]);

        const result = {
            timeRange,
            userIds: userIdArray,
            comparisonData
        };

        // 缓存结果
        setCachedData(cacheKey, result);

        res.json({ success: true, data: result });
    } catch (error) {
        console.error('获取用户对比分析失败:', error);
        res.status(500).json({ success: false, message: '服务器错误', error: error.message });
    }
});

// DELETE /api/analytics/cache - 清除缓存 (仅管理员)
router.delete('/cache', checkAdminRole, (req, res) => {
    try {
        cache.clear();
        res.json({ success: true, message: '缓存已清除' });
    } catch (error) {
        console.error('清除缓存失败:', error);
        res.status(500).json({ success: false, message: '服务器错误', error: error.message });
    }
});

// GET /api/analytics/cache-info - 获取缓存信息 (仅管理员)
router.get('/cache-info', checkAdminRole, (req, res) => {
    try {
        const cacheInfo = {
            size: cache.size,
            keys: Array.from(cache.keys()),
            duration: CACHE_DURATION / 1000 / 60 // 转换为分钟
        };
        res.json({ success: true, data: cacheInfo });
    } catch (error) {
        console.error('获取缓存信息失败:', error);
        res.status(500).json({ success: false, message: '服务器错误', error: error.message });
    }
});

module.exports = router;
