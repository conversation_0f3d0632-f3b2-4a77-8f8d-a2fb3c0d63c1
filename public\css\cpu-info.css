.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.image-preview {
    transition: all 0.3s ease;
}

.image-preview:hover {
    transform: scale(1.05);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 暗黑模式样式 */
.dark-mode {
    --bg-primary: #121212;
    --bg-secondary: #1e1e1e;
    --bg-card: #252525;
    --bg-input: #2a2a2a;
    --text-primary: #f5f5f5;
    --text-secondary: #a0aec0;
    --border-color: #333333;
    --input-bg: #2a2a2a;
    --input-text: #e2e8f0;
    --button-primary-bg: #2563eb;
    --button-primary-hover: #1d4ed8;
    --button-primary-text: #ffffff;
    --highlight-color: #3b82f6;
    --accent-color: #8b5cf6;
    --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);

    /* 表格标签颜色增强 */
    --tag-am5-bg: rgba(255, 153, 0, 0.3);
    --tag-am5-text: #fcd34d;
    --tag-am5-border: rgba(255, 153, 0, 0.5);

    --tag-cores-bg: rgba(52, 211, 153, 0.3);
    --tag-cores-text: #6ee7b7;
    --tag-cores-border: rgba(52, 211, 153, 0.6);

    --tag-freq-bg: rgba(139, 92, 246, 0.3);
    --tag-freq-text: #d8b4fe;
    --tag-freq-border: rgba(139, 92, 246, 0.6);
}

body.dark-mode {
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.dark-mode .bg-white {
    background-color: var(--bg-secondary);
}

.dark-mode .bg-gray-50 {
    background-color: var(--bg-primary);
}

.dark-mode .bg-gray-100 {
    background-color: #1a1a1a;
}

.dark-mode .text-gray-700,
.dark-mode .text-gray-800,
.dark-mode .text-gray-900 {
    color: var(--text-primary);
}

.dark-mode .text-gray-500,
.dark-mode .text-gray-600 {
    color: var(--text-secondary);
}

.dark-mode .border-gray-200,
.dark-mode .border-gray-300 {
    border-color: var(--border-color);
}

.dark-mode input,
.dark-mode select,
.dark-mode textarea {
    background-color: var(--input-bg);
    color: var(--input-text);
    border-color: var(--border-color);
}

.dark-mode .shadow-md {
    box-shadow: var(--card-shadow);
}

.dark-mode .bg-blue-600 {
    background-color: #2563eb;
}

.dark-mode .hover\:bg-blue-700:hover {
    background-color: #1d4ed8;
}

.dark-mode .text-blue-700 {
    color: #3b82f6;
}

.dark-mode .text-blue-800 {
    color: #3b82f6;
}

/* 表格美化 */
.dark-mode table thead {
    background-color: #1a1a1a;
}

.dark-mode table th {
    color: #94a3b8;
}

.dark-mode table tbody tr {
    border-color: #333;
    border-bottom: none;
    /* 移除表格行的底部边框 */
}

.dark-mode table tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.03);
}

/* 移除表格所有横线 */
.dark-mode .divide-y,
.dark-mode table,
.dark-mode tbody,
.dark-mode tr {
    border: none !important;
}

.dark-mode table tr:not(:last-child) {
    border-bottom: none !important;
}

.dark-mode .border-b,
.dark-mode .border-t {
    border-top: none !important;
    border-bottom: none !important;
}

/* 替代横线的视觉分隔 - 使用更微妙的行间距和悬停效果 */
.dark-mode table tbody tr {
    padding: 8px 0;
    transition: background-color 0.15s ease-in-out;
}

.dark-mode table tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

/* 表单美化 */
.dark-mode input:focus,
.dark-mode select:focus,
.dark-mode textarea:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
}

/* 按钮美化 */
.dark-mode button[type="submit"],
.dark-mode .bg-blue-600 {
    background-color: var(--button-primary-bg);
    transition: all 0.2s ease;
}

.dark-mode button[type="submit"]:hover,
.dark-mode .bg-blue-600:hover {
    background-color: var(--button-primary-hover);
    transform: translateY(-1px);
}

/* 卡片边框和阴影美化 */
.dark-mode .rounded-lg {
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.dark-mode .shadow-md {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
}

/* 模态框美化 */
.dark-mode #cpuModal .bg-white {
    background-color: var(--bg-card);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

/* 标签样式调整 */
.dark-mode .spec-tag {
    opacity: 0.85;
}

/* 暗黑模式下的特殊元素 */
.dark-mode #darkModeToggle {
    background-color: #fbbf24;
    color: #92400e;
}

.dark-mode #darkModeToggle:hover {
    background-color: #f59e0b;
}

/* 频率标签深色模式下增强可读性 */
.dark-mode .spec-tag.freq {
    color: #d8b4fe !important;
    background-color: rgba(139, 92, 246, 0.25) !important;
    border-color: rgba(139, 92, 246, 0.5) !important;
    font-weight: 600 !important;
    text-shadow: 0 0 1px rgba(0, 0, 0, 0.5);
}

/* 接口标签深色模式下增强可读性 */
.dark-mode .spec-tag.socket {
    color: #fcd34d !important;
    background-color: rgba(251, 191, 36, 0.25) !important;
    border-color: rgba(251, 191, 36, 0.5) !important;
    font-weight: 600 !important;
    text-shadow: 0 0 1px rgba(0, 0, 0, 0.5);
}

/* 核心/线程标签深色模式下增强可读性 */
.dark-mode .spec-tag.cores {
    color: #6ee7b7 !important;
    background-color: rgba(52, 211, 153, 0.25) !important;
    border-color: rgba(52, 211, 153, 0.5) !important;
    font-weight: 600 !important;
    text-shadow: 0 0 1px rgba(0, 0, 0, 0.5);
}

/* 智能识别输入区域暗夜模式样式 */
.dark-mode .border-gray-200 {
    border-color: #333 !important;
}

.dark-mode .bg-blue-50 {
    background-color: rgba(37, 99, 235, 0.08) !important;
    border-color: rgba(37, 99, 235, 0.2) !important;
}

.dark-mode #smartInput {
    background-color: #2a2a2a;
    color: #e2e8f0;
    border-color: #4a5568;
}

.dark-mode #autoFillBtn {
    background-color: #2563eb;
}

.dark-mode #autoFillBtn:hover {
    background-color: #1d4ed8;
}

.dark-mode .text-blue-600 {
    color: #60a5fa !important;
}

/* 暗黑模式过渡效果 */
body {
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* 全局表格修复 */
.table-wrapper {
    width: 100%;
    max-width: 100%;
    overflow-x: auto;
}

/* 确保内容不会被裁剪 */
* {
    box-sizing: border-box;
}

.container {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
}

/* 表格容器内部元素修复 */
.table-wrapper table {
    width: 100%;
}

/* 文本截断样式 */
.text-truncate {
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: calc(100% - 10px) !important;
    display: inline-block !important;
}

/* 表格样式 */
.table-compact td {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}

.model-column {
    min-width: 180px;
}

/* 标签样式 */
.spec-tag {
    padding: 3px 8px !important;
    border-radius: 4px !important;
    font-size: 0.75rem !important;
    font-weight: 500 !important;
    line-height: 1.5 !important;
    display: inline-block !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: fit-content !important;
    margin: 2px 4px 2px 0 !important;
}

.spec-tag.socket {
    background-color: rgba(180, 83, 9, 0.1) !important;
    color: #b45309 !important;
    border: 1px solid rgba(180, 83, 9, 0.2) !important;
}

.spec-tag.cores {
    background-color: rgba(22, 101, 52, 0.1) !important;
    color: #166534 !important;
    border: 1px solid rgba(22, 101, 52, 0.2) !important;
}

.spec-tag.freq {
    background-color: rgba(76, 29, 149, 0.1) !important;
    color: #4c1d95 !important;
    border: 1px solid rgba(76, 29, 149, 0.2) !important;
}

.spec-tag.process {
    background-color: rgba(6, 95, 70, 0.1) !important;
    color: #065f46 !important;
    border: 1px solid rgba(6, 95, 70, 0.2) !important;
}

.spec-tag.tdp {
    background-color: rgba(194, 65, 12, 0.1) !important;
    color: #c2410c !important;
    border: 1px solid rgba(194, 65, 12, 0.2) !important;
}

/* 品牌标签 */
.cpu-badge {
    padding: 4px 10px !important;
    font-weight: 600 !important;
    border-radius: 4px !important;
    display: inline-block !important;
}

.amd-cpu {
    background-color: rgba(237, 28, 36, 0.1) !important;
    color: #c70025 !important;
    border: 1px solid rgba(237, 28, 36, 0.2) !important;
}

.intel-cpu {
    background-color: rgba(0, 113, 197, 0.1) !important;
    color: #0071c5 !important;
    border: 1px solid rgba(0, 113, 197, 0.2) !important;
}

/* 卡片式布局 */
.cpu-card {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 0 16px 0 !important;
    box-sizing: border-box !important;
    border: none;
    border-radius: 10px;
    overflow: hidden;
    background-color: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    padding: 16px !important;
    display: flex;
    flex-direction: column;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.cpu-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.cpu-card-header {
    padding: 12px 16px !important;
    background-color: #f8f9fa;
    border-bottom: 1px solid #eee;
    width: 100% !important;
    box-sizing: border-box !important;
}

.cpu-card-body {
    padding: 16px !important;
    min-height: 120px !important;
    width: 100% !important;
    box-sizing: border-box !important;
    overflow: hidden !important;
}

.cpu-card-footer {
    padding: 12px !important;
    background-color: #fcfcfc;
    border-top: 1px solid #eee;
    width: 100% !important;
    box-sizing: border-box !important;
}

/* 标签组 */
.tag-group {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin: 8px 0;
    width: 100%;
    box-sizing: border-box !important;
}

/* 移动端优化 */
@media (max-width: 640px) {

    /* 页面容器修复 */
    .container {
        padding-left: 8px !important;
        padding-right: 8px !important;
    }

    /* 确保表格容器大小正确 */
    .table-container,
    .table-wrapper,
    .inline-block.min-w-full,
    .inline-block.min-w-full table {
        width: 100% !important;
        max-width: 100% !important;
        box-sizing: border-box !important;
        overflow-x: hidden !important;
    }

    /* 表格行适应屏幕宽度 */
    .table-modern.mobile-table tr {
        width: 100% !important;
        max-width: 100% !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        box-sizing: border-box !important;
    }

    /* 移动端卡片样式 */
    .cpu-card {
        margin: 0 0 16px 0 !important;
        padding: 16px !important;
    }

    /* 移动端品牌标签 */
    .mobile-table .cpu-badge,
    .cpu-card .cpu-badge {
        padding: 4px 10px !important;
        background-color: rgba(237, 28, 36, 0.1) !important;
        color: #c70025 !important;
        border: 1px solid rgba(237, 28, 36, 0.2) !important;
        font-weight: 600 !important;
        border-radius: 4px !important;
        font-size: 0.85rem;
        margin-right: 8px;
    }

    /* 移动端接口标签 */
    .cpu-card .socket-tag {
        padding: 4px 10px !important;
        background-color: rgba(180, 83, 9, 0.1) !important;
        color: #b45309 !important;
        border: 1px solid rgba(180, 83, 9, 0.2) !important;
        font-weight: 500 !important;
        border-radius: 4px !important;
        font-size: 0.85rem;
    }

    /* 移动端参数标签 */
    .cpu-card .spec-tag {
        padding: 4px 10px !important;
        font-weight: 500 !important;
        border-radius: 4px !important;
        font-size: 0.85rem;
        display: inline-block;
        margin: 0 4px 4px 0;
    }

    /* 移动端核心线程标签 */
    .cpu-card .cores-tag {
        background-color: rgba(22, 101, 52, 0.1) !important;
        color: #166534 !important;
        border: 1px solid rgba(22, 101, 52, 0.2) !important;
    }

    /* 移动端频率标签 */
    .cpu-card .freq-tag {
        background-color: rgba(76, 29, 149, 0.1) !important;
        color: #4c1d95 !important;
        border: 1px solid rgba(76, 29, 149, 0.2) !important;
    }

    /* 移动端核心数标签 */
    .cpu-card .core-count-tag {
        background-color: rgba(22, 101, 52, 0.1) !important;
        color: #166534 !important;
        border: 1px solid rgba(22, 101, 52, 0.2) !important;
    }

    /* 移动端TDP标签 */
    .cpu-card .tdp-tag {
        background-color: rgba(194, 65, 12, 0.1) !important;
        color: #c2410c !important;
        border: 1px solid rgba(194, 65, 12, 0.2) !important;
    }

    /* 移动端图片尺寸优化 */
    .cpu-card img {
        width: 48px !important;
        height: 48px !important;
        object-fit: cover !important;
        border-radius: 6px !important;
        border: 1px solid #eee !important;
    }

    /* L3缓存和核显样式 */
    .cpu-card .additional-info {
        font-size: 0.9rem;
        color: #4b5563;
        margin-top: 8px;
        line-height: 1.5;
    }

    /* 按钮容器样式 */
    .cpu-card .action-buttons {
        display: flex !important;
        justify-content: center !important;
        gap: 32px !important;
        margin-top: 12px !important;
    }

    /* 操作按钮样式 */
    .cpu-card .action-buttons button {
        width: 42px !important;
        height: 42px !important;
        border-radius: 50% !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08) !important;
        transition: all 0.2s ease !important;
    }
}

/* 移动端卡片暗夜模式样式 */
.dark-mode .cpu-card {
    background-color: var(--bg-secondary) !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3) !important;
    border: 1px solid rgba(255, 255, 255, 0.05) !important;
}

/* 卡片头部暗色样式 */
.dark-mode .cpu-card-header {
    background-color: rgba(0, 0, 0, 0.2) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05) !important;
}

/* 卡片内容区域暗色样式 */
.dark-mode .cpu-card-body {
    background-color: var(--bg-secondary) !important;
    color: var(--text-primary) !important;
}

/* 卡片底部暗色样式 */
.dark-mode .cpu-card-footer {
    background-color: rgba(0, 0, 0, 0.1) !important;
    border-top: 1px solid rgba(255, 255, 255, 0.05) !important;
}

/* 卡片内文字颜色 */
.dark-mode .cpu-card h2,
.dark-mode .cpu-card h3,
.dark-mode .cpu-card p,
.dark-mode .cpu-card div {
    color: var(--text-primary) !important;
}

.dark-mode .cpu-card .text-gray-600,
.dark-mode .cpu-card .text-gray-500 {
    color: var(--text-secondary) !important;
}

/* 卡片内按钮样式 */
.dark-mode .cpu-card button {
    background-color: transparent !important;
}

.dark-mode .cpu-card button:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
}

/* CPU标签暗夜模式调整 */
.dark-mode .amd-cpu {
    background-color: rgba(237, 28, 36, 0.2) !important;
    color: #f87171 !important;
    border-color: rgba(237, 28, 36, 0.3) !important;
}

.dark-mode .intel-cpu {
    background-color: rgba(0, 113, 197, 0.2) !important;
    color: #60a5fa !important;
    border-color: rgba(0, 113, 197, 0.3) !important;
}

/* 移动端卡片暗夜模式样式 */
.dark-mode .cpu-card-outer-container {
    color: var(--text-primary);
}

.dark-mode .cpu-card-new {
    background-color: var(--bg-secondary) !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3) !important;
    border: 1px solid rgba(255, 255, 255, 0.05) !important;
}

/* 卡片头部暗色样式 */
.dark-mode .cpu-card-new>div:first-child {
    background-color: rgba(0, 0, 0, 0.2) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05) !important;
}

/* 卡片内容区域暗色样式 */
.dark-mode .cpu-card-new>div:nth-child(2) {
    background-color: var(--bg-secondary) !important;
    color: var(--text-primary) !important;
}

/* 卡片底部暗色样式 */
.dark-mode .cpu-card-new>div:last-child {
    background-color: rgba(0, 0, 0, 0.1) !important;
    border-top: 1px solid rgba(255, 255, 255, 0.05) !important;
}

/* 卡片内文字颜色 */
.dark-mode .cpu-card-new h2,
.dark-mode .cpu-card-new h3,
.dark-mode .cpu-card-new p,
.dark-mode .cpu-card-new div {
    color: var(--text-primary) !important;
}

.dark-mode .cpu-card-new .text-gray-600,
.dark-mode .cpu-card-new .text-gray-500 {
    color: var(--text-secondary) !important;
}

/* 卡片内按钮样式 */
.dark-mode .cpu-card-new button {
    background-color: transparent !important;
}

.dark-mode .cpu-card-new button:hover,
.dark-mode .cpu-card-new button:active {
    background-color: rgba(255, 255, 255, 0.1) !important;
}

/* 卡片图片容器 */
.dark-mode .cpu-card-new .flex-shrink-0 {
    background-color: #1a1a1a !important;
    border-color: #333 !important;
}

/* AMD标签暗夜模式调整 */
.dark-mode .cpu-card-new span[style*="color: #f87171"] {
    color: #f87171 !important;
}

/* Intel标签暗夜模式调整 */
.dark-mode .cpu-card-new span[style*="color: #60a5fa"] {
    color: #60a5fa !important;
}

/* 标签组暗夜模式调整 */
.dark-mode .cpu-card-new .flex.flex-wrap.gap-1\.5 span {
    opacity: 0.85;
}

/* 针对移动端专有样式补充 */
@media (max-width: 640px) {
    .dark-mode .cpu-card-outer-container {
        margin-bottom: 15px !important;
    }

    /* 移动端暗夜模式下的文本颜色调整 */
    .dark-mode .cpu-card-outer-container span[style*="font-weight: bold"] {
        color: var(--text-primary) !important;
    }

    /* 移动端卡片文本颜色增强 - 提高对比度 */
    .dark-mode .cpu-card-new div {
        color: #e2e8f0 !important;
    }

    .dark-mode .cpu-card-new span {
        color: #f3f4f6 !important;
    }

    /* 型号名称和重要数据的高对比度 */
    .dark-mode .cpu-card-new>div:first-child>div {
        color: #ffffff !important;
        font-weight: 600 !important;
    }

    /* 核心/线程数据显示增强 */
    .dark-mode .cpu-card-new span[style*="font-weight: bold"] {
        color: #ffffff !important;
        text-shadow: 0 0 1px rgba(0, 0, 0, 0.5);
    }

    /* 标签颜色增强 */
    .dark-mode .cpu-card-outer-container .flex.flex-wrap.gap-1\.5.mt-1 span {
        background-color: rgba(75, 85, 99, 0.3) !important;
        border: 1px solid rgba(75, 85, 99, 0.4) !important;
        color: #ffffff !important;
        font-weight: 500 !important;
    }

    /* 针对不同类型的标签应用不同颜色 */
    .dark-mode .cpu-card-outer-container span[style*="#FBBF24"] {
        color: #fcd34d !important;
        background-color: rgba(251, 191, 36, 0.2) !important;
        border-color: rgba(251, 191, 36, 0.4) !important;
    }

    .dark-mode .cpu-card-outer-container span[style*="#A78BFA"] {
        color: #d8b4fe !important;
        background-color: rgba(167, 139, 250, 0.3) !important;
        border-color: rgba(167, 139, 250, 0.5) !important;
    }

    .dark-mode .cpu-card-outer-container span[style*="#F472B6"] {
        color: #f9a8d4 !important;
        background-color: rgba(244, 114, 182, 0.2) !important;
        border-color: rgba(244, 114, 182, 0.4) !important;
    }

    .dark-mode .cpu-card-outer-container span[style*="#34D399"] {
        color: #6ee7b7 !important;
        background-color: rgba(52, 211, 153, 0.3) !important;
        border-color: rgba(52, 211, 153, 0.5) !important;
    }

    /* 移动端暗夜模式下的按钮点击效果 */
    .dark-mode .cpu-card-outer-container button:active {
        background-color: rgba(255, 255, 255, 0.15) !important;
    }
}

.dark-mode .hover\:bg-gray-100:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

/* 增强表格行悬停样式 */
tr.hover\:bg-blue-50:hover {
    background-color: rgba(219, 234, 254, 0.6) !important;
    /* 亮色主题下的悬停颜色 */
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.05);
}

/* 当前行高亮样式 */
tr.current-row {
    background-color: rgba(219, 234, 254, 0.8) !important;
    border-left: 3px solid #3b82f6 !important;
}

/* 暗色模式下的表格行悬停样式 */
.dark-mode tr.hover\:bg-blue-50:hover {
    background-color: rgba(55, 65, 81, 0.7) !important;
    /* 暗色主题下的悬停颜色 */
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.2);
}

/* 暗色模式下的当前行高亮样式 */
.dark-mode tr.current-row {
    background-color: rgba(55, 65, 81, 0.8) !important;
    border-left: 3px solid #3b82f6 !important;
}