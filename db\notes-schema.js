const db = require('../config/db');

/**
 * 创建笔记系统相关的数据库表
 */
async function createNotesSchema() {
    try {
        console.log('开始创建笔记系统数据库表...');

        // 创建笔记分类表
        await db.query(`
            CREATE TABLE IF NOT EXISTS note_categories (
                id INT PRIMARY KEY AUTO_INCREMENT,
                name VARCHAR(100) NOT NULL UNIQUE,
                description TEXT,
                color VARCHAR(7) DEFAULT '#3B82F6',
                icon VARCHAR(50) DEFAULT 'fas fa-folder',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        `);
        console.log('创建note_categories表成功');

        // 创建笔记表
        await db.query(`
            CREATE TABLE IF NOT EXISTS notes (
                id INT PRIMARY KEY AUTO_INCREMENT,
                title VARCHAR(255) NOT NULL,
                content LONGTEXT,
                category_id INT,
                author_id INT NOT NULL,
                is_public BOOLEAN DEFAULT TRUE,
                is_pinned BOOLEAN DEFAULT FALSE,
                view_count INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (category_id) REFERENCES note_categories(id) ON DELETE SET NULL,
                FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE
            )
        `);
        console.log('创建notes表成功');

        // 为notes表添加全文索引
        try {
            await db.query(`ALTER TABLE notes ADD FULLTEXT(title, content)`);
            console.log('为notes表添加全文索引成功');
        } catch (error) {
            if (error.code !== 'ER_DUP_INDEX') {
                console.log('全文索引可能已存在或不支持:', error.message);
            }
        }

        // 创建笔记标签表
        await db.query(`
            CREATE TABLE IF NOT EXISTS note_tags (
                id INT PRIMARY KEY AUTO_INCREMENT,
                name VARCHAR(50) NOT NULL UNIQUE,
                color VARCHAR(7) DEFAULT '#6B7280',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `);
        console.log('创建note_tags表成功');

        // 创建笔记标签关联表
        await db.query(`
            CREATE TABLE IF NOT EXISTS note_tag_relations (
                id INT PRIMARY KEY AUTO_INCREMENT,
                note_id INT NOT NULL,
                tag_id INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (note_id) REFERENCES notes(id) ON DELETE CASCADE,
                FOREIGN KEY (tag_id) REFERENCES note_tags(id) ON DELETE CASCADE,
                UNIQUE KEY unique_note_tag (note_id, tag_id)
            )
        `);
        console.log('创建note_tag_relations表成功');

        // 创建笔记访问记录表
        await db.query(`
            CREATE TABLE IF NOT EXISTS note_access_logs (
                id INT PRIMARY KEY AUTO_INCREMENT,
                note_id INT NOT NULL,
                user_id INT,
                ip_address VARCHAR(45),
                user_agent TEXT,
                access_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (note_id) REFERENCES notes(id) ON DELETE CASCADE,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
            )
        `);
        console.log('创建note_access_logs表成功');

        // 创建笔记附件表
        await db.query(`
            CREATE TABLE IF NOT EXISTS note_attachments (
                id INT PRIMARY KEY AUTO_INCREMENT,
                note_id INT NOT NULL,
                file_name VARCHAR(255) NOT NULL,
                file_path VARCHAR(500) NOT NULL,
                file_type VARCHAR(50) NOT NULL,
                file_size INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (note_id) REFERENCES notes(id) ON DELETE CASCADE
            )
        `);
        console.log('创建note_attachments表成功');

        // 插入默认分类
        await insertDefaultCategories();

        // 插入默认标签
        await insertDefaultTags();

        console.log('笔记系统数据库表创建完成！');

    } catch (error) {
        console.error('创建笔记系统数据库表失败:', error);
        throw error;
    }
}

/**
 * 插入默认分类
 */
async function insertDefaultCategories() {
    const defaultCategories = [
        {
            name: '配置信息',
            description: '系统配置和设置相关的笔记',
            color: '#3B82F6',
            icon: 'fas fa-cog'
        },
        {
            name: '知识库',
            description: '技术知识和经验总结',
            color: '#10B981',
            icon: 'fas fa-book'
        },
        {
            name: '文档',
            description: '项目文档和说明',
            color: '#F59E0B',
            icon: 'fas fa-file-alt'
        },
        {
            name: '问题解决',
            description: '问题排查和解决方案',
            color: '#EF4444',
            icon: 'fas fa-tools'
        },
        {
            name: '其他',
            description: '其他类型的笔记',
            color: '#6B7280',
            icon: 'fas fa-folder'
        }
    ];

    for (const category of defaultCategories) {
        try {
            await db.query(
                'INSERT IGNORE INTO note_categories (name, description, color, icon) VALUES (?, ?, ?, ?)',
                [category.name, category.description, category.color, category.icon]
            );
        } catch (error) {
            console.log(`插入默认分类 ${category.name} 失败:`, error.message);
        }
    }
    console.log('插入默认分类完成');
}

/**
 * 插入默认标签
 */
async function insertDefaultTags() {
    const defaultTags = [
        { name: '重要', color: '#EF4444' },
        { name: '配置', color: '#3B82F6' },
        { name: '教程', color: '#10B981' },
        { name: '问题', color: '#F59E0B' },
        { name: '解决方案', color: '#8B5CF6' },
        { name: '文档', color: '#6B7280' }
    ];

    for (const tag of defaultTags) {
        try {
            await db.query(
                'INSERT IGNORE INTO note_tags (name, color) VALUES (?, ?)',
                [tag.name, tag.color]
            );
        } catch (error) {
            console.log(`插入默认标签 ${tag.name} 失败:`, error.message);
        }
    }
    console.log('插入默认标签完成');
}

/**
 * 删除笔记系统相关的数据库表（用于重置）
 */
async function dropNotesSchema() {
    try {
        console.log('开始删除笔记系统数据库表...');

        // 按依赖关系顺序删除表
        await db.query('DROP TABLE IF EXISTS note_access_logs');
        await db.query('DROP TABLE IF EXISTS note_tag_relations');
        await db.query('DROP TABLE IF EXISTS notes');
        await db.query('DROP TABLE IF EXISTS note_tags');
        await db.query('DROP TABLE IF EXISTS note_categories');

        console.log('笔记系统数据库表删除完成！');

    } catch (error) {
        console.error('删除笔记系统数据库表失败:', error);
        throw error;
    }
}

/**
 * 检查笔记系统表是否存在
 */
async function checkNotesSchema() {
    try {
        const [tables] = await db.query(`
            SELECT TABLE_NAME
            FROM INFORMATION_SCHEMA.TABLES
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME IN ('note_categories', 'notes', 'note_tags', 'note_tag_relations', 'note_access_logs', 'note_attachments')
        `);

        const existingTables = tables.map(row => row.TABLE_NAME);
        const requiredTables = ['note_categories', 'notes', 'note_tags', 'note_tag_relations', 'note_access_logs', 'note_attachments'];
        
        const missingTables = requiredTables.filter(table => !existingTables.includes(table));
        
        return {
            allExist: missingTables.length === 0,
            existingTables,
            missingTables
        };
    } catch (error) {
        console.error('检查笔记系统表失败:', error);
        return {
            allExist: false,
            existingTables: [],
            missingTables: ['note_categories', 'notes', 'note_tags', 'note_tag_relations', 'note_access_logs']
        };
    }
}

module.exports = {
    createNotesSchema,
    dropNotesSchema,
    checkNotesSchema,
    insertDefaultCategories,
    insertDefaultTags
};
