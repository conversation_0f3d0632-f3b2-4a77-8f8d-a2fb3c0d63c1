.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(2px);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.chart-container {
    position: relative;
    height: 400px;
    width: 100%;
}

.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.notification-enter {
    transform: translateX(100%);
    opacity: 0;
}

.notification-enter-active {
    transform: translateX(0);
    opacity: 1;
    transition: all 0.3s ease-out;
}

.notification-exit {
    transform: translateX(0);
    opacity: 1;
}

.notification-exit-active {
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease-in;
}

/* 深色主题样式 */
.dark {
    color-scheme: dark;
}

.dark body {
    background-color: #1f2937 !important;
    color: #f9fafb !important;
}

.dark .bg-gray-100 {
    background-color: #1f2937 !important;
}

.dark .bg-white {
    background-color: #374151 !important;
    color: #f9fafb !important;
}

.dark .text-gray-800 {
    color: #f9fafb !important;
}

.dark .text-gray-600 {
    color: #d1d5db !important;
}

.dark .text-gray-500 {
    color: #9ca3af !important;
}

.dark .text-gray-400 {
    color: #9ca3af !important;
}

.dark .text-black {
    color: #f9fafb !important;
}

.dark .border-gray-300 {
    border-color: #4b5563 !important;
}

.dark .border-gray-200 {
    border-color: #374151 !important;
}

.dark .bg-gray-50 {
    background-color: #4b5563 !important;
}

.dark .divide-gray-200> :not([hidden])~ :not([hidden]) {
    border-color: #4b5563 !important;
}

.dark nav {
    background-color: #1f2937 !important;
}

/* 表格样式修复 */
.dark table {
    color: #f9fafb !important;
}

.dark th {
    color: #f9fafb !important;
    background-color: #4b5563 !important;
}

.dark td {
    color: #f9fafb !important;
    border-color: #4b5563 !important;
}

.dark tr:hover {
    background-color: #4b5563 !important;
}

/* 输入框样式修复 */
.dark input {
    background-color: #4b5563 !important;
    color: #f9fafb !important;
    border-color: #6b7280 !important;
}

.dark input::placeholder {
    color: #9ca3af !important;
}

.dark select {
    background-color: #4b5563 !important;
    color: #f9fafb !important;
    border-color: #6b7280 !important;
}

.dark textarea {
    background-color: #4b5563 !important;
    color: #f9fafb !important;
    border-color: #6b7280 !important;
}

/* 按钮样式保持不变，因为它们有特定的颜色 */
.dark .text-green-600 {
    color: #10b981 !important;
}

.dark .text-red-600 {
    color: #ef4444 !important;
}

.dark .text-blue-600 {
    color: #3b82f6 !important;
}

.dark .text-yellow-600 {
    color: #eab308 !important;
}

/* 模态框样式修复 */
.dark .modal {
    background-color: rgba(0, 0, 0, 0.7) !important;
    color: #f9fafb !important;
}

.dark .modal-content {
    background-color: #374151 !important;
    color: #f9fafb !important;
}

.dark .modal h3 {
    color: #f9fafb !important;
}

.dark .modal h4 {
    color: #f9fafb !important;
}

.dark .modal label {
    color: #f9fafb !important;
}

.dark .modal p {
    color: #f9fafb !important;
}

.dark .modal span {
    color: #f9fafb !important;
}

.dark .modal div {
    color: #f9fafb !important;
}

.dark .modal pre {
    color: #f9fafb !important;
    background-color: #4b5563 !important;
}

.dark .modal .bg-white {
    background-color: #4b5563 !important;
}

.dark .modal .bg-gray-50 {
    background-color: #374151 !important;
}

.dark .modal .text-gray-900 {
    color: #f9fafb !important;
}

.dark .modal .text-gray-800 {
    color: #f9fafb !important;
}

.dark .modal .text-gray-600 {
    color: #d1d5db !important;
}

.dark .modal .text-gray-500 {
    color: #9ca3af !important;
}

.dark .modal .text-gray-400 {
    color: #6b7280 !important;
}

.dark .modal .border {
    border-color: #4b5563 !important;
}

.dark .modal .border-gray-200 {
    border-color: #4b5563 !important;
}

/* 状态标签深色主题适配 */
.dark .modal .bg-green-100 {
    background-color: #065f46 !important;
}

.dark .modal .text-green-800 {
    color: #10b981 !important;
}

.dark .modal .bg-yellow-100 {
    background-color: #78350f !important;
}

.dark .modal .text-yellow-800 {
    color: #fbbf24 !important;
}

/* 详情模态框特殊样式 */
.detail-modal-card {
    transition: all 0.3s ease;
}

.detail-modal-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* 详情模态框渐变背景动画 */
.detail-gradient-bg {
    background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* 详情模态框图标动画 */
.detail-icon-bounce {
    animation: iconBounce 2s ease-in-out infinite;
}

@keyframes iconBounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

/* 详情模态框按钮悬停效果 */
.detail-btn-hover {
    position: relative;
    overflow: hidden;
}

.detail-btn-hover::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.detail-btn-hover:hover::before {
    left: 100%;
}

/* 详情模态框区域背景色和边框 */
.dark .modal .bg-gray-50 {
    background-color: #374151 !important;
    border: 1px solid #4b5563 !important;
}

.dark .modal .bg-white {
    background-color: #4b5563 !important;
    border: 1px solid #6b7280 !important;
}

/* 浅色主题下的区域边框 */
.modal .bg-gray-50 {
    border: 1px solid #d1d5db;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.modal .bg-white {
    border: 1px solid #e5e7eb;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

/* 详情模态框深色主题适配 */
.dark .modal .bg-gradient-to-r {
    background: linear-gradient(to right, #1f2937, #374151) !important;
}

.dark .modal .bg-gradient-to-br {
    background: linear-gradient(to bottom right, #374151, #4b5563) !important;
}

.dark .modal .border-blue-100 {
    border-color: #4b5563 !important;
}

.dark .modal .border-green-100 {
    border-color: #4b5563 !important;
}

.dark .modal .border-yellow-100 {
    border-color: #4b5563 !important;
}

.dark .modal .border-purple-100 {
    border-color: #4b5563 !important;
}

.dark .modal .from-blue-50 {
    --tw-gradient-from: #374151 !important;
}

.dark .modal .from-green-50 {
    --tw-gradient-from: #374151 !important;
}

.dark .modal .from-yellow-50 {
    --tw-gradient-from: #374151 !important;
}

.dark .modal .from-purple-50 {
    --tw-gradient-from: #374151 !important;
}

/* 深色主题下的阴影效果 */
.dark .modal .shadow-sm {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3) !important;
}

.dark .modal .bg-gray-50 {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3) !important;
}

.dark .modal .bg-white {
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2) !important;
}

/* Viewer.js 图片预览器z-index修复 */
.viewer-backdrop {
    z-index: 99999 !important; /* 确保比模态框更高 */
}

.viewer-container {
    z-index: 99999 !important; /* 确保比模态框更高 */
}

.viewer-canvas {
    z-index: 99999 !important; /* 确保比模态框更高 */
}

.viewer-navbar {
    z-index: 100000 !important; /* 确保比模态框更高 */
}

.viewer-toolbar {
    z-index: 100000 !important; /* 确保比模态框更高 */
}

.viewer-tooltip {
    z-index: 100001 !important; /* 确保比模态框更高 */
}

.viewer-button {
    z-index: 100000 !important; /* 确保比模态框更高 */
}

/* 图片预览样式 */
.image-preview {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.image-preview:hover {
    transform: scale(1.05);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border-color: #3b82f6;
}

/* 上传区域样式 */
.mobile-hidden {
    display: inline;
}

@media (max-width: 640px) {
    .mobile-hidden {
        display: none;
    }
}

/* 上传进度条增强样式 */
#uploadProgressContainer {
    animation: slideDown 0.3s ease-out;
}

#uploadProgressBar {
    background: linear-gradient(90deg, #10b981, #059669);
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
    position: relative;
    overflow: hidden;
}

#uploadProgressBar::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
        max-height: 0;
    }
    to {
        opacity: 1;
        transform: translateY(0);
        max-height: 100px;
    }
}

/* 深色主题下的进度条 */
.dark #uploadProgressBar {
    background: linear-gradient(90deg, #34d399, #10b981);
    box-shadow: 0 2px 4px rgba(52, 211, 153, 0.3);
}

/* 上传提示动画 */
.upload-notification {
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 表格中状态标签的深色主题适配 */
.dark .bg-green-100 {
    background-color: #065f46 !important;
}

.dark .text-green-800 {
    color: #10b981 !important;
}

.dark .bg-yellow-100 {
    background-color: #78350f !important;
}

.dark .text-yellow-800 {
    color: #fbbf24 !important;
}

/* 卡片样式 - 适用于所有设备 */
.revenue-card {
    padding: 1.25rem;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 1rem;
    border: 1px solid #e2e8f0;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.revenue-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899, #10b981);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.revenue-card:hover {
    background: linear-gradient(135deg, #fefefe 0%, #f8fafc 100%);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.12), 0 8px 16px -8px rgba(0, 0, 0, 0.08);
    border-color: #e2e8f0;
}

.revenue-card:hover::before {
    opacity: 0.8;
}



.card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.75rem;
}

.card-title {
    font-weight: 600;
    color: #1e293b;
    font-size: 1.1rem;
    line-height: 1.25;
    display: flex;
    align-items: center;
    margin-bottom: 0.25rem;
}

.card-amount {
    font-weight: 700;
    color: #059669;
    font-size: 1.25rem;
    white-space: nowrap;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #10b981, #059669);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 1px 2px rgba(16, 185, 129, 0.1);
}

.card-meta {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
}

.card-meta-item {
    display: flex;
    flex-direction: column;
}

.card-meta-label {
    font-size: 0.75rem;
    color: #6b7280;
    font-weight: 500;
    margin-bottom: 0.125rem;
}

.card-meta-value {
    font-size: 0.875rem;
    color: #374151;
    word-break: break-all;
}

.card-content {
    margin-bottom: 0.75rem;
}

.card-product-info {
    background-color: #f9fafb;
    padding: 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    color: #374151;
    line-height: 1.4;
    border-left: 3px solid #3b82f6;
    word-wrap: break-word;
}

.card-notes-content {
    background-color: #fef3c7;
    color: #92400e;
    padding: 0.75rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    border-left: 4px solid #f59e0b;
    word-break: break-word;
    border: 1px solid #fde68a;
    line-height: 1.4;
}

.product-text-short,
.product-text-full {
    display: inline;
}

.card-product-info button {
    font-size: 0.75rem;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
    border: none;
    background: none;
    cursor: pointer;
}

.card-product-info button:hover {
    background-color: rgba(59, 130, 246, 0.1);
}

.card-image {
    width: 3rem;
    height: 3rem;
    object-fit: cover;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.card-image:hover {
    transform: scale(1.05);
}

.card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 0.75rem;
    border-top: 1px solid #e5e7eb;
}

.card-actions {
    display: flex;
    gap: 0.5rem;
}

.card-action-btn {
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.card-action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.card-action-detail {
    background: linear-gradient(135deg, #dcfce7, #bbf7d0);
    color: #166534;
    border: 1px solid #86efac;
}

.card-action-detail:hover {
    background: linear-gradient(135deg, #bbf7d0, #a7f3d0);
    border-color: #4ade80;
}

.card-action-edit {
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    color: #1e40af;
    border: 1px solid #93c5fd;
}

.card-action-edit:hover {
    background: linear-gradient(135deg, #bfdbfe, #a5b4fc);
    border-color: #60a5fa;
}

.card-action-delete {
    background: linear-gradient(135deg, #fee2e2, #fecaca);
    color: #dc2626;
    border: 1px solid #fca5a5;
}

.card-action-delete:hover {
    background: linear-gradient(135deg, #fecaca, #fbb6ce);
    border-color: #f87171;
}

.card-time {
    font-size: 0.75rem;
    color: #6b7280;
}

/* 深色主题适配 */
.dark .revenue-card {
    background: #374151;
    border: 1px solid #4b5563;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
}

.dark .revenue-card:hover {
    background: linear-gradient(135deg, #475569 0%, #3f4a5a 100%);
    border-color: #64748b;
    box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.4), 0 8px 16px -8px rgba(0, 0, 0, 0.3);
    transform: translateY(-3px);
}

.dark .card-title {
    color: #f3f4f6;
}

.dark .card-amount {
    color: #10b981;
    background: linear-gradient(135deg, #34d399, #10b981);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* 深色主题下的操作按钮 */
.dark .card-action-detail {
    background: linear-gradient(135deg, #064e3b, #065f46);
    color: #34d399;
    border-color: #047857;
}

.dark .card-action-detail:hover {
    background: linear-gradient(135deg, #047857, #059669);
    color: #6ee7b7;
    border-color: #10b981;
}

.dark .card-action-edit {
    background: linear-gradient(135deg, #1e3a8a, #1d4ed8);
    color: #93c5fd;
    border-color: #3b82f6;
}

.dark .card-action-edit:hover {
    background: linear-gradient(135deg, #1d4ed8, #2563eb);
    color: #bfdbfe;
    border-color: #60a5fa;
}

.dark .card-action-delete {
    background: linear-gradient(135deg, #7f1d1d, #991b1b);
    color: #fca5a5;
    border-color: #dc2626;
}

.dark .card-action-delete:hover {
    background: linear-gradient(135deg, #991b1b, #b91c1c);
    color: #fecaca;
    border-color: #ef4444;
}

.dark .card-time {
    color: #9ca3af;
}

.dark .card-meta-label {
    color: #d1d5db;
}

.dark .card-meta-value {
    color: #f3f4f6;
}

.dark .card-product-info {
    background-color: #4b5563;
    color: #e5e7eb;
    border-left-color: #60a5fa;
}

.dark .card-notes-content {
    background-color: #374151;
    color: #fbbf24;
    border-left-color: #f59e0b;
    border-color: #4b5563;
}

.dark .card-product-info button {
    color: #60a5fa;
}

.dark .card-product-info button:hover {
    background-color: rgba(96, 165, 250, 0.1);
    color: #93c5fd;
}

/* 模态框图标动画 */
.modal-icon-bounce {
    animation: modalIconBounce 2s infinite;
}

@keyframes modalIconBounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-3px);
    }
    60% {
        transform: translateY(-2px);
    }
}

/* 表单卡片悬停效果 */
.bg-gradient-to-br:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: all 0.2s ease;
}

/* 输入框聚焦效果增强 */
input:focus, textarea:focus, select:focus {
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* 按钮悬停效果 */
button:hover {
    transform: translateY(-1px);
}

button:active {
    transform: translateY(0);
}



.dark .card-title {
    color: #f9fafb;
}

.dark .card-amount {
    color: #10b981;
}

.dark .card-meta-label {
    color: #9ca3af;
}

.dark .card-meta-value {
    color: #d1d5db;
}

.dark .card-product-info {
    background-color: #4b5563;
    color: #d1d5db;
}

.dark .card-footer {
    border-top-color: #4b5563;
}

.dark .card-time {
    color: #9ca3af;
}

/* 搜索框样式 */
#searchInput {
    transition: all 0.3s ease;
}

#searchInput:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

#searchInput::placeholder {
    color: #9ca3af;
    transition: color 0.3s ease;
}

#searchInput:focus::placeholder {
    color: #6b7280;
}

/* 深色主题下的搜索框 */
.dark #searchInput {
    background-color: #374151;
    border-color: #4b5563;
    color: #f9fafb;
}

.dark #searchInput:focus {
    border-color: #60a5fa;
    box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
}

.dark #searchInput::placeholder {
    color: #6b7280;
}

.dark #searchInput:focus::placeholder {
    color: #9ca3af;
}

/* 搜索图标 */
.dark .fa-search {
    color: #9ca3af !important;
}

/* 搜索结果提示 */
#searchResultsInfo {
    transition: all 0.3s ease;
}

.dark #searchResultsInfo {
    background-color: #1e3a8a;
    border-color: #3b82f6;
    color: #dbeafe;
}

.dark #searchResultsInfo .text-blue-700 {
    color: #dbeafe !important;
}

.dark #searchResultsInfo .text-blue-500 {
    color: #60a5fa !important;
}

.dark #searchResultsInfo .text-blue-500:hover {
    color: #93c5fd !important;
}

/* 图片预览样式 */
.image-preview {
    transition: all 0.3s ease;
}

.image-preview:hover {
    transform: scale(1.05);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 移动端隐藏拖拽提示 */
@media (max-width: 640px) {
    .mobile-hidden {
        display: none;
    }
}

/* 深色主题下的上传区域 */
.dark .border-dashed {
    border-color: #4b5563;
}

.dark .text-gray-600 {
    color: #9ca3af;
}

.dark .text-gray-500 {
    color: #6b7280;
}

.dark .bg-white {
    background-color: #374151;
}

.dark .text-blue-600 {
    color: #60a5fa;
}

.dark .text-blue-600:hover {
    color: #93c5fd;
}

/* 模态框背景遮罩 */
.dark .fixed.inset-0.bg-black {
    background-color: rgba(0, 0, 0, 0.7) !important;
}

/* 模态框内的卡片 */
.dark .bg-white.rounded-lg {
    background-color: #374151 !important;
    color: #f9fafb !important;
}

/* 文件上传区域 */
.dark .border-dashed {
    border-color: #6b7280 !important;
    color: #d1d5db !important;
}

.dark .border-dashed:hover {
    border-color: #9ca3af !important;
    background-color: #4b5563 !important;
}

/* 按钮样式修复 */
.dark button {
    color: #f9fafb !important;
}

.dark #cancelBtn {
    background-color: #4b5563 !important;
    border-color: #6b7280 !important;
    color: #f9fafb !important;
}

.dark #cancelBtn:hover {
    background-color: #6b7280 !important;
}

.dark #saveBtn {
    background-color: #3b82f6 !important;
    color: #ffffff !important;
}

.dark #saveBtn:hover {
    background-color: #2563eb !important;
}

/* 主题切换按钮动画 */
#themeToggle {
    transition: all 0.3s ease;
}

#themeToggle:hover {
    transform: scale(1.1);
}

#themeToggle i {
    transition: transform 0.3s ease;
}

#themeToggle:active i {
    transform: rotate(180deg);
}

/* 状态选择框样式 */
.status-select {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.5rem center;
    background-size: 1em;
    padding-right: 2rem;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 80px;
}

.status-select:hover {
    transform: scale(1.02);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.status-select:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

.status-select:disabled {
    cursor: not-allowed;
    opacity: 0.6;
}

/* 状态选择框在不同状态下的样式 */
.status-select.bg-green-100 {
    background-color: #dcfce7;
    color: #166534;
}

.status-select.bg-yellow-100 {
    background-color: #fef3c7;
    color: #92400e;
}

/* 移动端状态选择框调整 */
@media (max-width: 768px) {
    .status-select {
        min-width: 70px;
        font-size: 0.75rem;
        padding: 0.25rem 1.5rem 0.25rem 0.5rem;
    }
}

/* 店铺搜索相关样式 */
#shopSearchResults {
    z-index: 1000;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

#shopSearchResults .hover\:bg-gray-100:hover {
    background-color: #f3f4f6;
}

#newShopContainer {
    animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 店铺搜索输入框样式 */
#shopSearch {
    transition: all 0.2s ease;
}

#shopSearch:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 添加店铺按钮样式 */
#addShopBtn {
    transition: all 0.2s ease;
}

#addShopBtn:hover {
    transform: scale(1.1);
}

/* 新增店铺表单样式 */
#newShopName {
    transition: all 0.2s ease;
}

#newShopName:focus {
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

#saveNewShop {
    transition: all 0.2s ease;
}

#saveNewShop:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}