<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - 电脑配件综合管理系统</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="/css/theme.css">
    <script>
        // 配置Tailwind CSS支持暗色模式
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        dark: {
                            50: '#f9fafb',
                            100: '#f3f4f6',
                            200: '#e5e7eb',
                            300: '#d1d5db',
                            400: '#9ca3af',
                            500: '#6b7280',
                            600: '#4b5563',
                            700: '#374151',
                            800: '#1f2937',
                            900: '#111827'
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 dark:bg-gray-900 min-h-screen transition-colors duration-200">
    <div class="container mx-auto px-2 sm:px-4 py-4 sm:py-8">
        <header class="mb-4 sm:mb-8">
            <h1 class="text-xl sm:text-3xl font-bold text-indigo-700 dark:text-indigo-400 flex items-center">
                <i class="fas fa-users-cog mr-2 sm:mr-3"></i> 用户管理
            </h1>
            <div class="flex justify-between items-center">
                <p class="text-gray-600 dark:text-gray-400 mt-1 sm:mt-2 text-sm sm:text-base">管理系统用户账号</p>
                <div class="mt-2 flex space-x-2">
                    <div id="userRoleBadge" class="hidden bg-blue-100 text-blue-800 text-xs font-semibold mr-2 px-2.5 py-0.5 rounded flex items-center">
                        <i class="fas fa-user mr-1"></i> <span id="userRoleText">普通用户</span>
                    </div>
                    <button id="themeToggle" class="inline-flex items-center bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 py-1 sm:py-2 px-3 sm:px-4 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 text-sm sm:text-base transition-colors duration-200">
                        <i id="themeIcon" class="fas fa-moon mr-1"></i>
                        <span id="themeText">暗色</span>
                    </button>
                    <a href="/index.html" class="inline-block bg-indigo-600 text-white py-1 sm:py-2 px-3 sm:px-4 rounded-md hover:bg-indigo-700 text-sm sm:text-base">
                        <i class="fas fa-home mr-1"></i> 返回首页
                    </a>
                </div>
            </div>
        </header>

        <div id="adminOnlyMessage" class="hidden bg-yellow-100 dark:bg-yellow-900 border-l-4 border-yellow-500 dark:border-yellow-400 text-yellow-700 dark:text-yellow-300 p-4 mb-4">
            <p class="font-bold">权限提示</p>
            <p>此页面仅限管理员访问。如果您看到此消息，请联系系统管理员获取权限。</p>
        </div>

        <div id="adminContent" class="grid grid-cols-1 lg:grid-cols-4 gap-4 sm:gap-6">
            <!-- 左侧表单区域 -->
            <div class="lg:col-span-1 bg-white dark:bg-gray-800 p-3 sm:p-6 rounded-lg shadow-md">
                <h2 class="text-lg sm:text-xl font-semibold text-gray-800 dark:text-gray-200 mb-3 sm:mb-4 flex items-center">
                    <i class="fas fa-user-edit mr-1 sm:mr-2 text-indigo-500 dark:text-indigo-400"></i> <span id="formTitle">添加用户</span>
                </h2>
                
                <form id="userForm" class="space-y-3 sm:space-y-4">
                    <input type="hidden" id="userId" value="">
                    
                    <div>
                        <label for="username" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">用户名</label>
                        <input type="text" id="username" name="username" required minlength="6"
                            class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">用户名至少6个字符，不能包含空格</p>
                    </div>

                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">密码</label>
                        <input type="password" id="password" name="password" placeholder="不修改请留空"
                            class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">密码至少6个字符，必须包含字母和数字</p>
                    </div>

                    <div>
                        <label for="role" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">用户角色</label>
                        <select id="role" name="role" required
                            class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                            <option value="user">普通用户</option>
                            <option value="admin">管理员</option>
                        </select>
                    </div>
                    
                    <button type="submit" id="submitBtn"
                        class="w-full bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800">
                        <i class="fas fa-save mr-1"></i> <span id="submitBtnText">添加用户</span>
                    </button>

                    <button type="button" id="resetBtn" class="w-full bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 py-2 px-4 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400 dark:focus:ring-offset-gray-800 hidden">
                        <i class="fas fa-times mr-1"></i> 取消编辑
                    </button>
                </form>
            </div>

            <!-- 右侧列表区域 -->
            <div class="lg:col-span-3 space-y-4 sm:space-y-6">
                <!-- 密码转换工具 -->
                <div class="bg-white dark:bg-gray-800 p-3 sm:p-6 rounded-lg shadow-md">
                    <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4 flex items-center">
                        <i class="fas fa-key mr-2 text-green-500 dark:text-green-400"></i> 密码转换工具
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="plainPassword" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">原始密码</label>
                            <input type="text" id="plainPassword" placeholder="输入要转换的密码..."
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                            <button id="convertPasswordBtn"
                                class="mt-2 w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 dark:focus:ring-offset-gray-800">
                                <i class="fas fa-exchange-alt mr-1"></i> 转换密码
                            </button>
                        </div>
                        <div>
                            <label for="hashedPassword" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">哈希密码</label>
                            <textarea id="hashedPassword" readonly rows="3" placeholder="转换后的哈希密码将显示在这里..."
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-gray-50 dark:bg-gray-600 text-sm font-mono resize-none text-gray-900 dark:text-gray-100"></textarea>
                            <button id="copyHashBtn" disabled
                                class="mt-2 w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed">
                                <i class="fas fa-copy mr-1"></i> 复制哈希值
                            </button>
                        </div>
                    </div>
                    <div class="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900 border-l-4 border-yellow-400 text-yellow-700 dark:text-yellow-300 text-sm">
                        <p class="font-semibold">说明：</p>
                        <ul class="mt-1 list-disc list-inside space-y-1">
                            <li>此工具使用与系统注册相同的 bcrypt 算法（salt rounds = 10）</li>
                            <li>每次转换同一密码会产生不同的哈希值，这是正常的安全特性</li>
                            <li>哈希值不可逆，无法从哈希值反推出原密码</li>
                        </ul>
                    </div>
                </div>

                <!-- 用户列表 -->
                <div class="bg-white dark:bg-gray-800 p-3 sm:p-6 rounded-lg shadow-md">
                    <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-2 flex items-center">
                        <i class="fas fa-list mr-2 text-blue-500 dark:text-blue-400"></i> 用户列表
                    </h2>
                    <div class="flex flex-wrap items-center gap-2 sm:gap-4 mb-4 mobile-stack mobile-full-width">
                        <div class="flex-1 w-full">
                            <div class="relative">
                                <input type="text" id="userSearch" placeholder="搜索用户名..."
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                                <span class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500">
                                    <i class="fas fa-search"></i>
                                </span>
                            </div>
                        </div>
                        <div class="flex flex-wrap gap-2 mobile-w-full mobile-mt-2">
                            <select id="roleFilter"
                                class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm mobile-w-full bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                                <option value="all">所有角色</option>
                                <option value="user">普通用户</option>
                                <option value="admin">管理员</option>
                            </select>
                            <button id="resetFilterBtn"
                                class="px-3 py-2 bg-gray-100 dark:bg-gray-600 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-500 mobile-w-full">
                                <i class="fas fa-sync-alt"></i> 重置
                            </button>
                        </div>
                    </div>
                    
                    <div class="overflow-x-auto w-full">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                        ID
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                        用户名
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                        角色
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                        创建时间
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                        操作
                                    </th>
                                </tr>
                            </thead>
                            <tbody id="userTableBody" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                <!-- 用户数据将通过JavaScript动态填充 -->
                                <tr>
                                    <td colspan="5" class="px-6 py-4 text-center text-gray-500">加载中...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="flex flex-wrap items-center justify-between mt-4 border-t border-gray-200 dark:border-gray-700 pt-4">
                        <div class="text-xs text-gray-700 dark:text-gray-300 mb-2 sm:mb-0" id="totalCount">
                            共 <span id="totalRecords">0</span> 条记录
                        </div>
                        <div class="flex items-center space-x-1 sm:space-x-2">
                            <button id="prevPage" title="上一页"
                                class="px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-30 disabled:cursor-not-allowed">
                                <i class="fas fa-chevron-left"></i>
                            </button>

                            <span id="pageInfo" class="px-2 text-sm text-gray-500 dark:text-gray-400">
                                第 <span id="currentPageDisplay">1</span> / <span id="totalPagesDisplay">1</span> 页
                            </span>

                            <button id="nextPage" title="下一页"
                                class="px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-30 disabled:cursor-not-allowed">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 确认删除模态框 -->
    <div id="deleteModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 max-w-md w-full">
            <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">确认删除</h3>
            <p class="text-gray-600 dark:text-gray-400 mb-6">您确定要删除用户 <span id="deleteUserName" class="font-semibold"></span> 吗？此操作不可撤销。</p>
            <div class="flex justify-end space-x-3">
                <button id="cancelDeleteBtn" class="px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500">
                    取消
                </button>
                <button id="confirmDeleteBtn" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">
                    确认删除
                </button>
            </div>
        </div>
    </div>

    <!-- Toast通知 -->
    <div id="toast" class="fixed top-4 right-4 max-w-xs bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4 z-50 transform transition-transform duration-300 translate-y-[-100%]">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <i id="toastIcon" class="fas fa-check-circle text-green-400"></i>
            </div>
            <div class="ml-3 text-sm font-medium text-gray-900 dark:text-gray-100" id="toastMessage"></div>
        </div>
    </div>

    <script src="/js/auth.js"></script>
    <script src="/js/main.js"></script>
    <script src="/js/user-management.js"></script>
</body>
</html>