<!DOCTYPE html>
<html lang="zh-CN" class="light">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="">
    <title>电源管理 - 配件管理系统</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/viewerjs/1.11.6/viewer.min.css">
    <link rel="stylesheet" href="/css/psu-info.css"></link>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/viewerjs/1.11.6/viewer.min.js"></script>
    <script src="/js/js-pinyin.js"></script>
    <script src="/js/pinyin-wrapper.js"></script>
    <script src="/js/main.js"></script>
    <script src="/js/permission-helper.js"></script>
</head>

<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-2 sm:px-4 py-2 sm:py-4">
        <header class="mb-4 sm:mb-6">
            <div class="flex justify-between items-center">
                <h1 class="text-xl sm:text-2xl font-bold text-gray-700 flex items-center">
                    <i class="fas fa-plug mr-2"></i> 电源管理
                </h1>
            </div>
            <div class="flex justify-between items-center mt-3">
                <p class="text-gray-600 mt-2 text-xs sm:text-sm">管理电脑电源信息</p>
                <div class="flex flex-wrap space-x-2 sm:space-x-4">
                    <!-- 主题切换按钮 -->
                    <button id="themeToggleBtn" class="theme-toggle-btn flex items-center justify-center bg-gray-600 text-white w-12 h-12 rounded-md hover:bg-gray-700" title="切换主题模式">
                        <i id="themeIcon" class="fas fa-sun theme-toggle-icon text-yellow-500 text-lg"></i>
                    </button>
                    <a href="/pc-components.html"
                        class="flex items-center justify-center bg-gray-600 text-white py-2 px-5 rounded-md hover:bg-gray-700 text-sm sm:text-base">
                        <i class="fas fa-arrow-left text-lg mr-2"></i> <span class="hidden xs:inline">返回配件列表</span><span class="xs:hidden">返回</span>
                    </a>
                    <a href="/"
                        class="flex items-center justify-center bg-gray-600 text-white py-2 px-5 rounded-md hover:bg-gray-700 text-sm sm:text-base">
                        <i class="fas fa-home text-lg mr-2"></i> 首页
                    </a>
                </div>
            </div>
        </header>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6 mt-2">
            <!-- 左侧表单区域 -->
            <div class="lg:col-span-1 bg-white p-3 sm:p-6 rounded-lg shadow-md">
                <h2 class="text-lg sm:text-xl font-semibold text-gray-800 mb-3 sm:mb-4 flex items-center">
                    <i class="fas fa-plus-circle mr-1 sm:mr-2 text-gray-500"></i> 添加电源信息
                </h2>

                <form id="psuForm" class="space-y-3 sm:space-y-4">
                    <!-- 智能识别输入 -->
                    <div class="border border-gray-200 rounded-md p-3 mb-3">
                        <h3 class="text-md font-medium text-gray-700 mb-2">智能识别输入</h3>
                        <div class="bg-gray-50 p-3 rounded-lg border border-gray-200">
                            <div class="flex gap-2 mobile:flex-col">
                                <input type="text" id="smartInput"
                                    placeholder="粘贴电源参数（示例：海韵 FOCUS GX-750 750W 金牌全模组 ATX 135mm 10年保修 ¥999）"
                                    class="flex-1 px-3 py-2 border rounded-md text-sm focus:ring-2 focus:ring-gray-500">
                                <button type="button" id="autoFillBtn"
                                    class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 text-sm transition-colors">
                                    <i class="fas fa-robot mr-2"></i>智能解析
                                </button>
                            </div>
                            <p class="mt-2 text-xs text-gray-600">支持格式：品牌 型号 功率 认证 模组类型 规格 保修期 价格</p>
                        </div>
                    </div>
                    
                    <!-- 基本信息 -->
                    <div class="border border-gray-200 rounded-md p-3">
                        <h3 class="text-md font-medium text-gray-700 mb-2">基本信息</h3>
                        <div class="space-y-3">
                            <div>
                                <label for="brand" class="block text-sm font-medium text-gray-700 mb-1">品牌 <span class="text-red-500">*</span></label>
                                <input type="text" id="brand" name="brand" required class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-gray-500 focus:border-gray-500 text-sm sm:text-base">
                            </div>
                            <div>
                                <label for="model" class="block text-sm font-medium text-gray-700 mb-1">型号 <span class="text-red-500">*</span></label>
                                <input type="text" id="model" name="model" required class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-gray-500 focus:border-gray-500 text-sm sm:text-base">
                            </div>
                            <div>
                                <label for="wattage" class="block text-sm font-medium text-gray-700 mb-1">功率 (W) <span class="text-red-500">*</span></label>
                                <input type="number" id="wattage" name="wattage" required min="0" class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-gray-500 focus:border-gray-500 text-sm sm:text-base">
                            </div>
                        </div>
                    </div>

                    <!-- 规格与认证 -->
                    <div class="border border-gray-200 rounded-md p-3">
                        <h3 class="text-md font-medium text-gray-700 mb-2">规格与认证</h3>
                        <div class="space-y-3">
                            <div>
                                <label for="length" class="block text-sm font-medium text-gray-700 mb-1">规格</label>
                                <select id="length" name="psu_length" class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-gray-500 focus:border-gray-500 text-sm sm:text-base">
                                    <option value="">请选择</option>
                                    <option value="ATX">ATX</option>
                                    <option value="SFX">SFX</option>
                                    <option value="SFX-L">SFX-L</option>
                                    <option value="FlexATX">FlexATX</option>
                                    <option value="TFX">TFX</option>
                                </select>
                            </div>
                            <div>
                                <label for="efficiency" class="block text-sm font-medium text-gray-700 mb-1">能效认证</label>
                                <select id="efficiency" name="efficiency" class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-gray-500 focus:border-gray-500 text-sm sm:text-base">
                                    <option value="">请选择</option>
                                    <option value="80+ White">80+ 白牌</option>
                                    <option value="80+ Bronze">80+ 铜牌</option>
                                    <option value="80+ Silver">80+ 银牌</option>
                                    <option value="80+ Gold">80+ 金牌</option>
                                    <option value="80+ Platinum">80+ 白金牌</option>
                                    <option value="80+ Titanium">80+ 钛金牌</option>
                                </select>
                            </div>
                            <div>
                                <label for="modular" class="block text-sm font-medium text-gray-700 mb-1">模块化类型</label>
                                <select id="modular" name="modular" class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-gray-500 focus:border-gray-500 text-sm sm:text-base">
                                    <option value="">请选择</option>
                                    <option value="Full">全模组</option>
                                    <option value="Semi">半模组</option>
                                    <option value="No">非模组</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- 接口和功能 -->
                    <div class="border border-gray-200 rounded-md p-3">
                        <h3 class="text-md font-medium text-gray-700 mb-2">接口和功能</h3>
                        <div class="space-y-3">
                            <div>
                                <label for="pcie_connector" class="block text-sm font-medium text-gray-700 mb-1">PCIe接口数</label>
                                <input type="text" id="pcie_connector" name="pcie_connector" class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-gray-500 focus:border-gray-500 text-sm sm:text-base">
                            </div>
                            <div>
                                <label for="sata_count" class="block text-sm font-medium text-gray-700 mb-1">SATA接口数</label>
                                <input type="text" id="sata_count" name="sata_count" class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-gray-500 focus:border-gray-500 text-sm sm:text-base">
                            </div>
                            <div>
                                <label for="cpu_connector" class="block text-sm font-medium text-gray-700 mb-1">CPU 8针接口数</label>
                                <input type="text" id="cpu_connector" name="cpu_connector" class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-gray-500 focus:border-gray-500 text-sm sm:text-base">
                            </div>
                        </div>
                    </div>

                    <!-- 物理特性和其他 -->
                    <div class="border border-gray-200 rounded-md p-3">
                        <h3 class="text-md font-medium text-gray-700 mb-2">物理特性和其他</h3>
                        <div class="space-y-3">
                            <div>
                                <label for="physical_length" class="block text-sm font-medium text-gray-700 mb-1">长度 (mm)</label>
                                <input type="number" id="physical_length" name="physical_length" min="0" class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-gray-500 focus:border-gray-500 text-sm sm:text-base">
                            </div>
                            <div>
                                <label for="fan_size" class="block text-sm font-medium text-gray-700 mb-1">风扇尺寸 (mm)</label>
                                <input type="number" id="fan_size" name="fan_size" min="0" class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-gray-500 focus:border-gray-500 text-sm sm:text-base">
                            </div>
                            <div>
                                <label for="warranty" class="block text-sm font-medium text-gray-700 mb-1">保修期 (年)</label>
                                <input type="number" id="warranty" name="warranty" min="0" class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-gray-500 focus:border-gray-500 text-sm sm:text-base">
                            </div>
                            <div>
                                <label for="price" class="block text-sm font-medium text-gray-700 mb-1">价格 (¥)</label>
                                <input type="number" id="price" name="price" step="0.01" min="0" class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-gray-500 focus:border-gray-500 text-sm sm:text-base">
                            </div>
                            <div>
                                <label for="image" class="block text-sm font-medium text-gray-700 mb-1">图片</label>
                                <input type="file" id="image" name="image" accept="image/*" class="w-full px-2 sm:px-3 py-1 sm:py-2 text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-gray-50 file:text-gray-700 hover:file:bg-gray-100">
                                <p class="text-xs text-slate-500 dark:text-slate-400 mt-1">PNG, JPG, GIF 格式，大小不超过5MB（将自动转换为WebP格式以优化加载速度）</p>

                                <!-- 上传进度条 -->
                                <div id="uploadProgressContainer" class="mt-2 hidden">
                                    <div class="bg-gray-200 dark:bg-gray-700 rounded-full h-3 mb-2 overflow-hidden">
                                        <div id="uploadProgressBar" class="bg-gradient-to-r from-blue-500 to-blue-600 dark:from-blue-400 dark:to-blue-500 h-3 rounded-full transition-all duration-500 ease-out relative" style="width: 0%">
                                            <!-- 进度条光泽效果 -->
                                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-20 animate-pulse"></div>
                                        </div>
                                    </div>
                                    <div class="flex justify-between items-center text-xs">
                                        <span id="uploadProgressText" class="text-gray-600 dark:text-gray-400 flex items-center">
                                            <i id="uploadProgressIcon" class="fas fa-upload mr-1"></i>
                                            准备上传...
                                        </span>
                                        <span id="uploadProgressPercent" class="text-blue-600 dark:text-blue-400 font-medium">0%</span>
                                    </div>
                                </div>

                                <div id="imagePreview" class="mt-2 hidden">
                                    <img id="previewImage" class="h-32 object-contain border rounded-md" alt="预览">
                                </div>
                            </div>
                            <div>
                                <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">备注</label>
                                <textarea id="notes" name="notes" rows="3" class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-gray-500 focus:border-gray-500 text-sm sm:text-base"></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end space-x-3 mt-6">
                        <button type="button" id="resetFormBtn" class="px-3 py-1 sm:px-4 sm:py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                            重置
                        </button>
                        <button type="submit" class="px-3 py-1 sm:px-4 sm:py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gray-600 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                            <i class="fas fa-save mr-1"></i> 保存电源信息
                        </button>
                    </div>
                </form>
            </div>

            <!-- 右侧列表区域 -->
            <div class="lg:col-span-2 bg-white p-3 sm:p-6 rounded-lg shadow-md">
                <h2 class="text-lg font-bold text-gray-800 mb-3 flex items-center">
                    <i class="fas fa-plug mr-2"></i> 电源列表
                </h2>
                
                <!-- 搜索和过滤 -->
                <div class="flex flex-wrap items-center gap-2 sm:gap-4">
                    <div class="w-full sm:w-auto sm:flex-1">
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-search text-gray-400"></i>
                            </div>
                            <input type="text" id="searchInput" placeholder="搜索电源型号、品牌等..." 
                                class="w-full py-2 pl-10 pr-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-gray-500 focus:border-gray-500">
                        </div>
                    </div>

                    <div class="flex mt-2 sm:mt-0 gap-2 w-full sm:w-auto">
                        <select id="brandFilter" class="flex-1 sm:flex-none border border-gray-300 rounded-md shadow-sm py-2 pl-3 pr-8 focus:outline-none focus:ring-gray-500 focus:border-gray-500 text-sm">
                            <option value="">所有品牌</option>
                        </select>
                        <select id="wattageFilter" class="flex-1 sm:flex-none border border-gray-300 rounded-md shadow-sm py-2 pl-3 pr-8 focus:outline-none focus:ring-gray-500 focus:border-gray-500 text-sm">
                            <option value="">所有功率</option>
                            <option value="500-650">500-650W</option>
                            <option value="651-850">651-850W</option>
                            <option value="851-1000">851-1000W</option>
                            <option value="1001+">1000W+</option>
                        </select>
                        <button id="resetFilters" class="flex-none bg-gray-200 hover:bg-gray-300 text-gray-700 py-2 px-4 rounded-md">
                            <i class="fas fa-sync-alt mr-1"></i> 重置
                        </button>
                    </div>
                </div>
                
                <div class="overflow-x-auto mt-4">
                    <table class="min-w-full table-compact sm:table-auto card-style no-dividers">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider model-column">图片 / 型号</th>
                                <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell">品牌</th>
                                <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell">规格</th>
                                <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">功率</th>
                                <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell">认证</th>
                                <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell">价格</th>
                                <th class="px-2 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody id="psuTableBody" class="bg-white">
                            <!-- 数据将在这里动态加载 -->
                        </tbody>
                    </table>
                </div>
                <div class="flex flex-wrap items-center justify-between mt-4 border-t border-gray-200 pt-4">
                    <div class="text-xs text-gray-700 mb-2 sm:mb-0" id="totalCount">
                        共 <span id="totalRecords">0</span> 条记录
                    </div>
                    <div class="flex items-center space-x-1 sm:space-x-2 page-controls-mobile">
                        <button id="firstPage" title="首页"
                            class="px-2 py-1 border border-gray-300 rounded-md text-sm bg-white text-gray-500 hover:bg-gray-50 disabled:opacity-30 disabled:cursor-not-allowed">
                            <i class="fas fa-angle-double-left"></i>
                        </button>
                        <button id="prevBtn" title="上一页"
                            class="px-2 py-1 border border-gray-300 rounded-md text-sm bg-white text-gray-500 hover:bg-gray-50 disabled:opacity-30 disabled:cursor-not-allowed">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        
                        <div id="pageNumbers" class="hidden sm:flex space-x-1">
                            <!-- 页码将通过JavaScript填充 -->
                        </div>
                        
                        <span id="pageInfo" class="px-2 py-1 text-sm text-gray-500 whitespace-nowrap">
                            第 <span id="currentPageDisplay">1</span> / <span id="totalPagesDisplay">1</span> 页
                        </span>
                        
                        <button id="nextBtn" title="下一页"
                            class="px-2 py-1 border border-gray-300 rounded-md text-sm bg-white text-gray-500 hover:bg-gray-50 disabled:opacity-30 disabled:cursor-not-allowed">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                        <button id="lastPage" title="尾页"
                            class="px-2 py-1 border border-gray-300 rounded-md text-sm bg-white text-gray-500 hover:bg-gray-50 disabled:opacity-30 disabled:cursor-not-allowed">
                            <i class="fas fa-angle-double-right"></i>
                        </button>
                        
                        <div class="hidden sm:flex items-center ml-2">
                            <span class="text-sm">跳转到</span>
                            <input type="number" id="pageJump" min="1" class="w-12 ml-1 px-2 py-1 border border-gray-300 rounded-md text-sm">
                            <button id="goToPage" class="ml-1 px-2 py-1 bg-gray-600 text-white text-sm rounded-md hover:bg-gray-700">
                                确定
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 详情模态框 -->
    <div id="detailsModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden z-50 p-4 pt-20 sm:p-6 md:p-8">
        <div class="bg-white dark:bg-gray-800 dark:bg-opacity-95 rounded-lg max-w-4xl w-full max-h-full flex flex-col">
            <div class="px-6 py-4 border-b dark:border-gray-700 flex justify-center items-center sticky top-0 bg-white dark:bg-gray-800 z-10 flex-shrink-0">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">电源详情</h3>
            </div>
            <div class="p-6 overflow-y-auto">
                <div id="detailsModalContent">
                    <!-- 详细信息将通过JS注入这里 -->
                </div>
            </div>
            <div class="p-4 bg-gray-50 dark:bg-gray-900 border-t dark:border-gray-700 flex justify-end space-x-3 flex-shrink-0">
                <button id="closeDetailsModal" class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:text-gray-200 dark:border-gray-700">
                    <i class="fas fa-times mr-1"></i> 关闭
                </button>
                <button id="detailsEditBtn" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gray-600 hover:bg-gray-700">
                    <i class="fas fa-edit mr-1"></i> 编辑
                </button>
                <button id="detailsDeleteBtn" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700">
                    <i class="fas fa-trash mr-1"></i> 删除
                </button>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div id="deleteConfirmModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden z-50">
        <div class="bg-white dark:bg-gray-800 dark:bg-opacity-95 rounded-lg p-6 max-w-md w-full">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">确认删除</h3>
            <p class="text-gray-500 dark:text-gray-400 mb-4">您确定要删除这条电源信息吗？此操作无法撤销。</p>
            <div class="flex justify-end space-x-3">
                <button id="cancelDeleteBtn" class="px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700">
                    取消
                </button>
                <button id="confirmDeleteBtn" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-600">
                    确认删除
                </button>
            </div>
        </div>
    </div>

    <!-- Toast消息 -->
    <div id="toast" class="fixed bottom-4 right-4 px-4 py-2 rounded-lg text-white bg-green-500 hidden"></div>

    <script src="/js/psu-info.js"></script>
    
</body>

</html> 