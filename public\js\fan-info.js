<!-- 配置 Tailwind 主题 -->
tailwind.config = {
    darkMode: 'class',
    theme: {
        extend: {
            colors: {
                primary: {
                    50: '#fefce8',
                    100: '#fef9c3',
                    200: '#fef08a',
                    300: '#fde047',
                    400: '#facc15',
                    500: '#eab308',
                    600: '#ca8a04',
                    700: '#a16207',
                    800: '#854d0e',
                    900: '#713f12',
                    950: '#422006'
                },
            },
            fontFamily: {
                sans: ['Inter var', 'sans-serif'],
            },
            boxShadow: {
                card: '0 0 0 1px rgba(0,0,0,0.05), 0 10px 15px -3px rgba(0,0,0,0.1), 0 4px 6px -2px rgba(0,0,0,0.05)',
                'card-dark': '0 0 0 1px rgba(255,255,255,0.1), 0 10px 15px -3px rgba(0,0,0,0.5), 0 4px 6px -2px rgba(0,0,0,0.3)',
            }
        }
    }
}

// DOM Elements
const fanForm = document.getElementById('fanForm');
const modelInput = document.getElementById('model');
const brandInput = document.getElementById('brand');
const sizeInput = document.getElementById('size');
const thicknessInput = document.getElementById('thickness');
const rpmInput = document.getElementById('rpm');
const speed_minInput = document.getElementById('speed_min');
const speed_maxInput = document.getElementById('speed_max');
const airflowInput = document.getElementById('airflow');
const noiseLevelInput = document.getElementById('noiseLevel');
const staticPressureInput = document.getElementById('staticPressure');
const connectorInput = document.getElementById('connector');
const pwmInput = document.getElementById('pwm');
const bearing_typeInput = document.getElementById('bearing_type');
const rgbLightingInput = document.getElementById('rgbLighting');
const warrantyInput = document.getElementById('warranty');
const colorInput = document.getElementById('color');
const priceInput = document.getElementById('price');
const notesInput = document.getElementById('notes');
const fanImageInput = document.getElementById('fanImage');
const imagePreviewContainer = document.getElementById('imagePreviewContainer');
const imagePreview = document.getElementById('imagePreview');
const removeImageBtn = document.getElementById('removeImageBtn');
const fanSearch = document.getElementById('fanSearch');
const brandFilter = document.getElementById('brandFilter');
const sizeFilter = document.getElementById('sizeFilter');
const resetFilterBtn = document.getElementById('resetFilterBtn');
const fanTableBody = document.getElementById('fanTableBody');
const prevPageBtn = document.getElementById('prevPage');
const nextPageBtn = document.getElementById('nextPage');
const firstPageBtn = document.getElementById('firstPage');
const lastPageBtn = document.getElementById('lastPage');
const pageInfo = document.getElementById('pageInfo');
const pageNumbers = document.getElementById('pageNumbers');
const currentPageDisplay = document.getElementById('currentPageDisplay');
const totalPagesDisplay = document.getElementById('totalPagesDisplay');
const pageJumpInput = document.getElementById('pageJump');
const goToPageBtn = document.getElementById('goToPage');
const totalCount = document.getElementById('totalCount');
const fanModal = document.getElementById('fanModal');
const closeModalBtn = document.getElementById('closeModalBtn');
const closeModal = document.getElementById('closeModal');
const fanDetails = document.getElementById('fanDetails');
const editBtn = document.getElementById('editBtn');
const deleteBtn = document.getElementById('deleteBtn');
const smartInput = document.getElementById('smartInput');
const autoFillBtn = document.getElementById('autoFillBtn');
const themeToggle = document.getElementById('themeToggle'); // 添加主题切换按钮

// Global Variables
let currentPage = 1;
let totalRecords = 0;
let fans = [];
let selectedFanId = null;
let currentImageFile = null;

// API Endpoints
const API_GET_FANS = '/api/fans';
const API_GET_FAN = '/api/fans/';
const API_ADD_FAN = '/api/fans';
const API_UPDATE_FAN = '/api/fans/';
const API_DELETE_FAN = '/api/fans/';

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    // Initialize event listeners
    initEventListeners();
    // Load fan data
    loadFans();
    // 初始化深色模式
    initTheme();
});

function initEventListeners() {
    // Form submission
    fanForm.addEventListener('submit', handleFormSubmit);

    // Smart identification
    if (autoFillBtn) {
        autoFillBtn.addEventListener('click', parseFanInfo);
    }

    // Image upload
    fanImageInput.addEventListener('change', handleImageUpload);
    removeImageBtn.addEventListener('click', removeImage);

    // Search and filters
    fanSearch.addEventListener('input', debounce(() => {
        currentPage = 1;
        loadFans();
    }, 300));
    brandFilter.addEventListener('change', () => {
        currentPage = 1;
        loadFans();
    });
    sizeFilter.addEventListener('change', () => {
        currentPage = 1;
        loadFans();
    });
    resetFilterBtn.addEventListener('click', resetFilters);

    // Pagination
    if (prevPageBtn) {
        prevPageBtn.addEventListener('click', () => changePage(currentPage - 1));
    }
    
    if (nextPageBtn) {
        nextPageBtn.addEventListener('click', () => changePage(currentPage + 1));
    }
    
    if (firstPageBtn) {
        firstPageBtn.addEventListener('click', () => changePage(1));
    }
    
    if (lastPageBtn) {
        lastPageBtn.addEventListener('click', () => {
            const totalPages = Math.ceil(totalRecords / 10);
            changePage(totalPages);
        });
    }
    
    if (goToPageBtn) {
        goToPageBtn.addEventListener('click', () => {
            if (pageJumpInput) {
                const pageNum = parseInt(pageJumpInput.value);
                if (!isNaN(pageNum)) {
                    changePage(pageNum);
                    pageJumpInput.value = '';
                }
            }
        });
    }
    
    // Page jump on Enter key
    if (pageJumpInput) {
        pageJumpInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                if (goToPageBtn) goToPageBtn.click();
            }
        });
    }

    // Modal
    closeModalBtn.addEventListener('click', hideModal);
    closeModal.addEventListener('click', hideModal);
    editBtn.addEventListener('click', handleEditClick);
    deleteBtn.addEventListener('click', handleDeleteClick);
    
    // 主题切换
    if (themeToggle) {
        themeToggle.addEventListener('click', toggleTheme);
    }
}

// 初始化主题
function initTheme() {
    // 检查本地存储中的主题设置
    const savedTheme = localStorage.getItem('fan-info-theme');
    const htmlElement = document.documentElement;
    
    console.log('[DEBUG] 初始化主题，保存的主题:', savedTheme);
    
    // 如果有保存的主题设置为深色，则应用深色主题，否则默认使用亮色主题
    if (savedTheme === 'dark') {
        // 使用HTML类控制深色模式（Tailwind的方式）
        htmlElement.classList.add('dark');
        updateThemeIcon(true);
    } else {
        // 确保使用亮色模式
        htmlElement.classList.remove('dark');
        updateThemeIcon(false);
    }
}

// 切换主题
function toggleTheme() {
    const htmlElement = document.documentElement;
    const isDarkMode = htmlElement.classList.toggle('dark');
    
    // 保存主题设置
    localStorage.setItem('fan-info-theme', isDarkMode ? 'dark' : 'light');
    console.log('[DEBUG] 切换主题:', isDarkMode ? 'dark' : 'light');
    
    // 更新主题图标
    updateThemeIcon(isDarkMode);
    
    // 添加动画效果
    themeToggle.querySelector('i').classList.add('fa-spin');
    setTimeout(() => {
        themeToggle.querySelector('i').classList.remove('fa-spin');
    }, 500);
    
    // 切换主题后重新渲染风扇列表，确保深色模式正确应用到所有元素
    if (window.innerWidth < 640) {
        renderFans();  // 如果是移动视图，重新渲染列表
    } else {
        // PC端只需要刷新表格样式，不需要重新渲染
        refreshTableStyles(isDarkMode);
    }
}

// DOM loaded event - 确保任何动态内容都能正确应用主题
document.addEventListener('DOMContentLoaded', function() {
    // 检查当前主题并记录
    const isDarkMode = document.documentElement.classList.contains('dark');
    console.log('[DEBUG] DOM加载完成，当前主题:', isDarkMode ? 'dark' : 'light');
    
    // 确保页面加载时立即应用正确的主题样式
    if (isDarkMode) {
        setTimeout(() => {
            refreshTableStyles(true);
        }, 500); // 给一点时间让表格内容首先渲染出来
    }
});

// 更新主题图标
function updateThemeIcon(isDarkMode) {
    const moonIcon = themeToggle.querySelector('.fa-moon');
    const sunIcon = themeToggle.querySelector('.fa-sun');
    
    if (isDarkMode) {
        // 在深色模式下显示太阳图标
        if (moonIcon) moonIcon.classList.add('hidden');
        if (sunIcon) sunIcon.classList.remove('hidden');
    } else {
        // 在亮色模式下显示月亮图标
        if (moonIcon) moonIcon.classList.remove('hidden');
        if (sunIcon) sunIcon.classList.add('hidden');
    }
}

// Load fans with search, filters and pagination
function loadFans() {
    const searchQuery = fanSearch.value.trim();
    const brandValue = brandFilter.value !== 'all' ? brandFilter.value : '';
    const sizeValue = sizeFilter.value !== 'all' ? sizeValue.value : '';

    fanTableBody.innerHTML = '<tr><td colspan="5" class="px-3 py-4 text-center text-gray-500">加载中...</td></tr>';

    let url = `${API_GET_FANS}?page=${currentPage}`;
    if (searchQuery) url += `&search=${encodeURIComponent(searchQuery)}`;
    if (brandValue) url += `&brand=${encodeURIComponent(brandValue)}`;
    if (sizeValue) url += `&size=${encodeURIComponent(sizeValue)}`;

    const fetchFansPromise = fetchWithAuth(url);
    const isAdminPromise = isAdmin();

    Promise.all([fetchFansPromise, isAdminPromise])
        .then(([response, isAdminUser]) => {
            if (!response.ok) {
                 return response.json().then(err => {
                    throw new Error(err.message || '加载风扇列表失败');
                }).catch(() => {
                    throw new Error('加载风扇列表失败');
                });
            }
            return response.json().then(data => ({ data, isAdminUser }));
        })
        .then(({ data, isAdminUser }) => {
            console.log('[DEBUG] API Response:', data); // 添加调试日志
            
            // 处理不同形式的API响应
            if (Array.isArray(data)) {
                fans = data;
                totalRecords = data.length;
            } else if (data.fans && Array.isArray(data.fans)) {
                fans = data.fans;
                totalRecords = data.total || data.fans.length;
            } else if (data.data && Array.isArray(data.data)) {
                fans = data.data;
                totalRecords = data.total || data.data.length;
            } else {
                console.error('[ERROR] 无法识别的API响应格式:', data);
                fans = [];
                totalRecords = 0;
            }
            
            // 记录第一个风扇记录以检查字段
            if (fans && fans.length > 0) {
                console.log('[DEBUG] 第一条风扇数据结构:', fans[0]);
            }
            
            updatePagination();
            renderFans(isAdminUser);
            
            // 确保主题正确应用
            const isDarkMode = document.documentElement.classList.contains('dark');
            if (isDarkMode) {
                console.log('[DEBUG] 检测到深色模式，应用深色主题样式');
                
                // 应用深色样式到表格容器
                const tableContainers = document.querySelectorAll('.rounded-lg.shadow-md');
                tableContainers.forEach(container => {
                    container.style.backgroundColor = '#1e293b';
                });
                
                // 应用深色样式到表头
                const tableHeaders = document.querySelectorAll('thead');
                tableHeaders.forEach(header => {
                    header.style.backgroundColor = 'rgba(51, 65, 85, 0.5)'; // slate-700/50
                });
                
                // 刷新表格样式
                refreshTableStyles(true);
                
                // 应用深色模式到所有元素
                applyCurrentThemeToElements();
            }
        })
        .catch(error => {
            console.error('Error loading fans:', error);
            fanTableBody.innerHTML = '<tr><td colspan="5" class="px-3 py-4 text-center text-red-500">加载风扇数据时出错</td></tr>';
        });
}

// Render fans in the table
function renderFans(isAdminUser) {
    fanTableBody.innerHTML = '';
    
    if (!fans || fans.length === 0) {
        const emptyRow = document.createElement('tr');
        const emptyCell = document.createElement('td');
        emptyCell.colSpan = 8;
        emptyCell.className = 'px-6 py-8 text-center text-gray-500 dark:text-slate-400';
        emptyCell.innerHTML = '<div class="flex flex-col items-center space-y-2"><i class="fas fa-wind text-4xl opacity-30 mb-2"></i><span>暂无风扇数据</span></div>';
        emptyRow.appendChild(emptyCell);
        fanTableBody.appendChild(emptyRow);
        return;
    }

    // 检测是否为移动设备
    const isMobile = window.innerWidth < 640;
    
    // 根据设备类型选择渲染方法
    if (isMobile) {
        renderMobileCards(fans, isAdminUser);
        return;
    }
    
    // PC端表格布局
    fans.forEach((fan, index) => {
        const row = document.createElement('tr');
        row.classList.add(
            'fade-in', 
            'hover:bg-slate-50', 
            'dark:hover:bg-slate-700/40', 
            'border-b', 
            'dark:border-slate-700/70', 
            'transition-colors',
            'duration-150'
        );
        
        // 添加隔行颜色
        if (index % 2 === 0) {
            row.classList.add('bg-slate-50/50', 'dark:bg-slate-800/30');
        }
        
        // 获取图片URL
        const imgUrl = getFieldValue(fan, ['image_url'], '/images/default-fan.png');
        
        // 处理转速显示
        let rpmDisplay = '-';
        const speed_min = getFieldValue(fan, ['speed_min'], '');
        const speed_max = getFieldValue(fan, ['speed_max'], '');
        
        if (speed_min && speed_max) {
            rpmDisplay = `${speed_min}-${speed_max}`;
        } else if (speed_max) {
            rpmDisplay = `${speed_max}`;
        } else if (speed_min) {
            rpmDisplay = `${speed_min}`;
        }
        
        // 为转速创建样式化的标签
        const rpmTag = rpmDisplay !== '-' ? 
            `<span class="spec-rpm inline-flex items-center justify-center px-2.5 py-1 rounded-md bg-amber-500/10 text-amber-600 dark:text-amber-400 dark:bg-amber-700/20 border border-amber-500/20 dark:border-amber-600/30 text-xs font-medium whitespace-nowrap">
                <i class="fas fa-tachometer-alt mr-1.5 opacity-70"></i>${rpmDisplay} RPM
            </span>` : 
            '<span class="text-gray-400 dark:text-gray-500">-</span>';

        // 品牌标签样式
        const brandName = getFieldValue(fan, ['brand'], '');
        const brandTag = `
            <span class="brand-tag inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium ${getBrandStyleClass(brandName)}">
                ${getBrandIcon(brandName)}${brandName}
            </span>
        `;
        
        // 尺寸标签样式
        const sizeVal = getFieldValue(fan, ['size'], '');
        const sizeTag = `
            <span class="size-tag inline-flex items-center justify-center px-2.5 py-1 rounded-md bg-blue-500/10 text-blue-600 dark:text-blue-400 dark:bg-blue-700/20 border border-blue-500/20 dark:border-blue-600/30 text-xs font-medium whitespace-nowrap">
                <i class="fas fa-ruler-combined mr-1.5 opacity-70"></i>${sizeVal} mm
            </span>
        `;
        
        // 操作按钮样式
        let actionButtonsHTML = `
            <button class="view-fan-btn p-1.5 rounded-full text-slate-600 hover:text-blue-500 hover:bg-blue-50 dark:text-slate-400 dark:hover:text-blue-400 dark:hover:bg-blue-900/30 transition-colors" data-id="${fan.id}" title="查看详情">
                <i class="fas fa-eye"></i>
            </button>
        `;

        if (isAdminUser) {
            actionButtonsHTML += `
                <button class="edit-fan-btn p-1.5 rounded-full text-slate-600 hover:text-green-500 hover:bg-green-50 dark:text-slate-400 dark:hover:text-green-400 dark:hover:bg-green-900/30 transition-colors" data-id="${fan.id}" title="编辑">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="delete-fan-btn p-1.5 rounded-full text-slate-600 hover:text-red-500 hover:bg-red-50 dark:text-slate-400 dark:hover:text-red-400 dark:hover:bg-red-900/30 transition-colors" data-id="${fan.id}" title="删除">
                    <i class="fas fa-trash"></i>
                </button>
            `;
        }

        const actionButtons = `
            <div class="flex items-center justify-center space-x-2">
                ${actionButtonsHTML}
            </div>
        `;
        
        row.innerHTML = `
            <td class="px-4 py-3 whitespace-nowrap">
                <div class="flex items-center">
                    <div class="flex-shrink-0 w-12 h-12 mr-3 relative group image-column">
                        <div class="w-12 h-12 rounded-lg overflow-hidden shadow-sm border border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-800">
                            <img class="w-full h-full object-contain" src="${imgUrl}" alt="${getFieldValue(fan, ['model'], '风扇')}" title="点击查看大图">
                        </div>
                        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer">
                            <i class="fas fa-search-plus text-white text-opacity-90"></i>
                        </div>
                    </div>
                    <div class="ml-1">
                        <div class="text-sm font-medium text-slate-800 dark:text-slate-200 model-title">${getFieldValue(fan, ['model'])}</div>
                        <div class="text-xs text-slate-500 dark:text-slate-400 mt-0.5 sm:hidden">${brandName}</div>
                    </div>
                </div>
            </td>
            <td class="px-4 py-3 whitespace-nowrap hidden sm:table-cell">
                ${brandTag}
            </td>
            <td class="px-4 py-3 whitespace-nowrap">
                ${sizeTag}
                <div class="text-xs text-slate-500 dark:text-slate-400 mt-1 sm:hidden">${rpmDisplay !== '-' ? `${rpmDisplay} RPM` : '-'}</div>
            </td>
            <td class="px-4 py-3 whitespace-nowrap hidden sm:table-cell">
                ${rpmTag}
            </td>
            <td class="px-4 py-3 whitespace-nowrap">
                ${actionButtons}
            </td>
        `;

        // 添加事件监听器
        const imgContainer = row.querySelector('.image-column');
        const img = imgContainer.querySelector('img');
        const viewOverlay = imgContainer.querySelector('.absolute');
        
        // 给图片添加点击事件
        img.onclick = (event) => {
            event.stopPropagation();
            openImageFullscreen(imgUrl);
        };
        
        // 给覆盖层添加点击事件
        viewOverlay.onclick = (event) => {
            event.stopPropagation();
            openImageFullscreen(imgUrl);
        };

        // 添加行内按钮点击事件
        row.querySelector('.view-fan-btn').addEventListener('click', () => viewFan(fan.id));
        if (isAdminUser) {
            row.querySelector('.edit-fan-btn').addEventListener('click', () => editFan(fan.id));
            row.querySelector('.delete-fan-btn').addEventListener('click', () => confirmDeleteFan(fan.id));
        }

        fanTableBody.appendChild(row);
    });

    // 添加窗口大小变化监听器进行响应式适配
    window.addEventListener('resize', debounce(() => {
        const nowIsMobile = window.innerWidth < 640;
        const wasMobile = fanTableBody.querySelector('.fan-card-new') !== null;
        if ((nowIsMobile && !wasMobile) || (!nowIsMobile && wasMobile)) {
            // 重新渲染需要权限信息，因此从根函数调用
            loadFans();
        }
    }, 250));
    
    // 应用当前的主题
    applyCurrentThemeToElements();
    
    // 确保在PC端表格中应用正确的主题样式
    const isDarkMode = document.documentElement.classList.contains('dark');
    if (isDarkMode && !isMobile) {
        refreshTableStyles(true);
    }
}

// 刷新表格样式以适应当前主题
function refreshTableStyles(isDarkMode) {
    console.log('[DEBUG] 刷新表格样式，当前主题:', isDarkMode ? 'dark' : 'light');
    
    // 文本颜色调整
    document.querySelectorAll('.model-title').forEach(el => {
        el.style.color = isDarkMode ? '#f1f5f9' : '#1a202c';
    });
    
    // 小标签文本颜色
    document.querySelectorAll('.text-gray-500').forEach(el => {
        if (!el.classList.contains('spec-tag') && !el.classList.contains('fan-badge')) {
            el.style.color = isDarkMode ? '#94a3b8' : '#6b7280';
        }
    });
    
    // 表格行样式
    document.querySelectorAll('tbody tr').forEach(row => {
        // 设置hover效果
        row.classList.toggle('hover:bg-gray-50', !isDarkMode);
        row.classList.toggle('dark:hover:bg-slate-700/50', isDarkMode);
        
        // 设置边框颜色
        row.style.borderColor = isDarkMode ? '#334155' : '#e5e7eb';
    });
    
    // 调整spec-tag样式 
    document.querySelectorAll('.spec-tag.size').forEach(tag => {
        if (isDarkMode) {
            tag.style.backgroundColor = 'rgba(30, 58, 138, 0.3)';
            tag.style.color = '#93c5fd';
            tag.style.borderColor = 'rgba(30, 58, 138, 0.4)';
        } else {
            tag.style.backgroundColor = '#dbeafe'; // 更深的蓝色背景 blue-100
            tag.style.color = '#1e40af'; // 更深的蓝色文本 blue-800
            tag.style.borderColor = '#bfdbfe'; // 更深的边框色 blue-200
            tag.style.fontWeight = '600'; // 字体加粗
        }
    });
    
    document.querySelectorAll('.spec-tag.rpm').forEach(tag => {
        if (isDarkMode) {
            tag.style.backgroundColor = 'rgba(146, 64, 14, 0.3)';
            tag.style.color = '#fcd34d';
            tag.style.borderColor = 'rgba(146, 64, 14, 0.4)';
        } else {
            tag.style.backgroundColor = '#fef3c7'; // 更深的琥珀色背景 amber-100
            tag.style.color = '#92400e'; // 更深的琥珀色文本 amber-800
            tag.style.borderColor = '#fde68a'; // 更深的边框色 amber-200
            tag.style.fontWeight = '600'; // 字体加粗
        }
    });
    
    // 替代文本字符(如'-')
    document.querySelectorAll('td').forEach(td => {
        if (td.textContent.trim() === '-') {
            td.style.color = isDarkMode ? '#94a3b8' : '#6b7280';
        }
    });
}

// 确保动态生成的元素应用正确的主题样式
function applyCurrentThemeToElements() {
    // 当元素动态渲染时，确保它们获得正确的深色模式类
    const isDarkMode = document.documentElement.classList.contains('dark');
    if (isDarkMode) {
        console.log('[DEBUG] 应用深色主题到新渲染元素');
        
        // 确保表格中的文本颜色正确
        const modelTitles = document.querySelectorAll('.model-title');
        modelTitles.forEach(title => {
            title.classList.add('dark:text-slate-100');
        });
        
        // 为表格行添加正确的hover效果
        const tableRows = document.querySelectorAll('tbody tr');
        tableRows.forEach(row => {
            row.classList.add('dark:hover:bg-slate-700/50');
            row.classList.add('dark:border-slate-700');
        });
        
        // 强制应用深色模式文字颜色
        document.querySelectorAll('td').forEach(td => {
            const textElements = td.querySelectorAll('div, span:not(.spec-tag):not(.fan-badge)');
            textElements.forEach(el => {
                if (el.classList.contains('text-gray-900')) {
                    el.classList.add('dark:text-gray-100');
                }
                if (el.classList.contains('text-gray-500')) {
                    el.classList.add('dark:text-gray-400');
                }
            });
        });
    }
}

// Render fan details in modal
function renderFanDetails(fan) {
    console.log('[DEBUG] 渲染风扇详情:', fan);
    
    // 使用辅助函数获取字段值
    const model = getFieldValue(fan, ['model']);
    const brand = getFieldValue(fan, ['brand']);
    const size = getFieldValue(fan, ['size']);
    const thickness = getFieldValue(fan, ['thickness']);
    
    // 处理转速数据 - 获取最低转速和最高转速
    const speed_min = getFieldValue(fan, ['speed_min']);
    const speed_max = getFieldValue(fan, ['speed_max']);
    
    // 组合转速显示
    let rpm = '';
    if (speed_min && speed_max) {
        rpm = `${speed_min}-${speed_max} RPM`;
    } else if (speed_min) {
        rpm = `${speed_min} RPM`;
    } else if (speed_max) {
        rpm = `${speed_max} RPM`;
    } else {
        rpm = '-';
    }
    
    const airflow = getFieldValue(fan, ['airflow']);
    const noiseLevel = getFieldValue(fan, ['noiseLevel']);
    const staticPressure = getFieldValue(fan, ['staticPressure']);
    const connector = getFieldValue(fan, ['connector']);
    const bearingType = getFieldValue(fan, ['bearing_type']);
    const rgbLighting = getFieldValue(fan, ['rgbLighting'], '-');
    const pwm = getFieldValue(fan, ['pwm'], '-');
    const warranty = getFieldValue(fan, ['warranty']);
    const color = getFieldValue(fan, ['color'], '-');
    const price = getFieldValue(fan, ['price']);
    const notes = getFieldValue(fan, ['notes']);
    
    console.log('[DEBUG] RGB灯效原始值:', rgbLighting, typeof rgbLighting);
    console.log('[DEBUG] 转速数据:', speed_min, speed_max, rpm);

    let rgbStatus = '-';
    // 处理各种可能的RGB值格式
    if (rgbLighting === '1' || rgbLighting === 1 || 
        rgbLighting === 'true' || rgbLighting === true || 
        rgbLighting === '有' || rgbLighting === 'yes' || 
        rgbLighting === 'y' || /rgb|led/i.test(rgbLighting)) {
        rgbStatus = '有';
    } else if (rgbLighting === '0' || rgbLighting === 0 || 
               rgbLighting === 'false' || rgbLighting === false || 
               rgbLighting === '无' || rgbLighting === 'no' || 
               rgbLighting === 'n' || rgbLighting === '-') {
        rgbStatus = '无';
    } else if (rgbLighting) {
        // 如果有值但不在上述条件中，可能是自定义RGB信息
        rgbStatus = rgbLighting;
    }
    
    // 处理PWM支持状态
    let pwmStatus = '-';
    if (pwm === '1' || pwm === 1 || pwm === 'true' || pwm === true || pwm === '支持') {
        pwmStatus = '支持';
    } else if (pwm === '0' || pwm === 0 || pwm === 'false' || pwm === false || pwm === '不支持') {
        pwmStatus = '不支持';
    } else if (pwm && pwm !== '-') {
        pwmStatus = pwm;
    }

    console.log('[DEBUG] RGB灯效处理后的值:', rgbStatus);
    console.log('[DEBUG] PWM支持状态:', pwmStatus);

    // 获取图片URL
    const imageUrl = getFieldValue(fan, ['image_url'], '');
    console.log('[DEBUG] 详情页图片URL:', imageUrl);
    
    fanDetails.innerHTML = `
        <!-- 主要信息区：图片和重要信息并排显示 -->
        <div class="flex flex-col md:flex-row gap-6">
            <!-- 左侧风扇图片 - 垂直居中 -->
            <div class="w-full md:w-1/3 flex items-center justify-center">
                <div class="bg-slate-100 dark:bg-slate-700/30 p-4 rounded-lg h-full flex items-center justify-center relative group image-preview-container transition-all">
                    ${imageUrl ? 
                        `<img src="${imageUrl}" alt="${model}" class="w-full rounded-lg image-preview object-contain max-h-64">
                        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer">
                            <i class="fas fa-search-plus text-white text-opacity-90 text-2xl"></i>
                        </div>` : 
                        `<div class="text-center text-slate-400 dark:text-slate-500 py-12 px-4">
                            <i class="fas fa-fan text-6xl mb-3 opacity-50"></i>
                            <p>暂无图片</p>
                        </div>`
                    }
                </div>
            </div>
            
            <!-- 右侧重要信息 -->
            <div class="md:w-2/3">
                <!-- 标题和价格区域 -->
                <div class="border-b border-slate-200 dark:border-slate-700 pb-3 mb-4">
                    <div class="flex items-center justify-between mb-1.5">
                        <h2 class="text-xl font-bold text-slate-800 dark:text-slate-200">${model || '未知型号'}</h2>
                        <div class="px-3 py-1 bg-primary-100 dark:bg-primary-900/30 text-primary-800 dark:text-primary-300 rounded-full text-sm font-semibold">
                            ${brand || '未知品牌'}
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <div class="text-slate-600 dark:text-slate-400 text-sm">
                                <i class="fas fa-ruler-combined mr-1 text-primary-500"></i> ${size ? `${size}mm` : '尺寸未知'}
                            </div>
                            ${thickness ? `
                            <div class="text-slate-600 dark:text-slate-400 text-sm">
                                <i class="fas fa-arrows-alt-v mr-1 text-primary-500"></i> 厚度: ${thickness}mm
                            </div>` : ''}
                        </div>
                        <p class="text-xl font-bold text-red-600 dark:text-red-400">${price ? `¥${price}` : ''}</p>
                    </div>
                </div>
                
                <!-- 关键信息卡片 -->
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
                    <div class="bg-amber-100 dark:bg-amber-900/20 p-4 rounded-lg border border-amber-200 dark:border-amber-800/20">
                        <h4 class="font-semibold text-amber-800 dark:text-amber-300 mb-1 flex items-center">
                            <i class="fas fa-tachometer-alt mr-1.5"></i> 转速
                        </h4>
                        <p class="text-2xl font-bold text-amber-800 dark:text-amber-400">${rpm}</p>
                        <div class="mt-1 text-sm font-medium text-amber-700 dark:text-amber-500">
                            <div class="flex items-center">
                                <i class="fas fa-wind mr-1.5"></i>
                                风量: ${airflow ? `${airflow} CFM` : '-'}
                            </div>
                        </div>
                    </div>

                    <div class="bg-blue-100 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800/20">
                        <h4 class="font-semibold text-blue-800 dark:text-blue-300 mb-1 flex items-center">
                            <i class="fas fa-volume-down mr-1.5"></i> 噪音级别
                        </h4>
                        <p class="text-2xl font-bold text-blue-800 dark:text-blue-400">${noiseLevel || '-'} <span class="text-sm">dBA</span></p>
                        <div class="mt-1 text-sm font-medium text-blue-700 dark:text-blue-500">
                            <div class="flex items-center">
                                <i class="fas fa-compress-arrows-alt mr-1.5"></i>
                                静压: ${staticPressure ? `${staticPressure} mmH₂O` : '-'}
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 连接器和轴承信息 -->
                <div class="grid grid-cols-2 gap-4 mb-4 bg-slate-50 dark:bg-slate-800/50 p-4 rounded-lg">
                    <div>
                        <h5 class="text-sm font-medium text-slate-500 dark:text-slate-400">接口类型</h5>
                        <p class="font-medium text-slate-800 dark:text-slate-200 flex items-center mt-1">
                            <i class="fas fa-plug mr-1.5 text-primary-500"></i> ${connector || '-'}
                        </p>
                    </div>
                    <div>
                        <h5 class="text-sm font-medium text-slate-500 dark:text-slate-400">轴承类型</h5>
                        <p class="font-medium text-slate-800 dark:text-slate-200 flex items-center mt-1">
                            <i class="fas fa-circle-notch mr-1.5 text-primary-500"></i> ${bearingType || '-'}
                        </p>
                    </div>
                </div>
                
                <!-- 特性标签 -->
                <div class="flex flex-wrap gap-2 mt-4">
                    ${rgbStatus !== '无' && rgbStatus !== '-' ? `
                    <span class="px-2.5 py-1 bg-purple-200 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 rounded-full text-xs font-semibold flex items-center border border-purple-300">
                        <i class="fas fa-lightbulb mr-1"></i> RGB灯效
                    </span>` : ''}
                    
                    ${pwmStatus === '支持' ? `
                    <span class="px-2.5 py-1 bg-blue-200 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded-full text-xs font-semibold flex items-center border border-blue-300">
                        <i class="fas fa-sliders-h mr-1"></i> PWM调速
                    </span>` : ''}
                    
                    ${warranty ? `
                    <span class="px-2.5 py-1 bg-green-200 dark:bg-green-900/30 text-green-800 dark:text-green-300 rounded-full text-xs font-semibold flex items-center border border-green-300">
                        <i class="fas fa-shield-alt mr-1"></i> ${warranty}年保修
                    </span>` : ''}
                    
                    ${parseFloat(noiseLevel) <= 20 ? `
                    <span class="px-2.5 py-1 bg-blue-200 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded-full text-xs font-semibold flex items-center border border-blue-300">
                        <i class="fas fa-volume-mute mr-1"></i> 静音设计
                    </span>` : ''}
                    
                    ${parseFloat(airflow) >= 70 ? `
                    <span class="px-2.5 py-1 bg-yellow-200 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300 rounded-full text-xs font-semibold flex items-center border border-yellow-300">
                        <i class="fas fa-bolt mr-1"></i> 高风量
                    </span>` : ''}
                    
                    ${color && color !== '-' ? `
                    <span class="px-2.5 py-1 bg-gray-200 dark:bg-gray-900/30 text-gray-800 dark:text-gray-300 rounded-full text-xs font-semibold flex items-center border border-gray-300">
                        <i class="fas fa-palette mr-1"></i> ${color}
                    </span>` : ''}
                </div>
            </div>
        </div>

        <!-- 详细规格表 -->
        <div class="mt-8">
            <h3 class="text-lg font-semibold text-slate-800 dark:text-slate-200 mb-3 pb-2 border-b border-slate-200 dark:border-slate-700 flex items-center">
                <i class="fas fa-clipboard-list mr-2 text-primary-500"></i> 详细规格
            </h3>
            
            <div class="grid grid-cols-2 md:grid-cols-3 gap-x-6 gap-y-4">
                <div>
                    <h5 class="text-sm font-medium text-slate-500 dark:text-slate-400">尺寸</h5>
                    <p class="font-medium text-slate-800 dark:text-slate-200 mt-1">${size ? `${size}mm` : '-'}</p>
                </div>
                
                <div>
                    <h5 class="text-sm font-medium text-slate-500 dark:text-slate-400">厚度</h5>
                    <p class="font-medium text-slate-800 dark:text-slate-200 mt-1">${thickness ? `${thickness}mm` : '-'}</p>
                </div>
                
                <div>
                    <h5 class="text-sm font-medium text-slate-500 dark:text-slate-400">转速范围</h5>
                    <p class="font-medium text-slate-800 dark:text-slate-200 mt-1">${rpm}</p>
                </div>
                
                <div>
                    <h5 class="text-sm font-medium text-slate-500 dark:text-slate-400">风量</h5>
                    <p class="font-medium text-slate-800 dark:text-slate-200 mt-1">${airflow ? `${airflow} CFM` : '-'}</p>
                </div>
                
                <div>
                    <h5 class="text-sm font-medium text-slate-500 dark:text-slate-400">噪音级别</h5>
                    <p class="font-medium text-slate-800 dark:text-slate-200 mt-1">${noiseLevel ? `${noiseLevel} dBA` : '-'}</p>
                </div>
                
                <div>
                    <h5 class="text-sm font-medium text-slate-500 dark:text-slate-400">静压</h5>
                    <p class="font-medium text-slate-800 dark:text-slate-200 mt-1">${staticPressure ? `${staticPressure} mmH₂O` : '-'}</p>
                </div>
                
                <div>
                    <h5 class="text-sm font-medium text-slate-500 dark:text-slate-400">接口类型</h5>
                    <p class="font-medium text-slate-800 dark:text-slate-200 mt-1">${connector || '-'}</p>
                </div>
                
                <div>
                    <h5 class="text-sm font-medium text-slate-500 dark:text-slate-400">轴承类型</h5>
                    <p class="font-medium text-slate-800 dark:text-slate-200 mt-1">${bearingType || '-'}</p>
                </div>
                
                <div>
                    <h5 class="text-sm font-medium text-slate-500 dark:text-slate-400">RGB灯效</h5>
                    <p class="font-medium text-slate-800 dark:text-slate-200 mt-1">${rgbStatus}</p>
                </div>
                
                <div>
                    <h5 class="text-sm font-medium text-slate-500 dark:text-slate-400">PWM支持</h5>
                    <p class="font-medium text-slate-800 dark:text-slate-200 mt-1">${pwmStatus}</p>
                </div>
                
                <div>
                    <h5 class="text-sm font-medium text-slate-500 dark:text-slate-400">颜色</h5>
                    <p class="font-medium text-slate-800 dark:text-slate-200 mt-1">${color || '-'}</p>
                </div>
                
                <div>
                    <h5 class="text-sm font-medium text-slate-500 dark:text-slate-400">保修期</h5>
                    <p class="font-medium text-slate-800 dark:text-slate-200 mt-1">${warranty ? `${warranty}年` : '-'}</p>
                </div>
                
                <div>
                    <h5 class="text-sm font-medium text-slate-500 dark:text-slate-400">价格</h5>
                    <p class="font-medium text-red-600 dark:text-red-400 mt-1">${price ? `¥${price}` : '-'}</p>
                </div>
            </div>
        </div>
        
        ${notes ? 
            `<div class="mt-6 bg-slate-50 dark:bg-slate-800/50 p-4 rounded-lg">
                <h3 class="text-lg font-semibold text-slate-800 dark:text-slate-200 mb-3 flex items-center">
                    <i class="fas fa-sticky-note mr-2 text-primary-500"></i> 备注
                </h3>
                <p class="text-slate-700 dark:text-slate-300">${notes}</p>
            </div>` : 
            ''
        }
    `;

    // 添加图片点击事件
    if (imageUrl) {
        const imgContainer = fanDetails.querySelector('.image-preview-container');
        const img = imgContainer.querySelector('img');
        const overlay = imgContainer.querySelector('.absolute');
        
        // 图片点击事件
        img.onclick = (event) => {
            event.stopPropagation();
            console.log('[DEBUG] Detail image clicked, opening fullscreen view');
            openImageFullscreen(imageUrl);
        };
        
        // 覆盖层点击事件
        if (overlay) {
            overlay.onclick = (event) => {
                event.stopPropagation();
                console.log('[DEBUG] Detail overlay clicked, opening fullscreen view');
                openImageFullscreen(imageUrl);
            };
        }
    }
}

// 获取品牌对应的CSS类
function getBrandClass(brand) {
    if (!brand) return '';
    
    const brandMap = {
        '猫头鹰': 'noctua-fan',
        'Arctic': 'arctic-fan',
        '海盗船': 'corsair-fan',
        '九州风神': 'deepcool-fan',
        '酷冷至尊': 'coolermaster-fan',
        '利民': 'thermalright-fan'
    };
    
    return brandMap[brand] || '';
}

// Update pagination controls
function updatePagination() {
    const totalPages = Math.ceil(totalRecords / 10);
    
    // Update text displays
    if (currentPageDisplay) currentPageDisplay.textContent = currentPage;
    if (totalPagesDisplay) totalPagesDisplay.textContent = totalPages;
    if (totalCount) totalCount.textContent = `共 ${totalRecords} 条记录`;
    
    // Enable/disable navigation buttons
    if (prevPageBtn) {
        prevPageBtn.disabled = currentPage === 1;
        prevPageBtn.classList.toggle('opacity-50', currentPage === 1);
    }
    
    if (nextPageBtn) {
        nextPageBtn.disabled = currentPage === totalPages || totalPages === 0;
        nextPageBtn.classList.toggle('opacity-50', currentPage === totalPages || totalPages === 0);
    }
    
    if (firstPageBtn) {
        firstPageBtn.disabled = currentPage === 1;
        firstPageBtn.classList.toggle('opacity-50', currentPage === 1);
    }
    
    if (lastPageBtn) {
        lastPageBtn.disabled = currentPage === totalPages || totalPages === 0;
        lastPageBtn.classList.toggle('opacity-50', currentPage === totalPages || totalPages === 0);
    }
    
    if (pageJumpInput) {
        pageJumpInput.max = totalPages;
    }
    
    // Generate page number buttons
    if (pageNumbers) {
        pageNumbers.innerHTML = '';
        
        // Determine the range of page numbers to display
        const maxPageButtons = 5;
        let startPage = Math.max(1, currentPage - Math.floor(maxPageButtons / 2));
        let endPage = startPage + maxPageButtons - 1;
        
        if (endPage > totalPages) {
            endPage = totalPages;
            startPage = Math.max(1, endPage - maxPageButtons + 1);
        }
        
        // Create page number buttons
        for (let i = startPage; i <= endPage; i++) {
            const pageButton = document.createElement('button');
            pageButton.type = 'button';
            pageButton.className = `px-3 py-1 border rounded-md text-sm ${
                i === currentPage
                    ? 'bg-yellow-600 text-white border-yellow-600'
                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
            }`;
            pageButton.textContent = i;
            pageButton.addEventListener('click', () => changePage(i));
            
            pageNumbers.appendChild(pageButton);
        }
    }
}

// Handle image upload
function handleImageUpload(event) {
    const file = event.target.files[0];
    if (!file) return;

    // Validate file size and type
    if (file.size > 5 * 1024 * 1024) { // 5MB
        alert('图片大小不能超过5MB');
        fanImageInput.value = '';
        return;
    }

    if (!['image/jpeg', 'image/png', 'image/gif'].includes(file.type)) {
        alert('只能上传JPG、PNG或GIF格式的图片');
        fanImageInput.value = '';
        return;
    }

    currentImageFile = file;
    
    // 显示转换提示
    const uploadHint = document.querySelector('.text-xs.text-slate-500.dark\\:text-slate-400');
    if (uploadHint && !uploadHint.querySelector('.upload-notification')) {
        const notification = document.createElement('div');
        notification.className = 'upload-notification mt-2 text-xs text-indigo-600 dark:text-indigo-400 flex items-center';
        notification.innerHTML = '<i class="fas fa-info-circle mr-1"></i>图片将自动转换为WebP格式以优化加载速度';
        uploadHint.appendChild(notification);
    }
    
    // Show preview
    const reader = new FileReader();
    reader.onload = function(e) {
        imagePreview.src = e.target.result;
        imagePreviewContainer.classList.remove('hidden');
    };
    reader.readAsDataURL(file);
}

// Remove image
function removeImage() {
    imagePreviewContainer.classList.add('hidden');
    fanImageInput.value = '';
    currentImageFile = null;
}

// Handle form submission
function handleFormSubmit(event) {
    event.preventDefault();

    isAdmin().then(isAdminUser => {
        if (!isAdminUser) {
            showMessage('权限不足，只有管理员可以添加或修改数据', 'error');
            return;
        }

        // Validate required fields
        if (!modelInput.value.trim() || !brandInput.value || !sizeInput.value) {
            alert('请填写必填字段');
            return;
        }

        // 处理转速字段的逻辑，如果最高和最低转速都有填写，组合成格式化的值
        if (speed_minInput.value && speed_maxInput.value) {
            rpmInput.value = `${speed_minInput.value}-${speed_maxInput.value}`;
        } else if (speed_maxInput.value) {
            rpmInput.value = speed_maxInput.value;
        } else if (speed_minInput.value) {
            rpmInput.value = speed_minInput.value;
        }

        // Create FormData object
        const formData = new FormData();
        formData.append('model', modelInput.value.trim());
        formData.append('brand', brandInput.value);
        formData.append('size', sizeInput.value);
        
        // Add optional fields - 使用与数据库字段名匹配的方式
        appendToFormData(formData, 'thickness', thicknessInput.value);
        appendToFormData(formData, 'speed_min', speed_minInput.value);
        appendToFormData(formData, 'speed_max', speed_maxInput.value);
        appendToFormData(formData, 'airflow', airflowInput.value);
        appendToFormData(formData, 'noiseLevel', noiseLevelInput.value);
        appendToFormData(formData, 'staticPressure', staticPressureInput.value);
        appendToFormData(formData, 'connector', connectorInput.value);
        appendToFormData(formData, 'pwm', pwmInput.value);
        appendToFormData(formData, 'bearing_type', bearing_typeInput.value);
        appendToFormData(formData, 'rgbLighting', rgbLightingInput.value);
        appendToFormData(formData, 'warranty', warrantyInput.value);
        appendToFormData(formData, 'color', colorInput.value);
        appendToFormData(formData, 'price', priceInput.value);
        appendToFormData(formData, 'notes', notesInput.value);
        
        // Add image file if selected
        if (currentImageFile) {
            formData.append('image', currentImageFile);
            console.log('[DEBUG] 正在上传图片:', currentImageFile.name, '类型:', currentImageFile.type, '大小:', (currentImageFile.size / 1024).toFixed(1) + 'KB', '(将自动转换为WebP格式以优化加载速度)');
        }

        // Determine if this is an add or update operation
        const url = selectedFanId ? `${API_UPDATE_FAN}${selectedFanId}` : API_ADD_FAN;
        const method = selectedFanId ? 'PUT' : 'POST';

        // 显示进度条并禁用提交按钮
        showUploadProgress();
        disableSubmitButton(true, selectedFanId !== null);

        // 使用XMLHttpRequest来支持上传进度
        uploadWithProgress(url, method, formData)
        .then(data => {
            showMessage(selectedFanId ? '风扇信息已更新' : '风扇信息已添加', 'success');
            resetForm();
            loadFans();
        })
        .catch(error => {
            console.error('Error:', error);
            showMessage('操作失败: ' + error.message, 'error');
        })
        .finally(() => {
            // 隐藏进度条并恢复提交按钮
            hideUploadProgress();
            disableSubmitButton(false, selectedFanId !== null);
        });
    });
}

// Helper for appending to FormData if value exists
function appendToFormData(formData, key, value) {
    if (value !== null && value !== undefined && value.trim() !== '') {
        formData.append(key, value.trim());
    }
}

// View fan details
function viewFan(id) {
    selectedFanId = id;
    
    fetchWithAuth(`${API_GET_FAN}${id}`)
        .then(response => response.json())
        .then(fan => {
            renderFanDetails(fan);
            isAdmin().then(isAdminUser => {
                const modalEditBtn = document.getElementById('editBtn');
                const modalDeleteBtn = document.getElementById('deleteBtn');

                if (modalEditBtn) {
                    modalEditBtn.style.display = isAdminUser ? 'inline-flex' : 'none';
                }
                if (modalDeleteBtn) {
                    modalDeleteBtn.style.display = isAdminUser ? 'inline-flex' : 'none';
                }
            });
            showModal();
        })
        .catch(error => {
            console.error('Error viewing fan:', error);
            showMessage('加载风扇详情失败', 'error');
        });
}

// Edit fan
function editFan(id) {
    isAdmin().then(isAdminUser => {
        if (!isAdminUser) {
            showMessage('权限不足，只有管理员可以编辑数据', 'error');
            return;
        }

        selectedFanId = id;
        
        fetchWithAuth(`${API_GET_FAN}${id}`)
            .then(response => response.json())
            .then(fan => {
                populateForm(fan);
                hideModal();
            })
            .catch(error => {
                console.error('Error editing fan:', error);
                showMessage('加载风扇信息失败', 'error');
            });
    });
}

// Populate form with fan data
function populateForm(fan) {
    console.log('[DEBUG] 填充表单数据:', fan);
    
    // 使用辅助函数获取各字段值
    modelInput.value = getFieldValue(fan, ['model'], '');
    brandInput.value = getFieldValue(fan, ['brand'], '');
    sizeInput.value = getFieldValue(fan, ['size'], '');
    thicknessInput.value = getFieldValue(fan, ['thickness'], '');
    
    // 处理转速字段
    const minSpeed = getFieldValue(fan, ['speed_min'], '');
    const maxSpeed = getFieldValue(fan, ['speed_max'], '');
    
    if (speed_minInput) speed_minInput.value = minSpeed;
    if (speed_maxInput) speed_maxInput.value = maxSpeed;
    
    // 如果有明确的rpm格式处理
    if (minSpeed && maxSpeed) {
        rpmInput.value = `${minSpeed}-${maxSpeed}`;
    } else if (maxSpeed) {
        rpmInput.value = maxSpeed;
    } else if (minSpeed) {
        rpmInput.value = minSpeed;
    } else {
        rpmInput.value = '';
    }
    
    airflowInput.value = getFieldValue(fan, ['airflow'], '');
    noiseLevelInput.value = getFieldValue(fan, ['noiseLevel'], '');
    staticPressureInput.value = getFieldValue(fan, ['staticPressure'], '');
    connectorInput.value = getFieldValue(fan, ['connector'], '');
    pwmInput.value = getFieldValue(fan, ['pwm'], '');
    bearing_typeInput.value = getFieldValue(fan, ['bearing_type'], '');
    rgbLightingInput.value = getFieldValue(fan, ['rgbLighting'], '');
    warrantyInput.value = getFieldValue(fan, ['warranty'], '');
    colorInput.value = getFieldValue(fan, ['color'], '');
    priceInput.value = getFieldValue(fan, ['price'], '');
    notesInput.value = getFieldValue(fan, ['notes'], '');
    
    console.log('[DEBUG] RGB灯效原始值:', rgbLightingInput.value, typeof rgbLightingInput.value);

    // 获取图片URL
    const imageUrl = getFieldValue(fan, ['image_url'], '');
    console.log('[DEBUG] 表单图片预览URL:', imageUrl);
    
    // 显示图片预览
    if (imageUrl) {
        imagePreview.src = imageUrl;
        imagePreviewContainer.classList.remove('hidden');
    } else {
        imagePreviewContainer.classList.add('hidden');
    }

    // 滚动到表单
    fanForm.scrollIntoView({ behavior: 'smooth' });
}

// Confirm delete fan
function confirmDeleteFan(id) {
    isAdmin().then(isAdminUser => {
        if (!isAdminUser) {
            showMessage('权限不足，只有管理员可以删除数据', 'error');
            return;
        }

        if (confirm('确定要删除这个风扇记录吗？此操作不可撤销。')) {
            deleteFan(id);
        }
    });
}

// Delete fan
function deleteFan(id) {
    fetchWithAuth(`${API_DELETE_FAN}${id}`, {
        method: 'DELETE'
    })
    .then(response => {
        if (response.status === 204) {
            // 204 No Content 响应不需要解析JSON
            return { success: true };
        }
        
        // 对于其他状态码，尝试解析JSON
        if (!response || !response.ok) {
            throw new Error(`服务器返回错误: ${response.status}`);
        }
        
        // 检查响应是否有内容
        const contentType = response.headers.get("content-type");
        if (contentType && contentType.indexOf("application/json") !== -1 && response.headers.get('content-length') !== '0') {
            return response.json();
        } else {
            return { success: true };
        }
    })
    .then(data => {
        showMessage('风扇信息已删除', 'success');
        loadFans();
        if (fanModal.classList.contains('block')) {
            hideModal();
        }
    })
    .catch(error => {
        console.error('Error deleting fan:', error);
        showMessage('删除风扇失败', 'error');
    });
}

// Reset the form
function resetForm() {
    fanForm.reset();
    selectedFanId = null;
    currentImageFile = null;
    imagePreviewContainer.classList.add('hidden');
    
    // 清空转速输入框
    if (speed_minInput) speed_minInput.value = '';
    if (speed_maxInput) speed_maxInput.value = '';
}

// Reset filters
function resetFilters() {
    fanSearch.value = '';
    brandFilter.value = 'all';
    sizeFilter.value = 'all';
    currentPage = 1;
    loadFans();
}

// Pagination control
function changePage(newPage) {
    const totalPages = Math.ceil(totalRecords / 10);
    
    if (newPage < 1 || newPage > totalPages) {
        return;
    }
    
    currentPage = newPage;
    loadFans();
}

// Modal controls
function showModal() {
    // 先重置滚动位置
    document.documentElement.style.overflow = 'hidden';
    document.body.style.overflow = 'hidden';
    
    // 显示模态框并添加动画效果
    fanModal.classList.remove('hidden');
    fanModal.classList.add('block');
    
    // 添加动画效果
    setTimeout(() => {
        const modalContent = fanModal.querySelector('.bg-white, .dark\\:bg-slate-800');
        if (modalContent) {
            modalContent.classList.add('scale-100', 'opacity-100');
            modalContent.classList.remove('scale-95', 'opacity-0');
        }
    }, 10);
    
    // 触发模态框显示事件
    const event = new CustomEvent('modalShown');
    document.dispatchEvent(event);
}

function hideModal() {
    // 恢复滚动
    document.documentElement.style.overflow = '';
    document.body.style.overflow = '';
    
    // 添加淡出动画
    const modalContent = fanModal.querySelector('.bg-white, .dark\\:bg-slate-800');
    if (modalContent) {
        modalContent.classList.add('scale-95', 'opacity-0');
        modalContent.classList.remove('scale-100', 'opacity-100');
    }
    
    // 等待动画完成后隐藏模态框
    setTimeout(() => {
        fanModal.classList.remove('block');
        fanModal.classList.add('hidden');
    }, 200);
    
    // 触发模态框隐藏事件
    const event = new CustomEvent('modalHidden');
    document.dispatchEvent(event);
}

// Edit button in modal
function handleEditClick() {
    editFan(selectedFanId);
}

// Delete button in modal
function handleDeleteClick() {
    confirmDeleteFan(selectedFanId);
}

// Show message
function showMessage(message, type = 'success') {
    // Create message element
    const messageElement = document.createElement('div');
    messageElement.className = `fixed top-4 right-4 px-4 py-2 rounded-md shadow-md text-white fade-in ${type === 'success' ? 'bg-green-500' : 'bg-red-500'}`;
    messageElement.textContent = message;
    
    // Add to body
    document.body.appendChild(messageElement);
    
    // Remove after timeout
    setTimeout(() => {
        messageElement.style.opacity = '0';
        messageElement.style.transform = 'translateY(-10px)';
        setTimeout(() => {
            document.body.removeChild(messageElement);
        }, 300);
    }, 3000);
}

// Debounce function to prevent excessive API calls
function debounce(func, wait) {
    let timeout;
    return function(...args) {
        const context = this;
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(context, args), wait);
    };
}

// Make viewFan, editFan, and confirmDeleteFan available globally
window.viewFan = viewFan;
window.editFan = editFan;
window.confirmDeleteFan = confirmDeleteFan;
window.openImageFullscreen = openImageFullscreen;

// Open fullscreen image
function openImageFullscreen(src) {
    // 创建一个临时的图片容器
    const container = document.createElement('div');
    container.className = 'viewer-container';
    container.style.display = 'none';
    document.body.appendChild(container);

    // 创建图片元素
    const img = document.createElement('img');
    img.src = src;
    container.appendChild(img);

    // 初始化 Viewer
    const viewer = new Viewer(img, {
        backdrop: true,          // 启用背景遮罩
        button: true,           // 显示关闭按钮
        navbar: false,          // 隐藏底部导航栏（只有一张图片时不需要）
        title: false,           // 不显示标题
        toolbar: {              // 自定义工具栏
            zoomIn: true,       // 放大按钮
            zoomOut: true,      // 缩小按钮
            oneToOne: true,     // 1:1 尺寸按钮
            reset: true,        // 重置按钮
            prev: false,        // 上一张（隐藏，因为只有一张图片）
            play: false,        // 播放按钮（隐藏）
            next: false,        // 下一张（隐藏）
            rotateLeft: true,   // 向左旋转
            rotateRight: true,  // 向右旋转
            flipHorizontal: true, // 水平翻转
            flipVertical: true,  // 垂直翻转
        },
        viewed() {
            // 图片加载完成后自动打开查看器
            if (isMobile) {
                viewer.zoomTo(0.8);  // 移动设备使用较小的初始缩放比例
            } else {
                viewer.zoomTo(1);    // 桌面设备使用正常的缩放比例
            }
        },
        hidden() {
            // 查看器关闭后移除临时元素
            viewer.destroy();
            document.body.removeChild(container);
        },
        maxZoomRatio: 5,        // 最大缩放比例
        minZoomRatio: 0.1,      // 最小缩放比例
        transition: true,       // 启用过渡效果
        keyboard: true,         // 启用键盘支持
    });

    // 显示查看器
    viewer.show();
}

// Parse fan information from text input
function parseFanInfo() {
    let input = smartInput.value.trim();
    if (!input) {
        showMessage('请输入风扇参数', 'error');
        return;
    }

    autoFillBtn.disabled = true;
    autoFillBtn.classList.add('processing');
    autoFillBtn.innerHTML = '<i class="fas fa-cog fa-spin mr-2"></i>解析中...';

    try {
        console.log(`[Parse Start] Initial Input: "${input}"`);
        
        // 初始化结果对象 - 使用与数据库字段匹配的名称
        const result = {
            model: '',
            brand: '',
            size: '',
            thickness: '',
            speed_min: '',
            speed_max: '',
            airflow: '',
            noiseLevel: '',
            staticPressure: '',
            connector: '',
            pwm: '',
            bearing_type: '',
            rgbLighting: '',
            color: '',
            warranty: '',
            price: '',
            notes: ''
        };

        // 保存原始输入用于备注
        const originalInput = input;
        
        // 预处理输入：替换特定替换字符
        input = input
            .replace(/：/g, ':') // 统一冒号
            .replace(/，/g, '、') // 统一分隔符
            .replace(/,/g, '、');
        
        // 品牌识别（允许品牌在任意位置）
        const brandList = ['猫头鹰', '九州风神', '酷冷至尊', '酷马', '海盗船', '利民', 'Arctic', 'Be Quiet', '爱国者', '安钛克', '快睿', '银欣', '恩杰'];
        const brandAlias = { 
            'Noctua': '猫头鹰', 
            'Deepcool': '九州风神', 
            'Cooler Master': '酷冷至尊',
            'Thermaltake': '酷马',
            'Corsair': '海盗船',
            'Thermalright': '利民',
            'Aigo': '爱国者',
            'Antec': '安钛克',
            'Scythe': '快睿',
            'SilverStone': '银欣',
            'NZXT': '恩杰'
        };
        
        const brandRegex = new RegExp(`(${[...Object.keys(brandAlias), ...brandList].join('|')})`, 'i');
        const brandMatch = input.match(brandRegex);
        if (brandMatch) {
            const matchedBrand = brandMatch[0];
            result.brand = brandAlias[matchedBrand] || brandList.find(b => b.toLowerCase() === matchedBrand.toLowerCase()) || matchedBrand;
            input = input.replace(brandRegex, '').trim();
        }

        // 型号识别（例如：NF-A12x25, P12）
        // 型号通常包含字母和数字的组合，可能有破折号
        const modelRegex = /([A-Za-z0-9]+-[A-Za-z0-9]+|[A-Za-z]+[0-9]+[a-zA-Z]*|[A-Za-z][0-9]+[A-Za-z0-9]*)/;
        const modelMatch = input.match(modelRegex);
        if (modelMatch) {
            result.model = modelMatch[0].trim();
            input = input.replace(modelMatch[0], '').trim();
        }

        // 尺寸识别（例如：120mm, 140mm）
        const sizeRegex = /(\d+)\s*(?:mm|毫米)(?!\s*x)/i;
        const sizeMatch = input.match(sizeRegex);
        if (sizeMatch) {
            result.size = sizeMatch[1];
            input = input.replace(sizeMatch[0], '').trim();
        }

        // 厚度识别（例如：25mm厚度）
        const thicknessRegex = /(?:厚度|厚)\s*(\d+)\s*(?:mm|毫米)/i;
        const thicknessMatch = input.match(thicknessRegex);
        if (thicknessMatch) {
            result.thickness = thicknessMatch[1];
            input = input.replace(thicknessMatch[0], '').trim();
        }

        // 转速识别（例如：2000RPM, 800-1500转）
        const rpmRegex = /((?:\d+(?:-\d+)?)\s*(?:RPM|转\/分|转速|转))/i;
        const rpmMatch = input.match(rpmRegex);
        if (rpmMatch) {
            // 提取数字部分
            const rpmNumbers = rpmMatch[0].match(/(\d+)(?:-(\d+))?/);
            if (rpmNumbers) {
                if (rpmNumbers[2]) {
                    // 如果是范围格式 (800-1500)
                    result.speed_min = rpmNumbers[1];
                    result.speed_max = rpmNumbers[2];
                } else {
                    // 只有一个数值，默认为最高转速
                    result.speed_max = rpmNumbers[1];
                }
            }
            input = input.replace(rpmMatch[0], '').trim();
        }

        // 风量识别（例如：60.1CFM）
        const airflowRegex = /(\d+(?:\.\d+)?)\s*(?:CFM|立方英尺\/分钟|风量)/i;
        const airflowMatch = input.match(airflowRegex);
        if (airflowMatch) {
            result.airflow = airflowMatch[1];
            input = input.replace(airflowMatch[0], '').trim();
        }

        // 噪音级别识别（例如：22.6dBA）
        const noiseLevelRegex = /(\d+(?:\.\d+)?)\s*(?:dBA|分贝|噪音|噪声)/i;
        const noiseLevelMatch = input.match(noiseLevelRegex);
        if (noiseLevelMatch) {
            result.noiseLevel = noiseLevelMatch[1];
            input = input.replace(noiseLevelMatch[0], '').trim();
        }

        // 静压识别（例如：1.45 mmH₂O）
        const staticPressureRegex = /(\d+(?:\.\d+)?)\s*(?:mmH₂O|mmH2O|毫米水柱|静压)/i;
        const staticPressureMatch = input.match(staticPressureRegex);
        if (staticPressureMatch) {
            result.staticPressure = staticPressureMatch[1];
            input = input.replace(staticPressureMatch[0], '').trim();
        }

        // 接口类型识别（例如：4-pin PWM, 3-pin）
        const connectorRegex = /(4-pin(?:\s*PWM)?|3-pin|PWM|ARGB|RGB|Molex|SATA)/i;
        const connectorMatch = input.match(connectorRegex);
        if (connectorMatch) {
            result.connector = connectorMatch[0];
            // 如果包含PWM关键字，自动设置PWM支持
            if (/PWM/i.test(connectorMatch[0])) {
                result.pwm = "1";
            }
            input = input.replace(connectorMatch[0], '').trim();
        }

        // 轴承类型识别
        const bearingTypeRegex = /(液压轴承|双滚珠轴承|磁悬浮轴承|FDB轴承|SSO轴承|流体动压轴承)/i;
        const bearingTypeMatch = input.match(bearingTypeRegex);
        if (bearingTypeMatch) {
            result.bearing_type = bearingTypeMatch[0];
            input = input.replace(bearingTypeMatch[0], '').trim();
        }

        // RGB灯效识别（例如：RGB, ARGB）
        const rgbRegex = /(RGB|ARGB|可编程RGB|灯效|RGB风扇)/i;
        const rgbMatch = input.match(rgbRegex);
        if (rgbMatch) {
            result.rgbLighting = "1";
            input = input.replace(rgbMatch[0], '').trim();
        }

        // 颜色识别
        const colorRegex = /(黑色|白色|灰色|红色|蓝色|绿色|银色|透明|RGB)/i;
        const colorMatch = input.match(colorRegex);
        if (colorMatch) {
            result.color = colorMatch[0];
            input = input.replace(colorMatch[0], '').trim();
        }

        // 保修期识别（例如：5年保修, 6年质保）
        const warrantyRegex = /(\d+)\s*(?:年|Years?)(?:\s*(?:保修|质保))?/i;
        const warrantyMatch = input.match(warrantyRegex);
        if (warrantyMatch) {
            result.warranty = warrantyMatch[1];
            input = input.replace(warrantyMatch[0], '').trim();
        }

        // 价格识别（支持¥、￥等符号）
        const priceMatch = input.match(/[￥¥$]\s*(\d+(?:\.\d{1,2})?)/);
        if (priceMatch) {
            result.price = priceMatch[1];
            input = input.replace(priceMatch[0], '').trim();
        }

        // 备注：保留所有未能识别的内容
        result.notes = input.replace(/\s+/g, ' ').trim();

        // 填充表单
        if (modelInput && result.model) modelInput.value = result.model;
        
        // 对于下拉菜单，需要特殊处理
        if (brandInput && result.brand) {
            const options = Array.from(brandInput.options);
            const option = options.find(opt => opt.text.includes(result.brand) || opt.value === result.brand);
            if (option) {
                brandInput.value = option.value;
            }
        }
        
        if (sizeInput && result.size) {
            const options = Array.from(sizeInput.options);
            const option = options.find(opt => opt.text.includes(result.size));
            if (option) {
                sizeInput.value = option.value;
            } else {
                // 如果没有匹配的选项，选择"其他尺寸"
                sizeInput.value = 'other';
            }
        }
        
        if (thicknessInput && result.thickness) thicknessInput.value = result.thickness;
        if (speed_minInput && result.speed_min) speed_minInput.value = result.speed_min;
        if (speed_maxInput && result.speed_max) speed_maxInput.value = result.speed_max;
        if (airflowInput && result.airflow) airflowInput.value = result.airflow;
        if (noiseLevelInput && result.noiseLevel) noiseLevelInput.value = result.noiseLevel;
        if (staticPressureInput && result.staticPressure) staticPressureInput.value = result.staticPressure;
        
        if (connectorInput && result.connector) {
            const options = Array.from(connectorInput.options);
            const option = options.find(opt => opt.text.includes(result.connector) || opt.value.includes(result.connector));
            if (option) {
                connectorInput.value = option.value;
            }
        }
        
        if (pwmInput && result.pwm) pwmInput.value = result.pwm;
        
        if (bearing_typeInput && result.bearing_type) {
            const options = Array.from(bearing_typeInput.options);
            const option = options.find(opt => opt.text.includes(result.bearing_type) || opt.value === result.bearing_type);
            if (option) {
                bearing_typeInput.value = option.value;
            }
        }
        
        if (rgbLightingInput && result.rgbLighting) rgbLightingInput.value = result.rgbLighting;
        if (colorInput && result.color) colorInput.value = result.color;
        if (warrantyInput && result.warranty) warrantyInput.value = result.warranty;
        if (priceInput && result.price) priceInput.value = result.price;
        if (notesInput && result.notes) notesInput.value = result.notes;

        // 计算成功填充的字段数量
        const filledFields = Object.values(result).filter(value => value !== '').length;
        
        showMessage(`成功识别 ${filledFields} 项参数！`, 'success');
        
    } catch (error) {
        console.error('解析风扇参数时发生错误:', error);
        showMessage('解析风扇参数时发生错误', 'error');
    } finally {
        autoFillBtn.disabled = false;
        autoFillBtn.classList.remove('processing');
        autoFillBtn.innerHTML = '<i class="fas fa-robot mr-2"></i>智能解析';
    }
}

// 渲染移动端卡片布局
function renderMobileCards(data, isAdminUser) {
    fanTableBody.innerHTML = '';
    
    // 确保表格容器样式正确
    const tableElement = fanTableBody.closest('table');
    if (tableElement) {
        tableElement.style.display = 'block';
        tableElement.style.width = '100%';
        tableElement.style.maxWidth = '100%';
        tableElement.style.overflow = 'hidden';
        tableElement.style.borderCollapse = 'separate';
        tableElement.style.borderSpacing = '0 12px';
        const theadElement = tableElement.querySelector('thead');
        if (theadElement) {
            theadElement.style.display = 'none';
        }
    }

    // 为表格体设置适合的样式
    fanTableBody.style.display = 'block';
    fanTableBody.style.width = '100%';
    fanTableBody.style.maxWidth = '100%';
    fanTableBody.style.paddingLeft = '4px';
    fanTableBody.style.paddingRight = '4px';
    fanTableBody.style.boxSizing = 'border-box';

    data.forEach((fan, index) => {
        // 创建卡片外部容器
        const cardOuterContainer = document.createElement('div');
        cardOuterContainer.className = 'fan-card-outer-container';
        cardOuterContainer.style.width = '100%';
        cardOuterContainer.style.maxWidth = '100%';
        cardOuterContainer.style.boxSizing = 'border-box';
        cardOuterContainer.style.marginBottom = '16px'; // 增加卡片间距
        cardOuterContainer.style.position = 'relative';
        cardOuterContainer.style.animation = `fadeIn 0.3s ease-in-out ${index * 0.05}s both`;

        // 创建主卡片容器
        const card = document.createElement('div');
        card.className = 'fan-card-new';
        card.style.width = '100%';
        card.style.borderRadius = '12px';
        card.style.overflow = 'hidden';
        
        // 根据当前主题设置背景色
        const isDarkMode = document.documentElement.classList.contains('dark');
        card.style.backgroundColor = isDarkMode ? '#1e293b' : 'white'; // 深色模式使用 slate-800 颜色
        card.style.boxShadow = isDarkMode ? 
            '0 4px 12px rgba(0,0,0,0.25)' : 
            '0 4px 12px rgba(0,0,0,0.08)';
        
        card.style.display = 'flex';
        card.style.flexDirection = 'column';
        card.style.minWidth = '0';
        card.style.minHeight = '180px'; // 设置最小高度
        card.style.transition = 'transform 0.2s ease, box-shadow 0.2s ease';

        // 添加触摸反馈效果
        card.addEventListener('touchstart', function() {
            this.style.transform = 'scale(0.98)';
            this.style.boxShadow = '0 2px 5px rgba(0,0,0,0.03)';
        });
        card.addEventListener('touchend', function() {
            this.style.transform = 'scale(1)';
            this.style.boxShadow = '0 4px 12px rgba(0,0,0,0.08)';
        });

        // 准备品牌颜色主题
        let brandColor = '#4A5568'; // 默认颜色
        let lightBrandColor = 'rgba(226, 232, 240, 0.5)';
        let borderBrandColor = 'rgba(203, 213, 225, 0.5)';
        let headerBgColor = '#f8f9fa'; // 默认头部颜色

        // 根据品牌设置不同颜色主题
        switch(fan.brand) {
            case 'Noctua':
                brandColor = '#A52A2A';
                lightBrandColor = 'rgba(165, 42, 42, 0.08)';
                borderBrandColor = 'rgba(165, 42, 42, 0.15)';
                headerBgColor = 'rgba(165, 42, 42, 0.05)';
                break;
            case 'Arctic':
                brandColor = '#0078d7';
                lightBrandColor = 'rgba(0, 120, 215, 0.08)';
                borderBrandColor = 'rgba(0, 120, 215, 0.15)';
                headerBgColor = 'rgba(0, 120, 215, 0.05)';
                break;
            case 'Corsair':
                brandColor = '#FF9900';
                lightBrandColor = 'rgba(255, 153, 0, 0.08)';
                borderBrandColor = 'rgba(255, 153, 0, 0.15)';
                headerBgColor = 'rgba(255, 153, 0, 0.05)';
                break;
            // 可以添加更多品牌的主题颜色
        }

        // 卡片头部
        const cardHeader = document.createElement('div');
        cardHeader.style.padding = '8px 12px'; // 减小内边距
        // 深色模式下调整头部背景色以增加对比度
        const darkHeaderBgColor = isDarkMode ? 'rgba(15, 23, 42, 0.9)' : headerBgColor;
        cardHeader.style.backgroundColor = isDarkMode ? darkHeaderBgColor : headerBgColor;
        cardHeader.style.borderBottom = `1px solid ${isDarkMode ? 'rgba(71, 85, 105, 0.7)' : borderBrandColor}`;
        cardHeader.style.display = 'flex';
        cardHeader.style.justifyContent = 'space-between';
        cardHeader.style.alignItems = 'center';

        // 风扇型号
        const modelName = document.createElement('div');
        modelName.textContent = fan.model || '未知型号';
        modelName.style.fontSize = '0.95rem'; // 略微减小字体
        modelName.style.fontWeight = 'bold';
        modelName.style.color = isDarkMode ? '#f1f5f9' : '#1a202c'; // 深色模式下使用亮色文本
        modelName.style.wordBreak = 'break-word';
        modelName.style.flexGrow = '1';
        modelName.style.marginRight = '8px';
        modelName.style.overflow = 'hidden';
        modelName.style.textOverflow = 'ellipsis';
        modelName.style.whiteSpace = 'nowrap';

        // 品牌标签
        const brandBadge = document.createElement('span');
        brandBadge.textContent = fan.brand || '未知';
        brandBadge.style.padding = '2px 8px'; // 减小内边距
        brandBadge.style.fontSize = '0.7rem';
        brandBadge.style.fontWeight = 'bold';
        brandBadge.style.borderRadius = '4px';
        brandBadge.style.color = 'white';
        brandBadge.style.backgroundColor = brandColor;
        brandBadge.style.border = `1px solid ${brandColor}`;
        brandBadge.style.flexShrink = '0';
        brandBadge.style.lineHeight = '1.2';

        cardHeader.appendChild(modelName);
        cardHeader.appendChild(brandBadge);

        // 卡片内容区
        const cardBody = document.createElement('div');
        cardBody.style.padding = '12px'; // 增加内边距
        cardBody.style.backgroundColor = isDarkMode ? '#1e293b' : 'white'; // 与卡片主体保持一致
        cardBody.style.display = 'flex';
        cardBody.style.gap = '12px'; // 增加图片和信息的间距
        cardBody.style.flexGrow = '1'; // 让内容区域自动填充空间

        // 图片容器
        const imgContainer = document.createElement('div');
        imgContainer.className = 'flex-shrink-0';
        imgContainer.style.width = '60px'; // 稍微增大图片尺寸
        imgContainer.style.height = '60px';
        imgContainer.style.borderRadius = '6px';
        // 深色模式下调整边框颜色和背景色
        imgContainer.style.border = isDarkMode ? 
            '1px solid rgba(148, 163, 184, 0.3)' : 
            `1px solid ${borderBrandColor}`;
        imgContainer.style.display = 'flex';
        imgContainer.style.alignItems = 'center';
        imgContainer.style.justifyContent = 'center';
        imgContainer.style.overflow = 'hidden';
        imgContainer.style.backgroundColor = isDarkMode ? '#1e293b' : '#f9f9f9';
        imgContainer.style.boxShadow = isDarkMode ? '0 2px 4px rgba(0,0,0,0.3)' : 'none';
        imgContainer.style.flexShrink = '0'; // 防止图片被挤压
        
        const imgElement = document.createElement('img');
        imgElement.alt = getFieldValue(fan, ['model', 'name'], '风扇图片');
        imgElement.style.width = '100%';
        imgElement.style.height = '100%';
        imgElement.style.objectFit = 'contain';
        imgElement.style.cursor = 'pointer';

        // 获取图片URL (尝试多种可能的字段名)
        const imgUrl = getFieldValue(fan, ['image_url', 'imageUrl', 'image', 'img_url', 'img'], '');
        console.log(`[DEBUG] Mobile Card: Fan ${fan.id || 'unknown'} image URL: ${imgUrl}`);

        if (imgUrl) {
            imgElement.src = imgUrl;
            imgElement.onclick = () => openImageFullscreen(imgUrl);
            imgContainer.appendChild(imgElement);
        } else {
            // 无图片时显示占位文字
            imgContainer.textContent = '风扇';
            imgContainer.style.fontSize = '0.75rem';
            imgContainer.style.color = '#6b7280';
            imgContainer.style.textAlign = 'center';
        }
        
        imgElement.onerror = function() {
            this.onerror = null;
            imgContainer.innerHTML = ''; // 清除之前的内容
            const placeholderText = document.createElement('span');
            placeholderText.textContent = '风扇';
            placeholderText.style.fontSize = '0.75rem';
            placeholderText.style.color = '#6b7280';
            placeholderText.style.textAlign = 'center';
            imgContainer.appendChild(placeholderText);
            this.style.display = 'none'; // 隐藏损坏的图片标签
        };
        
        imgContainer.style.overflow = 'hidden';

        // 信息容器
        const infoContainer = document.createElement('div');
        infoContainer.style.flexGrow = '1';
        infoContainer.style.display = 'flex';
        infoContainer.style.flexDirection = 'column';
        infoContainer.style.justifyContent = 'center'; // 垂直居中
        infoContainer.style.gap = '4px'; // 增加间距

        // 风扇尺寸信息
        const fanSize = getFieldValue(fan, ['size', 'fan_size'], '');
        if (fanSize) {
            const sizeText = document.createElement('div');
            sizeText.style.fontSize = '0.9rem';
            sizeText.style.fontWeight = '500';
            sizeText.style.color = isDarkMode ? '#e2e8f0' : '#374151'; // 深色模式使用更亮的文字颜色
            sizeText.style.overflow = 'hidden';
            sizeText.style.textOverflow = 'ellipsis';
            sizeText.style.whiteSpace = 'nowrap';
            sizeText.textContent = `${fanSize} 毫米`;
            infoContainer.appendChild(sizeText);
        }

        // 规格信息行
        const specContainer = document.createElement('div');
        specContainer.style.display = 'flex';
        specContainer.style.alignItems = 'center';
        specContainer.style.flexWrap = 'wrap';
        specContainer.style.gap = '4px';
        specContainer.style.marginTop = '2px';
        specContainer.style.fontSize = '0.75rem';
        specContainer.style.color = isDarkMode ? '#cbd5e1' : '#4B5563'; // 使用更亮的文字颜色提高对比度

        // 最大转速
        const maxSpeed = getFieldValue(fan, ['max_speed', 'rpm', 'speed'], '');
        if (maxSpeed) {
            const speedText = document.createElement('div');
            speedText.textContent = `最大: ${maxSpeed} RPM`;
            specContainer.appendChild(speedText);
        }

        // 噪音水平
        const noiseLevel = getFieldValue(fan, ['noise_level', 'noiseLevel', 'noise'], '');
        if (noiseLevel) {
            const noiseText = document.createElement('div');
            noiseText.textContent = noiseLevel.includes('dB') ? `噪音: ${noiseLevel}` : `噪音: ${noiseLevel} dB`;
            if (specContainer.children.length > 0) {
                noiseText.style.marginLeft = '8px';
                noiseText.style.paddingLeft = '8px';
                noiseText.style.borderLeft = '1px solid #D1D5DB';
            }
            specContainer.appendChild(noiseText);
        }

        if (specContainer.children.length > 0) {
            infoContainer.appendChild(specContainer);
        }

        // 连接器和轴承信息行
        const detailsContainer = document.createElement('div');
        detailsContainer.style.display = 'flex';
        detailsContainer.style.alignItems = 'center';
        detailsContainer.style.flexWrap = 'wrap';
        detailsContainer.style.gap = '4px';
        detailsContainer.style.marginTop = '2px';
        detailsContainer.style.fontSize = '0.75rem';
        detailsContainer.style.color = isDarkMode ? '#cbd5e1' : '#4B5563'; // 使用更亮的颜色

        // 连接器
        if (fan.connector) {
            const connectorText = document.createElement('div');
            connectorText.style.marginRight = '8px';
            connectorText.style.color = isDarkMode ? '#e2e8f0' : 'inherit'; // 确保文字在深色模式下足够亮
            connectorText.innerHTML = `<i class="fas fa-plug mr-1 ${isDarkMode ? 'text-yellow-400' : 'text-yellow-500'}"></i>${fan.connector}`;
            detailsContainer.appendChild(connectorText);
        }

        // 轴承类型
        if (fan.bearing_type) {
            const bearingText = document.createElement('div');
            bearingText.style.marginRight = '8px';
            bearingText.style.color = isDarkMode ? '#e2e8f0' : 'inherit'; // 确保文字在深色模式下足够亮
            bearingText.innerHTML = `<i class="fas fa-circle-notch mr-1 ${isDarkMode ? 'text-red-400' : 'text-red-500'}"></i>${fan.bearing_type}`;
            detailsContainer.appendChild(bearingText);
        }

        // 价格
        if (fan.price) {
            const priceText = document.createElement('div');
            priceText.style.fontWeight = '500';
            priceText.style.color = isDarkMode ? '#f87171' : '#EF4444'; // 深色模式下使用更亮的红色
            priceText.innerHTML = `<i class="fas fa-tag mr-1"></i>¥${fan.price}`;
            detailsContainer.appendChild(priceText);
        }

        // 添加详细信息行
        if (detailsContainer.children.length > 0) {
            infoContainer.appendChild(detailsContainer);
        }

        // 特性标签组
        const tagGroup = document.createElement('div');
        tagGroup.style.display = 'flex';
        tagGroup.style.flexWrap = 'wrap';
        tagGroup.style.gap = '4px';
        tagGroup.style.marginTop = '6px';
        tagGroup.style.maxWidth = '100%';
        tagGroup.style.overflow = 'hidden';

        // 创建标签的辅助函数
        function createTag(text, bgColor, textColor) {
            const tag = document.createElement('span');
            tag.style.fontSize = '0.65rem';
            tag.style.padding = '2px 5px';
            
            // 亮色主题下使背景更深、对比度更高
            if (isDarkMode) {
                // 深色模式 - 增加背景不透明度
                tag.style.backgroundColor = bgColor.replace(/0\.\d+\)$/, '0.4)');
                // 深色模式下文字颜色更亮
                tag.style.color = textColor.replace(/#[0-9a-f]{6}/, c => lightenColor(c, 20));
                tag.style.border = '1px solid rgba(255,255,255,0.1)';
            } else {
                // 亮色模式 - 使用更鲜明的颜色
                // 从rgba(x,y,z,0.1)改为rgba(x,y,z,0.25)增加背景不透明度
                tag.style.backgroundColor = bgColor.replace(/0\.\d+\)$/, '0.25)');
                // 文字颜色加深，增加对比度
                tag.style.color = textColor;
                tag.style.border = `1px solid ${bgColor.replace(/0\.\d+\)$/, '0.4)')}`;
            }
            
            tag.style.borderRadius = '3px';
            tag.style.whiteSpace = 'nowrap';
            tag.style.fontWeight = '600'; // 改为semibold增强可读性
            tag.textContent = text;
            return tag;
        }
        
        // 辅助函数：让颜色更亮
        function lightenColor(color, percent) {
            // 去掉可能存在的 #
            const hex = color.replace('#', '');
            const r = parseInt(hex.substr(0, 2), 16);
            const g = parseInt(hex.substr(2, 2), 16);
            const b = parseInt(hex.substr(4, 2), 16);
            
            // 增加亮度
            const lighter = (value) => {
                const val = Math.min(255, value + (255 - value) * (percent / 100));
                return Math.round(val).toString(16).padStart(2, '0');
            };
            
            return `#${lighter(r)}${lighter(g)}${lighter(b)}`;
        }

        // 静压
        if (fan.static_pressure) {
            tagGroup.appendChild(createTag(`静压${fan.static_pressure}`, 'rgba(79, 70, 229, 0.1)', '#4F46E5'));
        }

        // RGB灯效
        if (fan.rgb_lighting && fan.rgb_lighting !== "无" && fan.rgb_lighting !== "false") {
            tagGroup.appendChild(createTag('RGB灯效', 'rgba(236, 72, 153, 0.1)', '#EC4899'));
        }

        // 质保
        if (fan.warranty) {
            tagGroup.appendChild(createTag(`${fan.warranty}年质保`, isDarkMode ? 'rgba(217, 119, 6, 0.25)' : 'rgba(217, 119, 6, 0.1)', '#F59E0B'));
        }

        // 静音特性 (如果噪音低于20dBA)
        if (fan.noise_level && parseFloat(fan.noise_level) <= 20) {
            tagGroup.appendChild(createTag('静音设计', 'rgba(16, 185, 129, 0.1)', '#10B981'));
        }

        // 高性能 (如果风量特别高)
        if (fan.air_flow && parseFloat(fan.air_flow) > 70) {
            tagGroup.appendChild(createTag('高性能', 'rgba(245, 158, 11, 0.1)', '#F59E0B'));
        }

        // PWM控制
        if (fan.model && fan.model.toLowerCase().includes('pwm')) {
            tagGroup.appendChild(createTag('PWM控制', 'rgba(6, 182, 212, 0.1)', '#06B6D4'));
        }

        // 如果标签组不为空，添加到信息容器
        if (tagGroup.children.length > 0) {
            infoContainer.appendChild(tagGroup);
        }

        cardBody.appendChild(imgContainer);
        cardBody.appendChild(infoContainer);

        // 卡片底部按钮区
        const cardFooter = document.createElement('div');
        cardFooter.style.padding = '10px 12px'; // 增加内边距
        // 深色模式下调整底部背景色
        cardFooter.style.backgroundColor = isDarkMode ? '#0f172a' : '#f9f9fb'; 
        cardFooter.style.borderTop = isDarkMode ? '1px solid rgba(51, 65, 85, 0.7)' : '1px solid #e5e7eb';
        cardFooter.style.display = 'flex';
        cardFooter.style.justifyContent = 'space-evenly'; // 平均分布按钮
        cardFooter.style.alignItems = 'center';
        cardFooter.style.gap = '8px'; // 增加按钮间距

        // 创建操作按钮
        const viewButton = createActionButton('<i class="fas fa-eye"></i><span class="ml-1">查看</span>', '查看', '#3B82F6', () => viewFan(fan.id));
        const editButton = createActionButton('<i class="fas fa-edit"></i><span class="ml-1">编辑</span>', '编辑', '#10B981', () => editFan(fan.id));
        const deleteButton = createActionButton('<i class="fas fa-trash"></i><span class="ml-1">删除</span>', '删除', '#EF4444', () => confirmDeleteFan(fan.id));
        
        cardFooter.appendChild(viewButton);
        if (isAdminUser) {
            cardFooter.appendChild(editButton);
            cardFooter.appendChild(deleteButton);
        }

        // 组装卡片
        card.appendChild(cardHeader);
        card.appendChild(cardBody);
        card.appendChild(cardFooter);
        cardOuterContainer.appendChild(card);
        fanTableBody.appendChild(cardOuterContainer);
    });
}

// 创建操作按钮辅助函数
function createActionButton(innerHTML, title, textColor, onClick) {
    const button = document.createElement('button');
    const isDarkMode = document.documentElement.classList.contains('dark');
    button.style.padding = '8px 12px'; // 增加按钮内边距
    button.style.flex = '1'; // 按钮平均分配空间
    button.style.borderRadius = '6px';
    button.style.display = 'inline-flex';
    button.style.alignItems = 'center';
    button.style.justifyContent = 'center';
    button.style.backgroundColor = isDarkMode ? '#1e293b' : 'white'; // 深色模式使用深色背景
    button.style.border = isDarkMode ? '1px solid #334155' : '1px solid #e5e7eb'; // 深色模式边框调整
    button.style.color = textColor;
    button.style.fontSize = '0.85rem';
    button.style.fontWeight = '500';
    button.style.boxShadow = isDarkMode ? '0 1px 2px rgba(0,0,0,0.2)' : '0 1px 2px rgba(0,0,0,0.05)'; // 调整阴影
    button.style.transition = 'all 0.15s ease-out';
    button.innerHTML = innerHTML;
    button.title = title;
    button.addEventListener('click', onClick);
    
    // 添加轻微的悬停效果
    button.addEventListener('mouseenter', function() {
        this.style.backgroundColor = `${textColor}10`; // 更淡的背景色
        this.style.borderColor = textColor;
        this.style.transform = 'translateY(-1px)';
        this.style.boxShadow = '0 2px 5px rgba(0,0,0,0.1)';
    });
    button.addEventListener('mouseleave', function() {
        const isDark = document.documentElement.classList.contains('dark');
        this.style.backgroundColor = isDark ? '#1e293b' : 'white';
        this.style.borderColor = isDark ? '#334155' : '#e5e7eb';
        this.style.transform = 'translateY(0)';
        this.style.boxShadow = isDark ? '0 1px 2px rgba(0,0,0,0.2)' : '0 1px 2px rgba(0,0,0,0.05)';
    });
    
    // 添加触摸效果
    button.addEventListener('touchstart', function() {
        this.style.backgroundColor = `${textColor}20`;
        this.style.borderColor = textColor;
        this.style.transform = 'scale(0.98)';
    });
    button.addEventListener('touchend', function() {
        const isDark = document.documentElement.classList.contains('dark');
        this.style.backgroundColor = isDark ? '#1e293b' : 'white';
        this.style.borderColor = isDark ? '#334155' : '#e5e7eb';
        this.style.transform = 'scale(1)';
    });
    
    return button;
}

// 处理字段名不一致问题的辅助函数
function getFieldValue(obj, fields, defaultValue = '-') {
    for (const field of fields) {
        if (obj[field] !== undefined && obj[field] !== null && obj[field] !== '') {
            return obj[field];
        }
    }
    return defaultValue;
}

// 获取品牌对应的样式类
function getBrandStyleClass(brand) {
    if (!brand) return 'bg-slate-100 text-slate-600 dark:bg-slate-700/50 dark:text-slate-300';
    
    const brandMap = {
        '猫头鹰': 'bg-amber-100 text-amber-700 dark:bg-amber-900/30 dark:text-amber-400 border border-amber-200 dark:border-amber-800/30',
        'Arctic': 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400 border border-blue-200 dark:border-blue-800/30',
        '海盗船': 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400 border border-yellow-200 dark:border-yellow-800/30',
        '九州风神': 'bg-teal-100 text-teal-700 dark:bg-teal-900/30 dark:text-teal-400 border border-teal-200 dark:border-teal-800/30',
        '酷冷至尊': 'bg-violet-100 text-violet-700 dark:bg-violet-900/30 dark:text-violet-400 border border-violet-200 dark:border-violet-800/30',
        '利民': 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400 border border-red-200 dark:border-red-800/30'
    };
    
    return brandMap[brand] || 'bg-slate-100 text-slate-600 dark:bg-slate-700/50 dark:text-slate-300 border border-slate-200 dark:border-slate-700/30';
}

// 获取品牌对应的图标
function getBrandIcon(brand) {
    if (!brand) return '';
    
    const brandIcons = {
        '猫头鹰': '<i class="fas fa-feather-alt mr-1.5 opacity-70"></i>',
        'Arctic': '<i class="fas fa-snowflake mr-1.5 opacity-70"></i>',
        '海盗船': '<i class="fas fa-ship mr-1.5 opacity-70"></i>',
        '九州风神': '<i class="fas fa-wind mr-1.5 opacity-70"></i>',
        '酷冷至尊': '<i class="fas fa-icicles mr-1.5 opacity-70"></i>',
        '利民': '<i class="fas fa-temperature-low mr-1.5 opacity-70"></i>'
    };
    
    return brandIcons[brand] || '<i class="fas fa-fan mr-1.5 opacity-70"></i>';
}










// 初始化权限控制
function initPermissions() {
    console.log('初始化权限控制...');
    
    // 如果没有权限控制模块，发出警告
    if (typeof setupPermissionBasedUI !== 'function') {
        console.warn('警告：权限控制模块未加载！请确保已引入permission-helper.js');
        return;
    }
    
    // 执行权限控制初始化
    setupPermissionBasedUI().then(() => {
        // 根据用户角色处理按钮
        isAdmin().then(isAdminUser => {
            if (!isAdminUser) {
                // 禁用表单
                const form = document.querySelector('form');
                if (form) {
                    const inputs = form.querySelectorAll('input, select, textarea');
                    inputs.forEach(input => {
                        input.setAttribute('disabled', 'disabled');
                    });
                    
                    const submitBtn = form.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        submitBtn.setAttribute('disabled', 'disabled');
                        submitBtn.title = '只有管理员可以添加或修改数据';
                    }

                    const autoFillBtn = document.getElementById('autoFillBtn');
                    if(autoFillBtn) {
                        autoFillBtn.disabled = true;
                        autoFillBtn.title = '只有管理员可以添加或修改数据';
                    }
                }
            } else {
                // 添加管理员标识
                const header = document.querySelector('h1.font-bold');
                if (header && !header.querySelector('.admin-badge')) {
                    const adminBadge = document.createElement('span');
                    adminBadge.className = 'admin-badge ml-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full';
                    adminBadge.innerText = '管理员';
                    header.appendChild(adminBadge);
                }
            }
        });
    });
}

// 拦截所有删除操作的请求
function interceptDeleteOperations() {
    // 保存原始的fetch函数
    const originalFetch = window.fetch;
    
    // 重写fetch函数以拦截删除请求
    window.fetch = async function(url, options) {
        // 检查是否为删除操作
        if (options && options.method === 'DELETE') {
            console.log('拦截到删除操作请求:', url);
            
            // 检查用户权限
            const isAdminUser = await isAdmin();
            if (!isAdminUser) {
                console.warn('权限不足，普通用户无法执行删除操作');
                // 返回权限错误响应
                return Promise.resolve(new Response(JSON.stringify({
                    error: '权限不足',
                    message: '只有管理员可以删除数据'
                }), {
                    status: 403,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }));
            }
        }
        
        // 对于非删除请求或管理员的删除请求，正常执行
        return originalFetch.apply(this, arguments);
    };
    
    // 添加全局点击事件拦截
    document.addEventListener('click', async function(event) {
        // 查找可能触发删除操作的元素
        const deleteButton = event.target.closest('.btn-delete, [data-action="delete"]');
        if (deleteButton) {
            const isAdminUser = await isAdmin();
            if (!isAdminUser) {
                // 阻止事件传播
                event.preventDefault();
                event.stopPropagation();
                console.warn('权限不足，普通用户无法执行删除操作');
                
                // 可选：显示提示消息（使用toast或alert）
                if (typeof showToast === 'function') {
                    showToast('只有管理员可以删除数据', 'error');
                } else {
                    console.error('权限不足，只有管理员可以删除数据');
                }
                
                return false;
            }
        }
    }, true);
}

<!-- 确保图片点击预览功能正常工作 -->
// 在文档加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 创建一个新的样式表
    const style = document.createElement('style');
    style.textContent = `
                /* 确保图片和覆盖层可点击 */
                .image-preview-container img,
                .image-preview-container .absolute {
                    pointer-events: auto !important;
                    cursor: zoom-in !important;
                    z-index: 1;
                }
            `;
    document.head.appendChild(style);

    // 确保所有表格图片都有点击事件
    setTimeout(() => {
        const tableImages = document.querySelectorAll('.image-preview-container img, .fan-card img');
        tableImages.forEach(img => {
            if (img.src && !img.onclick) {
                img.style.cursor = 'zoom-in';
                img.onclick = () => openImageFullscreen(img.src);
            }
        });

        // 确保所有覆盖层也有点击事件
        const overlays = document.querySelectorAll('.image-preview-container .absolute, .img-overlay');
        overlays.forEach(overlay => {
            if (!overlay.onclick && overlay.parentNode) {
                const img = overlay.parentNode.querySelector('img');
                if (img && img.src) {
                    overlay.style.cursor = 'zoom-in';
                    overlay.onclick = () => openImageFullscreen(img.src);
                }
            }
        });
    }, 1000); // 等待表格完全渲染
});

// 页面加载完成后执行权限初始化
document.addEventListener('DOMContentLoaded', initPermissions);
