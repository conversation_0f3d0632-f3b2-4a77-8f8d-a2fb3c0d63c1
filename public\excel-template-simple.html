<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel模板编辑器（简化版） - 电脑配件管理系统</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="/css/excel-template-simple.css"></link>
</head>
<body class="bg-gray-50">
<div class="container mx-auto px-4 py-6">
    <!-- 顶部控制栏 -->
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-800">
            <i class="fas fa-file-excel mr-3"></i>Excel模板编辑器
        </h1>
        <div class="flex space-x-3">
            <button onclick="loadTemplate()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-download mr-2"></i>加载模板
            </button>
            <button onclick="clearTable()" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-trash mr-2"></i>清空表格
            </button>
            <button onclick="showSaveModal()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-save mr-2"></i>保存数据
            </button>
            <button onclick="goToRecords()" class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-list mr-2"></i>查看记录
            </button>
            <button onclick="goBack()" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-arrow-left mr-2"></i>返回
            </button>
        </div>
    </div>

    <!-- Excel数据粘贴区域 -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4">
            <i class="fas fa-clipboard mr-2"></i>Excel数据粘贴区域
        </h2>
        <div class="paste-area" onclick="focusPasteArea()">
                <textarea id="pasteArea" placeholder="在此处粘贴从Excel复制的数据，然后点击'解析数据'按钮"
                          class="w-full h-24 border-none bg-transparent resize-none focus:outline-none"></textarea>
        </div>
        <div class="flex space-x-3">
            <button onclick="parseExcelData()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-magic mr-2"></i>解析数据
            </button>
            <button onclick="clearPasteArea()" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-eraser mr-2"></i>清空粘贴区
            </button>
        </div>
    </div>

    <!-- 表格编辑区域 -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold mb-4">
            <i class="fas fa-table mr-2"></i>表格编辑区域
        </h2>

        <div class="table-container">
            <table id="dataTable" class="editable-table">
                <thead>
                <tr id="tableHeader">
                    <!-- 表头将通过JavaScript动态生成 -->
                </tr>
                </thead>
                <tbody id="tableBody">
                <!-- 表格内容将通过JavaScript动态生成 -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 保存模态框 -->
<div id="saveModal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="closeSaveModal()">&times;</span>
        <h2 class="text-xl font-bold mb-4">保存Excel数据</h2>

        <form id="saveForm" class="space-y-4">
            <div>
                <label for="templateName" class="block text-sm font-medium text-gray-700 mb-1">模板名称 *</label>
                <input type="text" id="templateName" required
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                       placeholder="请输入模板名称">
            </div>

            <div>
                <label for="remarks" class="block text-sm font-medium text-gray-700 mb-1">备注信息 *</label>
                <textarea id="remarks" required rows="4"
                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="请输入备注信息，方便后续查找..."></textarea>
            </div>

            <div class="flex justify-end space-x-3">
                <button type="button" onclick="closeSaveModal()"
                        class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">
                    取消
                </button>
                <button type="submit" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg">
                    <i class="fas fa-save mr-2"></i>保存
                </button>
            </div>
        </form>
    </div>
</div>

<script src="/js/excel-template-simple.js"></script>
</body>
</html>
