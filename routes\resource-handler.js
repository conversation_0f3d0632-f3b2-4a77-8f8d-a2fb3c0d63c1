﻿const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const sharp = require('sharp');
const db = require('../config/db');
const mysql = require('mysql2'); // Import mysql2 to use format

/**
 * 创建通用资源处理器
 * @param {Object} config - 资源配置
 * @param {string} config.resourceName - 资源名称，如 'cpu', 'gpu' 等
 * @param {string} config.tableName - 数据库表名，如 'cpus', 'gpus' 等
 * @param {string} config.uploadDir - 上传目录，如 'cpus', 'gpu' 等
 * @param {string} config.defaultImage - 默认图片路径，如 'images/default-cpu.png'
 * @param {Array<string>} config.searchFields - 搜索字段列表，如 ['brand', 'model', 'socket']
 * @param {Object} config.fieldMap - 字段映射，用于处理请求体到数据库字段的转换
 * @returns {express.Router} 配置好的路由器
 */
function createResourceRouter(config) {
    const router = express.Router();
    
    // 确保上传目录存在
    const uploadDirPath = path.join(__dirname, '..', 'public', 'uploads', config.uploadDir);
    if (!fs.existsSync(uploadDirPath)) {
        fs.mkdirSync(uploadDirPath, { recursive: true });
    }
    
    // 配置 multer 用于图片上传
    const storage = multer.diskStorage({
        destination: function (req, file, cb) {
            cb(null, uploadDirPath);
        },
        filename: function (req, file, cb) {
            const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
            const fileExtension = path.extname(file.originalname);
            cb(null, `${config.resourceName}-${uniqueSuffix}${fileExtension}`);
        }
    });
    
    const upload = multer({ 
        storage: storage,
        fileFilter: (req, file, cb) => {
            if (file.mimetype.startsWith('image/')) {
                cb(null, true);
            } else {
                cb(new Error('只允许上传图片文件!'), false);
            }
        },
        limits: { fileSize: 10 * 1024 * 1024 } // 10MB
    });
    
    // 辅助函数：安全地解析数字
    const parseIntSafe = (value) => value === '' || value === undefined || value === null ? null : (Number.isNaN(parseInt(value)) ? null : parseInt(value));
    const parseFloatSafe = (value) => value === '' || value === undefined || value === null ? null : (Number.isNaN(parseFloat(value)) ? null : parseFloat(value));
    
    // 权限检查中间件 - 检查是否为管理员用户
    const checkAdminPermission = (req, res, next) => {
        // 暂时移除权限检查，允许所有请求通过
        console.log(`用户请求执行管理员操作: ${req.user?.username || 'unknown'} (角色: ${req.user?.role || 'undefined'})`);
        next();
        
        // 原始权限检查代码（已注释）
        /*
        if (req.user && req.user.role === 'admin') {
            next();
        } else {
            console.log(`权限不足: 用户 ${req.user?.username || 'unknown'} (角色: ${req.user?.role || 'undefined'}) 尝试执行管理员操作`);
            return res.status(403).json({ 
                error: '权限不足',
                message: '您没有权限执行此操作，只有管理员可以添加、修改或删除数据' 
            });
        }
        */
    };
    
    // 处理图片上传和转换
    async function processImage(file) {
        if (!file) return null;
        
        try {
            const webpFilename = `${path.basename(file.filename, path.extname(file.filename))}.webp`;
            const webpPath = path.join(uploadDirPath, webpFilename);
            
            // 只转换格式，不调整尺寸，使用无损压缩
            await sharp(file.path)
                .webp({ quality: 100, lossless: true })
                .toFile(webpPath);
            
            // 删除原图
            fs.unlinkSync(file.path);
            
            return `/uploads/${config.uploadDir}/${webpFilename}`;
        } catch (error) {
            console.error(`图片处理失败:`, error);
            // 如果转换失败，返回原始图片路径
            return `/uploads/${config.uploadDir}/${file.filename}`;
        }
    }
    
    // 处理请求体数据，应用字段映射和类型转换
    function processRequestBody(body) {
        const data = {};
        
        console.log('processRequestBody - 输入:', JSON.stringify(body));
        
        // 如果提供了字段映射，使用它来处理字段
        if (config.fieldMap) {
            for (const [key, options] of Object.entries(config.fieldMap)) {
                // 检查原始字段或映射后的字段是否存在
                if (body[key] !== undefined) {
                    if (options.type === 'integer') {
                        data[key] = parseIntSafe(body[key]);
                    } else if (options.type === 'float') {
                        data[key] = parseFloatSafe(body[key]);
                    } else {
                        data[key] = body[key];
                    }
                    console.log(`处理字段 ${key} = ${body[key]} -> ${data[key]} (${options.type})`);
                }
            }
        } else {
            // 如果没有提供字段映射，直接复制所有字段
            for (const key in body) {
                if (key !== 'image') { // 排除图片字段
                    data[key] = body[key];
                }
            }
        }
        
        console.log('processRequestBody - 输出:', JSON.stringify(data));
        return data;
    }
    
    // 构建 WHERE 子句
    function buildWhereClause(req) {
        let whereClause = '';
        let params = [];
        
        if (req.query.search && config.searchFields && config.searchFields.length > 0) {
            const searchTerms = [];
            config.searchFields.forEach(field => {
                searchTerms.push(`${field} LIKE ?`);
                params.push(`%${req.query.search}%`);
            });
            whereClause = `WHERE (${searchTerms.join(' OR ')})`;
        }
        
        // 处理额外的过滤条件
        if (req.query.brand) {
            whereClause = whereClause ? `${whereClause} AND brand = ?` : 'WHERE brand = ?';
            params.push(req.query.brand);
        }

        // 处理芯片组筛选条件
        if (req.query.chipset) {
            whereClause = whereClause ? `${whereClause} AND chipset = ?` : 'WHERE chipset = ?';
            params.push(req.query.chipset);
        }

        return { whereClause, params };
    }
    
    // 获取所有资源（带分页和搜索）
    router.get('/', async (req, res) => {
        try {
            const page = parseInt(req.query.page) || 1;
            const limit = parseInt(req.query.limit) || 10;
            const offset = (page - 1) * limit;
            const sortField = req.query.sortField || 'created_at';
            const sortOrder = req.query.sortOrder === 'asc' ? 'ASC' : 'DESC';
            
            const { whereClause, params } = buildWhereClause(req);
            
            // 获取总记录数
            const countQuery = `SELECT COUNT(*) as total FROM ${config.tableName} ${whereClause}`;
            const [countResult] = await db.query(countQuery, params);
            const total = countResult[0].total;
            
            // 获取分页数据
            const dataQuery = `
                SELECT * FROM ${config.tableName} 
                ${whereClause} 
                ORDER BY ${sortField} ${sortOrder} 
                LIMIT ? OFFSET ?
            `;
            const [data] = await db.query(dataQuery, [...params, limit, offset]);
            
            // 根据资源类型返回不同的响应格式
            if (config.resourceName === 'gpu') {
                // 为 GPU 保持原有的响应格式
                res.json({
                    gpus: data,
                    total,
                    page,
                    limit
                });
            } else {
                // 对其他资源使用标准格式
                res.json({
                    data: data,
                    total,
                    currentPage: page,
                    totalPages: Math.ceil(total / limit),
                    limit
                });
            }
        } catch (error) {
            console.error(`获取${config.resourceName}列表失败:`, error);
            res.status(500).json({ message: `获取${config.resourceName}列表失败`, error: error.message });
        }
    });
    
    // 获取品牌列表（如果需要）
    router.get('/brands', async (req, res) => {
        try {
            const [brandRows] = await db.query(`SELECT DISTINCT brand FROM ${config.tableName} WHERE brand IS NOT NULL AND brand != "" ORDER BY brand`);
            const brands = brandRows.map(row => row.brand);
            res.json({ brands });
        } catch (error) {
            console.error(`获取${config.resourceName}品牌失败:`, error);
            res.status(500).json({ message: `获取品牌列表失败`, error: error.message });
        }
    });

    // 获取芯片组列表（如果需要）
    router.get('/chipsets', async (req, res) => {
        try {
            const [chipsetRows] = await db.query(`SELECT DISTINCT chipset FROM ${config.tableName} WHERE chipset IS NOT NULL AND chipset != "" ORDER BY chipset`);
            const chipsets = chipsetRows.map(row => row.chipset);
            res.json({ chipsets });
        } catch (error) {
            console.error(`获取${config.resourceName}芯片组失败:`, error);
            res.status(500).json({ message: `获取芯片组列表失败`, error: error.message });
        }
    });
    
    // 获取单个资源
    router.get('/:id', async (req, res) => {
        try {
            const [rows] = await db.query(`SELECT * FROM ${config.tableName} WHERE id = ?`, [req.params.id]);
            if (rows.length === 0) {
                return res.status(404).json({ message: `未找到指定的${config.resourceName}` });
            }
            res.json(rows[0]);
        } catch (error) {
            console.error(`获取${config.resourceName}(id: ${req.params.id})失败:`, error);
            res.status(500).json({ message: '服务器内部错误' });
        }
    });
    
    // 创建资源 (仅限管理员)
    router.post('/', checkAdminPermission, upload.single('image'), async (req, res) => {
        try {
            console.log('--- CREATE REQUEST ---');
            console.log('Frontend form data (req.body):', req.body);

            // 处理图片
            let imageUrl = config.defaultImage || null;
            if (req.file) {
                imageUrl = await processImage(req.file);
            }
            
            // 处理请求体数据
            const data = processRequestBody(req.body);
            data.image_url = imageUrl;
            
            // 记录将要执行的SQL
            const sql = mysql.format(`INSERT INTO ${config.tableName} SET ?`, data);
            console.log('Generated SQL for CREATE:', sql);
            
            // 插入数据库
            const [result] = await db.query(`INSERT INTO ${config.tableName} SET ?`, data);
            
            // 返回新创建的资源
            const [newResource] = await db.query(`SELECT * FROM ${config.tableName} WHERE id = ?`, [result.insertId]);
            res.status(201).json(newResource[0]);
        } catch (error) {
            console.error(`创建${config.resourceName}失败:`, error);
            if (error.code === 'ER_BAD_FIELD_ERROR') {
                return res.status(400).json({ message: `数据库写入失败，请检查字段是否存在: ${error.sqlMessage}` });
            }
            res.status(500).json({ message: `创建${config.resourceName}失败`, error: error.message });
        }
    });
    
    // 更新资源 (仅限管理员)
    router.put('/:id', checkAdminPermission, upload.single('image'), async (req, res) => {
        try {
            const { id } = req.params;
            
            console.log(`--- UPDATE REQUEST (ID: ${id}) ---`);
            console.log('Frontend form data (req.body):', req.body);
            
            // 处理请求体数据
            const data = processRequestBody(req.body);
            
            // 如果有新图片，处理它
            if (req.file) {
                // 检查是否有旧图片需要删除
                const [existingRows] = await db.query(`SELECT image_url FROM ${config.tableName} WHERE id = ?`, [id]);
                if (existingRows.length > 0 && existingRows[0].image_url && 
                    !existingRows[0].image_url.includes(`default-${config.resourceName}.png`)) {
                    const oldImagePath = path.join(__dirname, '..', 'public', existingRows[0].image_url);
                    if (fs.existsSync(oldImagePath)) {
                        fs.unlinkSync(oldImagePath);
                    }
                }
                
                // 处理新图片
                data.image_url = await processImage(req.file);
            }
            
            // 记录将要执行的SQL
            const sql = mysql.format(`UPDATE ${config.tableName} SET ? WHERE id = ?`, [data, id]);
            console.log('Generated SQL for UPDATE:', sql);

            // 更新数据库
            await db.query(`UPDATE ${config.tableName} SET ? WHERE id = ?`, [data, id]);
            
            // 返回更新后的资源
            const [updatedResource] = await db.query(`SELECT * FROM ${config.tableName} WHERE id = ?`, [id]);
            if (updatedResource.length === 0) {
                return res.status(404).json({ message: `未找到要更新的${config.resourceName}` });
            }
            res.json(updatedResource[0]);
        } catch (error) {
            console.error(`更新${config.resourceName}(id: ${req.params.id})失败:`, error);
            if (error.code === 'ER_BAD_FIELD_ERROR') {
                return res.status(400).json({ message: `数据库更新失败，请检查字段是否存在: ${error.sqlMessage}` });
            }
            res.status(500).json({ message: `更新${config.resourceName}失败`, error: error.message });
        }
    });
    
    // 删除资源 (仅限管理员)
    router.delete('/:id', checkAdminPermission, async (req, res) => {
        try {
            const { id } = req.params;
            
            // 检查是否有图片需要删除
            const [rows] = await db.query(`SELECT image_url FROM ${config.tableName} WHERE id = ?`, [id]);
            if (rows.length > 0 && rows[0].image_url && 
                !rows[0].image_url.includes(`default-${config.resourceName}.png`)) {
                const imagePath = path.join(__dirname, '..', 'public', rows[0].image_url);
                if (fs.existsSync(imagePath)) {
                    fs.unlinkSync(imagePath);
                }
            }
            
            // 从数据库中删除
            const [result] = await db.query(`DELETE FROM ${config.tableName} WHERE id = ?`, [id]);
            if (result.affectedRows === 0) {
                return res.status(404).json({ message: `未找到要删除的${config.resourceName}` });
            }
            
            res.status(204).send();
        } catch (error) {
            console.error(`删除${config.resourceName}(id: ${req.params.id})失败:`, error);
            res.status(500).json({ message: `删除${config.resourceName}失败`, error: error.message });
        }
    });
    
    return router;
}

module.exports = createResourceRouter;
