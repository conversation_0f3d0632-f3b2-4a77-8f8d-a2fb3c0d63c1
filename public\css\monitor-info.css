/* 颜色变量定义 - 亮色主题 */
:root {
    --bg-primary: #f9fafb;
    --bg-secondary: #ffffff;
    --bg-card: #ffffff;
    --bg-input: #ffffff;
    --text-primary: #111827;
    --text-secondary: #4b5563;
    --border-color: #e5e7eb;
    --input-bg: #ffffff;
    --input-text: #111827;
    --button-primary-bg: #dc2626;
    --button-primary-hover: #b91c1c;
    --button-primary-text: #ffffff;
    --highlight-color: #dc2626;
    --highlight-hover-color: #b91c1c;
    --card-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 暗黑模式样式 */
.dark-mode {
    --bg-primary: #121212;
    --bg-secondary: #1e1e1e;
    --bg-card: #252525;
    --bg-input: #2a2a2a;
    --text-primary: #f5f5f5;
    --text-secondary: #a0aec0;
    --border-color: #333333;
    --input-bg: #2a2a2a;
    --input-text: #e2e8f0;
    --button-primary-bg: #dc2626;
    --button-primary-hover: #b91c1c;
    --button-primary-text: #ffffff;
    --highlight-color: #dc2626;
    --highlight-hover-color: #b91c1c;
    --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

body {
    background-color: var(--bg-primary);
    color: var(--text-primary);
    transition: background-color 0.3s, color 0.3s;
}

body.dark-mode {
    background-color: var(--bg-primary);
}

.bg-card {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    box-shadow: var(--card-shadow);
}

.text-color {
    color: var(--text-primary);
}

.text-muted {
    color: var(--text-secondary);
}

.border-color {
    border-color: var(--border-color);
}

.highlight-bg {
    background-color: var(--highlight-color);
}

.highlight-bg:hover {
    background-color: var(--highlight-hover-color);
}

.highlight-text {
    color: var(--highlight-color);
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 行悬停动画 */
tr {
    transition: background-color 0.15s ease;
}

/* 按钮过渡效果 */
button,
a {
    transition: all 0.2s ease;
}

/* 图片悬停效果 */
.monitor-img-hover {
    transition: transform 0.3s ease;
}

.monitor-img-hover:hover {
    transform: scale(1.05);
}

/* 暗黑模式样式增强 */
.dark-mode input,
.dark-mode select,
.dark-mode textarea {
    background-color: var(--input-bg);
    color: var(--input-text);
    border-color: var(--border-color);
}

.dark-mode input::placeholder,
.dark-mode textarea::placeholder {
    color: #6b7280;
}

.dark-mode .hover\:bg-gray-50:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.dark-mode .divide-gray-200> :not([hidden])~ :not([hidden]) {
    border-color: var(--border-color);
}

/* 表格样式 */
.monitor-table {
    border-collapse: separate;
    border-spacing: 0;
}

.monitor-table thead th {
    background-color: #f9fafb;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: 0.75rem;
    padding: 0.75rem 1rem;
    border-bottom: 2px solid #e5e7eb;
}

.dark-mode .monitor-table thead th {
    background-color: rgba(255, 255, 255, 0.03);
    color: #9ca3af;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.monitor-table th,
.monitor-table td {
    vertical-align: middle;
}

/* 标签样式统一 */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
}

/* 品牌标签样式 */
.brand-badge {
    background-color: rgba(79, 70, 229, 0.1);
    color: #4f46e5;
    border: 1px solid rgba(79, 70, 229, 0.2);
}

/* 分辨率标签样式 */
.resolution-tag {
    background-color: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
    border: 1px solid rgba(59, 130, 246, 0.2);
}

/* 面板类型标签样式 */
.panel-tag {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
    background-color: rgba(245, 158, 11, 0.1);
    color: #d97706;
    border: 1px solid rgba(245, 158, 11, 0.2);
}

/* 刷新率标签样式 */
.refresh-rate-tag {
    background-color: rgba(220, 38, 38, 0.1);
    color: #dc2626;
    border: 1px solid rgba(220, 38, 38, 0.2);
}

/* 操作按钮样式 */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.btn-view,
.btn-edit,
.btn-delete {
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
    background-color: transparent;
    border: none;
    cursor: pointer;
}

.btn-view {
    color: #3b82f6;
}

.btn-view:hover {
    color: #2563eb;
    background-color: rgba(59, 130, 246, 0.1);
    transform: translateY(-2px);
}

.btn-edit {
    color: #10b981;
}

.btn-edit:hover {
    color: #059669;
    background-color: rgba(16, 185, 129, 0.1);
    transform: translateY(-2px);
}

.btn-delete {
    color: #ef4444;
}

.btn-delete:hover {
    color: #dc2626;
    background-color: rgba(239, 68, 68, 0.1);
    transform: translateY(-2px);
}

/* 图片容器悬停效果 */
.monitor-image-container {
    position: relative;
    overflow: hidden;
    border-radius: 0.375rem;
}

.img-overlay {
    position: absolute;
    inset: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.3);
    opacity: 0;
    transition: opacity 0.2s ease;
    border-radius: 0.375rem;
}

.monitor-image-container:hover .img-overlay {
    opacity: 1;
}

/* 暗色模式标签调整 */
.dark-mode .brand-badge {
    background-color: rgba(79, 70, 229, 0.15);
    color: #818cf8;
    border-color: rgba(79, 70, 229, 0.25);
}

.dark-mode .resolution-tag {
    background-color: rgba(59, 130, 246, 0.15);
    color: #60a5fa;
    border-color: rgba(59, 130, 246, 0.25);
}

.dark-mode .panel-tag {
    background-color: rgba(245, 158, 11, 0.15);
    color: #fbbf24;
    border-color: rgba(245, 158, 11, 0.25);
}

.dark-mode .refresh-rate-tag {
    background-color: rgba(220, 38, 38, 0.15);
    color: #ef4444;
    border-color: rgba(220, 38, 38, 0.25);
}

/* 移动端卡片布局样式 */
@media (max-width: 640px) {

    /* 卡片容器 */
    .monitor-card {
        border-radius: 6px;
        overflow: hidden;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        margin-bottom: 8px;
        animation: fadeIn 0.3s ease-in-out;
    }

    .dark-mode .monitor-card {
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
    }

    /* 卡片头部 */
    .monitor-card-header {
        padding: 8px 10px;
        background-color: #f8f9fa;
        border-bottom: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .dark-mode .monitor-card-header {
        background-color: rgba(0, 0, 0, 0.2);
        border-color: rgba(255, 255, 255, 0.05);
    }

    /* 卡片内容 */
    .monitor-card-body {
        padding: 10px;
        display: flex;
        gap: 10px;
    }

    /* 卡片底部 */
    .monitor-card-footer {
        border-top: 1px solid #eee;
        padding: 6px;
        display: flex;
        justify-content: space-around;
    }

    .dark-mode .monitor-card-footer {
        background-color: rgba(0, 0, 0, 0.1);
        border-color: rgba(255, 255, 255, 0.05);
    }

    /* 卡片触摸反馈 */
    .monitor-card:active {
        transform: scale(0.99);
    }
}

/* 新增加载中动画 */
@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.animate-spin {
    animation: spin 1s linear infinite;
}

/* 模态窗口适配 */
.modal-content-wrapper {
    max-height: 85vh;
    overflow-y: auto;
    scrollbar-width: none;
    /* Firefox */
    -ms-overflow-style: none;
    /* IE and Edge */
}

.modal-content-wrapper::-webkit-scrollbar {
    display: none;
    /* Chrome, Safari, Opera */
    width: 0;
    height: 0;
}

/* 移动端模态样式增强 */
@media (max-width: 640px) {
    .modal-card {
        width: 92% !important;
        margin: 0.5rem auto;
        max-height: 90vh;
        display: flex;
        flex-direction: column;
    }

    .modal-header {
        padding: 0.75rem 0.75rem 0.5rem 0.75rem;
    }

    .modal-content {
        padding: 0.5rem 0.75rem;
        flex: 1;
    }

    .modal-footer {
        padding: 0.75rem;
        flex-direction: row;
        justify-content: flex-end;
        border-top: 1px solid var(--border-color);
        background-color: rgba(0, 0, 0, 0.03);
        position: sticky;
        bottom: 0;
    }

    .spec-grid {
        grid-template-columns: 1fr !important;
        gap: 0.5rem !important;
    }

    .detail-card {
        padding: 0.5rem !important;
    }

    .btn-action {
        flex: 1;
        text-align: center;
        justify-content: center;
    }
}