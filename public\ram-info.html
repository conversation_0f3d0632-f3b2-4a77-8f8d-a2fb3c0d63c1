<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>内存信息管理 - 电脑配件综合管理系统</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/viewerjs/1.11.6/viewer.min.css">
    <link rel="stylesheet" href="/css/ram-info.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/viewerjs/1.11.6/viewer.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="/js/js-pinyin.js"></script>
    <script src="/js/pinyin-wrapper.js"></script>
    <script src="/js/main.js"></script>
    <script src="/js/permission-helper.js"></script>
</head>

<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-2 sm:px-4 py-4 sm:py-8">
        <header class="mb-4 sm:mb-8">
            <h1 class="text-xl sm:text-3xl font-bold text-green-700 flex items-center">
                <i class="fas fa-memory mr-2 sm:mr-3"></i> 内存信息管理
            </h1>
            <div class="flex justify-between items-center">
                <p class="text-gray-600 mt-1 sm:mt-2 text-sm sm:text-base">记录和管理内存配置信息</p>
                <div class="mt-2 flex space-x-2">
                    <button id="darkModeToggle"
                        class="w-10 h-10 rounded-full bg-gray-200 hover:bg-gray-300 flex items-center justify-center text-gray-700 shadow-sm transition-all duration-300"
                        title="切换暗夜模式">
                        <i class="fas fa-moon"></i>
                    </button>
                    <a href="/pc-components.html"
                        class="inline-block bg-green-600 text-white py-1 sm:py-2 px-3 sm:px-4 rounded-md hover:bg-green-700 text-sm sm:text-base">
                        <i class="fas fa-arrow-left mr-1"></i> 返回配件列表
                    </a>
                    <a href="/"
                        class="inline-block bg-green-600 text-white py-1 sm:py-2 px-3 sm:px-4 rounded-md hover:bg-green-700 text-sm sm:text-base">
                        <i class="fas fa-home mr-1"></i> 首页
                    </a>
                </div>
            </div>
        </header>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-4 sm:gap-6">
            <!-- 左侧表单区域 -->
            <div class="lg:col-span-1 bg-white p-3 sm:p-6 rounded-lg shadow-md">
                <h2 class="text-lg sm:text-xl font-semibold text-gray-800 mb-3 sm:mb-4 flex items-center">
                    <i class="fas fa-plus-circle mr-1 sm:mr-2 text-green-500"></i> 添加内存信息
                </h2>

                <form id="ramForm" class="space-y-3 sm:space-y-4">
                    <!-- 智能识别区域 -->
                    <div class="border border-green-200 rounded-md p-3 bg-green-50">
                        <h3 class="text-md font-medium text-green-700 mb-2 flex items-center">
                            <i class="fas fa-magic mr-1 text-green-500"></i> 智能识别
                        </h3>
                        <div class="space-y-2">
                            <textarea id="smartInput" rows="3"
                                class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 text-sm sm:text-base"
                                placeholder="粘贴内存参数文本，如：品牌：芝奇、型号：皇家戟、类型：DDR4、容量：16GB、频率：3200MHz、CL：16-18-18-38..."></textarea>
                            <button type="button" id="autoFillBtn"
                                class="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-md text-sm">
                                <i class="fas fa-magic mr-2"></i>智能识别
                            </button>
                        </div>
                    </div>

                    <!-- 1.内存核心信息 -->
                    <div class="border border-gray-200 rounded-md p-3">
                        <h3 class="text-md font-medium text-gray-700 mb-2">内存核心信息</h3>

                        <div class="space-y-3">
                            <div>
                                <label for="model" class="block text-sm font-medium text-gray-700 mb-1">内存型号
                                    <span class="text-red-500">*</span></label>
                                <input type="text" id="model"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 text-sm sm:text-base"
                                    placeholder="如: Corsair Vengeance RGB Pro" required>
                            </div>

                            <div>
                                <label for="brand" class="block text-sm font-medium text-gray-700 mb-1">品牌 <span
                                        class="text-red-500">*</span></label>
                                <select id="brand"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 text-sm sm:text-base"
                                    required>
                                    <option value="">选择品牌</option>
                                    <option value="威刚">威刚 (ADATA)</option>
                                    <option value="雷克沙">雷克沙 (Lexar)</option>
                                    <option value="芝奇">芝奇 (G.Skill)</option>
                                    <option value="宏碁 - 掠夺者">宏碁 - 掠夺者 (Acer - Predator)</option>
                                    <option value="亿储">亿储 (Essence)</option>
                                    <option value="金士顿">金士顿 (Kingston)</option>
                                    <option value="阿斯加特">阿斯加特 (Asgard)</option>
                                    <option value="金百达">金百达 (KingSpec)</option>
                                    <option value="机械师">机械师</option>
                                    <option value="美商海盗船">美商海盗船 (Corsair)</option>
                                    <option value="英睿达">英睿达 (Crucial)</option>
                                    <option value="宇瞻">宇瞻 (Apacer)</option>
                                    <option value="光威">光威 (Gloway)</option>
                                    <option value="十铨">十铨 (Team)</option>
                                    <option value="其他">其他</option>
                                </select>
                            </div>

                            <div>
                                <!-- <label for="capacity" class="block text-sm font-medium text-gray-700 mb-1">内存容量 (GB)
                                    <span class="text-red-500">*</span></label>
                                <input type="number" id="capacity"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 text-sm sm:text-base"
                                    placeholder="如: 16" required> -->

                                <label for="capacity" class="block text-sm font-medium text-gray-700 mb-1">内存容量 <span
                                        class="text-red-500">*</span></label>
                                <select id="capacity"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 text-sm sm:text-base"
                                    required>
                                    <option value="">选择内存容量</option>
                                    <option value="8">8G</option>
                                    <option value="16">16G</option>
                                    <option value="24">24G</option>
                                    <option value="32">32G</option>
                                    <option value="48">48G</option>

                                    <option value="64">64G</option>
                                    <option value="96">96G</option>
                                    <option value="128">128G</option>

                                </select>
                            </div>

                            <div>
                                <label for="memoryType" class="block text-sm font-medium text-gray-700 mb-1">内存类型 <span
                                        class="text-red-500">*</span></label>
                                <select id="memoryType"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 text-sm sm:text-base"
                                    required>
                                    <option value="">选择内存类型</option>
                                    <option value="DDR5">DDR5</option>
                                    <option value="DDR4">DDR4</option>
                                    <option value="DDR3">DDR3</option>
                                    <option value="DDR2">DDR2</option>
                                </select>
                            </div>

                            <div>
                                <label for="speed" class="block text-sm font-medium text-gray-700 mb-1">频率 (MHz)</label>
                                <input type="number" id="speed"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 text-sm sm:text-base"
                                    placeholder="如: 3200">
                            </div>
                        </div>
                    </div>

                    <!-- 2.内存规格信息 -->
                    <div class="border border-gray-200 rounded-md p-3">
                        <h3 class="text-md font-medium text-gray-700 mb-2">内存规格信息</h3>

                        <div class="space-y-3">
                            <div>
                                <label for="modules" class="block text-sm font-medium text-gray-700 mb-1">内存根数</label>
                                <select id="modules" name="kit_size"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 text-sm sm:text-base">
                                    <option value="">选择根数</option>
                                    <option value="1">1根</option>
                                    <option value="2">2根</option>
                                    <option value="4">4根</option>
                                    <option value="8">8根</option>
                                </select>
                            </div>

                            <div>
                                <label for="casLatency"
                                    class="block text-sm font-medium text-gray-700 mb-1">CAS延迟</label>
                                <input type="number" step="0.1" id="casLatency"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 text-sm sm:text-base"
                                    placeholder="如: 16">
                            </div>

                            <div>
                                <label for="voltage" class="block text-sm font-medium text-gray-700 mb-1">电压 (V)</label>
                                <input type="number" step="0.01" id="voltage"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 text-sm sm:text-base"
                                    placeholder="如: 1.35">
                            </div>

                            <div class="grid grid-cols-2 gap-3">
                                <div>
                                    <label for="heatSpreader"
                                        class="block text-sm font-medium text-gray-700 mb-1">散热片</label>
                                    <select id="heatSpreader"
                                        class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 text-sm sm:text-base">
                                        <option value="">选择</option>
                                        <option value="1">有</option>
                                        <option value="0">无</option>
                                    </select>
                                </div>

                                <div>
                                    <label for="rgbLighting"
                                        class="block text-sm font-medium text-gray-700 mb-1">RGB灯效</label>
                                    <select id="rgbLighting" name="rgb"
                                        class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 text-sm sm:text-base">
                                        <option value="">选择</option>
                                        <option value="1">有</option>
                                        <option value="0">无</option>
                                    </select>
                                </div>
                            </div>

                            <div>
                                <label for="color" class="block text-sm font-medium text-gray-700 mb-1">颜色</label>
                                <input type="text" id="color" name="color"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 text-sm sm:text-base"
                                    placeholder="如: 黑色">
                            </div>
                        </div>
                    </div>

                    <!-- 3.价格与备注 -->
                    <div class="border border-gray-200 rounded-md p-3">
                        <h3 class="text-md font-medium text-gray-700 mb-2">价格与备注</h3>

                        <div class="space-y-3">
                            <div>
                                <label for="price" class="block text-sm font-medium text-gray-700 mb-1">价格</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 sm:text-sm">¥</span>
                                    </div>
                                    <input type="number" id="price"
                                        class="w-full pl-7 px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 text-sm sm:text-base"
                                        placeholder="0.00" min="0" step="0.01">
                                </div>
                            </div>

                            <div>
                                <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">备注</label>
                                <textarea id="notes" rows="3"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 text-sm sm:text-base"
                                    placeholder="其他需要备注的信息..."></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- 4.图片上传 -->
                    <div class="border border-gray-200 rounded-md p-3">
                        <h3 class="text-md font-medium text-gray-700 mb-2">图片上传</h3>

                        <div
                            class="mt-1 flex justify-center px-4 sm:px-6 pt-3 sm:pt-5 pb-4 sm:pb-6 border-2 border-gray-300 border-dashed rounded-md">
                            <div class="space-y-1 text-center">
                                <div class="flex text-sm text-gray-600 items-center justify-center flex-wrap">
                                    <label for="ramImage"
                                        class="relative cursor-pointer bg-white rounded-md font-medium text-green-600 hover:text-green-500 focus-within:outline-none">
                                        <span>上传图片</span>
                                        <input id="ramImage" name="ramImage" type="file" accept="image/*"
                                            class="sr-only">
                                    </label>
                                    <p class="pl-1 mobile-hidden">或将图片拖拽到此处</p>
                                </div>
                                <p class="text-xs text-gray-500">PNG, JPG, GIF 格式，大小不超过5MB（将自动转换为WebP格式以提高加载速度）</p>
                            </div>
                        </div>

                        <!-- 上传进度条 -->
                        <div id="uploadProgressContainer" class="mt-2 hidden">
                            <div class="bg-gray-200 dark:bg-gray-700 rounded-full h-3 mb-2 overflow-hidden">
                                <div id="uploadProgressBar"
                                    class="bg-gradient-to-r from-pink-500 to-pink-600 dark:from-pink-400 dark:to-pink-500 h-3 rounded-full transition-all duration-500 ease-out relative"
                                    style="width: 0%">
                                    <!-- 进度条光泽效果 -->
                                    <div
                                        class="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-20 animate-pulse">
                                    </div>
                                </div>
                            </div>
                            <div class="flex justify-between items-center text-xs">
                                <span id="uploadProgressText"
                                    class="text-gray-600 dark:text-gray-400 flex items-center">
                                    <i id="uploadProgressIcon" class="fas fa-upload mr-1"></i>
                                    准备上传...
                                </span>
                                <span id="uploadProgressPercent"
                                    class="text-pink-600 dark:text-pink-400 font-medium">0%</span>
                            </div>
                        </div>

                        <div id="imagePreviewContainer" class="mt-2 hidden">
                            <div class="relative inline-block">
                                <img id="imagePreview" src="#" alt="预览图"
                                    class="h-24 sm:h-32 rounded-md shadow image-preview">
                                <button type="button" id="removeImageBtn"
                                    class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 sm:w-6 sm:h-6 flex items-center justify-center hover:bg-red-600">
                                    <i class="fas fa-times text-xs"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div>
                        <button type="submit"
                            class="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-md">
                            <i class="fas fa-save mr-1"></i> 保存内存信息
                        </button>
                    </div>
                </form>
            </div>

            <!-- 右侧列表区域 -->
            <div class="lg:col-span-3">
                <!-- 搜索和过滤 -->
                <div class="bg-white p-3 sm:p-6 rounded-lg shadow-md mb-4 sm:mb-6">
                    <h2 class="text-xl font-semibold text-green-800 mb-2 flex items-center">
                        <i class="fas fa-memory mr-2 sm:mr-3"></i> 内存列表
                    </h2>
                    <div class="flex flex-wrap items-center gap-2 sm:gap-4">
                        <div class="flex items-center flex-wrap sm:flex-nowrap w-full gap-2">
                            <div class="relative w-full sm:w-auto sm:flex-1" style="max-width:680px;">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-search text-gray-400"></i>
                                </div>
                                <input type="text" id="searchInput" placeholder="搜索内存型号、品牌等..."
                                    class="w-full py-2 pl-10 pr-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500">
                            </div>

                            <select id="brandFilter"
                                class="w-auto border border-gray-300 rounded-md shadow-sm py-2 pl-3 pr-8 focus:outline-none focus:ring-green-500 focus:border-green-500">
                                <option value="all">所有品牌</option>
                                <option value="威刚">威刚</option>
                                <option value="雷克沙">雷克沙</option>
                                <option value="芝奇">芝奇</option>
                                <option value="宏碁 - 掠夺者">宏碁 - 掠夺者</option>
                                <option value="亿储">亿储</option>
                                <option value="金士顿">金士顿</option>
                                <option value="阿斯加特">阿斯加特</option>
                                <option value="金百达">金百达</option>
                                <option value="机械师">机械师</option>
                                <option value="美商海盗船">美商海盗船</option>
                                <option value="英睿达">英睿达</option>
                                <option value="宇瞻">宇瞻</option>
                                <option value="光威">光威</option>
                                <option value="十铨">十铨</option>
                                <option value="其他">其他</option>
                            </select>

                            <select id="capacityFilter"
                                class="w-auto border border-gray-300 rounded-md shadow-sm py-2 pl-3 pr-8 focus:outline-none focus:ring-green-500 focus:border-green-500">
                                <option value="all">所有容量</option>
                                <option value="8">8GB</option>
                                <option value="16">16GB</option>
                                <option value="24">24GB</option>
                                <option value="32">32GB</option>
                                <option value="48">48GB</option>
                                <option value="64">64GB</option>
                                <option value="96">96GB</option>
                                <option value="128">128GB</option>
                            </select>

                            <button id="resetFilterBtn"
                                class="flex-none bg-gray-200 hover:bg-gray-300 text-gray-700 py-2 px-4 rounded-md">
                                <i class="fas fa-sync-alt mr-1"></i> 重置
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 内存列表 -->
                <div class="bg-white rounded-lg shadow-md">
                    <div class="overflow-x-auto sm:mx-0">
                        <div class="inline-block min-w-full align-middle">
                            <table class="min-w-full divide-y divide-gray-200 table-compact sm:table-auto">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col"
                                            class="px-3 py-2 sm:px-4 sm:py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell">
                                            图片
                                        </th>
                                        <th scope="col"
                                            class="px-3 py-2 sm:px-4 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider model-column">
                                            内存型号
                                        </th>
                                        <th scope="col"
                                            class="px-3 py-2 sm:px-4 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell">
                                            品牌
                                        </th>
                                        <th scope="col"
                                            class="px-3 py-2 sm:px-4 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider memory-column">
                                            容量/类型
                                        </th>
                                        <th scope="col"
                                            class="px-3 py-2 sm:px-4 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell">
                                            频率
                                        </th>
                                        <th scope="col"
                                            class="px-3 py-2 sm:px-4 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell">
                                            CAS延迟
                                        </th>
                                        <th scope="col"
                                            class="px-3 py-2 sm:px-4 sm:py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider action-column">
                                            操作
                                        </th>
                                    </tr>
                                </thead>
                                <tbody id="ramTableBody" class="bg-white divide-y divide-gray-200">
                                    <!-- 数据将通过JavaScript填充 -->
                                    <tr>
                                        <td colspan="7" class="px-3 py-4 text-center text-sm text-gray-500">
                                            加载中...
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 分页控件 -->
                    <div
                        class="mt-4 px-4 py-4 border-t border-gray-200 sm:px-6 flex flex-wrap justify-between items-center pagination-mobile">
                        <div class="text-sm text-gray-700 mb-2 sm:mb-0" id="totalCount">
                            共 0 条记录
                        </div>
                        <div class="flex items-center space-x-1 sm:space-x-2 page-controls-mobile">
                            <button id="firstPage" title="首页"
                                class="px-2 py-1 border border-gray-300 rounded-md text-sm bg-white text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                <i class="fas fa-angle-double-left"></i>
                            </button>
                            <button id="prevPageBtn" title="上一页"
                                class="px-2 py-1 border border-gray-300 rounded-md text-sm bg-white text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                <i class="fas fa-chevron-left"></i>
                            </button>

                            <div id="pageNumbers" class="hidden sm:flex space-x-1">
                                <!-- 页码将通过JavaScript填充 -->
                            </div>

                            <span id="pageInfo" class="px-2 py-1 text-sm whitespace-nowrap">第 <span
                                    id="currentPageDisplay">1</span> / <span id="totalPagesDisplay">1</span> 页</span>

                            <button id="nextPageBtn" title="下一页"
                                class="px-2 py-1 border border-gray-300 rounded-md text-sm bg-white text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                            <button id="lastPage" title="尾页"
                                class="px-2 py-1 border border-gray-300 rounded-md text-sm bg-white text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                <i class="fas fa-angle-double-right"></i>
                            </button>

                            <div class="hidden sm:flex items-center ml-2">
                                <span class="text-sm">跳转到</span>
                                <input type="number" id="pageJump" min="1"
                                    class="w-12 ml-1 px-2 py-1 border border-gray-300 rounded-md text-sm">
                                <button id="goToPage"
                                    class="ml-1 px-2 py-1 bg-green-600 text-white text-sm rounded-md hover:bg-green-700">
                                    确定
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 内存详情模态框 -->
        <div id="ramDetailModal"
            class="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center z-50 hidden">
            <div
                class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto mobile-modal-content">
                <div
                    class="px-4 py-3 border-b border-gray-200 flex justify-between items-center sticky top-0 bg-white z-10">
                    <h3 class="text-lg font-semibold text-gray-900">内存详细信息</h3>
                    <button id="closeDetailModalBtn" class="text-gray-400 hover:text-gray-500"
                        onclick="closeRamDetailModal()">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                <div id="ramDetailContent" class="p-4">
                    <!-- 内存详情内容将通过JavaScript填充 -->
                </div>
                <div class="px-4 py-3 border-t border-gray-200 flex justify-end space-x-3">
                    <button id="editBtn"
                        class="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-green-600 hover:bg-green-700">
                        <i class="fas fa-edit mr-1"></i> 编辑
                    </button>
                    <button id="deleteBtn"
                        class="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700">
                        <i class="fas fa-trash mr-1"></i> 删除
                    </button>
                    <button
                        class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                        onclick="closeRamDetailModal()">
                        关闭
                    </button>
                </div>
            </div>
            <!-- 添加移动端专用关闭按钮 -->
            <button class="mobile-close-button hidden" onclick="closeRamDetailModal()">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <!-- 确认删除模态框 -->
        <div id="deleteConfirmModal"
            class="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center z-50 hidden">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="px-4 py-3 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">确认删除</h3>
                </div>
                <div class="p-4">
                    <p class="text-gray-700">您确定要删除这条内存记录吗？此操作无法撤销。</p>
                    <div class="mt-4 flex justify-end space-x-3">
                        <button id="cancelDeleteBtn"
                            class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            取消
                        </button>
                        <button id="confirmDeleteBtn"
                            class="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700">
                            确认删除
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="/js/ram-info.js"></script>
    <script>
        function closeRamDetailModal() {
            document.getElementById('ramDetailModal').classList.add('hidden');
            document.body.style.overflow = '';
        }
    </script>
    <script src="js/upload-progress.js"></script>
</body>

</html>