/**
 * 笔记系统主题修复样式
 * 专门解决主题切换和文字可见性问题
 */

/* 基础样式重置 */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* 浅色主题强制样式 */
body:not(.dark) {
    background-color: #f9fafb !important;
    color: #111827 !important;
}

body:not(.dark) .text-gray-900 {
    color: #111827 !important;
}

body:not(.dark) .text-gray-600 {
    color: #4b5563 !important;
}

body:not(.dark) .text-gray-500 {
    color: #6b7280 !important;
}

body:not(.dark) .text-gray-400 {
    color: #9ca3af !important;
}

body:not(.dark) .bg-white {
    background-color: #ffffff !important;
}

body:not(.dark) .bg-gray-100 {
    background-color: #f3f4f6 !important;
}

body:not(.dark) .bg-gray-200 {
    background-color: #e5e7eb !important;
}

/* 深色主题强制样式 */
.dark {
    background-color: #111827 !important;
    color: #f9fafb !important;
}

.dark .text-gray-900,
.dark .text-white {
    color: #f9fafb !important;
}

.dark .text-gray-600 {
    color: #d1d5db !important;
}

.dark .text-gray-500 {
    color: #9ca3af !important;
}

.dark .text-gray-400 {
    color: #9ca3af !important;
}

.dark .text-gray-300 {
    color: #d1d5db !important;
}

.dark .bg-white {
    background-color: #1f2937 !important;
}

.dark .bg-gray-100 {
    background-color: #374151 !important;
}

.dark .bg-gray-200 {
    background-color: #4b5563 !important;
}

.dark .bg-gray-700 {
    background-color: #374151 !important;
}

.dark .bg-gray-800 {
    background-color: #1f2937 !important;
}

.dark .bg-gray-600 {
    background-color: #4b5563 !important;
}

/* 边框颜色修复 */
.dark .border-gray-200 {
    border-color: #374151 !important;
}

.dark .border-gray-300 {
    border-color: #4b5563 !important;
}

/* 悬停状态修复 */
.dark .hover\:bg-gray-100:hover {
    background-color: #374151 !important;
}

.dark .hover\:bg-gray-200:hover {
    background-color: #4b5563 !important;
}

.dark .hover\:bg-gray-600:hover {
    background-color: #4b5563 !important;
}

.dark .hover\:bg-gray-700:hover {
    background-color: #374151 !important;
}

/* 蓝色主题色保持一致 */
.text-blue-600 {
    color: #2563eb !important;
}

.dark .text-blue-400 {
    color: #60a5fa !important;
}

.hover\:text-blue-600:hover {
    color: #2563eb !important;
}

.dark .hover\:text-blue-300:hover {
    color: #93c5fd !important;
}

/* 按钮样式修复 */
.bg-blue-600 {
    background-color: #2563eb !important;
}

.hover\:bg-blue-700:hover {
    background-color: #1d4ed8 !important;
}

/* 确保主题切换按钮可见 */
#themeToggleBtn {
    background-color: #f3f4f6 !important;
    color: #4b5563 !important;
}

.dark #themeToggleBtn {
    background-color: #374151 !important;
    color: #d1d5db !important;
}

#themeToggleBtn:hover {
    background-color: #e5e7eb !important;
}

.dark #themeToggleBtn:hover {
    background-color: #4b5563 !important;
}

/* 侧边栏特殊修复 */
.sidebar-section {
    background-color: #ffffff !important;
    border: 1px solid #e5e7eb !important;
}

.dark .sidebar-section {
    background-color: #1f2937 !important;
    border-color: #374151 !important;
}

/* 分类和标签项目修复 */
.category-item,
.tag-item {
    color: #4b5563 !important;
}

.dark .category-item,
.dark .tag-item {
    color: #d1d5db !important;
}

.category-item:hover,
.tag-item:hover {
    background-color: #f3f4f6 !important;
    color: #111827 !important;
}

.dark .category-item:hover,
.dark .tag-item:hover {
    background-color: #374151 !important;
    color: #f9fafb !important;
}

/* 计数器样式修复 */
.category-count,
.tag-count {
    background-color: #e5e7eb !important;
    color: #6b7280 !important;
}

.dark .category-count,
.dark .tag-count {
    background-color: #4b5563 !important;
    color: #d1d5db !important;
}

/* 输入框修复 */
input, select, textarea {
    background-color: #ffffff !important;
    color: #111827 !important;
    border-color: #d1d5db !important;
}

.dark input,
.dark select,
.dark textarea {
    background-color: #374151 !important;
    color: #f9fafb !important;
    border-color: #4b5563 !important;
}

input::placeholder,
textarea::placeholder {
    color: #9ca3af !important;
}

.dark input::placeholder,
.dark textarea::placeholder {
    color: #6b7280 !important;
}

/* 模态框深色主题修复 */
.dark #noteModal .bg-white {
    background-color: #1f2937 !important;
}

.dark #noteModal .text-gray-700 {
    color: #d1d5db !important;
}

.dark #noteModal .text-gray-900 {
    color: #f9fafb !important;
}

.dark #noteModal .border-gray-300 {
    border-color: #4b5563 !important;
}

.dark #noteModal .border-gray-200 {
    border-color: #374151 !important;
}

.dark #noteModal .bg-gray-200 {
    background-color: #4b5563 !important;
}

.dark #noteModal .bg-gray-600 {
    background-color: #374151 !important;
}

.dark #noteModal .hover\:bg-gray-300:hover {
    background-color: #6b7280 !important;
}

.dark #noteModal .hover\:bg-gray-500:hover {
    background-color: #4b5563 !important;
}

/* 详情模态框样式 */
#noteDetailModal {
    z-index: 60;
}

.dark #noteDetailModal .bg-white {
    background-color: #1f2937 !important;
}

.dark #noteDetailModal .text-gray-700,
.dark #noteDetailModal .text-gray-900 {
    color: #f9fafb !important;
}

.dark #noteDetailModal .text-gray-600 {
    color: #d1d5db !important;
}

.dark #noteDetailModal .text-gray-500 {
    color: #9ca3af !important;
}

.dark #noteDetailModal .border-gray-200 {
    border-color: #374151 !important;
}

.dark #noteDetailModal .bg-gray-50 {
    background-color: #374151 !important;
}

.dark #noteDetailModal .bg-gray-100 {
    background-color: #4b5563 !important;
}
