tailwind.config = {
    darkMode: 'class',
    theme: {
        extend: {
            animation: {
                'fade-in': 'fadeIn 0.3s ease-in-out',
                'slide-up': 'slideUp 0.3s ease-out',
                'bounce-gentle': 'bounceGentle 0.6s ease-in-out'
            },
            keyframes: {
                fadeIn: {
                    '0%': { opacity: '0' },
                    '100%': { opacity: '1' }
                },
                slideUp: {
                    '0%': { transform: 'translateY(10px)', opacity: '0' },
                    '100%': { transform: 'translateY(0)', opacity: '1' }
                },
                bounceGentle: {
                    '0%, 100%': { transform: 'translateY(0)' },
                    '50%': { transform: 'translateY(-5px)' }
                }
            }
        }
    }
}

// 调试主题切换功能
function checkThemeManager() {
    console.log('检查主题管理器状态');

    // 检查主题管理器是否存在
    if (window.themeManager) {
        console.log('主题管理器已加载，当前主题:', window.themeManager.getCurrentTheme());

        // 检查主题切换按钮
        const toggleBtn = document.querySelector('[data-theme-toggle]');
        if (toggleBtn) {
            console.log('找到主题切换按钮:', toggleBtn);

            // 添加额外的点击监听器用于调试
            toggleBtn.addEventListener('click', function (e) {
                console.log('主题切换按钮点击事件触发');
            });
        } else {
            console.error('未找到主题切换按钮');
        }

        // 监听主题变化事件
        document.addEventListener('themechange', function (e) {
            console.log('主题变化事件:', e.detail);
        });

        return true;
    } else {
        console.log('主题管理器尚未加载，等待中...');
        return false;
    }
}

// DOM加载完成后检查
document.addEventListener('DOMContentLoaded', function () {
    console.log('页面DOM加载完成');

    // 如果主题管理器还没有加载，等待一段时间后重试
    if (!checkThemeManager()) {
        let retryCount = 0;
        const maxRetries = 10;
        const retryInterval = setInterval(() => {
            retryCount++;
            if (checkThemeManager() || retryCount >= maxRetries) {
                clearInterval(retryInterval);
                if (retryCount >= maxRetries) {
                    console.error('主题管理器加载超时');
                }
            }
        }, 100);
    }
});

// 全局函数用于手动测试
window.testThemeToggle = function () {
    if (window.themeManager) {
        console.log('手动切换主题');
        window.themeManager.toggleTheme();
    } else {
        console.error('主题管理器不可用');
    }
};

// 备用主题切换功能
window.fallbackThemeToggle = function () {
    console.log('使用备用主题切换功能');
    const html = document.documentElement;
    const toggleBtn = document.querySelector('[data-theme-toggle]');

    if (html.classList.contains('dark')) {
        // 切换到浅色主题
        html.classList.remove('dark');
        html.classList.add('light');
        if (toggleBtn) {
            toggleBtn.innerHTML = '<i class="fas fa-moon"></i>';
            toggleBtn.title = '切换到深色主题';
        }
        localStorage.setItem('preferred-theme', 'light');
        console.log('已切换到浅色主题');
    } else {
        // 切换到深色主题
        html.classList.remove('light');
        html.classList.add('dark');
        if (toggleBtn) {
            toggleBtn.innerHTML = '<i class="fas fa-sun"></i>';
            toggleBtn.title = '切换到浅色主题';
        }
        localStorage.setItem('preferred-theme', 'dark');
        console.log('已切换到深色主题');
    }
};

// 如果主题管理器加载失败，使用备用功能
setTimeout(() => {
    if (!window.themeManager) {
        console.warn('主题管理器加载失败，启用备用主题切换功能');
        const toggleBtn = document.querySelector('[data-theme-toggle]');
        if (toggleBtn) {
            toggleBtn.addEventListener('click', window.fallbackThemeToggle);
            console.log('备用主题切换功能已启用');
        }
    }
}, 2000);