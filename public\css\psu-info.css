.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.image-preview {
    transition: all 0.3s ease;
}

.image-preview:hover {
    transform: scale(1.05);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 全局表格修复 */
.table-wrapper {
    width: 100%;
    max-width: 100%;
    overflow-x: auto;
}

/* 确保内容不会被裁剪 */
* {
    box-sizing: border-box;
}

.container {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
}

/* 表格容器内部元素修复 */
.table-wrapper table {
    width: 100%;
}

/* 文本截断样式 */
.text-truncate {
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: calc(100% - 10px) !important;
    display: inline-block !important;
}

/* 表格样式 */
.table-compact td {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}

.model-column {
    min-width: 180px;
}

/* 标签样式 */
.spec-tag {
    padding: 3px 8px !important;
    border-radius: 4px !important;
    font-size: 0.75rem !important;
    font-weight: 500 !important;
    line-height: 1.5 !important;
    display: inline-block !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: fit-content !important;
    margin: 2px 4px 2px 0 !important;
}

.spec-tag.wattage {
    background-color: rgba(22, 101, 52, 0.1) !important;
    color: #166534 !important;
    border: 1px solid rgba(22, 101, 52, 0.2) !important;
}

.spec-tag.efficiency {
    background-color: rgba(76, 29, 149, 0.1) !important;
    color: #4c1d95 !important;
    border: 1px solid rgba(76, 29, 149, 0.2) !important;
}

.spec-tag.modular {
    background-color: rgba(180, 83, 9, 0.1) !important;
    color: #b45309 !important;
    border: 1px solid rgba(180, 83, 9, 0.2) !important;
}

/* 品牌标签 */
.psu-badge {
    padding: 4px 10px !important;
    font-weight: 600 !important;
    border-radius: 4px !important;
    display: inline-block !important;
}

/* 卡片式布局 */
.psu-card {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 0 16px 0 !important;
    box-sizing: border-box !important;
    border: none;
    border-radius: 10px;
    overflow: hidden;
    background-color: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    padding: 16px !important;
    display: flex;
    flex-direction: column;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.psu-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.psu-card-header {
    padding: 12px 16px !important;
    background-color: #f8f9fa;
    border-bottom: 1px solid #eee;
    width: 100% !important;
    box-sizing: border-box !important;
}

.psu-card-body {
    padding: 16px !important;
    min-height: 120px !important;
    width: 100% !important;
    box-sizing: border-box !important;
    overflow: hidden !important;
}

.psu-card-footer {
    padding: 12px !important;
    background-color: #fcfcfc;
    border-top: 1px solid #eee;
    width: 100% !important;
    box-sizing: border-box !important;
}

/* 标签组 */
.tag-group {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin: 8px 0;
    width: 100%;
    box-sizing: border-box !important;
}

/* 无分隔线表格 */
.no-dividers tbody tr {
    border-bottom: none !important;
}

.no-dividers tbody tr:not(:last-child) {
    margin-bottom: 8px !important;
}

/* 表格隔行变色 */
.no-dividers tbody tr:nth-child(even) {
    background-color: rgba(0, 0, 0, 0.02);
}

.dark .no-dividers tbody tr:nth-child(even) {
    background-color: rgba(255, 255, 255, 0.03);
}

/* 移动端优化 */
@media (max-width: 640px) {

    /* 页面容器修复 */
    .container {
        padding-left: 8px !important;
        padding-right: 8px !important;
    }

    /* 确保表格容器大小正确 */
    .table-container,
    .table-wrapper,
    .inline-block.min-w-full,
    .inline-block.min-w-full table {
        width: 100% !important;
        max-width: 100% !important;
        box-sizing: border-box !important;
        overflow-x: hidden !important;
    }

    /* 表格行适应屏幕宽度 */
    .table-modern.mobile-table tr {
        width: 100% !important;
        max-width: 100% !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        box-sizing: border-box !important;
    }

    /* 移动端卡片样式 */
    .psu-card {
        margin: 0 0 16px 0 !important;
        padding: 12px !important;
    }

    /* 移动端图片尺寸优化 */
    .psu-card img {
        width: 48px !important;
        height: 48px !important;
        object-fit: cover !important;
        border-radius: 6px !important;
        border: 1px solid #eee !important;
    }

    /* 按钮容器样式 */
    .psu-card .action-buttons {
        display: flex !important;
        justify-content: center !important;
        gap: 32px !important;
        margin-top: 12px !important;
    }

    /* 操作按钮样式 */
    .psu-card .action-buttons button {
        width: 42px !important;
        height: 42px !important;
        border-radius: 50% !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08) !important;
        transition: all 0.2s ease !important;
    }

    /* 移动端表格改为卡片布局 */
    .mobile-card-view thead {
        display: none !important;
    }

    .mobile-card-view tbody {
        display: block !important;
        width: 100% !important;
    }

    .mobile-card-view tr {
        display: block !important;
        margin-bottom: 1rem !important;
        border-radius: 8px !important;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1) !important;
        overflow: hidden !important;
    }

    .mobile-card-view td {
        display: block !important;
        text-align: right !important;
        position: relative !important;
        padding: 0.75rem !important;
    }

    .mobile-card-view td:before {
        content: attr(data-label);
        float: left;
        font-weight: bold;
    }

    /* 基于表格布局的卡片设计 */
    table.card-style {
        border-spacing: 0 10px !important;
        border-collapse: separate !important;
    }

    table.card-style tr {
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05) !important;
        border-radius: 8px !important;
        background-color: white !important;
    }

    table.card-style td:first-child {
        border-radius: 8px 0 0 8px !important;
    }

    table.card-style td:last-child {
        border-radius: 0 8px 8px 0 !important;
    }

    /* 修复移动端搜索和过滤器布局 */
    .flex.flex-wrap.items-center.gap-2 {
        flex-direction: column !important;
        align-items: stretch !important;
    }

    .flex.flex-wrap.items-center.gap-2>div {
        width: 100% !important;
        margin-bottom: 8px !important;
    }

    .flex.mt-2.sm\:mt-0.gap-2.w-full {
        flex-wrap: wrap !important;
    }

    .flex.mt-2.sm\:mt-0.gap-2.w-full select,
    .flex.mt-2.sm\:mt-0.gap-2.w-full button {
        flex: 1 1 auto !important;
        min-width: 0 !important;
    }

    /* 修复重置按钮超出边框问题 */
    #resetFilters {
        padding-left: 8px !important;
        padding-right: 8px !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        font-size: 0.75rem !important;
    }
}

/* 暗色模式样式 */
.dark {
    background-color: #1a1a1a;
    color: #e0e0e0;
}

.dark .bg-white {
    background-color: #2a2a2a !important;
}

.dark .bg-gray-50 {
    background-color: #1a1a1a !important;
}

.dark .text-gray-700 {
    color: #d1d1d1 !important;
}

.dark .text-gray-600 {
    color: #b0b0b0 !important;
}

.dark .text-gray-800 {
    color: #e0e0e0 !important;
}

.dark .text-gray-900 {
    color: #f0f0f0 !important;
}

.dark .text-gray-500 {
    color: #a0a0a0 !important;
}

.dark .border-gray-200,
.dark .border-gray-300 {
    border-color: #3a3a3a !important;
}

.dark .shadow-md {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3) !important;
}

.dark table.card-style tr {
    background-color: #1e1e1e !important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2) !important;
}

.dark .spec-tag.wattage {
    background-color: rgba(22, 101, 52, 0.2) !important;
    color: #34d399 !important;
    border-color: rgba(22, 101, 52, 0.4) !important;
}

.dark .spec-tag.efficiency {
    background-color: rgba(76, 29, 149, 0.2) !important;
    color: #a78bfa !important;
    border-color: rgba(76, 29, 149, 0.4) !important;
}

.dark .spec-tag.modular {
    background-color: rgba(180, 83, 9, 0.2) !important;
    color: #fbbf24 !important;
    border-color: rgba(180, 83, 9, 0.4) !important;
}

.dark .psu-badge {
    background-color: rgba(59, 130, 246, 0.2) !important;
    color: #60a5fa !important;
    border-color: rgba(59, 130, 246, 0.4) !important;
}

.dark .bg-gray-600 {
    background-color: #4b5563 !important;
}

.dark .hover\:bg-gray-700:hover {
    background-color: #374151 !important;
}

.dark .bg-gray-200 {
    background-color: #374151 !important;
}

.dark .text-blue-600 {
    color: #60a5fa !important;
}

.dark .text-green-600 {
    color: #34d399 !important;
}

.dark .text-red-600 {
    color: #f87171 !important;
}

.dark .hover\:bg-gray-50:hover {
    background-color: #333333 !important;
}

/* 修复深色模式下输入框背景色 */
.dark input,
.dark select,
.dark textarea,
.dark .bg-gray-50 {
    background-color: #2a2a2a !important;
    color: #e0e0e0 !important;
    border-color: #3a3a3a !important;
}

.dark input::placeholder,
.dark select::placeholder,
.dark textarea::placeholder {
    color: #909090 !important;
}

.dark input:focus,
.dark select:focus,
.dark textarea:focus {
    border-color: #4f46e5 !important;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.3) !important;
}

.dark input[type="file"]::file-selector-button {
    background-color: #4b5563 !important;
    color: #e0e0e0 !important;
    border-color: #3a3a3a !important;
}

.dark input[type="file"]:hover::file-selector-button {
    background-color: #374151 !important;
}

/* 修复深色模式下特定表单元素 */
.dark .form-select,
.dark .form-input,
.dark .form-textarea,
.dark input[type="text"],
.dark input[type="number"],
.dark input[type="email"],
.dark input[type="search"],
.dark input[type="password"] {
    background-color: #2a2a2a !important;
    color: #e0e0e0 !important;
}

.dark option {
    background-color: #2a2a2a;
    color: #e0e0e0;
}

/* 主题切换过渡 */
body,
div,
table,
th,
td,
input,
select,
button {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

/* 主题切换按钮样式 */
.theme-toggle-btn {
    position: relative;
    width: 32px;
    height: 32px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 0;
    margin: 0;
}

.theme-toggle-btn:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.dark .theme-toggle-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.theme-toggle-icon {
    font-size: 1rem;
    transition: transform 0.5s ease;
}

.theme-toggle-btn:active .theme-toggle-icon {
    transform: rotate(360deg);
}

/* 深色模式下卡片高级样式 */
.dark .psu-card-new {
    position: relative;
    overflow: hidden;
    background-color: #1e1e1e;
    background-image:
        radial-gradient(circle at 100% 0%, transparent 12px, #1e1e1e 13px),
        radial-gradient(circle at 0% 100%, transparent 12px, #1e1e1e 13px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.dark .psu-card-new::before {
    content: '';
    position: absolute;
    left: -5px;
    top: 20px;
    height: 10px;
    width: 5px;
    background-color: #2d3748;
    border-radius: 0 5px 5px 0;
}

.dark .psu-card-new::after {
    content: '';
    position: absolute;
    right: -5px;
    bottom: 20px;
    height: 10px;
    width: 5px;
    background-color: #2d3748;
    border-radius: 5px 0 0 5px;
}

/* 亮色模式下卡片高级样式 */
.light .psu-card-new {
    position: relative;
    overflow: hidden;
    background-color: white;
    background-image:
        radial-gradient(circle at 100% 0%, transparent 12px, white 13px),
        radial-gradient(circle at 0% 100%, transparent 12px, white 13px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
    border: 1px solid #f0f0f0;
}

.light .psu-card-new::before {
    content: '';
    position: absolute;
    left: -5px;
    top: 20px;
    height: 10px;
    width: 5px;
    background-color: #3b82f6;
    border-radius: 0 5px 5px 0;
    opacity: 0.7;
}

.light .psu-card-new::after {
    content: '';
    position: absolute;
    right: -5px;
    bottom: 20px;
    height: 10px;
    width: 5px;
    background-color: #3b82f6;
    border-radius: 5px 0 0 5px;
    opacity: 0.7;
}