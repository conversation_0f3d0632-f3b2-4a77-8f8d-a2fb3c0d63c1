let currentPage = 1;
let totalPages = 1;
const pageSize = 10;

// 页面加载时获取记录
document.addEventListener('DOMContentLoaded', function() {
    loadRecords();
});

// 加载记录
async function loadRecords(page = 1) {
    try {
        const searchTerm = document.getElementById('searchInput').value;
        const url = `/api/excel-template/records?page=${page}&limit=${pageSize}&search=${encodeURIComponent(searchTerm)}`;

        const response = await fetch(url, {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });

        if (!response.ok) {
            throw new Error('获取数据失败');
        }

        const result = await response.json();

        if (result.success) {
            displayRecords(result.data);
            updatePagination(result.pagination);
        } else {
            throw new Error(result.message);
        }
    } catch (error) {
        console.error('加载记录失败:', error);
        alert('加载记录失败: ' + error.message);
    }
}

// 显示记录
function displayRecords(data) {
    const tbody = document.getElementById('recordsTableBody');
    tbody.innerHTML = '';

    if (data.length === 0) {
        tbody.innerHTML = `
                    <tr>
                        <td colspan="6" class="px-4 py-8 text-center text-gray-500">
                            <i class="fas fa-inbox text-4xl mb-2"></i>
                            <p>暂无记录</p>
                        </td>
                    </tr>
                `;
        return;
    }

    data.forEach(record => {
        const row = document.createElement('tr');
        row.className = 'hover:bg-gray-50';
        row.innerHTML = `
                    <td class="px-4 py-3 text-sm text-gray-900">${record.id}</td>
                    <td class="px-4 py-3 text-sm text-gray-900">${record.template_name}</td>
                    <td class="px-4 py-3 text-sm text-gray-900">
                        <div class="max-w-xs truncate" title="${record.remarks || ''}">${record.remarks || '-'}</div>
                    </td>
                    <td class="px-4 py-3 text-sm text-gray-900">${record.created_by || '未知'}</td>
                    <td class="px-4 py-3 text-sm text-gray-900">${formatDateTime(record.created_at)}</td>
                    <td class="px-4 py-3 text-sm">
                        <button onclick="viewDetail(${record.id})" class="text-blue-600 hover:text-blue-800 mr-2">
                            <i class="fas fa-eye mr-1"></i>查看
                        </button>
                        <button onclick="loadToEditor(${record.id})" class="text-purple-600 hover:text-purple-800 mr-2">
                            <i class="fas fa-edit mr-1"></i>引用
                        </button>
                        <button onclick="downloadFile(${record.id})" class="text-green-600 hover:text-green-800 mr-2">
                            <i class="fas fa-download mr-1"></i>下载
                        </button>
                        <button onclick="deleteRecord(${record.id})" class="text-red-600 hover:text-red-800">
                            <i class="fas fa-trash mr-1"></i>删除
                        </button>
                    </td>
                `;
        tbody.appendChild(row);
    });
}

// 更新分页信息
function updatePagination(pagination) {
    currentPage = pagination.page;
    totalPages = pagination.totalPages;

    document.getElementById('currentPage').textContent = currentPage;
    document.getElementById('totalCount').textContent = pagination.total;

    const start = (currentPage - 1) * pageSize + 1;
    const end = Math.min(currentPage * pageSize, pagination.total);
    document.getElementById('pageInfo').textContent = `${start}-${end}`;

    document.getElementById('prevBtn').disabled = currentPage <= 1;
    document.getElementById('nextBtn').disabled = currentPage >= totalPages;
}

// 翻页
function changePage(direction) {
    const newPage = currentPage + direction;
    if (newPage >= 1 && newPage <= totalPages) {
        loadRecords(newPage);
    }
}

// 查看详情
async function viewDetail(id) {
    try {
        const response = await fetch(`/api/excel-template/records/${id}`, {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });

        if (!response.ok) {
            throw new Error('获取详情失败');
        }

        const result = await response.json();

        if (result.success) {
            displayDetail(result.data);
            document.getElementById('detailModal').style.display = 'block';
        } else {
            throw new Error(result.message);
        }
    } catch (error) {
        console.error('获取详情失败:', error);
        alert('获取详情失败: ' + error.message);
    }
}

// 显示详情
function displayDetail(data) {
    const modalContent = document.getElementById('modalContent');

    let html = `
                <div class="space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div><strong>模板名称:</strong> ${data.template_name}</div>
                        <div><strong>创建人:</strong> ${data.created_by || '未知'}</div>
                        <div><strong>创建时间:</strong> ${formatDateTime(data.created_at)}</div>
                        <div><strong>更新时间:</strong> ${formatDateTime(data.updated_at)}</div>
                    </div>
                    
                    <div>
                        <strong>备注信息:</strong>
                        <div class="mt-2 p-3 bg-gray-50 rounded border">
                            ${data.remarks || '无备注'}
                        </div>
                    </div>
                    
                    <div>
                        <strong>数据预览:</strong>
                        <div class="mt-2 overflow-x-auto">
                            <table class="w-full border border-gray-300 text-sm">
                                <tbody>
            `;

    if (data.data_json && Array.isArray(data.data_json)) {
        data.data_json.slice(0, 10).forEach((row, rowIndex) => {
            html += '<tr>';
            if (Array.isArray(row)) {
                row.slice(0, 8).forEach(cell => {
                    html += `<td class="border border-gray-300 px-2 py-1">${cell || ''}</td>`;
                });
            }
            html += '</tr>';
        });

        if (data.data_json.length > 10) {
            html += '<tr><td colspan="8" class="border border-gray-300 px-2 py-1 text-center text-gray-500">... 还有更多数据 ...</td></tr>';
        }
    } else {
        html += '<tr><td class="border border-gray-300 px-2 py-1 text-center text-gray-500">无数据</td></tr>';
    }

    html += `
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;

    modalContent.innerHTML = html;
}

// 下载文件
async function downloadFile(id) {
    try {
        const response = await fetch(`/api/excel-template/download/${id}`, {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });

        if (!response.ok) {
            throw new Error('下载失败');
        }

        // 获取文件名
        const contentDisposition = response.headers.get('Content-Disposition');
        let filename = 'template.xlsx';
        if (contentDisposition) {
            const matches = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
            if (matches && matches[1]) {
                filename = decodeURIComponent(matches[1].replace(/['"]/g, ''));
            }
        }

        // 下载文件
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

    } catch (error) {
        console.error('下载文件失败:', error);
        alert('下载文件失败: ' + error.message);
    }
}

// 引用数据到编辑器
async function loadToEditor(id) {
    try {
        const response = await fetch(`/api/excel-template/records/${id}`, {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });

        if (!response.ok) {
            throw new Error('获取数据失败');
        }

        const result = await response.json();

        if (result.success && result.data.data_json) {
            // 将数据存储到localStorage，供编辑器页面使用
            const editorData = {
                templateName: result.data.template_name,
                remarks: result.data.remarks,
                data: result.data.data_json,
                sheetName: 'Sheet1',
                isReference: true,
                originalId: id
            };

            localStorage.setItem('excelEditorData', JSON.stringify(editorData));

            // 跳转到编辑器页面
            window.location.href = '/excel-template-simple.html?ref=' + id;
        } else {
            throw new Error(result.message || '数据格式错误');
        }
    } catch (error) {
        console.error('引用数据失败:', error);
        alert('引用数据失败: ' + error.message);
    }
}

// 删除记录
async function deleteRecord(id) {
    if (!confirm('确定要删除这条记录吗？此操作不可恢复。')) {
        return;
    }

    try {
        const response = await fetch(`/api/excel-template/records/${id}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });

        if (!response.ok) {
            throw new Error('删除失败');
        }

        const result = await response.json();

        if (result.success) {
            alert('删除成功');
            loadRecords(currentPage);
        } else {
            throw new Error(result.message);
        }
    } catch (error) {
        console.error('删除记录失败:', error);
        alert('删除记录失败: ' + error.message);
    }
}

// 关闭模态框
function closeModal() {
    document.getElementById('detailModal').style.display = 'none';
}

// 刷新数据
function refreshData() {
    loadRecords(currentPage);
}

// 跳转到编辑器页面
function goToEditor() {
    window.location.href = '/excel-template-simple.html';
}

// 返回上一页
function goBack() {
    window.history.back();
}

// 格式化日期时间
function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

// 点击模态框外部关闭
window.onclick = function(event) {
    const modal = document.getElementById('detailModal');
    if (event.target === modal) {
        closeModal();
    }
}

// 搜索框回车事件
document.getElementById('searchInput').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        loadRecords(1);
    }
});