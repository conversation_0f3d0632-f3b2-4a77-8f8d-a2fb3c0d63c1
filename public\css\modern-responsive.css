/**
 * 现代化响应式样式系统
 * 基于Tailwind CSS，支持亮色/深色主题切换
 * 移动端优先的响应式设计
 */

/* CSS自定义属性 - 主题颜色 */
:root {
    /* 亮色主题 */
    --color-primary: #3B82F6;
    --color-secondary: #6B7280;
    --color-success: #10B981;
    --color-warning: #F59E0B;
    --color-error: #EF4444;
    --color-background: #FFFFFF;
    --color-surface: #F9FAFB;
    --color-text: #111827;
    
    /* 间距和尺寸 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* 圆角 */
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* 过渡动画 */
    --transition-fast: 150ms ease;
    --transition-normal: 300ms ease;
    --transition-slow: 500ms ease;
    
    /* 断点 */
    --breakpoint-sm: 640px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 1024px;
    --breakpoint-xl: 1280px;
    --breakpoint-2xl: 1536px;
}

/* 深色主题 */
.dark {
    --color-primary: #60A5FA;
    --color-secondary: #9CA3AF;
    --color-success: #34D399;
    --color-warning: #FBBF24;
    --color-error: #F87171;
    --color-background: #111827;
    --color-surface: #1F2937;
    --color-text: #F9FAFB;
    
    /* 深色主题的阴影 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
}

/* 基础重置和全局样式 */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: var(--color-text);
    background-color: var(--color-background);
    transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--color-surface);
    border-radius: var(--radius-md);
}

::-webkit-scrollbar-thumb {
    background: var(--color-secondary);
    border-radius: var(--radius-md);
    transition: background-color var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--color-primary);
}

/* Firefox滚动条 */
* {
    scrollbar-width: thin;
    scrollbar-color: var(--color-secondary) var(--color-surface);
}

/* 响应式工具类 */
.container-responsive {
    width: 100%;
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

@media (min-width: 640px) {
    .container-responsive {
        padding: 0 var(--spacing-lg);
    }
}

@media (min-width: 1024px) {
    .container-responsive {
        padding: 0 var(--spacing-xl);
    }
}

/* 网格系统 */
.grid-responsive {
    display: grid;
    gap: var(--spacing-md);
    grid-template-columns: 1fr;
}

@media (min-width: 768px) {
    .grid-responsive {
        grid-template-columns: 1fr 2fr;
        gap: var(--spacing-lg);
    }
}

@media (min-width: 1024px) {
    .grid-responsive {
        grid-template-columns: 1fr 3fr;
        gap: var(--spacing-xl);
    }
}

/* 卡片组件 */
.card {
    background-color: var(--color-surface);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    padding: var(--spacing-lg);
    transition: all var(--transition-normal);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.dark .card {
    border-color: rgba(255, 255, 255, 0.1);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* 按钮组件 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-weight: 500;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    min-height: 44px; /* 触摸友好 */
    font-size: 0.875rem;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background-color: var(--color-primary);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background-color: color-mix(in srgb, var(--color-primary) 85%, black);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background-color: var(--color-secondary);
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background-color: color-mix(in srgb, var(--color-secondary) 85%, black);
}

.btn-success {
    background-color: var(--color-success);
    color: white;
}

.btn-warning {
    background-color: var(--color-warning);
    color: white;
}

.btn-error {
    background-color: var(--color-error);
    color: white;
}

.btn-outline {
    background-color: transparent;
    border: 2px solid var(--color-primary);
    color: var(--color-primary);
}

.btn-outline:hover:not(:disabled) {
    background-color: var(--color-primary);
    color: white;
}

/* 表单组件 */
.form-group {
    margin-bottom: var(--spacing-md);
}

.form-label {
    display: block;
    font-weight: 500;
    margin-bottom: var(--spacing-sm);
    color: var(--color-text);
}

.form-input {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-md);
    background-color: var(--color-background);
    color: var(--color-text);
    font-size: 1rem;
    transition: all var(--transition-fast);
    min-height: 44px; /* 触摸友好 */
}

.dark .form-input {
    border-color: rgba(255, 255, 255, 0.2);
}

.form-input:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input::placeholder {
    color: var(--color-secondary);
}

/* 表格组件 */
.table-responsive {
    overflow-x: auto;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
}

.table {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--color-surface);
}

.table th,
.table td {
    padding: var(--spacing-sm) var(--spacing-md);
    text-align: left;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.dark .table th,
.dark .table td {
    border-color: rgba(255, 255, 255, 0.1);
}

.table th {
    background-color: rgba(0, 0, 0, 0.05);
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.dark .table th {
    background-color: rgba(255, 255, 255, 0.05);
}

.table tbody tr:hover {
    background-color: rgba(59, 130, 246, 0.05);
}

/* 导航栏 */
.navbar {
    background-color: var(--color-surface);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 50;
}

.dark .navbar {
    border-color: rgba(255, 255, 255, 0.1);
}

/* 主题切换按钮 */
.theme-toggle-btn {
    position: relative;
    overflow: hidden;
    border-radius: 50%;
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: transparent;
    border: none;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.theme-toggle-btn:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.dark .theme-toggle-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.theme-toggle-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-slow);
}

.theme-toggle-btn:hover::before {
    left: 100%;
}

/* 加载动画 */
.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-top: 2px solid var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 移动端优化 */
@media (max-width: 767px) {
    .mobile-hidden {
        display: none !important;
    }
    
    .mobile-full-width {
        width: 100% !important;
    }
    
    .mobile-stack {
        flex-direction: column !important;
    }
    
    .mobile-center {
        text-align: center !important;
    }
    
    /* 移动端表格优化 */
    .table-mobile-stack {
        display: block;
    }
    
    .table-mobile-stack thead {
        display: none;
    }
    
    .table-mobile-stack tbody,
    .table-mobile-stack tr,
    .table-mobile-stack td {
        display: block;
    }
    
    .table-mobile-stack tr {
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: var(--radius-md);
        margin-bottom: var(--spacing-md);
        padding: var(--spacing-md);
    }
    
    .table-mobile-stack td {
        border: none;
        padding: var(--spacing-xs) 0;
        position: relative;
        padding-left: 50%;
    }
    
    .table-mobile-stack td::before {
        content: attr(data-label);
        position: absolute;
        left: 0;
        width: 45%;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.75rem;
        color: var(--color-secondary);
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .btn,
    .form-input,
    .theme-toggle-btn {
        min-height: 48px; /* 更大的触摸目标 */
    }
    
    /* 移除悬停效果 */
    .card:hover {
        transform: none;
        box-shadow: var(--shadow-md);
    }
    
    .btn:hover {
        transform: none;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .btn,
    .form-input,
    .card {
        border-width: 2px;
    }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    html {
        scroll-behavior: auto;
    }
}
