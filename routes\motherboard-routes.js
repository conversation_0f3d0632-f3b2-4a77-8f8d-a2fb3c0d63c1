const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const sharp = require('sharp');
const db = require('../db');

// 配置主板图片上传
const motherboardUpload = multer({
    storage: multer.diskStorage({
        destination: function (req, file, cb) {
            const uploadDir = path.join(__dirname, '../public/uploads/motherboards');
            if (!fs.existsSync(uploadDir)) {
                fs.mkdirSync(uploadDir, { recursive: true });
            }
            cb(null, uploadDir);
        },
        filename: function (req, file, cb) {
            const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
            const extension = path.extname(file.originalname);
            cb(null, 'motherboard-' + uniqueSuffix + extension);
        }
    })
});

// 获取主板列表
router.get('/', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const offset = (page - 1) * limit;
        const search = req.query.search || '';
        const brand = req.query.brand || '';
        const chipset = req.query.chipset || '';

        console.log('查询参数:', req.query);

        // 构建查询条件
        let conditions = [];
        let params = [];

        if (search) {
            conditions.push(`(
                motherboardModel LIKE ? OR 
                brand LIKE ? OR 
                motherboardType LIKE ? OR 
                chipset LIKE ? OR 
                formFactor LIKE ? OR
                memoryCapacity LIKE ? OR
                memoryGeneration LIKE ? OR
                memoryFrequency LIKE ? OR
                biosVersion LIKE ? OR
                cpuSocket LIKE ? OR
                cpuModel LIKE ? OR
                powerPhases LIKE ? OR
                vrmCooling LIKE ? OR
                powerConnector LIKE ? OR
                notes LIKE ?
            )`);
            const searchParam = `%${search}%`;
            params.push(
                searchParam, searchParam, searchParam, searchParam, searchParam,
                searchParam, searchParam, searchParam, searchParam, searchParam,
                searchParam, searchParam, searchParam, searchParam, searchParam
            );
        }

        if (brand && brand !== 'all') {
            conditions.push(`brand = ?`);
            params.push(brand);
        }
        
        if (chipset && chipset !== 'all') {
            conditions.push(`chipset = ?`);
            params.push(chipset);
        }

        const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

        // 计算总记录数
        const [countResults] = await db.query(
            `SELECT COUNT(*) as total FROM motherboards ${whereClause}`,
            params
        );
        const total = countResults[0].total;

        // 获取分页数据
        const [rows] = await db.query(
            `SELECT * FROM motherboards ${whereClause} ORDER BY id DESC LIMIT ? OFFSET ?`,
            [...params, limit, offset]
        );

        res.json({
            items: rows,
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit)
        });
    } catch (error) {
        console.error('获取主板列表错误:', error);
        res.status(500).json({ message: '服务器错误' });
    }
});

// 获取单个主板信息
router.get('/:id', async (req, res) => {
    try {
        const [rows] = await db.query('SELECT * FROM motherboards WHERE id = ?', [req.params.id]);

        if (rows.length === 0) {
            return res.status(404).json({ message: '未找到主板信息' });
        }

        res.json(rows[0]);
    } catch (error) {
        console.error('获取主板详情错误:', error);
        res.status(500).json({ message: '服务器错误' });
    }
});

// 创建主板信息
router.post('/', motherboardUpload.single('image'), async (req, res) => {
    try {
        console.log('创建主板请求体:', req.body);
        console.log('上传文件:', req.file);
        
        const {
            motherboardModel, brand, motherboardType, chipset, formFactor,
            memoryCapacity, memoryGeneration, memoryFrequency,
            biosVersion, releaseDate, cpuSocket, cpuModel, maxTdp, overclockSupport,
            pcieSlots, m2Slots, sataSlots, usbSlots, wifiSupport,
            powerPhases, vrmCooling, powerConnector, warranty, notes
        } = req.body;

        // 检查必填字段
        if (!motherboardModel || !brand) {
            return res.status(400).json({ message: '主板型号和品牌为必填项' });
        }

        // 图片URL处理和WebP转换
        let imageUrl = null;
        
        if (req.file) {
            // 检查是否为图片类型
            if (req.file.mimetype.startsWith('image/')) {
                // 转换为WebP格式
                const originalPath = req.file.path;
                const fileNameWithoutExt = path.basename(req.file.filename, path.extname(req.file.filename));
                const webpFilename = `${fileNameWithoutExt}.webp`;
                const webpPath = path.join(path.dirname(originalPath), webpFilename);
                
                try {
                    // 使用sharp转换为WebP格式
                    await sharp(originalPath)
                        .webp({ quality: 100, lossless: true }) // 无损压缩，只转换格式
                        .toFile(webpPath);
                    
                    // 删除原始文件
                    fs.unlinkSync(originalPath);
                    
                    // 更新图片URL为WebP文件
                    imageUrl = `/uploads/motherboards/${webpFilename}`;
                    console.log(`主板图片已转换为WebP格式: ${webpFilename}`);
                } catch (err) {
                    console.error('WebP转换失败:', err);
                    // 如果转换失败，仍使用原始文件
                    imageUrl = `/uploads/motherboards/${req.file.filename}`;
                }
            } else {
                // 非图片类型，使用原始路径
                imageUrl = `/uploads/motherboards/${req.file.filename}`;
            }
        }

        // 处理可能为空的整数字段
        const parsedPcieSlots = pcieSlots === '' ? null : parseInt(pcieSlots) || null;
        const parsedM2Slots = m2Slots === '' ? null : parseInt(m2Slots) || null;
        const parsedSataSlots = sataSlots === '' ? null : parseInt(sataSlots) || null;
        const parsedUsbSlots = usbSlots === '' ? null : parseInt(usbSlots) || null;
        const parsedWarranty = warranty === '' ? null : parseInt(warranty) || null;

        // 插入数据库
        const [result] = await db.query(
            `INSERT INTO motherboards (
                motherboardModel, brand, motherboardType, chipset, formFactor,
                memoryCapacity, memoryGeneration, memoryFrequency,
                biosVersion, releaseDate, cpuSocket, cpuModel, maxTdp, overclockSupport,
                pcieSlots, m2Slots, sataSlots, usbSlots, wifiSupport,
                powerPhases, vrmCooling, powerConnector, warranty, notes, imageUrl
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [
                motherboardModel, brand, motherboardType, chipset, formFactor,
                memoryCapacity, memoryGeneration, memoryFrequency,
                biosVersion, releaseDate, cpuSocket, cpuModel, maxTdp, overclockSupport,
                parsedPcieSlots, parsedM2Slots, parsedSataSlots, parsedUsbSlots, wifiSupport,
                powerPhases, vrmCooling, powerConnector, parsedWarranty, notes, imageUrl
            ]
        );

        // 获取新插入的记录
        const [newMotherboard] = await db.query('SELECT * FROM motherboards WHERE id = ?', [result.insertId]);

        res.status(201).json(newMotherboard[0]);
    } catch (error) {
        console.error('创建主板信息错误:', error);
        res.status(500).json({ message: '服务器错误' });
    }
});

// 更新主板信息
router.put('/:id', motherboardUpload.single('image'), async (req, res) => {
    try {
        const id = req.params.id;
        
        console.log('更新主板请求体:', req.body);
        console.log('上传文件:', req.file);
        
        // 检查主板是否存在
        const [existingRows] = await db.query('SELECT * FROM motherboards WHERE id = ?', [id]);
        
        if (existingRows.length === 0) {
            return res.status(404).json({ message: '未找到主板信息' });
        }

        const existingMotherboard = existingRows[0];
        
        const {
            motherboardModel, brand, motherboardType, chipset, formFactor,
            memoryCapacity, memoryGeneration, memoryFrequency,
            biosVersion, releaseDate, cpuSocket, cpuModel, maxTdp, overclockSupport,
            pcieSlots, m2Slots, sataSlots, usbSlots, wifiSupport,
            powerPhases, vrmCooling, powerConnector, warranty, notes
        } = req.body;

        // 检查必填字段
        if (!motherboardModel || !brand) {
            return res.status(400).json({ message: '主板型号和品牌为必填项' });
        }

        // 处理可能为空的整数字段
        const parsedPcieSlots = pcieSlots === '' ? null : parseInt(pcieSlots) || null;
        const parsedM2Slots = m2Slots === '' ? null : parseInt(m2Slots) || null;
        const parsedSataSlots = sataSlots === '' ? null : parseInt(sataSlots) || null;
        const parsedUsbSlots = usbSlots === '' ? null : parseInt(usbSlots) || null;
        const parsedWarranty = warranty === '' ? null : parseInt(warranty) || null;

        // 图片URL处理
        let imageUrl = existingMotherboard.imageUrl;
        if (req.file) {
            // 如果上传了新图片，删除旧图片
            if (existingMotherboard.imageUrl) {
                const oldImagePath = path.join(__dirname, '../public', existingMotherboard.imageUrl);
                if (fs.existsSync(oldImagePath)) {
                    fs.unlinkSync(oldImagePath);
                }
            }
            
            // 检查是否为图片类型并转换为WebP
            if (req.file.mimetype.startsWith('image/')) {
                // 转换为WebP格式
                const originalPath = req.file.path;
                const fileNameWithoutExt = path.basename(req.file.filename, path.extname(req.file.filename));
                const webpFilename = `${fileNameWithoutExt}.webp`;
                const webpPath = path.join(path.dirname(originalPath), webpFilename);
                
                try {
                    // 使用sharp转换为WebP格式
                    await sharp(originalPath)
                        .webp({ quality: 100, lossless: true }) // 无损压缩，只转换格式
                        .toFile(webpPath);
                    
                    // 删除原始文件
                    fs.unlinkSync(originalPath);
                    
                    // 更新图片URL为WebP文件
                    imageUrl = `/uploads/motherboards/${webpFilename}`;
                    console.log(`主板图片已更新并转换为WebP格式: ${webpFilename}`);
                } catch (err) {
                    console.error('WebP转换失败:', err);
                    // 如果转换失败，仍使用原始文件
                    imageUrl = `/uploads/motherboards/${req.file.filename}`;
                }
            } else {
                // 非图片类型，使用原始路径
                imageUrl = `/uploads/motherboards/${req.file.filename}`;
            }
        }

        // 更新数据库
        await db.query(
            `UPDATE motherboards SET
                motherboardModel = ?, brand = ?, motherboardType = ?, chipset = ?, formFactor = ?,
                memoryCapacity = ?, memoryGeneration = ?, memoryFrequency = ?,
                biosVersion = ?, releaseDate = ?, cpuSocket = ?, cpuModel = ?, maxTdp = ?, overclockSupport = ?,
                pcieSlots = ?, m2Slots = ?, sataSlots = ?, usbSlots = ?, wifiSupport = ?,
                powerPhases = ?, vrmCooling = ?, powerConnector = ?, warranty = ?, notes = ?, imageUrl = ?
            WHERE id = ?`,
            [
                motherboardModel, brand, motherboardType, chipset, formFactor,
                memoryCapacity, memoryGeneration, memoryFrequency,
                biosVersion, releaseDate, cpuSocket, cpuModel, maxTdp, overclockSupport,
                parsedPcieSlots, parsedM2Slots, parsedSataSlots, parsedUsbSlots, wifiSupport,
                powerPhases, vrmCooling, powerConnector, parsedWarranty, notes, imageUrl,
                id
            ]
        );

        // 获取更新后的记录
        const [updatedMotherboard] = await db.query('SELECT * FROM motherboards WHERE id = ?', [id]);

        res.json(updatedMotherboard[0]);
    } catch (error) {
        console.error('更新主板信息错误:', error);
        res.status(500).json({ message: '服务器错误' });
    }
});

// 删除主板信息
router.delete('/:id', async (req, res) => {
    try {
        const id = req.params.id;
        
        // 检查主板是否存在
        const [existingRows] = await db.query('SELECT * FROM motherboards WHERE id = ?', [id]);
        
        if (existingRows.length === 0) {
            return res.status(404).json({ message: '未找到主板信息' });
        }

        const existingMotherboard = existingRows[0];
        
        // 删除关联图片
        if (existingMotherboard.imageUrl) {
            const imagePath = path.join(__dirname, '../public', existingMotherboard.imageUrl);
            if (fs.existsSync(imagePath)) {
                fs.unlinkSync(imagePath);
            }
        }

        // 从数据库删除记录
        await db.query('DELETE FROM motherboards WHERE id = ?', [id]);

        res.status(204).end();
    } catch (error) {
        console.error('删除主板信息错误:', error);
        res.status(500).json({ message: '服务器错误' });
    }
});

module.exports = router;
