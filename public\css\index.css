/* 主题切换动画 */
.theme-transition {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* 主题切换按钮样式 */
#themeToggle {
    position: relative;
    overflow: hidden;
}

#themeToggle:hover {
    transform: scale(1.05);
}

#themeIcon {
    transition: transform 0.3s ease;
}

.dark #themeIcon {
    transform: rotate(180deg);
}

.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.image-preview {
    transition: all 0.3s ease;
}

.image-preview:hover {
    transform: scale(1.05);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 移动端优化 */
@media (max-width: 640px) {
    .mobile-stack {
        flex-direction: column;
    }

    .mobile-full-width {
        width: 100%;
    }

    .mobile-p-2 {
        padding: 0.5rem;
    }

    .mobile-text-sm {
        font-size: 0.875rem;
        line-height: 1.25rem;
    }

    .mobile-hidden {
        display: none;
    }

    .mobile-flex-col {
        flex-direction: column;
    }

    .mobile-w-full {
        width: 100%;
    }

    .mobile-mt-2 {
        margin-top: 0.5rem;
    }
}

/* 暗色主题特定样式 */
.dark {
    color-scheme: dark;
}

/* 图表在暗色主题下的样式调整 */
.dark canvas {
    filter: brightness(0.9);
}

/* 滚动条样式 */
.dark ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.dark ::-webkit-scrollbar-track {
    background: #1F2937;
}

.dark ::-webkit-scrollbar-thumb {
    background: #4B5563;
    border-radius: 4px;
}

.dark ::-webkit-scrollbar-thumb:hover {
    background: #6B7280;
}

/* 选择框选项在暗色主题下的样式 */
.dark select option {
    background-color: #1F2937;
    color: #F9FAFB;
}

/* 表格行悬停效果 */
.dark tbody tr:hover {
    background-color: #374151 !important;
}

/* 确保表格中的图片容器也有正确的背景 */
.dark tbody tr td img {
    background-color: transparent;
}

/* 模态框内容优化 */
.dark .modal-content {
    background-color: #1F2937;
}

/* 输入框焦点状态优化 */
.dark input:focus,
.dark select:focus,
.dark textarea:focus {
    background-color: #374151;
    border-color: #6366F1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* 按钮样式优化 */
.dark .btn-secondary {
    background-color: #374151;
    color: #D1D5DB;
    border-color: #4B5563;
}

.dark .btn-secondary:hover {
    background-color: #4B5563;
    color: #F9FAFB;
}

/* 移动端样式 */
@media (max-width: 640px) {
    .mobile-hidden {
        display: none;
    }

    /* 配件明细表格样式 */
    #detailPartsTableBody td,
    #editPartsTableBody td {
        padding: 0.5rem;
        font-size: 0.875rem;
    }

    /* 输入框样式 */
    .part-type,
    .part-model,
    .part-quantity,
    .part-price {
        width: 100%;
        min-width: 60px;
    }

    /* 表格滚动容器 */
    .overflow-x-auto {
        margin: 0 -1rem;
        padding: 0 1rem;
    }

    /* 配件明细表格布局 */
    .min-w-full {
        min-width: 600px;
        /* 确保在移动端可以横向滚动 */
    }
}