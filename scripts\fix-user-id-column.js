#!/usr/bin/env node

/**
 * 修复user_id字段类型问题
 * 将varchar类型的user_id转换为int类型并正确关联用户
 */

const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
    host: 'localhost',
    user: 'root',
    password: 'root',
    database: 'delivery_system'
};

async function fixUserIdColumn() {
    let connection;
    
    try {
        console.log('🔧 开始修复user_id字段类型问题...\n');
        
        // 连接数据库
        connection = await mysql.createConnection(dbConfig);
        console.log('✅ 数据库连接成功');
        
        // 1. 检查当前表结构
        console.log('\n📊 检查当前表结构...');
        
        const [priceRecordsColumns] = await connection.query('DESCRIBE price_records');
        const userIdColumn = priceRecordsColumns.find(col => col.Field === 'user_id');
        
        if (userIdColumn) {
            console.log(`price_records.user_id 当前类型: ${userIdColumn.Type}, 默认值: ${userIdColumn.Default}`);
        }
        
        // 2. 检查当前数据
        console.log('\n📋 检查当前数据...');
        
        const [userIdValues] = await connection.query(`
            SELECT user_id, COUNT(*) as count 
            FROM price_records 
            GROUP BY user_id 
            ORDER BY count DESC
        `);
        
        console.log('当前user_id值分布:');
        userIdValues.forEach(row => {
            console.log(`  - "${row.user_id}": ${row.count} 条记录`);
        });
        
        // 3. 获取管理员用户ID
        const [adminUsers] = await connection.query(`
            SELECT id, username FROM users WHERE role = 'admin' ORDER BY id LIMIT 1
        `);
        
        let defaultUserId = 1;
        if (adminUsers.length > 0) {
            defaultUserId = adminUsers[0].id;
            console.log(`\n👤 使用管理员用户: ${adminUsers[0].username} (ID: ${defaultUserId})`);
        }
        
        // 4. 开始修复过程
        console.log('\n🔧 开始修复过程...');
        
        // 4.1 添加新的临时列
        console.log('步骤1: 添加临时的user_id_new列...');
        try {
            await connection.query(`
                ALTER TABLE price_records 
                ADD COLUMN user_id_new INT NOT NULL DEFAULT ${defaultUserId}
            `);
            console.log('✅ 临时列添加成功');
        } catch (error) {
            if (error.code === 'ER_DUP_FIELDNAME') {
                console.log('⚠️  临时列已存在，跳过创建');
            } else {
                throw error;
            }
        }
        
        // 4.2 更新新列的值
        console.log('步骤2: 更新新列的值...');
        
        // 对于数字字符串，尝试转换为对应的用户ID
        const [numericUserIds] = await connection.query(`
            SELECT DISTINCT user_id 
            FROM price_records 
            WHERE user_id REGEXP '^[0-9]+$'
        `);
        
        for (const row of numericUserIds) {
            const userId = parseInt(row.user_id);
            // 检查这个用户ID是否存在
            const [userExists] = await connection.query('SELECT id FROM users WHERE id = ?', [userId]);
            
            if (userExists.length > 0) {
                await connection.query(`
                    UPDATE price_records 
                    SET user_id_new = ? 
                    WHERE user_id = ?
                `, [userId, row.user_id]);
                console.log(`✅ 更新用户ID ${row.user_id} 的记录`);
            } else {
                await connection.query(`
                    UPDATE price_records 
                    SET user_id_new = ? 
                    WHERE user_id = ?
                `, [defaultUserId, row.user_id]);
                console.log(`⚠️  用户ID ${row.user_id} 不存在，设置为默认用户`);
            }
        }
        
        // 对于非数字值（如'anonymous'），设置为默认用户
        await connection.query(`
            UPDATE price_records 
            SET user_id_new = ? 
            WHERE user_id NOT REGEXP '^[0-9]+$' OR user_id IS NULL
        `, [defaultUserId]);
        console.log('✅ 非数字用户ID已设置为默认用户');
        
        // 4.3 删除旧列
        console.log('步骤3: 删除旧的user_id列...');
        await connection.query('ALTER TABLE price_records DROP COLUMN user_id');
        console.log('✅ 旧列已删除');
        
        // 4.4 重命名新列
        console.log('步骤4: 重命名新列...');
        await connection.query('ALTER TABLE price_records CHANGE user_id_new user_id INT NOT NULL');
        console.log('✅ 列重命名成功');
        
        // 4.5 添加外键约束
        console.log('步骤5: 添加外键约束...');
        try {
            await connection.query(`
                ALTER TABLE price_records 
                ADD CONSTRAINT fk_price_records_user_id 
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            `);
            console.log('✅ 外键约束添加成功');
        } catch (error) {
            if (error.code === 'ER_DUP_KEYNAME') {
                console.log('⚠️  外键约束已存在');
            } else {
                console.warn('⚠️  外键约束添加失败:', error.message);
            }
        }
        
        // 4.6 添加索引
        console.log('步骤6: 添加索引...');
        try {
            await connection.query('CREATE INDEX idx_price_records_user_id ON price_records(user_id)');
            console.log('✅ 索引添加成功');
        } catch (error) {
            if (error.code === 'ER_DUP_KEYNAME') {
                console.log('⚠️  索引已存在');
            } else {
                console.warn('⚠️  索引添加失败:', error.message);
            }
        }
        
        // 5. 验证修复结果
        console.log('\n✅ 验证修复结果...');
        
        const [newColumns] = await connection.query('DESCRIBE price_records');
        const newUserIdColumn = newColumns.find(col => col.Field === 'user_id');
        console.log(`新的user_id字段类型: ${newUserIdColumn.Type}`);
        
        const [orphanedRecords] = await connection.query(`
            SELECT COUNT(*) as count 
            FROM price_records pr 
            LEFT JOIN users u ON pr.user_id = u.id 
            WHERE u.id IS NULL
        `);
        console.log(`孤立记录数量: ${orphanedRecords[0].count}`);
        
        const [newUserIdDistribution] = await connection.query(`
            SELECT pr.user_id, u.username, COUNT(*) as count 
            FROM price_records pr 
            LEFT JOIN users u ON pr.user_id = u.id 
            GROUP BY pr.user_id, u.username 
            ORDER BY count DESC
        `);
        
        console.log('新的用户ID分布:');
        newUserIdDistribution.forEach(row => {
            console.log(`  - 用户ID ${row.user_id} (${row.username || '未知'}): ${row.count} 条记录`);
        });
        
        // 6. 总结
        console.log('\n📋 修复总结:');
        
        if (orphanedRecords[0].count === 0) {
            console.log('🎉 user_id字段修复成功！');
            console.log('✅ 字段类型已改为INT');
            console.log('✅ 所有记录都正确关联到用户');
            console.log('✅ 外键约束已建立');
        } else {
            console.log('⚠️  修复可能不完整:');
            console.log(`❌ 仍有 ${orphanedRecords[0].count} 条孤立记录`);
        }
        
    } catch (error) {
        console.error('❌ 修复过程中发生错误:', error);
        throw error;
    } finally {
        if (connection) {
            await connection.end();
            console.log('\n🔌 数据库连接已关闭');
        }
    }
}

// 运行修复
if (require.main === module) {
    fixUserIdColumn()
        .then(() => {
            console.log('\n✅ 修复完成');
            process.exit(0);
        })
        .catch(error => {
            console.error('\n💥 修复失败:', error);
            process.exit(1);
        });
}

module.exports = { fixUserIdColumn };
