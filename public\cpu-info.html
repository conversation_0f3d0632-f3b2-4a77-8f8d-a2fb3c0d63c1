<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CPU信息管理 - 电脑配件管理系统</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/viewerjs/1.11.6/viewer.min.css">
    <link rel="stylesheet" href="/css/cpu-info.css"></link>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/viewerjs/1.11.6/viewer.min.js"></script>
    <script src="/js/js-pinyin.js"></script>
    <script src="/js/pinyin-wrapper.js"></script>
    <script src="/js/main.js"></script>
    <script src="/js/permission-helper.js"></script>
</head>

<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-2 sm:px-4 py-4 sm:py-8">
        <header class="mb-4 sm:mb-8">
            <h1 class="text-xl sm:text-3xl font-bold text-blue-700 flex items-center">
                <i class="fas fa-microchip mr-2 sm:mr-3"></i> CPU信息管理
            </h1>
            <div class="flex justify-between items-center">
                <p class="text-gray-600 mt-1 sm:mt-2 text-sm sm:text-base">记录和管理CPU配置信息</p>
                <div class="mt-2 flex space-x-2">
                    <button id="darkModeToggle" class="w-10 h-10 rounded-full bg-gray-200 hover:bg-gray-300 flex items-center justify-center text-gray-700 shadow-sm transition-all duration-300" title="切换暗夜模式">
                        <i class="fas fa-moon"></i>
                    </button>
                    <a href="/pc-components.html"
                        class="inline-block bg-blue-600 text-white py-1 sm:py-2 px-3 sm:px-4 rounded-md hover:bg-blue-700 text-sm sm:text-base">
                        <i class="fas fa-arrow-left mr-1"></i> 返回配件列表
                    </a>
                    <a href="/"
                        class="inline-block bg-blue-600 text-white py-1 sm:py-2 px-3 sm:px-4 rounded-md hover:bg-blue-700 text-sm sm:text-base">
                        <i class="fas fa-home mr-1"></i> 首页
                    </a>
                </div>
            </div>
        </header>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-4 sm:gap-6">
            <!-- 左侧表单区域 -->
            <div class="lg:col-span-1 bg-white p-3 sm:p-6 rounded-lg shadow-md">
                <h2 class="text-lg sm:text-xl font-semibold text-gray-800 mb-3 sm:mb-4 flex items-center">
                    <i class="fas fa-plus-circle mr-1 sm:mr-2 text-blue-500"></i> 添加CPU信息
                </h2>

                
                <form id="cpuForm" class="space-y-3 sm:space-y-4">
                     <!-- 智能识别 -->
                     <div class="border border-gray-200 rounded-md p-3 bg-blue-50">
                        <h3 class="text-md font-medium text-gray-700 mb-2 flex items-center">
                            <i class="fas fa-robot text-blue-500 mr-1"></i> 智能识别
                        </h3>
                        <div class="space-y-3">
                            <p class="text-xs text-gray-600">粘贴CPU参数文本，系统将自动识别并填充表单</p>
                            <textarea id="smartInput" 
                                class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base"
                                placeholder="粘贴CPU参数，如：品牌：Intel、型号：i7-13700K、核心数：16、线程数：24..." rows="3"></textarea>
                            <button type="button" id="autoFillBtn" 
                                class="w-full bg-blue-500 hover:bg-blue-600 text-white py-1 sm:py-2 px-3 sm:px-4 rounded-md shadow-sm text-sm sm:text-base transition duration-200">
                                <i class="fas fa-robot mr-2"></i>智能解析
                            </button>
                        </div>
                    </div>
                    <!-- 基本信息 -->
                    <div class="border border-gray-200 rounded-md p-3">
                        <h3 class="text-md font-medium text-gray-700 mb-2">基本信息</h3>

                        <!-- FORM_FIELDS_START -->
                        <div class="space-y-3">
                            <div>
                                <label for="brand" class="block text-sm font-medium text-gray-700 mb-1">品牌 <span
                                        class="text-red-500">*</span></label>
                                <select id="brand"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base"
                                    required>
                                    <option value="">选择品牌</option>
                                    <option value="Intel">Intel</option>
                                    <option value="AMD">AMD</option>
                                    <option value="其他">其他</option>
                                </select>
                            </div>

                            <div>
                                <label for="model" class="block text-sm font-medium text-gray-700 mb-1">CPU型号 <span
                                        class="text-red-500">*</span></label>
                                <input type="text" id="model"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base"
                                    placeholder="如: Core i7-12700K" required>
                            </div>

                            <div>
                                <label for="series" class="block text-sm font-medium text-gray-700 mb-1">系列</label>
                                <input type="text" id="series"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base"
                                    placeholder="如: Core i7, Ryzen 7">
                            </div>

                            <div>
                                <label for="socket" class="block text-sm font-medium text-gray-700 mb-1">接口</label>
                                <select id="socket"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base">
                                    <option value="">选择CPU接口</option>
                                    <optgroup label="Intel">
                                        <option value="LGA 1700">LGA 1700</option>
                                        <option value="LGA 1200">LGA 1200</option>
                                        <option value="LGA 1151">LGA 1151</option>
                                        <option value="LGA 1150">LGA 1150</option>
                                        <option value="LGA 2066">LGA 2066</option>
                                        <option value="LGA 1851">LGA 1851</option>
                                    </optgroup>
                                    <optgroup label="AMD">
                                        <option value="AM4">AM4</option>
                                        <option value="AM5">AM5</option>
                                        <option value="sTRX4">sTRX4</option>
                                        <option value="TR4">TR4</option>
                                    </optgroup>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- 性能参数 -->
                    <div class="border border-gray-200 rounded-md p-3">
                        <h3 class="text-md font-medium text-gray-700 mb-2">性能参数</h3>

                        <div class="space-y-3">
                            <div>
                                <label for="coreCount" class="block text-sm font-medium text-gray-700 mb-1">核心数</label>
                                <input type="number" id="coreCount"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base"
                                    placeholder="如: 8" min="1">
                            </div>

                            <div>
                                <label for="threadCount" class="block text-sm font-medium text-gray-700 mb-1">线程数</label>
                                <input type="number" id="threadCount"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base"
                                    placeholder="如: 16" min="1">
                            </div>

                            <div>
                                <label for="baseClock" class="block text-sm font-medium text-gray-700 mb-1">基础频率 (GHz)</label>
                                <input type="number" id="baseClock" step="0.1"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base"
                                    placeholder="如: 3.6" min="0">
                            </div>

                            <div>
                                <label for="boostClock" class="block text-sm font-medium text-gray-700 mb-1">加速频率 (GHz)</label>
                                <input type="number" id="boostClock" step="0.1"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base"
                                    placeholder="如: 5.0" min="0">
                            </div>

                            <div>
                                <label for="tdp" class="block text-sm font-medium text-gray-700 mb-1">功耗 (W)</label>
                                <input type="number" id="tdp"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base"
                                    placeholder="如: 125" min="0">
                            </div>

                            <div>
                                <label for="l3Cache" class="block text-sm font-medium text-gray-700 mb-1">L3缓存 (MB)</label>
                                <input type="number" id="l3Cache"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base"
                                    placeholder="如: 30" min="0">
                            </div>
                        </div>
                    </div>

                    <!-- 技术规格 -->
                    <div class="border border-gray-200 rounded-md p-3">
                        <h3 class="text-md font-medium text-gray-700 mb-2">技术规格</h3>

                        <div class="space-y-3">
                            <div>
                                <label for="processNode" class="block text-sm font-medium text-gray-700 mb-1">制程工艺</label>
                                <input type="text" id="processNode"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base"
                                    placeholder="如: 10nm, 7nm, 5nm">
                            </div>

                            <div>
                                <label for="releaseDate" class="block text-sm font-medium text-gray-700 mb-1">发布日期</label>
                                <input type="date" id="releaseDate"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base">
                            </div>

                            <div>
                                <label for="integratedGraphics" class="block text-sm font-medium text-gray-700 mb-1">集成显卡</label>
                                <input type="text" id="integratedGraphics"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base"
                                    placeholder="如: Intel UHD 770, AMD Radeon Graphics">
                            </div>
                        </div>
                    </div>

                    <!-- 其他信息 -->
                    <div class="border border-gray-200 rounded-md p-3">
                        <h3 class="text-md font-medium text-gray-700 mb-2">其他信息</h3>

                        <div class="space-y-3">
                            <div>
                                <label for="price" class="block text-sm font-medium text-gray-700 mb-1">价格 (元)</label>
                                <input type="number" id="price" step="0.01"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base"
                                    placeholder="如: 2499.99" min="0">
                            </div>

                            

                            <div>
                                <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">备注</label>
                                <textarea id="notes" name="notes" rows="2"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base"
                                    placeholder="额外信息..."></textarea>
                            </div>
                            <div>
                                <label for="cpuImage" class="block text-sm font-medium text-gray-700 mb-1">CPU图片</label>
                                <div
                                    class="mt-1 flex justify-center px-4 sm:px-6 pt-3 sm:pt-5 pb-4 sm:pb-6 border-2 border-gray-300 border-dashed rounded-md">
                                    <div class="space-y-1 text-center">
                                        <div class="flex text-sm text-gray-600 items-center justify-center flex-wrap">
                                            <label for="cpuImage"
                                                class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none">
                                                <span>上传图片</span>
                                                <input id="cpuImage" name="cpuImage" type="file" accept="image/*"
                                                    class="sr-only">
                                            </label>
                                            <p class="pl-1 mobile-hidden">或将图片拖拽到此处</p>
                                        </div>
                                        <p class="text-xs text-gray-500">PNG, JPG, GIF 格式（将自动转换为WebP格式以提高加载速度）</p>
                                    </div>
                                </div>

                                <!-- 上传进度条 -->
                                <div id="uploadProgressContainer" class="mt-2 hidden">
                                    <div class="bg-gray-200 dark:bg-gray-700 rounded-full h-3 mb-2 overflow-hidden">
                                        <div id="uploadProgressBar" class="bg-gradient-to-r from-purple-500 to-purple-600 dark:from-purple-400 dark:to-purple-500 h-3 rounded-full transition-all duration-500 ease-out relative" style="width: 0%">
                                            <!-- 进度条光泽效果 -->
                                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-20 animate-pulse"></div>
                                        </div>
                                    </div>
                                    <div class="flex justify-between items-center text-xs">
                                        <span id="uploadProgressText" class="text-gray-600 dark:text-gray-400 flex items-center">
                                            <i id="uploadProgressIcon" class="fas fa-upload mr-1"></i>
                                            准备上传...
                                        </span>
                                        <span id="uploadProgressPercent" class="text-purple-600 dark:text-purple-400 font-medium">0%</span>
                                    </div>
                                </div>

                                <div id="imagePreviewContainer" class="mt-2 hidden">
                                    <div class="relative inline-block">
                                        <img id="imagePreview" src="#" alt="预览图"
                                            class="h-24 sm:h-32 rounded-md shadow image-preview">
                                        <button type="button" id="removeImageBtn"
                                            class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 sm:w-6 sm:h-6 flex items-center justify-center hover:bg-red-600">
                                            <i class="fas fa-times text-xs"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- FORM_FIELDS_END -->

                    <button type="submit"
                        class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md shadow-sm flex items-center justify-center">
                        <i class="fas fa-save mr-2"></i> 保存CPU信息
                    </button>
                </form>
            </div>

            <!-- 右侧列表区域 -->
            <div class="lg:col-span-3 space-y-4 sm:space-y-6">
                <!-- CPU列表 -->
                <div class="bg-white p-3 sm:p-6 rounded-lg shadow-md">
                    <h2 class="text-xl font-semibold text-gray-800 mb-2 flex items-center">
                        <i class="fas fa-list mr-2 text-blue-500"></i> CPU 列表
                    </h2>
                    <div class="flex flex-wrap items-center gap-2 sm:gap-4 mb-4 mobile-stack mobile-full-width">
                        <div class="flex-1 w-full">
                            <div class="relative">
                                <input type="text" id="cpuSearch" placeholder="搜索全部字段..." 
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm">
                                <span class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                                    <i class="fas fa-search"></i>
                                </span>
                            </div>
                        </div>
                        <div class="flex flex-wrap gap-2 mobile-w-full mobile-mt-2">
                            <select id="brandFilter"
                                class="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm mobile-w-full">
                                <option value="all">所有品牌</option>
                                <option value="Intel">Intel</option>
                                <option value="AMD">AMD</option>
                                <option value="其他">其他</option>
                            </select>
                            <button id="resetFilterBtn"
                                class="px-3 py-2 bg-gray-100 border border-gray-300 rounded-md shadow-sm text-sm text-gray-600 hover:bg-gray-200 mobile-w-full">
                                <i class="fas fa-sync-alt"></i> 重置
                            </button>
                        </div>
                    </div>
                    

                    <div class="overflow-x-auto w-full">
                        <div class="table-wrapper">
                            <table class="min-w-full divide-y divide-gray-200 table-compact sm:table-auto table-modern sm:table-modern mobile-table sm:mobile-table-none">
                                <thead class="bg-gray-50 hidden sm:table-header-group">
                                    <tr>
                                        <!-- TABLE_HEADERS_START -->
                                        <th scope="col"
                                            class="px-2 py-2 sm:px-4 sm:py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell image-column">
                                            图片</th>
                                        <th scope="col"
                                            class="px-2 py-2 sm:px-4 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider model-column">
                                            型号</th>
                                        <th scope="col"
                                            class="px-2 py-2 sm:px-4 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell">
                                            品牌</th>
                                        <th scope="col"
                                            class="px-2 py-2 sm:px-4 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell">
                                            接口</th>
                                        <th scope="col"
                                            class="px-2 py-2 sm:px-4 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            核心/线程</th>
                                        <th scope="col"
                                            class="px-2 py-2 sm:px-4 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell">
                                            频率</th>
                                        <th scope="col"
                                            class="px-2 py-2 sm:px-4 sm:py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider action-column">
                                            操作</th>
                                        <!-- TABLE_HEADERS_END -->
                                    </tr>
                                </thead>
                                <tbody id="cpuTableBody" class="bg-white divide-y sm:divide-y divide-gray-200">
                                    <!-- 数据将通过JavaScript动态填充 -->
                                    <tr>
                                        <td colspan="7" class="px-2 py-4 text-center text-gray-500">暂无数据</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="flex flex-wrap items-center justify-between mt-4 border-t border-gray-200 pt-4">
                        <div class="text-xs text-gray-700 mb-2 sm:mb-0" id="totalCount">
                            共 <span id="totalRecords">0</span> 条记录
                        </div>
                        <div class="flex items-center space-x-1 sm:space-x-2 page-controls-mobile">
                            <button id="firstPage" title="首页"
                                class="px-2 py-1 border border-gray-300 rounded-md text-sm bg-white text-gray-500 hover:bg-gray-50 disabled:opacity-30 disabled:cursor-not-allowed">
                                <i class="fas fa-angle-double-left"></i>
                            </button>
                            <button id="prevPage" title="上一页"
                                class="px-2 py-1 border border-gray-300 rounded-md text-sm bg-white text-gray-500 hover:bg-gray-50 disabled:opacity-30 disabled:cursor-not-allowed">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            
                            <div id="pageNumbers" class="hidden sm:flex space-x-1">
                                <!-- 页码将通过JavaScript填充 -->
                            </div>
                            
                            <span id="pageInfo" class="px-2 text-sm text-gray-500">
                                第 <span id="currentPageDisplay">1</span> / <span id="totalPagesDisplay">1</span> 页
                            </span>
                            
                            <button id="nextPage" title="下一页"
                                class="px-2 py-1 border border-gray-300 rounded-md text-sm bg-white text-gray-500 hover:bg-gray-50 disabled:opacity-30 disabled:cursor-not-allowed">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                            <button id="lastPage" title="尾页"
                                class="px-2 py-1 border border-gray-300 rounded-md text-sm bg-white text-gray-500 hover:bg-gray-50 disabled:opacity-30 disabled:cursor-not-allowed">
                                <i class="fas fa-angle-double-right"></i>
                            </button>
                            
                            <div class="hidden sm:flex items-center ml-2">
                                <span class="text-sm">跳转到</span>
                                <input type="number" id="pageJump" min="1" class="w-12 ml-1 px-2 py-1 border border-gray-300 rounded-md text-sm">
                                <button id="goToPage" class="ml-1 px-2 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700">
                                    确定
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- CPU详情模态框 -->
        <div id="cpuModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
            <div class="bg-white rounded-lg shadow-xl p-4 sm:p-6 max-w-4xl w-full max-h-[90vh] overflow-y-auto">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg sm:text-xl font-semibold text-gray-800">CPU详情</h3>
                    <button id="closeModal" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div id="cpuDetails" class="space-y-4">
                    <!-- 详情会动态填充 -->
                </div>

                <div class="mt-6 flex justify-end space-x-2">
                    <button id="editBtn"
                        class="px-3 py-1 sm:px-4 sm:py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm sm:text-base">
                        <i class="fas fa-edit mr-1"></i> 编辑
                    </button>
                    <button id="deleteBtn"
                        class="px-3 py-1 sm:px-4 sm:py-2 bg-red-600 text-white rounded-md hover:bg-red-700 text-sm sm:text-base">
                        <i class="fas fa-trash mr-1"></i> 删除
                    </button>
                    <button id="closeModalBtn"
                        class="px-3 py-1 sm:px-4 sm:py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 text-sm sm:text-base">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/upload-progress.js"></script>
    <script src="/js/cpu-info.js"></script>
</body>

</html> 