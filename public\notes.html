<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识管理 - 电脑配件综合管理系统</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">

    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Viewer.js for image preview -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/viewerjs/1.11.6/viewer.min.css">
    <link rel="stylesheet" href="/css/modern-responsive.css">
    <link rel="stylesheet" href="/css/notes.css">
    <link rel="stylesheet" href="/css/notes-theme-fix.css">
    
    <!-- Meta tags for theme -->
    <meta name="theme-color" content="#3B82F6">
    <meta name="msapplication-navbutton-color" content="#3B82F6">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">

    <!-- 主题初始化脚本 - 必须在页面渲染前执行 -->
    <script>
        // 立即检查并应用保存的主题设置，避免闪烁
        (function() {
            const savedTheme = localStorage.getItem('theme');
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            const shouldUseDark = savedTheme === 'dark' || (!savedTheme && prefersDark);

            if (shouldUseDark) {
                document.documentElement.classList.add('dark');
            }
        })();
    </script>
</head>
<body class="bg-gray-50 dark:bg-gray-900 min-h-screen transition-colors duration-300">
    <!-- 顶部导航栏 -->
    <nav class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-40">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- 左侧标题和导航 -->
                <div class="flex items-center space-x-4">
                    <h1 class="text-xl font-bold text-gray-900 dark:text-white flex items-center">
                        <i class="fas fa-sticky-note mr-2 text-blue-600 dark:text-blue-400"></i>
                        知识管理
                    </h1>
                    
                    <!-- 面包屑导航 -->
                    <nav class="hidden md:flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                        <a href="/index.html" class="hover:text-blue-600 dark:hover:text-blue-400">首页</a>
                        <i class="fas fa-chevron-right text-xs"></i>
                        <span class="text-gray-900 dark:text-white">知识管理</span>
                    </nav>
                </div>
                
                <!-- 右侧操作按钮 -->
                <div class="flex items-center space-x-3">
                    <!-- 主题切换按钮 -->
                    <button id="themeToggleBtn" class="nav-btn theme-toggle-btn">
                        <i id="themeIcon" class="fas fa-moon"></i>
                    </button>

                    <!-- 新建笔记按钮（仅管理员可见） -->
                    <button id="newNoteBtn" class="hidden nav-btn new-note-btn" title="新建笔记">
                        <i class="fas fa-plus"></i>
                    </button>
                    
                    <!-- 用户信息 -->
                    <div class="flex items-center space-x-2">
                        <div id="userInfo" class="text-sm text-gray-600 dark:text-gray-300">
                            <span id="username">加载中...</span>
                            <span id="userRole" class="ml-2 px-2 py-1 rounded text-xs bg-gray-200 dark:bg-gray-700"></span>
                        </div>
                    </div>
                    
                    <!-- 返回首页按钮 -->
                    <a href="/index.html" class="nav-btn home-btn">
                        <i class="fas fa-home"></i>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <!-- 左侧边栏 -->
            <div class="lg:col-span-1 space-y-6">
                <!-- 搜索框 -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4">
                    <div class="relative">
                        <input type="text" id="searchInput" placeholder="搜索笔记..." 
                               class="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                </div>
                
                <!-- 快速过滤 -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4">
                    <h3 class="font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                        <i class="fas fa-filter mr-2 text-blue-600 dark:text-blue-400"></i>
                        快速过滤
                    </h3>
                    
                    <!-- 置顶笔记 -->
                    <div class="mb-3">
                        <button id="filterPinned" class="w-full text-left px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex items-center justify-between">
                            <span class="flex items-center">
                                <i class="fas fa-thumbtack mr-2 text-yellow-500"></i>
                                置顶笔记
                            </span>
                            <span id="pinnedCount" class="text-xs bg-gray-200 dark:bg-gray-600 px-2 py-1 rounded">0</span>
                        </button>
                    </div>
                    
                    <!-- 我的笔记（仅管理员） -->
                    <div id="myNotesFilter" class="hidden mb-3">
                        <button id="filterMyNotes" class="w-full text-left px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex items-center justify-between">
                            <span class="flex items-center">
                                <i class="fas fa-user-edit mr-2 text-green-500"></i>
                                我的笔记
                            </span>
                            <span id="myNotesCount" class="text-xs bg-gray-200 dark:bg-gray-600 px-2 py-1 rounded">0</span>
                        </button>
                    </div>
                    
                    <!-- 最近更新 -->
                    <div class="mb-3">
                        <button id="filterRecent" class="w-full text-left px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex items-center justify-between">
                            <span class="flex items-center">
                                <i class="fas fa-clock mr-2 text-purple-500"></i>
                                最近更新
                            </span>
                        </button>
                    </div>
                </div>
                
                <!-- 分类列表 -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="font-semibold text-gray-900 dark:text-white flex items-center">
                            <i class="fas fa-folder mr-2 text-blue-600 dark:text-blue-400"></i>
                            分类
                        </h3>
                        <button id="manageCategoriesBtn" class="hidden btn btn-primary btn-sm" title="管理分类">
                            <i class="fas fa-cog"></i>
                        </button>
                    </div>
                    <div id="categoriesList" class="space-y-1">
                        <!-- 分类列表将通过JavaScript动态加载 -->
                        <div class="text-center py-4 text-gray-500 dark:text-gray-400">
                            <i class="fas fa-spinner fa-spin"></i>
                            <div class="mt-2 text-sm">加载中...</div>
                        </div>
                    </div>
                </div>
                
                <!-- 标签云 -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="font-semibold text-gray-900 dark:text-white flex items-center">
                            <i class="fas fa-tags mr-2 text-blue-600 dark:text-blue-400"></i>
                            标签
                        </h3>
                        <button id="manageTagsBtn" class="hidden btn btn-primary btn-sm" title="管理标签">
                            <i class="fas fa-cog"></i>
                        </button>
                    </div>
                    <div id="tagsList" class="flex flex-wrap gap-2">
                        <!-- 标签列表将通过JavaScript动态加载 -->
                        <div class="text-center py-4 text-gray-500 dark:text-gray-400 w-full">
                            <i class="fas fa-spinner fa-spin"></i>
                            <div class="mt-2 text-sm">加载中...</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 主内容区域 -->
            <div class="lg:col-span-3">
                <!-- 工具栏 -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 mb-6">
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                        <!-- 左侧：排序和视图选项 -->
                        <div class="flex items-center space-x-4">
                            <div class="flex items-center space-x-2">
                                <label class="text-sm text-gray-600 dark:text-gray-300">排序：</label>
                                <select id="sortSelect" class="text-sm border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                                    <option value="updated_at">最近更新</option>
                                    <option value="created_at">创建时间</option>
                                    <option value="title">标题</option>
                                    <option value="view_count">浏览次数</option>
                                </select>
                            </div>
                            
                            <div class="flex items-center space-x-2">
                                <label class="text-sm text-gray-600 dark:text-gray-300">视图：</label>
                                <div class="view-toggle-group">
                                    <button id="gridViewBtn" class="view-toggle-btn active">
                                        <i class="fas fa-th-large"></i>
                                    </button>
                                    <button id="listViewBtn" class="view-toggle-btn">
                                        <i class="fas fa-list"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 右侧：统计信息 -->
                        <div class="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-300">
                            <span>共 <span id="totalNotes">0</span> 篇笔记</span>
                            <span id="currentFilter" class="text-blue-600 dark:text-blue-400"></span>
                        </div>
                    </div>
                </div>
                
                <!-- 笔记列表 -->
                <div id="notesContainer" class="space-y-4">
                    <!-- 笔记列表将通过JavaScript动态加载 -->
                    <div class="text-center py-12 text-gray-500 dark:text-gray-400">
                        <i class="fas fa-spinner fa-spin text-2xl"></i>
                        <div class="mt-4 text-lg">加载笔记中...</div>
                    </div>
                </div>
                
                <!-- 分页 -->
                <div id="pagination" class="mt-8 flex justify-center">
                    <!-- 分页控件将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 笔记编辑模态框 -->
    <div id="noteModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
            <!-- 模态框头部 -->
            <div class="flex items-center justify-between p-4 sm:p-6 border-b border-gray-200 dark:border-gray-700">
                <h2 id="modalTitle" class="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white">新建笔记</h2>
                <button id="closeModalBtn" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            
            <!-- 模态框内容 -->
            <div class="p-4 sm:p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
                <form id="noteForm" class="space-y-4 sm:space-y-6">
                    <input type="hidden" id="noteId" value="">
                    
                    <!-- 标题 -->
                    <div>
                        <label for="noteTitle" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">标题 *</label>
                        <input type="text" id="noteTitle" required 
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    </div>
                    
                    <!-- 分类和标签 -->
                    <div class="grid grid-cols-1 gap-4">
                        <div>
                            <label for="noteCategory" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">分类</label>
                            <select id="noteCategory" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                                <option value="">选择分类</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="noteTags" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">标签</label>
                            <div id="tagsContainer" class="border border-gray-300 dark:border-gray-600 rounded-lg p-2 min-h-[42px] flex flex-wrap gap-2">
                                <!-- 标签选择器将通过JavaScript动态生成 -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- 图片上传 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">图片附件</label>
                        <div class="image-upload-area">
                            <input type="file" id="imageUpload" accept="image/*" multiple class="hidden">
                            <div class="text-center">
                                <button type="button" id="uploadBtn" class="upload-btn">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                    选择图片
                                </button>
                                <p class="upload-hint">支持 JPG, PNG, GIF 格式，最大 5MB，可选择多张图片</p>
                            </div>
                            <div id="imagePreview" class="hidden">
                                <!-- 图片预览将在这里显示 -->
                            </div>
                        </div>
                    </div>

                    <!-- 内容编辑器 -->
                    <div>
                        <label for="noteContent" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">内容</label>
                        <textarea id="noteContent" rows="10" 
                                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white font-mono text-sm"
                                  placeholder="在这里输入笔记内容...支持Markdown格式"></textarea>
                    </div>
                    
                    <!-- 选项 -->
                    <div class="flex flex-wrap items-center gap-4 sm:space-x-6">
                        <label class="flex items-center">
                            <input type="checkbox" id="notePublic" checked class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-xs sm:text-sm text-gray-700 dark:text-gray-300">公开笔记</span>
                        </label>
                        
                        <label class="flex items-center">
                            <input type="checkbox" id="notePinned" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-xs sm:text-sm text-gray-700 dark:text-gray-300">置顶笔记</span>
                        </label>
                    </div>
                    
                    <!-- 按钮 -->
                    <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
                        <button type="button" id="cancelBtn" class="btn btn-secondary btn-sm">
                            <i class="fas fa-times"></i>
                            取消
                        </button>
                        <button type="submit" id="saveBtn" class="btn btn-primary btn-sm">
                            <i class="fas fa-save"></i>
                            保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 笔记详情模态框 -->
    <div id="noteDetailModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-60 flex items-center justify-center p-4">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
            <!-- 模态框头部 -->
            <div class="flex items-center justify-between p-4 sm:p-6 border-b border-gray-200 dark:border-gray-700">
                <div class="flex items-center space-x-2 sm:space-x-3">
                    <h2 id="detailTitle" class="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white">笔记详情</h2>
                    <span id="detailCategory" class="px-2 py-1 text-xs rounded-full bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200"></span>
                </div>
                <div class="flex items-center space-x-2">
                    <button id="editNoteBtn" class="nav-btn new-note-btn" title="编辑笔记">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button id="closeDetailBtn" class="nav-btn theme-toggle-btn" title="关闭">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>

            <!-- 模态框内容 -->
            <div class="p-4 sm:p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
                <!-- 笔记信息 -->
                <div class="mb-4 sm:mb-6">
                    <div class="grid grid-cols-1 gap-2 sm:grid-cols-3 sm:gap-4 text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                        <div>
                            <span class="font-medium">创建时间：</span>
                            <span id="detailCreatedAt" class="break-all">-</span>
                        </div>
                        <div>
                            <span class="font-medium">更新时间：</span>
                            <span id="detailUpdatedAt" class="break-all">-</span>
                        </div>
                        <div>
                            <span class="font-medium">作者：</span>
                            <span id="detailAuthor">-</span>
                        </div>
                    </div>
                </div>

                <!-- 标签 -->
                <div id="detailTagsContainer" class="mb-4 sm:mb-6 hidden">
                    <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">标签</h4>
                    <div id="detailTags" class="flex flex-wrap gap-2">
                        <!-- 标签将在这里显示 -->
                    </div>
                </div>

                <!-- 图片附件 -->
                <div id="detailImagesContainer" class="mb-4 sm:mb-6 hidden">
                    <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">图片附件</h4>
                    <div id="detailImages" class="grid grid-cols-2 sm:grid-cols-3 gap-2 sm:gap-4">
                        <!-- 图片将在这里显示 -->
                    </div>
                </div>

                <!-- 笔记内容 -->
                <div>
                    <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">内容</h4>
                    <div id="detailContent" class="prose dark:prose-invert max-w-none text-sm sm:text-base bg-gray-50 dark:bg-gray-900 rounded-lg p-3 sm:p-4">
                        <!-- 渲染后的内容将在这里显示 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast 通知 -->
    <div id="toast" class="fixed top-4 right-4 max-w-sm bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4 z-50 transform transition-transform duration-300 translate-x-full">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <i id="toastIcon" class="fas fa-check-circle text-green-500"></i>
            </div>
            <div class="ml-3">
                <p id="toastMessage" class="text-sm font-medium text-gray-900 dark:text-white"></p>
            </div>
            <div class="ml-auto pl-3">
                <button id="toastClose" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/viewerjs/1.11.6/viewer.min.js"></script>
    <script src="/js/theme-manager.js"></script>
    <script src="/js/main.js"></script>
    <script src="/js/notes.js"></script>
    <script src="/js/notes-editor.js"></script>
    <script src="/js/notes-search.js"></script>


</body>
</html>
