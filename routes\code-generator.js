const express = require('express');
const fs = require('fs');
const path = require('path');
const db = require('../config/db');

const router = express.Router();

// 生成代码的主要端点
router.post('/generate', async (req, res) => {
    try {
        const { chineseName, englishName, tableName, iconClass, pageDescription, fields } = req.body;

        // 验证输入
        if (!chineseName || !englishName || !tableName || !fields || fields.length === 0) {
            return res.status(400).json({
                success: false,
                message: '请提供完整的配置信息'
            });
        }

        console.log('开始生成代码模块:', { chineseName, englishName, tableName });

        // 1. 创建数据库表
        await createDatabaseTable(tableName, fields);

        // 2. 生成路由文件
        await generateRouteFile(englishName, tableName, fields);

        // 3. 生成HTML页面
        await generateHTMLFile(chineseName, englishName, tableName, iconClass, pageDescription, fields);

        // 4. 生成JavaScript文件
        await generateJSFile(chineseName, englishName, tableName, fields);

        // 5. 创建默认图片
        await createDefaultImage(englishName);

        // 6. 更新主页面导航
        await updateMainNavigation(chineseName, englishName, iconClass, pageDescription);

        // 7. 更新app.js路由注册
        await updateAppRoutes(englishName, tableName);

        res.json({
            success: true,
            message: `${chineseName}管理模块生成成功！`,
            data: {
                chineseName,
                englishName,
                tableName,
                files: [
                    `routes/${englishName}-routes.js`,
                    `public/${englishName}-info.html`,
                    `public/js/${englishName}-info.js`,
                    `public/images/default-${englishName}.png`
                ]
            }
        });

    } catch (error) {
        console.error('代码生成失败:', error);
        res.status(500).json({
            success: false,
            message: '代码生成失败: ' + error.message
        });
    }
});

// 创建数据库表
async function createDatabaseTable(tableName, fields) {
    let sql = `CREATE TABLE IF NOT EXISTS ${tableName} (\n`;
    sql += `    id INT AUTO_INCREMENT PRIMARY KEY,\n`;

    fields.forEach(field => {
        let fieldType;
        switch (field.type) {
            case 'string':
                fieldType = 'VARCHAR(255)';
                break;
            case 'text':
                fieldType = 'TEXT';
                break;
            case 'integer':
                fieldType = 'INT';
                break;
            case 'float':
                fieldType = 'DECIMAL(10,2)';
                break;
            default:
                fieldType = 'VARCHAR(255)';
        }

        const nullable = field.required ? 'NOT NULL' : 'NULL';
        sql += `    ${field.name} ${fieldType} ${nullable},\n`;
    });

    sql += `    image_url VARCHAR(500) NULL,\n`;
    sql += `    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n`;
    sql += `    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n`;
    sql += `)`;

    await db.query(sql);
    console.log(`数据库表 ${tableName} 创建成功`);
}

// 生成路由文件
async function generateRouteFile(englishName, tableName, fields) {
    const routeTemplate = `const express = require('express');
const router = express.Router();
const resourceHandler = require('../middleware/resource-handler');

// 使用通用资源处理器
router.use('/', resourceHandler('${tableName}'));

module.exports = router;`;

    const routeFilePath = path.join(__dirname, `${englishName}-routes.js`);
    fs.writeFileSync(routeFilePath, routeTemplate, 'utf8');
    console.log(`路由文件已生成: ${routeFilePath}`);
}

// 生成HTML页面
async function generateHTMLFile(chineseName, englishName, tableName, iconClass, pageDescription, fields) {
    // 读取CPU的HTML模板文件作为基础
    const cpuHTMLPath = path.join(__dirname, '..', 'public', 'cpu-info.html');
    let htmlTemplate = fs.readFileSync(cpuHTMLPath, 'utf8');

    // 1. 替换基本信息
    htmlTemplate = htmlTemplate.replace(/CPU信息管理 - 电脑配件管理系统/g, `${chineseName}信息管理 - 电脑配件管理系统`);
    htmlTemplate = htmlTemplate.replace(/CPU信息管理/g, `${chineseName}信息管理`);
    htmlTemplate = htmlTemplate.replace(/记录和管理CPU配置信息/g, pageDescription);
    htmlTemplate = htmlTemplate.replace(/fas fa-microchip/g, iconClass);

    // 2. 替换所有CPU相关的标识符
    htmlTemplate = htmlTemplate.replace(/CPU/g, chineseName);
    htmlTemplate = htmlTemplate.replace(/cpu/g, englishName);
    htmlTemplate = htmlTemplate.replace(/cpus/g, tableName);

    // 3. 保持原始布局结构 - 不修改CPU页面的网格布局
    htmlTemplate = replaceLayoutStructure(htmlTemplate);

    // 4. 替换表单字段
    htmlTemplate = replaceFormFields(htmlTemplate, fields, englishName);

    // 5. 替换表格头部
    htmlTemplate = replaceTableHeaders(htmlTemplate, fields);

    // 6. 替换JavaScript文件引用
    htmlTemplate = htmlTemplate.replace(/\/js\/cpu-info\.js/g, `/js/${englishName}-info.js`);

    // 7. 替换保存按钮文本
    htmlTemplate = htmlTemplate.replace(/保存CPU信息/g, `保存${chineseName}信息`);

    const htmlFilePath = path.join(__dirname, '..', 'public', `${englishName}-info.html`);
    fs.writeFileSync(htmlFilePath, htmlTemplate, 'utf8');
    console.log(`HTML文件已生成: ${htmlFilePath}`);
}

// 保持原始布局结构 - 不修改CPU页面的网格布局
function replaceLayoutStructure(htmlTemplate) {
    console.log('保持原始布局结构，不进行布局修改...');

    // 不再修改布局结构，保持CPU页面的原始网格布局
    // 这样生成的页面将完全保持CPU页面的布局样式

    console.log('布局结构保持完成');
    return htmlTemplate;
}

// 替换表单字段
function replaceFormFields(htmlTemplate, fields, englishName) {
    // 找到表单字段的开始和结束标记
    const formFieldsStart = htmlTemplate.indexOf('<!-- FORM_FIELDS_START -->');
    const formFieldsEnd = htmlTemplate.indexOf('<!-- FORM_FIELDS_END -->');

    if (formFieldsStart === -1 || formFieldsEnd === -1) {
        console.warn('未找到表单字段标记，将在智能识别后添加字段');
        // 在智能识别部分后添加字段
        const smartInputEnd = htmlTemplate.indexOf('</div>\n                    <!-- 基本信息 -->');
        if (smartInputEnd !== -1) {
            const formFieldsHTML = generateFormFieldsHTML(fields);
            const beforeSmart = htmlTemplate.substring(0, smartInputEnd);
            const afterSmart = htmlTemplate.substring(smartInputEnd);
            return beforeSmart + '</div>\n                    <!-- 基本信息 -->\n                    <div class="border border-gray-200 rounded-md p-3">\n                        <h3 class="text-md font-medium text-gray-700 mb-2">基本信息</h3>\n\n                        <!-- FORM_FIELDS_START -->\n                        <div class="space-y-3">\n' + formFieldsHTML + '\n                        </div>\n                        <!-- FORM_FIELDS_END -->' + afterSmart;
        }
        return htmlTemplate;
    }

    const formFieldsHTML = generateFormFieldsHTML(fields);
    const beforeFields = htmlTemplate.substring(0, formFieldsStart);
    const afterFields = htmlTemplate.substring(formFieldsEnd + '<!-- FORM_FIELDS_END -->'.length);

    return beforeFields + '<!-- FORM_FIELDS_START -->\n                        <div class="space-y-3">\n' + formFieldsHTML + '\n                        </div>\n                        <!-- FORM_FIELDS_END -->' + afterFields;
}

// 替换表格头部
function replaceTableHeaders(htmlTemplate, fields) {
    // 找到表格头部的开始和结束标记
    const tableHeaderStart = htmlTemplate.indexOf('<!-- TABLE_HEADERS_START -->');
    const tableHeaderEnd = htmlTemplate.indexOf('<!-- TABLE_HEADERS_END -->');

    if (tableHeaderStart === -1 || tableHeaderEnd === -1) {
        console.warn('未找到表格头部标记，将替换默认的CPU表格头部');
        // 查找CPU表格头部并替换
        const cpuTableHeaderRegex = /<thead class="bg-gray-50">\s*<tr>([\s\S]*?)<\/tr>\s*<\/thead>/;
        const match = htmlTemplate.match(cpuTableHeaderRegex);
        if (match) {
            const tableHeaderHTML = generateTableHeaderHTML(fields);
            return htmlTemplate.replace(match[1], '\n' + tableHeaderHTML + '\n                                    ');
        }
        return htmlTemplate;
    }

    const tableHeaderHTML = generateTableHeaderHTML(fields);
    const beforeHeaders = htmlTemplate.substring(0, tableHeaderStart);
    const afterHeaders = htmlTemplate.substring(tableHeaderEnd + '<!-- TABLE_HEADERS_END -->'.length);

    return beforeHeaders + '<!-- TABLE_HEADERS_START -->\n' + tableHeaderHTML + '\n                                    <!-- TABLE_HEADERS_END -->' + afterHeaders;
}

// 生成表单字段HTML
function generateFormFieldsHTML(fields) {
    let html = '';

    // 过滤掉系统字段，这些字段不应该在用户界面中显示
    const systemFields = ['price', 'notes', 'image_url', 'created_at', 'updated_at'];
    const userFields = fields.filter(field => !systemFields.includes(field.name));

    userFields.forEach(field => {
        const requiredMark = field.required ? ' <span class="text-red-500">*</span>' : '';
        const requiredAttr = field.required ? ' required' : '';

        let inputHTML = '';

        if (field.type === 'text') {
            inputHTML = `<textarea id="${field.name}"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base resize-none"
                                    placeholder="请输入${field.label}" rows="3"${requiredAttr}></textarea>`;
        } else if (field.type === 'integer') {
            inputHTML = `<input type="number" id="${field.name}"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base"
                                    placeholder="请输入${field.label}"${requiredAttr}>`;
        } else if (field.type === 'float') {
            inputHTML = `<input type="number" step="0.01" id="${field.name}"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base"
                                    placeholder="请输入${field.label}"${requiredAttr}>`;
        } else {
            inputHTML = `<input type="text" id="${field.name}"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base"
                                    placeholder="请输入${field.label}"${requiredAttr}>`;
        }

        html += `                            <div>
                                <label for="${field.name}" class="block text-sm font-medium text-gray-700 mb-1">${field.label}${requiredMark}</label>
                                ${inputHTML}
                            </div>

`;
    });

    return html.trim();
}

// 生成表格头部HTML
function generateTableHeaderHTML(fields) {
    let html = '';

    // 过滤掉系统字段，这些字段不应该在用户界面中显示
    const systemFields = ['price', 'notes', 'image_url', 'created_at', 'updated_at'];
    const userFields = fields.filter(field => !systemFields.includes(field.name));

    // 图片列
    html += `                                    <th scope="col"
                                        class="px-2 py-2 sm:px-4 sm:py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell image-column">
                                        图片</th>`;

    // 显示前4个用户字段
    userFields.slice(0, 4).forEach(field => {
        html += `
                                    <th scope="col"
                                        class="px-2 py-2 sm:px-4 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        ${field.label}</th>`;
    });

    // 操作列
    html += `
                                    <th scope="col"
                                        class="px-2 py-2 sm:px-4 sm:py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider action-column">
                                        操作</th>`;

    return html;
}

// 生成JavaScript文件
async function generateJSFile(chineseName, englishName, tableName, fields) {
    // 读取CPU的JS模板文件作为基础
    const cpuJSPath = path.join(__dirname, '..', 'public', 'js', 'cpu-info.js');
    let jsTemplate = fs.readFileSync(cpuJSPath, 'utf8');

    // 1. 替换基本信息 - 修复：避免替换JavaScript对象属性名
    jsTemplate = jsTemplate.replace(/CPU页面/g, `${chineseName}页面`);

    // 先替换API端点常量名（避免被后续替换影响）
    jsTemplate = jsTemplate.replace(/CPUS:/g, `${tableName.toUpperCase()}:`);
    jsTemplate = jsTemplate.replace(/CPU:/g, `${tableName.toUpperCase().slice(0, -1)}:`);

    // 替换注释和字符串中的CPU
    jsTemplate = jsTemplate.replace(/CPU信息/g, `${chineseName}信息`);
    jsTemplate = jsTemplate.replace(/CPU数据/g, `${chineseName}数据`);
    jsTemplate = jsTemplate.replace(/CPU管理/g, `${chineseName}管理`);

    // 替换函数名
    jsTemplate = jsTemplate.replace(/function loadCpus/g, `function load${chineseName}s`);
    jsTemplate = jsTemplate.replace(/loadCpus\(/g, `load${chineseName}s(`);

    // 替换变量名和函数名中的cpu/cpus
    jsTemplate = jsTemplate.replace(/\bcpus\b/g, tableName);
    jsTemplate = jsTemplate.replace(/\bcpu\b/g, englishName);

    // 替换其他CPU引用（但保护JavaScript关键字和属性名）
    jsTemplate = jsTemplate.replace(/(?<![\w.])(CPU)(?![\w:])/g, chineseName);

    // 2. 替换API端点 - 修复：使用tableName而不是englishName
    jsTemplate = jsTemplate.replace(/\/api\/cpus/g, `/api/${tableName}`);
    jsTemplate = jsTemplate.replace(/\/api\/cpu/g, `/api/${tableName}`);

    // 3. 替换字段引用
    jsTemplate = replaceFieldReferences(jsTemplate, fields, englishName);

    // 4. 替换表格渲染代码
    jsTemplate = replaceTableRenderCode(jsTemplate, fields, englishName);

    // 5. 替换表单填充代码
    jsTemplate = replaceFormFillCode(jsTemplate, fields, englishName);

    // 6. 替换表单数据收集代码
    jsTemplate = replaceFormDataCode(jsTemplate, fields, englishName);

    const jsFilePath = path.join(__dirname, '..', 'public', 'js', `${englishName}-info.js`);
    fs.writeFileSync(jsFilePath, jsTemplate, 'utf8');
    console.log(`JavaScript文件已生成: ${jsFilePath}`);
}

// 替换字段引用 - 修复：更智能的字段映射逻辑
function replaceFieldReferences(jsTemplate, fields, englishName) {
    console.log('开始替换字段引用，目标字段:', fields.map(f => f.name));

    // CPU特定字段的引用
    const cpuFields = ['brand', 'model', 'series', 'socket', 'core_count', 'thread_count', 'base_clock', 'boost_clock', 'tdp', 'l3_cache', 'process_node', 'release_date', 'integrated_graphics', 'price', 'notes'];

    // 创建字段映射表
    const fieldMapping = {};

    // 优先映射相同名称的字段
    cpuFields.forEach(cpuField => {
        const matchingField = fields.find(f => f.name === cpuField);
        if (matchingField) {
            fieldMapping[cpuField] = cpuField; // 保持原名
        } else {
            // 根据字段类型和位置进行智能映射
            if (cpuField === 'brand' && fields.length > 0) {
                fieldMapping[cpuField] = fields[0].name; // 第一个字段通常是品牌
            } else if (cpuField === 'model' && fields.length > 1) {
                fieldMapping[cpuField] = fields[1].name; // 第二个字段通常是型号
            } else if (cpuField === 'price') {
                fieldMapping[cpuField] = 'price'; // 价格字段保持不变
            } else if (cpuField === 'notes') {
                fieldMapping[cpuField] = 'notes'; // 备注字段保持不变
            } else {
                // 其他字段映射到第一个可用字段
                fieldMapping[cpuField] = fields[0] ? fields[0].name : 'name';
            }
        }
    });

    console.log('字段映射表:', fieldMapping);

    // 应用字段映射
    Object.entries(fieldMapping).forEach(([cpuField, newField]) => {
        // 使用更精确的正则表达式，避免误替换
        const regex = new RegExp(`\\b${cpuField}\\b(?=\\s*[\\.,;:\\)\\]\\}]|\\s*$|\\s+)`, 'g');
        jsTemplate = jsTemplate.replace(regex, newField);
    });

    return jsTemplate;
}

// 替换表格渲染代码 - 修复：使用正确的字段属性名
function replaceTableRenderCode(jsTemplate, fields, englishName) {
    console.log('开始替换表格渲染代码...');

    // 查找表格渲染代码的位置
    const renderStart = jsTemplate.indexOf('// 品牌单元格');
    const renderEnd = jsTemplate.indexOf('// 操作单元格');

    if (renderStart !== -1 && renderEnd !== -1) {
        console.log('找到表格渲染代码位置');
        const beforeCode = jsTemplate.substring(0, renderStart);
        const afterCode = jsTemplate.substring(renderEnd);

        // 生成新的表格渲染代码
        let newRenderCode = '';
        const displayFields = fields.slice(0, 4); // 只显示前4个字段

        displayFields.forEach((field, index) => {
            newRenderCode += `            // ${field.displayName}单元格\n`;
            newRenderCode += `            const ${field.name}Cell = document.createElement('td');\n`;
            newRenderCode += `            ${field.name}Cell.className = 'px-3 py-2';\n`;

            if (field.type === 'integer' || field.type === 'float') {
                newRenderCode += `            ${field.name}Cell.textContent = ${englishName}.${field.name} ? ${englishName}.${field.name} : '未知';\n`;
            } else {
                newRenderCode += `            ${field.name}Cell.textContent = ${englishName}.${field.name} || '未知';\n`;
            }
            newRenderCode += `            row.appendChild(${field.name}Cell);\n\n`;
        });

        jsTemplate = beforeCode + newRenderCode + afterCode;
        console.log('表格渲染代码替换完成');
    } else {
        console.warn('未找到表格渲染代码的标记位置');
    }

    return jsTemplate;
}

// 替换表单填充代码 - 修复：直接替换具体的字段引用
function replaceFormFillCode(jsTemplate, fields, englishName) {
    console.log('开始替换表单填充代码...');

    // 查找表单填充的具体位置（在编辑功能中）
    const editSectionStart = jsTemplate.indexOf('// 将CPU数据填充到表单中');
    if (editSectionStart !== -1) {
        console.log('找到表单填充代码位置');

        // 替换具体的字段填充代码
        const cpuFields = ['brand', 'model', 'series', 'socket', 'coreCount', 'threadCount', 'baseClock', 'boostClock', 'tdp', 'l3Cache', 'processNode', 'releaseDate', 'integratedGraphics', 'price', 'notes'];

        cpuFields.forEach((cpuField, index) => {
            if (index < fields.length) {
                const newField = fields[index];
                // 替换表单填充代码
                const fillPattern = new RegExp(`document\\.getElementById\\('${cpuField}'\\)\\.value = cpu\\.\\w+ \\|\\| '';`, 'g');
                jsTemplate = jsTemplate.replace(fillPattern, `document.getElementById('${newField.name}').value = ${englishName}.${newField.name} || '';`);
            }
        });

        console.log('表单填充代码替换完成');
    } else {
        console.warn('未找到表单填充代码的位置');
    }

    return jsTemplate;
}

// 替换表单数据收集代码 - 修复：直接替换具体的字段引用
function replaceFormDataCode(jsTemplate, fields, englishName) {
    console.log('开始替换表单数据收集代码...');

    // 查找表单数据收集的具体位置（在提交功能中）
    const formDataStart = jsTemplate.indexOf('// 添加文本字段');
    if (formDataStart !== -1) {
        console.log('找到表单数据收集代码位置');

        // 替换具体的字段收集代码
        const cpuFields = ['brand', 'model', 'series', 'socket'];

        cpuFields.forEach((cpuField, index) => {
            if (index < fields.length) {
                const newField = fields[index];
                // 替换formData.append代码
                const appendPattern = new RegExp(`formData\\.append\\('${cpuField}', document\\.getElementById\\('${cpuField}'\\)\\.value\\);`, 'g');
                jsTemplate = jsTemplate.replace(appendPattern, `formData.append('${newField.name}', document.getElementById('${newField.name}').value);`);
            }
        });

        // 替换数字字段的处理代码
        const numericFields = ['coreCount', 'threadCount', 'baseClock', 'boostClock', 'tdp', 'l3Cache'];
        numericFields.forEach((cpuField, index) => {
            const fieldIndex = index + 4; // 跳过前4个文本字段
            if (fieldIndex < fields.length && (fields[fieldIndex].type === 'integer' || fields[fieldIndex].type === 'float')) {
                const newField = fields[fieldIndex];
                // 替换数字字段的处理代码
                const numPattern = new RegExp(`const ${cpuField} = document\\.getElementById\\('${cpuField}'\\)\\.value;`, 'g');
                jsTemplate = jsTemplate.replace(numPattern, `const ${newField.name} = document.getElementById('${newField.name}').value;`);

                const appendNumPattern = new RegExp(`formData\\.append\\('\\w+', ${cpuField} \\|\\| '0'\\);`, 'g');
                jsTemplate = jsTemplate.replace(appendNumPattern, `formData.append('${newField.name}', ${newField.name} || '0');`);
            }
        });

        console.log('表单数据收集代码替换完成');
    } else {
        console.warn('未找到表单数据收集代码的位置');
    }

    return jsTemplate;
}

// 创建默认图片
async function createDefaultImage(englishName) {
    const sourcePath = path.join(__dirname, '..', 'public', 'images', 'default-cpu.png');
    const targetPath = path.join(__dirname, '..', 'public', 'images', `default-${englishName}.png`);

    if (fs.existsSync(sourcePath)) {
        fs.copyFileSync(sourcePath, targetPath);
        console.log(`默认图片已创建: ${targetPath}`);
    } else {
        console.warn('默认CPU图片不存在，跳过图片创建');
    }
}

// 更新主页面导航
async function updateMainNavigation(chineseName, englishName, iconClass, pageDescription) {
    const mainPagePath = path.join(__dirname, '..', 'public', 'pc-components.html');
    let content = fs.readFileSync(mainPagePath, 'utf8');

    // 生成新的卡片HTML
    const newCard = `
            <!-- ${chineseName} 卡片 -->
            <div class="component-card bg-white rounded-lg shadow-md overflow-hidden">
                <div class="bg-blue-500 text-white p-4">
                    <h2 class="text-xl font-semibold flex items-center">
                        <i class="${iconClass} mr-2"></i> ${chineseName}
                    </h2>
                </div>
                <div class="p-5">
                    <p class="text-gray-600 mb-4">${pageDescription}</p>
                    <a href="/${englishName}-info.html"
                        class="block w-full bg-blue-500 hover:bg-blue-600 text-white text-center py-2 rounded-md">
                        <i class="fas fa-arrow-right mr-1"></i> 管理${chineseName}
                    </a>
                </div>
            </div>`;

    // 查找插入位置（在最后一个卡片后）
    const insertPosition = content.lastIndexOf('</div>\n        </div>');
    if (insertPosition !== -1) {
        const beforeInsert = content.substring(0, insertPosition);
        const afterInsert = content.substring(insertPosition);
        content = beforeInsert + newCard + '\n\n        ' + afterInsert;

        fs.writeFileSync(mainPagePath, content, 'utf8');
        console.log('主页面导航已更新');
    } else {
        console.log('未找到插入位置，跳过导航更新');
    }
}

// 更新app.js路由注册
async function updateAppRoutes(englishName, tableName) {
    const appPath = path.join(__dirname, '..', 'app.js');
    let content = fs.readFileSync(appPath, 'utf8');

    // 添加路由引入
    const routeImport = `const ${englishName}Routes = require('./routes/${englishName}-routes');`;
    const importPosition = content.indexOf('// 路由配置');
    if (importPosition !== -1) {
        const beforeImport = content.substring(0, importPosition);
        const afterImport = content.substring(importPosition);
        content = beforeImport + routeImport + '\n' + afterImport;
    }

    // 添加路由使用
    const routeUse = `app.use('/api/${englishName}', ${englishName}Routes);`;
    const usePosition = content.indexOf('// 启动服务器');
    if (usePosition !== -1) {
        const beforeUse = content.substring(0, usePosition);
        const afterUse = content.substring(usePosition);
        content = beforeUse + routeUse + '\n\n' + afterUse;
    }

    fs.writeFileSync(appPath, content, 'utf8');
    console.log('app.js路由注册已更新');
}

module.exports = router;
