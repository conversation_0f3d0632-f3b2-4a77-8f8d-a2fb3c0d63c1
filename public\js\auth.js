// auth.js - 身份验证相关工具函数

// 确保在浏览器环境中运行
if (typeof window === 'undefined') {
    throw new Error('此文件仅用于浏览器环境');
}

// 获取存储的token
function getToken() {
    return localStorage.getItem('token') || sessionStorage.getItem('token');
}

// 设置token
function setToken(token, remember = false) {
    if (remember) {
        localStorage.setItem('token', token);
    } else {
        sessionStorage.setItem('token', token);
    }
}

// 清除token
function clearToken() {
    localStorage.removeItem('token');
    sessionStorage.removeItem('token');
}

// 发送带认证的请求
async function sendAuthenticatedRequest(url, method = 'GET', data = null) {
    const token = getToken();
    if (!token) {
        throw new Error('未找到认证token');
    }

    const options = {
        method: method,
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        }
    };

    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
        options.body = JSON.stringify(data);
    }

    const response = await fetch(url, options);

    // 如果是401错误，说明token过期或无效
    if (response.status === 401) {
        clearToken();
        window.location.href = '/login.html?redirect=' + encodeURIComponent(window.location.pathname);
        throw new Error('认证失败，请重新登录');
    }

    // 检查响应的Content-Type
    const contentType = response.headers.get('content-type');
    let result;

    if (contentType && contentType.includes('application/json')) {
        try {
            result = await response.json();
        } catch (e) {
            throw new Error(`JSON解析失败: ${e.message}`);
        }
    } else {
        // 如果不是JSON响应，获取文本内容
        const text = await response.text();
        throw new Error(`服务器返回非JSON响应 (${response.status}): ${text.substring(0, 100)}...`);
    }

    if (!response.ok) {
        throw new Error(result.message || `请求失败: ${response.status}`);
    }

    return result;
}

// 验证token是否有效
async function validateToken() {
    const token = getToken();
    if (!token) {
        return null;
    }

    try {
        const response = await fetch('/api/validate-token', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (response.ok) {
            const data = await response.json();
            return data.valid ? data.user : null;
        }
    } catch (error) {
        console.error('Token验证失败:', error);
    }

    return null;
}

// 检查用户是否为管理员
function isAdmin(user) {
    return user && user.role === 'admin';
}

// 登出
function logout() {
    clearToken();
    window.location.href = '/login.html';
}

// 重定向到登录页面
function redirectToLogin() {
    const currentUrl = encodeURIComponent(window.location.pathname + window.location.search);
    window.location.href = `/login.html?redirect=${currentUrl}`;
}

// 检查是否需要登录
async function requireAuth() {
    const user = await validateToken();
    if (!user) {
        redirectToLogin();
        return null;
    }
    return user;
}

// 检查是否需要管理员权限
async function requireAdmin() {
    const user = await requireAuth();
    if (!user || !isAdmin(user)) {
        throw new Error('需要管理员权限');
    }
    return user;
}
