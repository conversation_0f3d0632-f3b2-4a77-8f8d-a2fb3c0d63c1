/**
 * 权限辅助工具 - 处理用户权限检查和UI适配
 */

// 存储用户角色信息
let currentUserRole = null;

// 检查用户是否为管理员
async function isAdmin() {
    if (currentUserRole !== null) {
        return currentUserRole === 'admin';
    }
    
    const token = localStorage.getItem('token');
    if (!token) {
        return false;
    }
    
    try {
        const response = await fetch('/api/validate-token', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        if (!response.ok) {
            return false;
        }
        
        const data = await response.json();
        currentUserRole = data.user?.role || null;
        return currentUserRole === 'admin';
    } catch (error) {
        console.error('检查管理员权限时出错:', error);
        return false;
    }
}

// 根据用户角色显示/隐藏UI元素
async function setupPermissionBasedUI() {
    const isAdminUser = await isAdmin();
    
    // 定义需要管理员权限的元素选择器
    const adminOnlySelectors = [
        '.admin-only',
        '.btn-add',
        '.btn-edit',
        '.btn-delete',
        '[data-action="add"]',
        '[data-action="edit"]',
        '[data-action="delete"]'
    ];
    
    // 处理需要管理员权限的元素
    adminOnlySelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(el => {
            if (isAdminUser) {
                el.classList.remove('hidden');
                el.removeAttribute('disabled');
            } else {
                // 隐藏或禁用元素
                if (el.tagName === 'BUTTON' || el.tagName === 'INPUT' || el.tagName === 'SELECT') {
                    el.setAttribute('disabled', 'disabled');
                    el.title = '只有管理员可以执行此操作';
                } else {
                    el.classList.add('hidden');
                }
            }
        });
    });
    
    // 处理表格中的操作按钮
    handleTableActionButtons(isAdminUser);
    
    // 添加表单提交拦截
    interceptFormSubmissions(isAdminUser);
    
    // 添加权限不足的错误处理
    setupPermissionErrorHandling();
    
    // 添加删除操作的拦截器
    setupDeleteOperationInterception();
    
    // 返回用户是否为管理员，以便调用者使用
    return isAdminUser;
}

// 处理表格中的操作按钮
function handleTableActionButtons(isAdminUser) {
    // 查找所有表格
    const tables = document.querySelectorAll('table');
    
    tables.forEach(table => {
        // 查找表格中的操作按钮
        const actionButtons = table.querySelectorAll('.action-btn, .edit-btn, .delete-btn, button[data-action], a[data-action]');
        
        actionButtons.forEach(btn => {
            // 检查按钮是否用于编辑或删除（通过类名或数据属性）
            const isEditOrDelete = btn.classList.contains('edit-btn') || 
                                 btn.classList.contains('delete-btn') ||
                                 btn.dataset.action === 'edit' || 
                                 btn.dataset.action === 'delete';
            
            if (isEditOrDelete && !isAdminUser) {
                // 如果是普通用户，隐藏编辑和删除按钮
                btn.style.display = 'none';
                
                // 如果是TD元素内的按钮，可能需要隐藏整个TD
                const parentTd = btn.closest('td');
                if (parentTd && parentTd.children.length === 1) {
                    // 如果TD中只有这一个按钮，就隐藏整个TD
                    parentTd.style.display = 'none';
                }
            }
        });
    });
}

// 拦截表单提交
function interceptFormSubmissions(isAdminUser) {
    if (isAdminUser) return; // 管理员不需要拦截
    
    // 查找所有表单
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(event) {
            // 阻止表单提交
            event.preventDefault();
            
            // 显示权限不足提示
            if (typeof showToast === 'function') {
                showToast('权限不足，只有管理员可以保存数据', 'error');
            } else if (typeof showErrorMessage === 'function') {
                showErrorMessage('权限不足，只有管理员可以保存数据');
            } else {
                alert('权限不足，只有管理员可以保存数据');
            }
            
            return false;
        });
    });
    
    // 禁用保存/提交按钮
    const submitButtons = document.querySelectorAll('button[type="submit"], input[type="submit"], .save-btn, .submit-btn');
    submitButtons.forEach(btn => {
        btn.addEventListener('click', function(event) {
            event.preventDefault();
            
            // 显示权限不足提示
            if (typeof showToast === 'function') {
                showToast('权限不足，只有管理员可以保存数据', 'error');
            } else if (typeof showErrorMessage === 'function') {
                showErrorMessage('权限不足，只有管理员可以保存数据');
            } else {
                alert('权限不足，只有管理员可以保存数据');
            }
            
            return false;
        });
    });
}

// 设置删除操作拦截
function setupDeleteOperationInterception() {
    // 检查是否已经设置过删除操作拦截，避免重复设置
    if (window._deleteOperationInterceptionSetup) {
        return;
    }
    window._deleteOperationInterceptionSetup = true;

    // 保存原始的fetch函数
    const originalFetch = window.fetch;

    // 重写fetch函数以拦截删除请求
    window.fetch = async function(url, options = {}) {
        // 检查是否为删除操作
        if (options && options.method === 'DELETE') {
            console.log('[权限检查] 拦截到删除请求:', url);
            
            // 检查用户权限
            const isAdminUser = await isAdmin();
            if (!isAdminUser) {
                console.warn('[权限检查] 权限不足，普通用户无法执行删除操作');
                // 返回权限错误响应
                return Promise.resolve(new Response(JSON.stringify({
                    error: '权限不足',
                    message: '只有管理员可以删除数据'
                }), {
                    status: 403,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }));
            }
            
            // 确保删除请求包含Authorization头
            const token = localStorage.getItem('token');
            if (token) {
                // 初始化headers对象（如果不存在）
                options.headers = options.headers || {};
                
                // 如果options.headers是Headers对象，将其转换为普通对象
                if (options.headers instanceof Headers) {
                    const plainHeaders = {};
                    options.headers.forEach((value, key) => {
                        plainHeaders[key] = value;
                    });
                    options.headers = plainHeaders;
                }
                
                // 添加Authorization头
                options.headers['Authorization'] = `Bearer ${token}`;
            } else {
                console.warn('[权限检查] 未找到token，可能导致后端权限验证失败');
            }
        }
        
        // 对于非删除请求或管理员的删除请求，正常执行
        return originalFetch.apply(this, [url, options]);
    };
    
    // 添加全局点击事件监听，拦截删除按钮点击
    document.addEventListener('click', async function(event) {
        // 查找可能触发删除操作的元素
        const deleteButton = event.target.closest('.btn-delete, [data-action="delete"]');
        if (deleteButton) {
            const isAdminUser = await isAdmin();
            if (!isAdminUser) {
                // 阻止事件传播
                event.preventDefault();
                event.stopPropagation();
                console.warn('[权限检查] 权限不足，阻止删除操作');
                
                // 提示用户
                if (typeof showToast === 'function') {
                    showToast('只有管理员可以删除数据', 'error');
                } else if (typeof showErrorMessage === 'function') {
                    showErrorMessage('只有管理员可以删除数据');
                } else {
                    console.error('权限不足，只有管理员可以删除数据');
                }
                
                return false;
            }
        }
    }, true);
}

// 设置权限错误处理
function setupPermissionErrorHandling() {
    // 检查是否已经设置过权限错误处理，避免重复设置
    if (window._permissionErrorHandlerSetup) {
        return;
    }
    window._permissionErrorHandlerSetup = true;

    // 拦截API响应，处理权限错误
    const originalFetch = window.fetch;
    window.fetch = async function() {
        try {
            const response = await originalFetch.apply(this, arguments);

            // 如果是403错误且不是token验证问题，显示权限不足提示
            if (response.status === 403 && !arguments[0].includes('validate-token')) {
                try {
                    const clonedResponse = response.clone();
                    const data = await clonedResponse.json();

                    if (data.error === '权限不足') {
                        showPermissionError(data.message || '您没有权限执行此操作');
                    }
                } catch (e) {
                    // 无法解析JSON，忽略
                }
            }

            return response;
        } catch (error) {
            throw error;
        }
    };
}

// 显示权限错误提示
function showPermissionError(message) {
    // 如果页面有Toast组件，使用Toast显示
    if (typeof showToast === 'function') {
        showToast(message, 'error');
        return;
    }
    
    // 否则使用alert
    alert(`权限错误: ${message}`);
}

// 初始化通用信息管理页面权限控制
async function initCommonPagePermissions() {
    // 权限检查
    const isAdminUser = await isAdmin();
    console.log(`用户角色: ${isAdminUser ? '管理员' : '普通用户'}`);
    
    // 找到表单元素(如果存在)
    const form = document.querySelector('form');
    if (form) {
        // 如果不是管理员，禁用表单
        if (!isAdminUser) {
            const inputs = form.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                input.setAttribute('disabled', 'disabled');
            });
            
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.setAttribute('disabled', 'disabled');
                submitBtn.title = '只有管理员可以添加或修改数据';
            }
        }
    }
    
    // 找到操作按钮
    const actionButtons = document.querySelectorAll('.btn-add, .btn-edit, .btn-delete, [data-action="add"], [data-action="edit"], [data-action="delete"]');
    actionButtons.forEach(btn => {
        if (!isAdminUser) {
            btn.style.display = 'none';
        }
    });
    
    // 如果是管理员，显示管理员标识
    if (isAdminUser) {
        const header = document.querySelector('h1, h2') || document.body;
        const adminBadge = document.createElement('span');
        adminBadge.className = 'bg-green-500 text-white text-xs px-2 py-1 rounded ml-2';
        adminBadge.innerText = '管理员';
        if (header.tagName === 'H1' || header.tagName === 'H2') {
            header.appendChild(adminBadge);
        }
    }
}

// 页面加载时初始化权限UI
document.addEventListener('DOMContentLoaded', function() {
    // 仅在非登录页面和非用户管理页面进行权限处理
    // 用户管理页面有自己的权限检查逻辑
    if (!window.location.pathname.endsWith('/login.html') &&
        !window.location.pathname.endsWith('/user-management.html')) {
        setupPermissionBasedUI();
    }
});