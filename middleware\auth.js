const jwt = require('jsonwebtoken');

// 验证JWT令牌中间件
const verifyToken = (req, res, next) => {
    const token = req.headers.authorization?.split(' ')[1];
    
    if (!token) {
        return res.status(401).json({ error: '未提供令牌' });
    }
    
    try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your_jwt_secret');
        req.user = decoded;
        console.log('Token验证成功，用户信息:', decoded);
        next();
    } catch (error) {
        console.error('Token验证失败:', error);
        return res.status(401).json({ error: '令牌无效或已过期' });
    }
};

// 检查管理员角色中间件
const checkAdminRole = (req, res, next) => {
    verifyToken(req, res, () => {
        if (req.user && req.user.role === 'admin') {
            next();
        } else {
            return res.status(403).json({ error: '需要管理员权限' });
        }
    });
};

// 检查读取权限中间件（允许所有已登录用户）
const checkReadPermission = (req, res, next) => {
    verifyToken(req, res, next);
};

// 检查并添加用户角色信息中间件
const checkUserRole = (req, res, next) => {
    verifyToken(req, res, () => {
        // 给响应添加用户角色信息，以便前端判断权限
        res.locals.userRole = req.user.role;
        next();
    });
};

module.exports = {
    verifyToken,
    checkAdminRole,
    checkReadPermission,
    checkUserRole
}; 