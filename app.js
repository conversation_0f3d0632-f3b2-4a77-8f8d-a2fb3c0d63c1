﻿const express = require('express');
const cors = require('cors');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcrypt');
const cookieParser = require('cookie-parser');
require('dotenv').config();

const app = express();
const PORT = 3000;
const BACKUP_PORTS = [3001, 3002, 3003, 3004, 3005];

// =======================================
// Middleware
// =======================================
app.use(cors());
app.use(express.json());
app.use(cookieParser());

// 定义验证用户角色的中间件，用于主页面访问限制
const restrictIndexPageToAdmin = (req, res, next) => {
  // 如果是请求根路径或index.html，检查是否为管理员
  if (req.path === '/' || req.path === '/index.html') {
    // 尝试从请求头或cookie中获取token
    let token = null;
    
    // 检查Authorization头
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7);
    }
    
    // 如果headers中没有token，尝试从cookie获取
    if (!token && req.cookies && req.cookies.token) {
      token = req.cookies.token;
    }
    
    // 如果从localStorage中获取token（通过查询参数）
    if (!token && req.query && req.query.token) {
      token = req.query.token;
    }
    
    if (!token) {
      // 没有token，重定向到登录页面
      console.log('没有找到token，重定向到登录页面');
      const currentUrl = encodeURIComponent(req.originalUrl);
      return res.redirect(`/login.html?redirect=${currentUrl}`);
    }
    
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your_jwt_secret');
      console.log('Token解析结果:', decoded);
      
      if (decoded && decoded.role === 'admin') {
        // 是管理员，允许访问
        console.log('用户是管理员，允许访问主页');
        next();
      } else {
        // 不是管理员，重定向到pc-components.html页面
        console.log('用户不是管理员，重定向到组件页面');
        res.redirect('/pc-components.html');
      }
    } catch (error) {
      // token无效，重定向到登录
      console.error('无效的token:', error);
      // 重定向到登录页面，并包含当前页面作为重定向参数
      const currentUrl = encodeURIComponent(req.originalUrl);
      res.redirect(`/login.html?redirect=${currentUrl}`);
    }
  } else {
    // 不是请求主页面，放行
    next();
  }
};

// 拦截根路径和index.html请求
app.get('/', restrictIndexPageToAdmin);
app.get('/index.html', restrictIndexPageToAdmin);

// Code generator routes will be mounted in the protected API section

// Test route for code generator
app.get('/api/code-generator/test', (req, res) => {
    console.log('测试路由被访问');
    res.json({ message: 'Code generator route is working!' });
});

// Serve static files from 'public' directory. This must come before any authentication
// middleware to allow loading of HTML, CSS, JS, and images.
app.use(express.static('public'));

// Serve uploaded files from 'uploads' directory
app.use('/uploads', express.static('uploads'));

app.get('*.webp', (req, res, next) => {
  res.set('Content-Type', 'image/webp');
  next();
});

// =======================================
// Database
// =======================================
const db = require('./config/db');
require('./db');

// 初始化笔记系统数据库表
const { createNotesSchema, checkNotesSchema } = require('./db/notes-schema');
(async () => {
    try {
        const schemaCheck = await checkNotesSchema();
        if (!schemaCheck.allExist) {
            console.log('笔记系统表不完整，开始创建...');
            console.log('缺失的表:', schemaCheck.missingTables);
            await createNotesSchema();
        } else {
            console.log('笔记系统数据库表已存在');
        }
    } catch (error) {
        console.error('初始化笔记系统数据库失败:', error);
    }
})();

// =======================================
// Authentication Middleware
// =======================================
// 导入认证中间件
const { verifyToken, checkAdminRole, checkReadPermission } = require('./middleware/auth');
const actionLogger = require('./middleware/action-logger');


// =======================================
// Public Routes (No Authentication)
// =======================================
const JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret';
const SALT_ROUNDS = 10; // bcrypt salt rounds

// Helper endpoint to hash a password. 
// In a real application, you would use this logic in your user registration or password update endpoints.
app.post('/api/hash-password', async (req, res) => {
    const { password } = req.body;
    if (!password) {
        return res.status(400).json({ error: 'Password is required' });
    }
    try {
        const hashedPassword = await bcrypt.hash(password, SALT_ROUNDS);
        res.json({ original: password, hashed: hashedPassword });
    } catch (err) {
        console.error('Hashing failed:', err);
        res.status(500).json({ error: 'Server error during hashing' });
    }
});

// =======================================
// User Routes will be registered after protected routes
// =======================================

// =======================================
// Public API Routes
// =======================================
// Load route modules for public access
const commonPhrasesRoutes = require('./routes/common-phrases');
const customerServiceQaRoutes = require('./routes/customer-service-qa-routes');
const cpuRoutes = require('./routes/cpuRoutes');
const ramRoutes = require('./routes/ramRoutes');

// Mount the public routes
app.use('/api/customer-service', customerServiceQaRoutes);
app.use('/api/common-phrases', commonPhrasesRoutes);
// 添加 RAM 路由作为公共路由，不需要认证
app.use('/api/rams', ramRoutes);

// 代码生成器路由 - 开发工具，不需要认证
const modernCodeGeneratorRoutes = require('./routes/modern-code-generator');
app.use('/api/modern-code-generator', modernCodeGeneratorRoutes);

// 图片上传路由 - 公共路由，不需要认证
const uploadRoutes = require('./routes/upload-routes');

app.use('/api/upload', uploadRoutes);

// 笔记系统路由 - 需要认证
const notesRoutes = require('./routes/notes-routes');
app.use('/api/notes', notesRoutes);


// Token验证路由 - 公共路由 (支持GET和POST)
app.post('/api/validate-token', verifyToken, (req, res) => {
    res.json({
        success: true,
        message: 'Token有效',
        user: req.user
    });
});

app.get('/api/validate-token', verifyToken, (req, res) => {
    res.json({
        valid: true,
        message: 'Token有效',
        user: req.user
    });
});

// CPU路由已移动到受保护路由下

// =======================================
// Protected API Routes
// =======================================
// Create a new router for all protected API calls
const apiRouter = express.Router();
apiRouter.use(verifyToken); // 使用导入的verifyToken中间件
apiRouter.use(actionLogger); // 在身份验证后添加操作日志记录

// Load route modules
const storageRoutes = require('./routes/storageRoutes');
const gpuRoutes = require('./routes/gpu-routes');
const motherboardRoutes = require('./routes/motherboard-routes');
const psuRoutes = require('./routes/psu-routes');
const coolerRoutes = require('./routes/cooler-routes');
const fanRoutes = require('./routes/fan-routes');
const caseRoutes = require('./routes/case-routes');
const monitorRoutes = require('./routes/monitor-routes');
const deliveryRoutes = require('./routes/delivery');
const feedbackRoutes = require('./routes/store-feedback');
const priceRoutes = require('./routes/price-routes');
const rawTableDataRoutes = require('./routes/raw-table-data-routes');
const analyticsRoutes = require('./routes/analytics-routes');
const actionLogsRoutes = require('./routes/action-logs-routes');

const codeGeneratorRoutes = require('./routes/code-generator');
const excelTemplateRoutes = require('./routes/excel-template-routes');
const revenueRecordsRoutes = require('./routes/revenue-records');

// Register modular routes on the protected router
apiRouter.use('/cpus', cpuRoutes); // 将CPU路由移动到这里，使其受到验证保护
apiRouter.use('/storages', storageRoutes);
apiRouter.use('/gpus', gpuRoutes);
apiRouter.use('/motherboards', motherboardRoutes);
apiRouter.use('/psus', psuRoutes);
apiRouter.use('/coolers', coolerRoutes);
apiRouter.use('/fans', fanRoutes);
apiRouter.use('/cases', caseRoutes);
apiRouter.use('/monitors', monitorRoutes);
apiRouter.use('/store-feedback', feedbackRoutes);
apiRouter.use('/', deliveryRoutes); // Contains /records, /stores, /stats etc.
apiRouter.use('/', priceRoutes);
apiRouter.use('/raw-table-data', rawTableDataRoutes);
apiRouter.use('/analytics', analyticsRoutes);
apiRouter.use('/action-logs', actionLogsRoutes);

apiRouter.use('/code-generator', codeGeneratorRoutes);

apiRouter.use('/excel-template', excelTemplateRoutes);
apiRouter.use('/revenue-records', revenueRecordsRoutes);

// Register any remaining one-off APIs on the protected router
apiRouter.post('/price', async (req, res) => {
    const { cpu, ram, storage, gpu, motherboard, psu, cooler, fan, cases, monitor, quantity, profit, includeInstall, shippingFee, otherFee } = req.body;
    try {
        const query = 'CALL CalculatePrice(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)';
        const [result] = await db.query(query, [cpu, ram, storage, gpu, motherboard, psu, cooler, fan, cases, monitor, quantity, profit, includeInstall, shippingFee, otherFee]);
        res.json(result);
    } catch (err) {
        console.error('价格计算失败:', err);
        return res.status(500).json({ error: '数据库查询错误', details: err.message });
    }
});

apiRouter.get('/total-components', async (req, res) => {
    try {
        // 使用单一SQL查询替代多次查询，通过UNION ALL合并结果
        const query = `
            SELECT 'cpus' as component_type, COUNT(*) as count FROM cpus
            UNION ALL
            SELECT 'rams' as component_type, COUNT(*) as count FROM ram
            UNION ALL
            SELECT 'storages' as component_type, COUNT(*) as count FROM storage
            UNION ALL
            SELECT 'gpus' as component_type, COUNT(*) as count FROM gpus
            UNION ALL
            SELECT 'motherboards' as component_type, COUNT(*) as count FROM motherboards
            UNION ALL
            SELECT 'psus' as component_type, COUNT(*) as count FROM psus
            UNION ALL
            SELECT 'coolers' as component_type, COUNT(*) as count FROM coolers
            UNION ALL
            SELECT 'fans' as component_type, COUNT(*) as count FROM fans
            UNION ALL
            SELECT 'cases' as component_type, COUNT(*) as count FROM cases
            UNION ALL
            SELECT 'monitors' as component_type, COUNT(*) as count FROM monitors
        `;
        
        const [results] = await db.query(query);
        
        // 将结果转换为前端期望的格式
        const response = {};
        results.forEach(row => {
            response[row.component_type] = row.count;
        });
        
        res.json(response);
    } catch (err) {
        console.error('获取组件统计数据失败:', err);
        return res.status(500).json({ error: '数据库查询错误' });
    }
});

// 调试API：检查当前用户角色
app.get('/api/debug/check-role', (req, res) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    
    if (!token) {
        return res.json({ 
            status: 'error',
            message: '未提供Token',
            hasToken: false
        });
    }
    
    try {
        const user = jwt.verify(token, JWT_SECRET);
        console.log('调试API - 用户角色检查:', user);
        
        res.json({
            status: 'success',
            hasToken: true,
            user: {
                id: user.id,
                username: user.username,
                role: user.role
            }
        });
    } catch (err) {
        console.error('调试API - Token验证失败:', err);
        res.json({
            status: 'error',
            message: 'Token验证失败',
            hasToken: true,
            error: err.message
        });
    }
});

// Mount public user routes BEFORE protected routes
const userRoutes = require('./routes/user-routes');
app.use('/api', userRoutes);



// 临时测试Excel模板读取（不需要认证）
app.get('/test/excel-template', async (req, res) => {
    try {
        const XLSX = require('xlsx');
        const path = require('path');
        const fs = require('fs');

        const templatePath = path.join(__dirname, 'demo.xlsx');

        if (!fs.existsSync(templatePath)) {
            return res.status(404).json({
                success: false,
                message: '模板文件不存在'
            });
        }

        // 读取Excel文件
        const workbook = XLSX.readFile(templatePath);
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];

        // 转换为JSON格式
        const jsonData = XLSX.utils.sheet_to_json(worksheet, {
            header: 1,
            defval: ''
        });

        res.json({
            success: true,
            message: 'Excel模板读取成功',
            data: {
                sheetName: sheetName,
                data: jsonData,
                range: worksheet['!ref']
            }
        });

    } catch (error) {
        console.error('读取Excel模板失败:', error);
        res.status(500).json({
            success: false,
            message: '读取模板失败: ' + error.message
        });
    }
});

// 临时测试保存Excel数据（不需要认证）
app.post('/test/excel-template/save', async (req, res) => {
    try {
        const { templateName, remarks, data, sheetName } = req.body;

        if (!templateName || !remarks || !data) {
            return res.status(400).json({
                success: false,
                message: '缺少必要参数'
            });
        }

        // 保存到数据库
        const [result] = await db.query(
            'INSERT INTO excel_template_records (template_name, remarks, data_json, created_by) VALUES (?, ?, ?, ?)',
            [templateName, remarks, JSON.stringify(data), 'test-user']
        );

        // 生成Excel文件
        const XLSX = require('xlsx');
        const path = require('path');
        const fs = require('fs');

        const workbook = XLSX.utils.book_new();
        const worksheet = XLSX.utils.aoa_to_sheet(data);
        XLSX.utils.book_append_sheet(workbook, worksheet, sheetName || 'Sheet1');

        // 确保目录存在
        const uploadsDir = path.join(__dirname, 'uploads', 'excel_templates');
        if (!fs.existsSync(uploadsDir)) {
            fs.mkdirSync(uploadsDir, { recursive: true });
        }

        // 生成文件名
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const fileName = `${templateName}_${timestamp}.xlsx`;
        const filePath = path.join(uploadsDir, fileName);

        // 写入文件
        XLSX.writeFile(workbook, filePath);

        // 更新数据库记录，添加文件路径
        await db.query(
            'UPDATE excel_template_records SET file_path = ? WHERE id = ?',
            [path.join('uploads', 'excel_templates', fileName), result.insertId]
        );

        res.json({
            success: true,
            message: '数据保存成功',
            recordId: result.insertId,
            fileName: fileName
        });

    } catch (error) {
        console.error('保存Excel数据失败:', error);
        res.status(500).json({
            success: false,
            message: '保存失败: ' + error.message
        });
    }
});

// 临时测试获取记录列表（不需要认证）
app.get('/test/excel-template/records', async (req, res) => {
    try {
        const [records] = await db.query(`
            SELECT id, template_name, remarks, created_by, created_at, updated_at
            FROM excel_template_records
            ORDER BY created_at DESC
            LIMIT 10
        `);

        res.json({
            success: true,
            data: records,
            pagination: {
                page: 1,
                totalPages: 1,
                total: records.length
            }
        });

    } catch (error) {
        console.error('获取记录列表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取记录失败: ' + error.message
        });
    }
});

// 临时测试获取记录详情（不需要认证）
app.get('/test/excel-template/records/:id', async (req, res) => {
    try {
        const recordId = req.params.id;

        const [records] = await db.query(
            'SELECT * FROM excel_template_records WHERE id = ?',
            [recordId]
        );

        if (records.length === 0) {
            return res.status(404).json({
                success: false,
                message: '记录不存在'
            });
        }

        const record = records[0];

        // 解析JSON数据
        if (record.data_json) {
            try {
                record.data_json = JSON.parse(record.data_json);
            } catch (e) {
                console.error('解析JSON数据失败:', e);
                record.data_json = [];
            }
        }

        res.json({
            success: true,
            data: record
        });

    } catch (error) {
        console.error('获取记录详情失败:', error);
        res.status(500).json({
            success: false,
            message: '获取记录详情失败: ' + error.message
        });
    }
});



// Mount the protected router to the /api path
app.use('/api', apiRouter);

// =======================================
// Server Startup
// =======================================
function startServer(port) {
    app.listen(port, () => {
        console.log(`服务器运行在 http://localhost:${port}`);
    }).on('error', (err) => {
        if (err.code === 'EADDRINUSE') {
            console.log(`端口 ${port} 被占用, 尝试下一个端口`);
            const currentPortIndex = (port === PORT) ? -1 : BACKUP_PORTS.indexOf(port);
            const nextPort = (currentPortIndex + 1 < BACKUP_PORTS.length) ? BACKUP_PORTS[currentPortIndex + 1] : null;
            if (nextPort) {
                startServer(nextPort);
            } else {
                console.error('所有备用端口都已被占用');
            }
        } else {
            console.error(err);
        }
    });
}

startServer(PORT);