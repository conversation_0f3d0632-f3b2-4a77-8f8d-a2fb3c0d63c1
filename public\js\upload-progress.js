/**
 * 通用上传进度条功能库
 * 为所有页面提供统一的图片上传进度条功能
 */

// 进度条相关函数
function showUploadProgress() {
    const progressContainer = document.getElementById('uploadProgressContainer');
    const progressBar = document.getElementById('uploadProgressBar');
    const progressText = document.getElementById('uploadProgressText');
    const progressPercent = document.getElementById('uploadProgressPercent');
    
    if (progressContainer) {
        progressContainer.classList.remove('hidden');
        progressBar.style.width = '0%';
        if (progressText) {
            progressText.innerHTML = '<i id="uploadProgressIcon" class="fas fa-clock mr-1"></i>准备上传...';
        }
        if (progressPercent) {
            progressPercent.textContent = '0%';
        }
    }
}

function hideUploadProgress() {
    const progressContainer = document.getElementById('uploadProgressContainer');
    if (progressContainer) {
        setTimeout(() => {
            progressContainer.classList.add('hidden');
        }, 1000); // 延迟1秒隐藏，让用户看到完成状态
    }
}

function updateUploadProgress(percent, text, icon = 'fa-upload') {
    const progressBar = document.getElementById('uploadProgressBar');
    const progressText = document.getElementById('uploadProgressText');
    const progressPercent = document.getElementById('uploadProgressPercent');
    
    if (progressBar) {
        progressBar.style.width = percent + '%';
    }
    if (progressText && text) {
        const iconElement = `<i id="uploadProgressIcon" class="fas ${icon} mr-1"></i>`;
        progressText.innerHTML = iconElement + text;
    }
    if (progressPercent) {
        progressPercent.textContent = Math.round(percent) + '%';
    }
}

function disableSubmitButton(disabled, editingMode = false) {
    const submitBtn = document.querySelector('button[type="submit"]');
    if (submitBtn) {
        submitBtn.disabled = disabled;
        if (disabled) {
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> 正在处理...';
            submitBtn.classList.add('opacity-50', 'cursor-not-allowed');
        } else {
            // 根据页面类型恢复按钮文本
            const pageName = document.title.toLowerCase();
            let buttonText = '保存信息';
            
            if (pageName.includes('散热器')) {
                buttonText = editingMode ? '更新散热器信息' : '保存散热器信息';
            } else if (pageName.includes('显示器')) {
                buttonText = editingMode ? '更新显示器信息' : '保存显示器信息';
            } else if (pageName.includes('主板')) {
                buttonText = editingMode ? '更新主板信息' : '保存主板信息';
            } else if (pageName.includes('电源')) {
                buttonText = editingMode ? '更新电源信息' : '保存电源信息';
            } else if (pageName.includes('机箱')) {
                buttonText = editingMode ? '更新机箱信息' : '保存机箱信息';
            } else if (pageName.includes('风扇')) {
                buttonText = editingMode ? '更新风扇信息' : '保存风扇信息';
            } else if (pageName.includes('内存')) {
                buttonText = editingMode ? '更新内存信息' : '保存内存信息';
            } else if (pageName.includes('存储')) {
                buttonText = editingMode ? '更新存储信息' : '保存存储信息';
            }
            
            submitBtn.innerHTML = `<i class="fas fa-save mr-1"></i> ${buttonText}`;
            submitBtn.classList.remove('opacity-50', 'cursor-not-allowed');
        }
    }
}

// 带进度的上传函数
function uploadWithProgress(url, method, formData) {
    return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        
        // 设置超时时间（30秒）
        xhr.timeout = 30000;
        
        // 上传进度监听
        xhr.upload.addEventListener('progress', (e) => {
            if (e.lengthComputable) {
                const percentComplete = (e.loaded / e.total) * 70; // 上传占70%
                const uploadedMB = (e.loaded / 1024 / 1024).toFixed(1);
                const totalMB = (e.total / 1024 / 1024).toFixed(1);
                updateUploadProgress(percentComplete, `正在上传图片... (${uploadedMB}MB / ${totalMB}MB)`, 'fa-upload');
            }
        });
        
        // 请求状态变化监听
        xhr.addEventListener('readystatechange', () => {
            if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
                updateUploadProgress(75, '上传完成，正在处理数据...', 'fa-check-circle');
            } else if (xhr.readyState === XMLHttpRequest.LOADING) {
                updateUploadProgress(85, '正在转换为WebP格式...', 'fa-sync fa-spin');
            }
        });
        
        // 请求完成监听
        xhr.addEventListener('load', () => {
            updateUploadProgress(100, '处理完成！', 'fa-check-circle');
            
            if (xhr.status >= 200 && xhr.status < 300) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    resolve(response);
                } catch (e) {
                    reject(new Error('响应解析失败'));
                }
            } else {
                try {
                    const errorResponse = JSON.parse(xhr.responseText);
                    reject(new Error(errorResponse.message || '操作失败'));
                } catch (e) {
                    reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
                }
            }
        });
        
        // 错误监听
        xhr.addEventListener('error', () => {
            reject(new Error('网络错误'));
        });
        
        // 超时监听
        xhr.addEventListener('timeout', () => {
            reject(new Error('请求超时'));
        });
        
        // 打开请求
        xhr.open(method, url);
        
        // 设置请求头（必须在open之后）
        const token = localStorage.getItem('token');
        if (token) {
            xhr.setRequestHeader('Authorization', `Bearer ${token}`);
        }
        
        // 发送请求
        xhr.send(formData);
    });
}

// 通用进度条HTML模板
function getProgressBarHTML(colorClass = 'cyan') {
    return `
        <!-- 上传进度条 -->
        <div id="uploadProgressContainer" class="mt-2 hidden">
            <div class="bg-gray-200 dark:bg-gray-700 rounded-full h-3 mb-2 overflow-hidden">
                <div id="uploadProgressBar" class="bg-gradient-to-r from-${colorClass}-500 to-${colorClass}-600 dark:from-${colorClass}-400 dark:to-${colorClass}-500 h-3 rounded-full transition-all duration-500 ease-out relative" style="width: 0%">
                    <!-- 进度条光泽效果 -->
                    <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-20 animate-pulse"></div>
                </div>
            </div>
            <div class="flex justify-between items-center text-xs">
                <span id="uploadProgressText" class="text-gray-600 dark:text-gray-400 flex items-center">
                    <i id="uploadProgressIcon" class="fas fa-upload mr-1"></i>
                    准备上传...
                </span>
                <span id="uploadProgressPercent" class="text-${colorClass}-600 dark:text-${colorClass}-400 font-medium">0%</span>
            </div>
        </div>
    `;
}
