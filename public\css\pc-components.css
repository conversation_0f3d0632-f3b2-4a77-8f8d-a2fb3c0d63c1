.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.component-card {
    transition: all 0.2s ease;
}

.component-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 暗色主题下的悬停效果 */
.dark .component-card:hover {
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
}

/* 主题切换按钮动画 */
#themeToggle {
    transition: all 0.2s ease;
}

#themeToggle:hover {
    transform: scale(1.05);
}

#themeIcon {
    transition: transform 0.3s ease;
}

.theme-switching #themeIcon {
    transform: rotate(180deg);
}