<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主板信息管理 - 电脑配件综合管理系统</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/viewerjs/1.11.6/viewer.min.css">
    <link rel="stylesheet" href="/css/motherboard-info.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/viewerjs/1.11.6/viewer.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="/js/js-pinyin.js"></script>
    <script src="/js/pinyin-wrapper.js"></script>
    <script>
        // 添加Tailwind暗模式配置
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {}
            }
        }
    </script>
    <script src="/js/main.js"></script>
    <script src="/js/permission-helper.js"></script>
</head>

<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-2 sm:px-4 py-4 sm:py-8">
        <header class="mb-4 sm:mb-8">
            <div class="flex justify-between items-center">
            <h1 class="text-xl sm:text-3xl font-bold text-orange-700 flex items-center">
                <i class="fas fa-microchip mr-2 sm:mr-3"></i> 主板信息管理
            </h1>
                <!-- 主题切换按钮 -->
                <button id="theme-toggle" class="bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none" aria-label="切换深色/浅色模式" title="切换主题">
                    <!-- 月亮图标 (浅色模式显示) -->
                    <svg class="theme-toggle-icon theme-toggle-moon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M21.752 15.002A9.718 9.718 0 0118 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 003 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 009.002-5.998z" />
                    </svg>
                    <!-- 太阳图标 (深色模式显示) -->
                    <svg class="theme-toggle-icon theme-toggle-sun" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2.25a.75.75 0 01.75.75v2.25a.75.75 0 01-1.5 0V3a.75.75 0 01.75-.75zM7.5 12a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM18.894 6.166a.75.75 0 00-1.06-1.06l-1.591 1.59a.75.75 0 101.06 1.061l1.591-1.59zM21.75 12a.75.75 0 01-.75.75h-2.25a.75.75 0 010-1.5H21a.75.75 0 01.75.75zM17.834 18.894a.75.75 0 001.06-1.06l-1.59-1.591a.75.75 0 10-1.061 1.06l1.59 1.591zM12 18a.75.75 0 01.75.75V21a.75.75 0 01-1.5 0v-2.25A.75.75 0 0112 18zM7.758 17.303a.75.75 0 00-1.061-1.06l-1.591 1.59a.75.75 0 001.06 1.061l1.591-1.59zM6 12a.75.75 0 01-.75.75H3a.75.75 0 010-1.5h2.25A.75.75 0 016 12zM6.697 7.757a.75.75 0 001.06-1.06l-1.59-1.591a.75.75 0 00-1.061 1.06l1.59 1.591z" />
                    </svg>
                </button>
            </div>
            <div class="flex justify-between items-center">
                <p class="text-gray-600 mt-1 sm:mt-2 text-sm sm:text-base dark:text-gray-300">记录和管理主板配置信息</p>
                <div class="mt-2 flex space-x-2">
                    <a href="/pc-components.html"
                        class="inline-block bg-orange-600 text-white py-1 sm:py-2 px-3 sm:px-4 rounded-md hover:bg-orange-700 text-sm sm:text-base">
                        <i class="fas fa-arrow-left mr-1"></i> 返回配件列表
                    </a>
                    <a href="/"
                        class="inline-block bg-orange-600 text-white py-1 sm:py-2 px-3 sm:px-4 rounded-md hover:bg-orange-700 text-sm sm:text-base">
                        <i class="fas fa-home mr-1"></i> 返回首页
                    </a>
                </div>
            </div>
        </header>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-4 sm:gap-6">
            <!-- 左侧表单区域 -->
            <div class="lg:col-span-1 bg-white dark:bg-gray-800 p-3 sm:p-6 rounded-lg shadow-md">
                <h2 class="text-lg sm:text-xl font-semibold text-gray-800 dark:text-gray-200 mb-3 sm:mb-4 flex items-center">
                    <i class="fas fa-plus-circle mr-1 sm:mr-2 text-orange-500"></i> 添加主板信息
                </h2>

                <form id="motherboardForm" class="space-y-3 sm:space-y-4">
                    <!-- 智能识别区域 -->
                    <div class="border border-orange-200 rounded-md p-3 bg-orange-50">
                        <h3 class="text-md font-medium text-orange-700 mb-2 flex items-center">
                            <i class="fas fa-magic mr-1"></i> 智能识别
                        </h3>
                        <div class="space-y-2">
                            <textarea id="smartInput" rows="3"
                                class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 text-sm sm:text-base"
                                placeholder="粘贴主板参数文本，如：品牌：华硕、型号：ROG STRIX B550-F、芯片组：B550、内存代数：DDR4、接口：AM4、供电相数：12+2..."></textarea>
                            <button type="button" id="autoFillBtn"
                                class="w-full bg-orange-600 hover:bg-orange-700 text-white py-2 px-4 rounded-md text-sm">
                                <i class="fas fa-magic mr-2"></i>智能识别
                            </button>
                        </div>
                    </div>

                    <!-- 1.主板核心信息 -->
                    <div class="border border-gray-200 rounded-md p-3">
                        <h3 class="text-md font-medium text-gray-700 mb-2">主板核心信息</h3>

                        <div class="space-y-3">
                            <div>
                                <label for="motherboardModel" class="block text-sm font-medium text-gray-700 mb-1">主板型号
                                    <span class="text-red-500">*</span></label>
                                <input type="text" id="motherboardModel"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 text-sm sm:text-base"
                                    placeholder="如: ASUS ROG STRIX B550-F" required>
                            </div>

                            <div>
                                <label for="brand" class="block text-sm font-medium text-gray-700 mb-1">品牌 <span
                                        class="text-red-500">*</span></label>
                                <select id="brand"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 text-sm sm:text-base"
                                    required>
                                    <option value="">选择品牌</option>
                                    <option value="华硕">华硕 (ASUS)</option>
                                    <option value="微星">微星 (MSI)</option>
                                    <option value="技嘉">技嘉 (GIGABYTE)</option>
                                    <option value="华擎">华擎 (ASRock)</option>
                                    <option value="七彩虹">七彩虹 (Colorful)</option>
                                    <option value="铭瑄">铭瑄 (MAXSUN)</option>
                                    <option value="映泰">映泰 (BIOSTAR)</option>
                                    <option value="其他">其他</option>
                                </select>
                            </div>

                            <div>
                                <label for="motherboardType"
                                    class="block text-sm font-medium text-gray-700 mb-1">主板类型</label>
                                <select id="motherboardType"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 text-sm sm:text-base">
                                    <option value="">选择类型</option>
                                    <option value="Intel">Intel</option>
                                    <option value="AMD">AMD</option>
                                </select>
                            </div>

                            <div>
                                <label for="chipset" class="block text-sm font-medium text-gray-700 mb-1">芯片组型号</label>
                                <select id="chipset"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 text-sm sm:text-base">
                                    <option value="">选择芯片组</option>
                                    <option value="">Intel</option>
                                    <!-- Intel 芯片组 -->
                                    <option value="Z890">Z890 (Intel)</option>
                                    <option value="Z790">Z790 (Intel)</option>
                                    <option value="Z690">Z690 (Intel)</option>
                                    <option value="Z590">Z590 (Intel)</option>
                                    <option value="Z490">Z490 (Intel)</option>
                                    <option value="Z390">Z390 (Intel)</option>
                                    <option value="Z370">Z370 (Intel)</option>
                                    <option value="Z270">Z270 (Intel)</option>
                                    <option value="Z170">Z170 (Intel)</option>
                                    <option value="B860">B860 (Intel)</option>
                                    <option value="B760">B760 (Intel)</option>
                                    <option value="B660">B660 (Intel)</option>
                                    <option value="B560">B560 (Intel)</option>
                                    <option value="B460">B460 (Intel)</option>
                                    <option value="B360">B360 (Intel)</option>
                                    <option value="H770">H770 (Intel)</option>
                                    <option value="H670">H670 (Intel)</option>
                                    <option value="H610">H610 (Intel)</option>
                                    <option value="H510">H510 (Intel)</option>
                                    <option value="H410">H410 (Intel)</option>

                                    <!-- AMD 芯片组 -->
                                    <option value="">AMD</option>
                                    <option value="X870E">X870E (AMD)</option>
                                    <option value="X870">X870 (AMD)</option>
                                    <option value="X670E">X670E (AMD)</option>
                                    <option value="X670">X670 (AMD)</option>
                                    <option value="X570">X570 (AMD)</option>
                                    <option value="X470">X470 (AMD)</option>
                                    <option value="X370">X370 (AMD)</option>
                                    <option value="B850">B850 (AMD)</option>
                                    <option value="B650E">B650E (AMD)</option>
                                    <option value="B650">B650 (AMD)</option>
                                    <option value="B550">B550 (AMD)</option>
                                    <option value="B450">B450 (AMD)</option>
                                    <option value="B350">B350 (AMD)</option>
                                    <option value="A620">A620 (AMD)</option>
                                    <option value="A520">A520 (AMD)</option>
                                    <option value="A320">A320 (AMD)</option>
                                    <option value="X300">X300 (AMD)</option>
                                    <option value="A300">A300 (AMD)</option>
                                    <option value="其他">其他</option>

                                </select>
                            </div>

                            <div>
                                <label for="formFactor"
                                    class="block text-sm font-medium text-gray-700 mb-1">主板尺寸</label>
                                <select id="formFactor"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 text-sm sm:text-base">
                                    <option value="">选择尺寸</option>
                                    <option value="ATX">ATX</option>
                                    <option value="Micro-ATX">Micro-ATX</option>
                                    <option value="Mini-ITX">Mini-ITX</option>
                                    <option value="E-ATX">E-ATX</option>
                                </select>
                            </div>

                            <div>
                                <label for="memoryCapacity"
                                    class="block text-sm font-medium text-gray-700 mb-1">内存容量</label>
                                <select id="memoryCapacity"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 text-sm sm:text-base">
                                    <option value="">选择内存容量</option>
                                    <option value="32GB">32GB</option>
                                    <option value="64GB">64GB</option>
                                    <option value="128GB">128GB</option>
                                    <option value="256GB">256GB</option>
                                </select>
                            </div>

                            <div>
                                <label for="memoryGeneration"
                                    class="block text-sm font-medium text-gray-700 mb-1">内存代数</label>
                                <select id="memoryGeneration"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 text-sm sm:text-base">
                                    <option value="">选择内存代数</option>
                                    <option value="DDR5">DDR5</option>
                                    <option value="DDR4">DDR4</option>
                                    <option value="DDR3">DDR3</option>
                                    <option value="DDR2">DDR2</option>
                                </select>
                            </div>

                            <div>
                                <label for="memoryFrequency"
                                    class="block text-sm font-medium text-gray-700 mb-1">内存频率</label>
                                <input type="text" id="memoryFrequency"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 text-sm sm:text-base"
                                    placeholder="如: 3200MHz/6000MT/s">
                            </div>

                            <div>
                                <label for="biosVersion"
                                    class="block text-sm font-medium text-gray-700 mb-1">推荐CPU</label>
                                <input type="text" id="biosVersion"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 text-sm sm:text-base"
                                    placeholder="如: i5-14600KF">
                            </div>

                            <div>
                                <label for="releaseDate"
                                    class="block text-sm font-medium text-gray-700 mb-1">发布日期</label>
                                <input type="month" id="releaseDate"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 text-sm sm:text-base">
                            </div>
                        </div>
                    </div>

                    <!-- 2.CPU支持信息 -->
                    <div class="border border-gray-200 rounded-md p-3">
                        <h3 class="text-md font-medium text-gray-700 mb-2">CPU支持信息</h3>

                        <div class="space-y-3">
                            <div>
                                <label for="cpuSocket"
                                    class="block text-sm font-medium text-gray-700 mb-1">支持的CPU接口类型</label>
                                <select id="cpuSocket"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 text-sm sm:text-base">
                                    <!-- Intel 平台 -->
                                    <option value="">选择 CPU 接口</option>
                                    <!-- 最新桌面/主流 -->
                                    <option value="">最新桌面/主流</option>
                                    <option value="LGA 1851">LGA 1851（Intel® Core™ Ultra 系列，如 Ultra 7 265K／285K，需配合 800
                                        系主板）</option> <!-- 265K 用此插槽 -->
                                    <option value="LGA 1700">LGA 1700（Intel 12/13/14 代 Alder/Raptor/Arrow Lake-S
                                        系列第二代前不兼容 Ultra 系列）</option>
                                    <option value="LGA 1200">LGA 1200（Intel 10/11 代 Comet/Lake 系列）</option>
                                    <option value="LGA 1151">LGA 1151（Intel 8/9 代 Coffee/Kaby Lake 系列）</option>
                                    <option value="LGA 1155">LGA 1155（Intel 2/3 代 Sandy/Ivy Bridge 系列，如 i7‑2600K）
                                    </option>
                                    <!-- HEDT 平台 -->
                                    <option value="">HEDT 平台</option>
                                    <option value="LGA 2066">LGA 2066（Intel X299 HEDT）</option>
                                    <option value="LGA 2011">LGA 2011（Intel X79/HEDT）</option>
                                    <option value="LGA 1366">LGA 1366（Intel X58 平台）</option>

                                    <!-- AMD 平台 -->
                                    <!-- 最新桌面 -->
                                    <option value="">最新桌面/主流</option>
                                    <option value="AM5">AM5（AMD Ryzen™ 7000/8000/9000 系统一插槽）</option>
                                    <option value="AM4">AM4（AMD Ryzen™ 1000–5000 系）</option>
                                    <!-- HEDT/高端桌面 -->
                                    <option value="">HEDT/高端桌面</option>
                                    <option value="sTRX4">sTRX4（AMD Threadripper 2/3 代）</option>
                                    <option value="TR4">TR4（AMD Threadripper 1 代）</option>
                                    <!-- 服务器/工作站 -->
                                    <option value="">服务器/工作站</option>
                                    <option value="SP5">SP5（AMD EPYC 7003/9004 系）</option>
                                    <option value="SP6">SP6（AMD EPYC 9004 系）</option>
                                    <option value="其他">其他</option>
                                </select>
                            </div>

                            <div>
                                <label for="cpuModel"
                                    class="block text-sm font-medium text-gray-700 mb-1">支持的CPU型号</label>
                                <input type="text" id="cpuModel"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 text-sm sm:text-base"
                                    placeholder="如: Intel 12代/13代 Core i系列">
                            </div>

                            <div>
                                <label for="maxTdp" class="block text-sm font-medium text-gray-700 mb-1">支持的最大CPU功耗
                                    (TDP)</label>
                                <div class="flex">
                                    <input type="number" id="maxTdp"
                                        class="flex-1 px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-l-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 text-sm sm:text-base"
                                        placeholder="如: 125">
                                    <span
                                        class="inline-flex items-center px-3 py-1 rounded-r-md border border-l-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
                                        W
                                    </span>
                                </div>
                            </div>

                            <div>
                                <label for="overclockSupport"
                                    class="block text-sm font-medium text-gray-700 mb-1">超频支持</label>
                                <select id="overclockSupport"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 text-sm sm:text-base">
                                    <option value="">选择</option>
                                    <option value="否">否</option>
                                    <option value="内存超频">内存超频</option>
                                    <option value="CPU超频">CPU超频</option>
                                    <option value="内存/CPU超频">内存/CPU超频</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- 3.扩展接口 -->
                    <div class="border border-gray-200 rounded-md p-3">
                        <h3 class="text-md font-medium text-gray-700 mb-2">扩展接口</h3>

                        <div class="space-y-3">
                            <div>
                                <label for="pcieSlots" class="block text-sm font-medium text-gray-700 mb-1">PCIe
                                    x16插槽数量</label>
                                <input type="number" id="pcieSlots"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 text-sm sm:text-base"
                                    placeholder="如: 2" min="0">
                            </div>

                            <div>
                                <label for="m2Slots"
                                    class="block text-sm font-medium text-gray-700 mb-1">M.2接口数量</label>
                                <input type="number" id="m2Slots"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 text-sm sm:text-base"
                                    placeholder="如: 2" min="0">
                            </div>

                            <div>
                                <label for="sataSlots"
                                    class="block text-sm font-medium text-gray-700 mb-1">SATA接口数量</label>
                                <input type="number" id="sataSlots"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 text-sm sm:text-base"
                                    placeholder="如: 6" min="0">
                            </div>

                            <div>
                                <label for="usbSlots" class="block text-sm font-medium text-gray-700 mb-1">USB 3.2 Gen
                                    接口数量</label>
                                <input type="number" id="usbSlots"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 text-sm sm:text-base"
                                    placeholder="如: 8" min="0">
                            </div>

                            <div>
                                <label for="wifiSupport"
                                    class="block text-sm font-medium text-gray-700 mb-1">板载WiFi支持</label>
                                <select id="wifiSupport"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 text-sm sm:text-base">
                                    <option value="">选择</option>
                                    <option value="是">是</option>
                                    <option value="否">否</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- 4.供电信息 -->
                    <div class="border border-gray-200 rounded-md p-3">
                        <h3 class="text-md font-medium text-gray-700 mb-2">供电信息</h3>

                        <div class="space-y-3">
                            <div>
                                <label for="powerPhases"
                                    class="block text-sm font-medium text-gray-700 mb-1">供电相数</label>
                                <input type="text" id="powerPhases"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 text-sm sm:text-base"
                                    placeholder="如: 14+2">
                            </div>

                            <div>
                                <label for="vrmCooling"
                                    class="block text-sm font-medium text-gray-700 mb-1">VRM散热配置</label>
                                <input type="text" id="vrmCooling"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 text-sm sm:text-base"
                                    placeholder="如: 散热片式, 热管式">
                            </div>

                            <div>
                                <label for="powerConnector"
                                    class="block text-sm font-medium text-gray-700 mb-1">电源接口</label>
                                <input type="text" id="powerConnector"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 text-sm sm:text-base"
                                    placeholder="如: 8+4-pin">
                            </div>
                        </div>
                    </div>

                    <!-- 5.其他信息 -->
                    <div class="border border-gray-200 rounded-md p-3">
                        <h3 class="text-md font-medium text-gray-700 mb-2">其他信息</h3>

                        <div class="space-y-3">
                            <div>
                                <label for="motherboardImage"
                                    class="block text-sm font-medium text-gray-700 mb-1">主板图片</label>
                                <div
                                    class="mt-1 flex justify-center px-4 sm:px-6 pt-3 sm:pt-5 pb-4 sm:pb-6 border-2 border-gray-300 border-dashed rounded-md">
                                    <div class="space-y-1 text-center">
                                        <div class="flex text-sm text-gray-600 items-center justify-center flex-wrap">
                                            <label for="motherboardImage"
                                                class="relative cursor-pointer bg-white rounded-md font-medium text-orange-600 hover:text-orange-500 focus-within:outline-none">
                                                <span>上传图片</span>
                                                <input id="motherboardImage" name="motherboardImage" type="file"
                                                    accept="image/*" class="sr-only">
                                            </label>
                                            <p class="pl-1 mobile-hidden">或将图片拖拽到此处</p>
                                        </div>
                                        <p class="text-xs text-gray-500">PNG, JPG, GIF 格式（将自动转换为WebP格式以优化加载速度）</p>
                                    </div>
                                </div>
                                <!-- 上传进度条 -->
                                <div id="uploadProgressContainer" class="mt-2 hidden">
                                    <div class="bg-gray-200 dark:bg-gray-700 rounded-full h-3 mb-2 overflow-hidden">
                                        <div id="uploadProgressBar" class="bg-gradient-to-r from-orange-500 to-orange-600 dark:from-orange-400 dark:to-orange-500 h-3 rounded-full transition-all duration-500 ease-out relative" style="width: 0%">
                                            <!-- 进度条光泽效果 -->
                                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-20 animate-pulse"></div>
                                        </div>
                                    </div>
                                    <div class="flex justify-between items-center text-xs">
                                        <span id="uploadProgressText" class="text-gray-600 dark:text-gray-400 flex items-center">
                                            <i id="uploadProgressIcon" class="fas fa-upload mr-1"></i>
                                            准备上传...
                                        </span>
                                        <span id="uploadProgressPercent" class="text-orange-600 dark:text-orange-400 font-medium">0%</span>
                                    </div>
                                </div>

                                <div id="imagePreviewContainer" class="mt-2 hidden">
                                    <div class="relative inline-block">
                                        <img id="imagePreview" src="#" alt="预览图"
                                            class="h-24 sm:h-32 rounded-md shadow image-preview">
                                        <button type="button" id="removeImageBtn"
                                            class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 sm:w-6 sm:h-6 flex items-center justify-center hover:bg-red-600">
                                            <i class="fas fa-times text-xs"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div>
                                <label for="warranty" class="block text-sm font-medium text-gray-700 mb-1">质保期限</label>
                                <div class="flex">
                                    <input type="number" id="warranty"
                                        class="flex-1 px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-l-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 text-sm sm:text-base"
                                        placeholder="如: 3" min="0">
                                    <span
                                        class="inline-flex items-center px-3 py-1 rounded-r-md border border-l-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
                                        年
                                    </span>
                                </div>
                            </div>

                            <div>
                                <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">备注</label>
                                <textarea id="notes" name="notes" rows="2"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 text-sm sm:text-base"
                                    placeholder="额外信息..."></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="flex space-x-2 sm:space-x-3 mobile-stack">
                        <button type="submit"
                            class="flex-1 bg-orange-600 text-white py-1 sm:py-2 px-2 sm:px-4 rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 text-sm sm:text-base">
                            <i class="fas fa-save mr-1 sm:mr-2"></i> 保存主板信息
                        </button>
                        <button type="reset"
                            class="flex-1 bg-gray-200 text-gray-800 py-1 sm:py-2 px-2 sm:px-4 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 text-sm sm:text-base">
                            <i class="fas fa-undo mr-1 sm:mr-2"></i> 重置
                        </button>
                    </div>
                </form>
            </div>

            <!-- 右侧列表区域 -->
            <div class="lg:col-span-3 space-y-4 sm:space-y-6">
                <!-- 主板列表 -->
                <div class="bg-white dark:bg-gray-800 p-3 sm:p-6 rounded-lg shadow-md">
                    <h2 class="text-xl font-semibold text-orange-800 dark:text-orange-300 mb-4 flex items-center">
                        <i class="fas fa-microchip mr-2 sm:mr-3"></i> 主板列表
                    </h2>
                    <div class="filter-container sm:flex sm:items-center sm:space-x-4">
                        <div class="search-container w-full sm:w-1/2 mb-3 sm:mb-0">
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-search text-gray-400"></i>
                                </div>
                                <input id="motherboardSearch" type="text" placeholder="搜索主板型号、品牌等..."
                                    class="w-full py-2 pl-10 pr-3 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all">
                            </div>
                        </div>

                        <div class="filter-group sm:flex sm:items-center sm:space-x-2 sm:flex-1 sm:justify-end">
                            <select id="brandFilter"
                                class="w-full sm:w-auto mb-2 sm:mb-0 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 pl-3 pr-8 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all">
                                <option value="all">所有品牌</option>
                                <option value="华硕">华硕</option>
                                <option value="微星">微星</option>
                                <option value="技嘉">技嘉</option>
                                <option value="华擎">华擎</option>
                                <option value="其他">其他</option>
                            </select>

                            <select id="chipsetFilter"
                                class="w-full sm:w-auto mb-2 sm:mb-0 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 pl-3 pr-8 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all">
                                <option value="all">所有芯片组</option>
                                <option value="">Intel</option>
                                <!-- Intel 芯片组 -->
                                <option value="Z890">Z890 (Intel)</option>
                                <option value="Z790">Z790 (Intel)</option>
                                <option value="B860">B860 (Intel)</option>
                                <option value="B760">B760 (Intel)</option>
                                <option value="H610">H610 (Intel)</option>
                                <option value="H510">H510 (Intel)</option>
                                <!-- AMD 芯片组 -->
                                <option value="">AMD</option>
                                <option value="X870E">X870E (AMD)</option>
                                <option value="X870">X870 (AMD)</option>
                                <option value="X670E">X670E (AMD)</option>
                                <option value="X670">X670 (AMD)</option>
                                <option value="B850">B850 (AMD)</option>
                                <option value="B650E">B650E (AMD)</option>
                                <option value="B650">B650 (AMD)</option>
                                <option value="其他">其他</option>
                            </select>

                            <button id="resetFilterBtn"
                                class="w-full sm:w-auto bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-200 py-2 px-4 rounded-md border border-gray-300 dark:border-gray-600 transition-colors">
                                <i class="fas fa-sync-alt mr-1"></i> 重置
                            </button>
                        </div>
                    </div>

                    <!-- 优化表格容器结构 -->
                    <div class="w-full mt-3">
                        <div class="table-wrapper overflow-x-auto table-container">
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200 table-modern">
                                    <thead class="bg-gray-50 hidden sm:table-header-group">
                                        <tr>
                                            <th scope="col"
                                                class="px-2 py-2 sm:px-4 sm:py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider image-column">
                                                图片</th>
                                            <th scope="col"
                                                class="px-2 py-2 sm:px-4 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider model-column">
                                                主板型号</th>
                                            <th scope="col"
                                                class="px-2 py-2 sm:px-4 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider memory-column cpu-column">
                                                供电相数</th>
                                            <th scope="col"
                                                class="px-2 py-2 sm:px-4 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell cpu-model-column">
                                                M.2接口</th>
                                            <th scope="col"
                                                class="px-2 py-2 sm:px-4 sm:py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider action-column">
                                                操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="motherboardTableBody"
                                        class="bg-white divide-y sm:divide-y divide-gray-200">
                                        <!-- 数据将通过JavaScript动态填充 -->
                                        <tr>
                                            <td colspan="6" class="px-2 py-4 text-center text-gray-500">暂无数据</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 优化分页区域 -->
                    <div
                        class="mt-6 px-4 py-3 bg-white rounded-lg shadow-sm flex flex-wrap justify-between items-center pagination-mobile">
                        <div class="text-sm text-gray-700 mb-2 sm:mb-0" id="totalCount">
                            共 <span id="totalRecords" class="font-medium text-orange-600">0</span> 条记录
                        </div>
                        <div class="flex items-center space-x-1 sm:space-x-2 page-controls-mobile">
                            <button id="firstPage" title="首页"
                                class="px-2 py-1 border border-gray-300 rounded-md text-sm bg-white text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all">
                                <i class="fas fa-angle-double-left"></i>
                            </button>
                            <button id="prevPage" title="上一页"
                                class="px-2 py-1 border border-gray-300 rounded-md text-sm bg-white text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all">
                                <i class="fas fa-chevron-left"></i>
                            </button>

                            <div id="pageNumbers" class="hidden sm:flex space-x-1">
                                <!-- 页码将通过JavaScript填充 -->
                            </div>

                            <span id="pageInfo"
                                class="px-3 py-1 text-sm whitespace-nowrap bg-gray-50 rounded-md border border-gray-200">第
                                <span id="currentPageDisplay" class="font-medium text-orange-600">1</span> / <span
                                    id="totalPagesDisplay" class="font-medium">1</span> 页</span>

                            <button id="nextPage" title="下一页"
                                class="px-2 py-1 border border-gray-300 rounded-md text-sm bg-white text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                            <button id="lastPage" title="尾页"
                                class="px-2 py-1 border border-gray-300 rounded-md text-sm bg-white text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all">
                                <i class="fas fa-angle-double-right"></i>
                            </button>

                            <div
                                class="hidden sm:flex items-center ml-2 bg-gray-50 rounded-md px-2 py-1 border border-gray-200">
                                <span class="text-sm">跳转到</span>
                                <input type="number" id="pageJump" min="1"
                                    class="w-12 mx-1 px-2 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:border-orange-400 focus:ring-1 focus:ring-orange-400">
                                <button id="goToPage"
                                    class="px-2 py-1 bg-orange-600 text-white text-sm rounded-md hover:bg-orange-700 transition-colors">
                                    确定
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主板详情模态框 -->
        <div id="motherboardModal"
            class="fixed inset-0 bg-black bg-opacity-50 dark:bg-opacity-70 flex items-center justify-center z-50 hidden">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-4 sm:p-6 max-w-4xl w-full max-h-[90vh] overflow-y-auto">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg sm:text-xl font-semibold text-gray-800 dark:text-gray-200">主板详情</h3>
                    <button id="closeModal" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div id="motherboardDetails" class="space-y-4">
                    <!-- 详情会动态填充 -->
                </div>

                <div class="mt-6 flex justify-end space-x-2">
                    <button id="editBtn"
                        class="px-3 py-1 sm:px-4 sm:py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm sm:text-base">
                        <i class="fas fa-edit mr-1"></i> 编辑
                    </button>
                    <button id="deleteBtn"
                        class="px-3 py-1 sm:px-4 sm:py-2 bg-red-600 text-white rounded-md hover:bg-red-700 text-sm sm:text-base">
                        <i class="fas fa-trash mr-1"></i> 删除
                    </button>
                    <button id="closeModalBtn"
                        class="px-3 py-1 sm:px-4 sm:py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 text-sm sm:text-base">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="/js/motherboard-info.js"></script>

    <!-- 添加主题切换的JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 获取主题切换按钮
            const themeToggle = document.getElementById('theme-toggle');
            
            // 初始化主题
            function initTheme() {
                // 检查localStorage中是否有保存的主题偏好
                const savedTheme = localStorage.getItem('theme');
                
                if (savedTheme === 'dark') {
                    document.documentElement.classList.add('dark');
                } else if (savedTheme === 'light') {
                    document.documentElement.classList.remove('dark');
                } else {
                    // 如果没有保存的主题，则根据操作系统偏好设置
                    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                        document.documentElement.classList.add('dark');
                        localStorage.setItem('theme', 'dark');
                    } else {
                        document.documentElement.classList.remove('dark');
                        localStorage.setItem('theme', 'light');
                    }
                }
            }
            
            // 切换主题函数
            function toggleTheme() {
                if (document.documentElement.classList.contains('dark')) {
                    document.documentElement.classList.remove('dark');
                    localStorage.setItem('theme', 'light');
                } else {
                    document.documentElement.classList.add('dark');
                    localStorage.setItem('theme', 'dark');
                }
            }
            
            // 初始化主题
            initTheme();
            
            // 添加按钮点击事件监听
            themeToggle.addEventListener('click', toggleTheme);
            
            // 添加键盘快捷键支持 (Ctrl+Shift+D)
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey && e.shiftKey && e.key === 'D') {
                    e.preventDefault();
                    toggleTheme();
                }
            });
            
            // 监听系统主题变化
            if (window.matchMedia) {
                window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', function(e) {
                    // 只有当用户没有手动设置过主题时才跟随系统
                    const savedTheme = localStorage.getItem('theme');
                    if (!savedTheme) {
                        if (e.matches) {
                            document.documentElement.classList.add('dark');
                        } else {
                            document.documentElement.classList.remove('dark');
                        }
                    }
                });
            }
        });
    </script>

    <!-- 确保图片点击功能正常工作的内联脚本 -->
    <script>
        // 添加CSS规则，确保图片可点击并有正确的z-index
        document.addEventListener('DOMContentLoaded', function () {
            // 创建一个新的样式表
            const style = document.createElement('style');
            style.textContent = `
                /* 确保图片和覆盖层可点击 */
                .image-column img,
                .w-12 img,
                .image-column .absolute {
                    pointer-events: auto !important;
                    cursor: pointer !important;
                    z-index: 1;
                }
                
                /* 覆盖层z-index设置 */
                .image-column .absolute.inset-0 {
                    z-index: 2 !important;
                    pointer-events: auto !important;
                }
                
                /* 确保图片容器可点击 */
                .image-column .w-12 {
                    pointer-events: auto !important;
                    cursor: pointer !important;
                    position: relative;
                }
            `;
            document.head.appendChild(style);

            // 确保所有表格图片都有点击事件
            setTimeout(() => {
                const tableImages = document.querySelectorAll('.image-column img');
                tableImages.forEach(img => {
                    if (img.src && !img.onclick) {
                        img.style.cursor = 'pointer';
                        img.onclick = () => openImageFullscreen(img.src);
                        console.log('Added click event to image:', img.src);
                    }
                });

                // 确保所有覆盖层也有点击事件
                const overlays = document.querySelectorAll('.image-column .absolute');
                overlays.forEach(overlay => {
                    if (!overlay.onclick && overlay.parentNode) {
                        const img = overlay.parentNode.querySelector('img');
                        if (img && img.src) {
                            overlay.style.cursor = 'pointer';
                            overlay.onclick = () => openImageFullscreen(img.src);
                            console.log('Added click event to overlay for image:', img.src);
                        }
                    }
                });
            }, 1000); // 等待表格完全渲染
        });
    </script>
</body>

</html>