# 电脑硬件管理及价格计算系统

这是一个集成了电脑硬件库存管理、价格计算、历史记录查询和配送追踪等功能的多功能Web应用。

## 功能特点

- **硬件库存管理**: 对CPU、显卡、主板、内存、硬盘等各类电脑硬件进行增、删、改、查管理。
- **图片管理**: 支持上传和展示硬件图片。
- **价格计算**: 可动态添加项目并实时计算总价。
- **历史记录**: 所有计算记录自动保存到数据库，支持按多种条件进行搜索和回溯。
- **数据导出**: 支持将数据导出为CSV或JSON格式。
- **数据库管理**: 提供数据库备份与恢复功能。

## 安装步骤

1.  **环境准备**: 确保您已安装 [Node.js](https://nodejs.org/) 和 [MySQL](https://www.mysql.com/)。

2.  **克隆代码库**:
    ```bash
    git clone [仓库地址]
    cd delivery_system
    ```

3.  **安装依赖**:
    ```bash
    npm install
    ```

4.  **配置环境变量**:
    在项目根目录下创建一个名为 `.env` 的文件，并填入您的MySQL数据库连接信息。文件内容如下：
    ```
    DB_HOST=localhost
    DB_USER=root
    DB_PASSWORD=your_mysql_password
    DB_NAME=delivery_system
    DB_PORT=3306
    ```
    请将 `your_mysql_password` 替换为您的真实密码。您可以自定义 `DB_NAME`。

5.  **启动服务器**:
    ```bash
    npm start
    ```
    或者在开发模式下启动（文件变动时自动重启）：
    ```bash
    npm run dev
    ```

当服务器启动时，应用会自动创建所需的数据库和数据表。

现在，您可以访问 `http://localhost:3000` 开始使用系统。

## 技术栈
- **前端**: HTML, CSS, JavaScript (由Express提供服务)
- **后端**: Node.js, Express
- **数据库**: MySQL 