const express = require('express');
const createResourceRouter = require('./resource-handler');

// CPU 资源配置
const cpuConfig = {
    resourceName: 'cpu',
    tableName: 'cpus',
    uploadDir: 'cpus',
    defaultImage: 'images/default-cpu.png',
    searchFields: ['brand', 'model', 'series', 'socket'],
    fieldMap: {
        brand: { type: 'string' },
        model: { type: 'string' },
        series: { type: 'string' },
        socket: { type: 'string' },
        core_count: { type: 'integer' },
        thread_count: { type: 'integer' },
        base_clock: { type: 'float' },
        boost_clock: { type: 'float' },
        tdp: { type: 'integer' },
        l3_cache: { type: 'integer' },
        process_node: { type: 'string' },
        release_date: { type: 'string' },
        integrated_graphics: { type: 'string' },
        price: { type: 'float' },
        notes: { type: 'string' }
    }
};

// 创建 CPU 路由
const router = createResourceRouter(cpuConfig);

module.exports = router; 