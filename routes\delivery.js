const express = require('express');
const sharp = require('sharp');
const router = express.Router();
const db = require('../config/db');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// 配置multer
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        const dir = 'public/uploads';
        if (!fs.existsSync(dir)){
            fs.mkdirSync(dir, { recursive: true });
        }
        cb(null, dir);
    },
    filename: function (req, file, cb) {
        cb(null, Date.now() + path.extname(file.originalname));
    }
});

const upload = multer({ 
    storage: storage,
    limits: { fileSize: 10 * 1024 * 1024 }, // 10MB限制
    fileFilter: function (req, file, cb) {
        // 检查文件类型是否为图片
        if (file.mimetype.startsWith('image/')) {
          cb(null, true);
        } else {
          cb(new Error('只允许上传图片文件'), false);
        }
    }
});


// ====================================
// 店铺和地址管理 (Stores & Addresses)
// ====================================

// 获取所有店铺
router.get('/stores', async (req, res) => {
    try {
        const query = `
            SELECT 
                s.id, 
                s.name,
                s.default_address_id
            FROM stores s 
            ORDER BY s.name
        `;
        const [stores] = await db.query(query);
        const result = stores.map(store => ({
            id: store.id.toString(),
            name: store.name,
            default_address_id: store.default_address_id ? store.default_address_id.toString() : null
        }));
        res.json(result);
    } catch (error) {
        console.error("Error fetching stores:", error);
        res.status(500).json({ error: '获取店铺列表失败: ' + error.message });
    }
});

// 添加新店铺
router.post('/stores', async (req, res) => {
    try {
        const { name, address_id } = req.body;
        if (!name) {
            return res.status(400).json({ error: '店铺名称不能为空' });
        }
        const defaultAddressId = address_id ? parseInt(address_id) : null;
        const [result] = await db.query(
            'INSERT INTO stores (name, default_address_id) VALUES (?, ?)',
            [name, defaultAddressId]
        );
        const newStoreId = result.insertId;
        res.json({ 
            id: newStoreId.toString(), 
            name: name, 
            default_address_id: defaultAddressId ? defaultAddressId.toString() : null 
        });
    } catch (error) {
        console.error("Error adding new store:", error);
        if (error.code === 'ER_NO_REFERENCED_ROW_2') {
            return res.status(400).json({ error: '选择的默认地址无效' });
        }
        if (error.code === 'ER_DUP_ENTRY') {
            return res.status(400).json({ error: `店铺名称已存在，请使用其他名称` });
        }
        res.status(500).json({ error: '添加新店铺失败: ' + error.message });
    }
});

// 获取所有地址
router.get('/addresses', async (req, res) => {
    try {
        const [addresses] = await db.query('SELECT * FROM addresses ORDER BY address');
        res.json(addresses);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// 添加新地址
router.post('/addresses', async (req, res) => {
    try {
        const { address } = req.body;
        const [result] = await db.query('INSERT INTO addresses (address) VALUES (?)', [address]);
        res.json({ id: result.insertId, address });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});


// ====================================
// 送货记录管理 (Delivery Records)
// ====================================

// 获取所有送货记录 (带搜索和分页)
router.get('/records', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 5;
        const offset = (page - 1) * limit;
        const search = req.query.search || '';

        let query = `
            SELECT 
                r.*,
                s.name as store_name,
                a.address,
                GROUP_CONCAT(
                    JSON_OBJECT(
                        'id', p.id,
                        'type', p.type,
                        'model', p.model,
                        'price', p.price,
                        'quantity', p.quantity
                    ) SEPARATOR '|'
                ) as parts_json
            FROM delivery_records r
            JOIN stores s ON r.store_id = s.id
            JOIN addresses a ON r.address_id = a.id
            LEFT JOIN parts p ON r.id = p.record_id
        `;
        
        let countQuery = `
            SELECT COUNT(DISTINCT r.id) as total
            FROM delivery_records r
            JOIN stores s ON r.store_id = s.id
            JOIN addresses a ON r.address_id = a.id
            ${search ? 'LEFT JOIN parts p ON r.id = p.record_id' : ''} 
        `;

        const searchParams = [];
        if (search) {
            const searchTerm = `%${search}%`;
            const whereClause = ` WHERE s.name LIKE ? OR a.address LIKE ? OR r.notes LIKE ? OR p.type LIKE ? OR p.model LIKE ?`;
            query += whereClause;
            countQuery += whereClause;
            searchParams.push(searchTerm, searchTerm, searchTerm, searchTerm, searchTerm);
        }
        
        query += ' GROUP BY r.id ORDER BY r.created_at DESC LIMIT ? OFFSET ?';
        const queryParams = [...searchParams, limit, offset];
        
        const [records] = await db.query(query, queryParams);
        const [[{ total }]] = await db.query(countQuery, searchParams);

        records.forEach(record => {
            if (record.parts_json) {
                try {
                    record.parts = record.parts_json.split('|').map(jsonString => {
                        try {
                            return JSON.parse(jsonString);
                        } catch (parseError) {
                            console.error('Error parsing part JSON:', parseError, 'String:', jsonString);
                            return null; 
                        }
                    }).filter(part => part !== null && part.id !== null);
                } catch (splitError) {
                    console.error('Error splitting/processing parts_json:', splitError);
                    record.parts = [];
                }
            } else {
                record.parts = [];
            }
            delete record.parts_json;
        });

        res.json({ records, total });
    } catch (error) {
        console.error('Error in /api/records:', error);
        res.status(500).json({ error: '获取记录失败: ' + error.message });
    }
});

// 添加新送货记录
router.post('/records', upload.single('image'), async (req, res) => {
    const conn = await db.getConnection();
    try {
        await conn.beginTransaction();

        const { store_id, address_id, notes, parts } = req.body;
        if (!store_id || !address_id) {
            return res.status(400).json({ error: '店铺和送货地点不能为空' });
        }
        
        let parsedParts = [];
        if (parts) {
            try {
                parsedParts = JSON.parse(parts);
            } catch (e) {
                return res.status(400).json({ error: '配件数据格式不正确' });
            }
        }
        
        let imageUrl = null;
        if (req.file) {
            const originalPath = req.file.path;
            const webpPath = originalPath.replace(/\.[^.]+$/, '.webp');
            try {
                await sharp(originalPath).webp({ quality: 75 }).toFile(webpPath);
                fs.unlinkSync(originalPath); // Delete original file
                imageUrl = `/uploads/${req.file.filename.replace(/\.[^.]+$/, '.webp')}`;
            } catch (err) {
                console.error('WebP conversion failed:', err);
                imageUrl = `/uploads/${req.file.filename}`; // Use original if conversion fails
            }
        }
        
        const [result] = await conn.query(
            'INSERT INTO delivery_records (store_id, address_id, image_url, notes) VALUES (?, ?, ?, ?)',
            [store_id, address_id, imageUrl, notes]
        );
        const recordId = result.insertId;
        
        if (parsedParts.length > 0) {
            const partsValues = parsedParts.map(part => [
                recordId, 
                part.type || null, 
                part.model || null, 
                part.price || 0,
                part.quantity || 1
            ]);
            await conn.query(
                'INSERT INTO parts (record_id, type, model, price, quantity) VALUES ?',
                [partsValues]
            );
        }
        
        await conn.commit();
        res.status(201).json({ id: recordId, message: '记录已创建' });
    } catch (error) {
        await conn.rollback();
        if (req.file) {
            // Attempt to delete uploaded file on failure
            const originalPath = req.file.path;
            const webpPath = originalPath.replace(/\.[^.]+$/, '.webp');
            if (fs.existsSync(originalPath)) fs.unlinkSync(originalPath);
            if (fs.existsSync(webpPath)) fs.unlinkSync(webpPath);
        }
        console.error('创建记录失败:', error);
        res.status(500).json({ error: '创建记录失败' });
    } finally {
        conn.release();
    }
});

// 获取特定送货记录
router.get('/records/:id', async (req, res) => {
    try {
        const [records] = await db.query(`
            SELECT r.*, s.name as store_name, a.address
            FROM delivery_records r
            JOIN stores s ON r.store_id = s.id
            JOIN addresses a ON r.address_id = a.id
            WHERE r.id = ?
        `, [req.params.id]);

        if (records.length === 0) {
            return res.status(404).json({ error: '记录不存在' });
        }
        res.json(records[0]);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});


// 更新送货记录
router.put('/records/:id', upload.single('image'), async (req, res) => {
    const conn = await db.getConnection();
    try {
        await conn.beginTransaction();
        const id = req.params.id;
        const { store_id, address_id, notes, parts } = req.body;

        if (!store_id || !address_id) {
            return res.status(400).json({ error: '店铺和送货地点不能为空' });
        }
        
        let parsedParts = [];
        if (parts) {
            try {
                parsedParts = JSON.parse(parts);
            } catch (e) {
                return res.status(400).json({ error: '配件数据格式不正确' });
            }
        }
        
        const [existingRecord] = await conn.query('SELECT image_url FROM delivery_records WHERE id = ?', [id]);
        if (existingRecord.length === 0) {
             return res.status(404).json({ error: '记录不存在' });
        }
        const existingImageUrl = existingRecord[0].image_url;

        let imageUrl = existingImageUrl;
        if (req.file) {
             // Delete old image if it exists
            if (existingImageUrl) {
                const oldImagePath = path.join(__dirname, '..', 'public', existingImageUrl);
                if (fs.existsSync(oldImagePath)) fs.unlinkSync(oldImagePath);
            }
            // Process new image
            const originalPath = req.file.path;
            const webpPath = originalPath.replace(/\.[^.]+$/, '.webp');
            try {
                await sharp(originalPath).webp({ quality: 75 }).toFile(webpPath);
                fs.unlinkSync(originalPath);
                imageUrl = `/uploads/${req.file.filename.replace(/\.[^.]+$/, '.webp')}`;
            } catch (err) {
                console.error('WebP conversion failed:', err);
                imageUrl = `/uploads/${req.file.filename}`;
            }
        }

        await conn.query(
            'UPDATE delivery_records SET store_id = ?, address_id = ?, notes = ?, image_url = ? WHERE id = ?',
            [store_id, address_id, notes, imageUrl, id]
        );

        await conn.query('DELETE FROM parts WHERE record_id = ?', [id]);
        if (parsedParts.length > 0) {
            const partsValues = parsedParts.map(part => [
                id, 
                part.type || null, 
                part.model || null, 
                part.price || 0,
                part.quantity || 1
            ]);
            await conn.query(
                'INSERT INTO parts (record_id, type, model, price, quantity) VALUES ?',
                [partsValues]
            );
        }
        
        await conn.commit();
        res.json({ message: '记录已更新' });
    } catch (error) {
        await conn.rollback();
        console.error('更新记录失败:', error);
        res.status(500).json({ error: '更新记录失败' });
    } finally {
        conn.release();
    }
});


// 删除送货记录
router.delete('/records/:id', async (req, res) => {
    const conn = await db.getConnection();
    try {
        await conn.beginTransaction();
        const recordId = req.params.id;
        
        const [records] = await conn.query('SELECT image_url FROM delivery_records WHERE id = ?', [recordId]);
        if (records.length === 0) {
            await conn.rollback();
            return res.status(404).json({ error: '记录不存在' });
        }

        if (records[0].image_url) {
            const imagePath = path.join(__dirname, '..', 'public', records[0].image_url);
            if (fs.existsSync(imagePath)) {
                fs.unlinkSync(imagePath);
            }
            const webpPath = imagePath.replace(/\.[^.]+$/, '.webp');
             if (fs.existsSync(webpPath)) {
                fs.unlinkSync(webpPath);
            }
        }

        await conn.query('DELETE FROM parts WHERE record_id = ?', [recordId]);
        await conn.query('DELETE FROM delivery_records WHERE id = ?', [recordId]);
        
        await conn.commit();
        res.json({ message: '记录及关联配件已成功删除' });
    } catch (error) {
        await conn.rollback();
        console.error('Error deleting record:', error);
        res.status(500).json({ error: '删除记录失败: ' + error.message });
    } finally {
        conn.release();
    }
});


// 获取特定日期和店铺的送货记录
router.get('/records/by-date', async (req, res) => {
    try {
        const { date, storeId } = req.query;
        if (!date) {
            return res.status(400).json({ error: '需要提供日期参数' });
        }
        
        let sql = `
            SELECT r.id, r.created_at, r.notes, r.image_url,
                   s.name as store_name, s.id as store_id,
                   a.address, a.id as address_id
            FROM delivery_records r
            LEFT JOIN stores s ON r.store_id = s.id
            LEFT JOIN addresses a ON r.address_id = a.id
            WHERE DATE(r.created_at) = ?
        `;
        const params = [date];
        
        if (storeId) {
            sql += ' AND r.store_id = ?';
            params.push(storeId);
        }
        sql += ' ORDER BY r.created_at DESC';
        
        const [records] = await db.query(sql, params);
        
        for (const record of records) {
            const [parts] = await db.query('SELECT * FROM parts WHERE record_id = ?', [record.id]);
            record.parts = parts;
        }
        
        res.json(records);
    } catch (error) {
        console.error('获取特定日期记录失败:', error);
        res.status(500).json({ error: '获取记录失败' });
    }
});


// 获取记录详情（包含配件）
router.get('/records/:id/details', async (req, res) => {
    try {
        const id = req.params.id;
        const recordQuery = 'SELECT r.*, s.name as store_name, a.address FROM delivery_records r JOIN stores s ON r.store_id = s.id JOIN addresses a ON r.address_id = a.id WHERE r.id = ?';
        const [records] = await db.query(recordQuery, [id]);
        
        if (records.length === 0) {
            return res.status(404).json({ error: '记录不存在' });
        }
        
        const record = records[0];
        const partsQuery = 'SELECT * FROM parts WHERE record_id = ?';
        const [parts] = await db.query(partsQuery, [id]);
        record.parts = parts;
        
        res.json(record);
    } catch (error) {
        console.error('获取记录详情失败:', error);
        res.status(500).json({ error: '获取记录详情失败' });
    }
});


// ====================================
// 数据统计与分析 (Statistics & Analytics)
// ====================================

// 获取每日送货总金额统计
router.get('/statistics/daily-amounts', async (req, res) => {
    try {
        const sql = `
            SELECT 
                DATE(dr.created_at) AS delivery_date,
                SUM(p.price * p.quantity) AS total_amount
            FROM parts p
            JOIN delivery_records dr ON p.record_id = dr.id
            GROUP BY DATE(dr.created_at)
            ORDER BY delivery_date ASC
        `;
        
        const [results] = await db.query(sql);
        
        const labels = results.map(r => {
            const date = new Date(r.delivery_date);
            return `${date.getMonth() + 1}-${date.getDate()}`;
        });
        const data = results.map(r => parseFloat(r.total_amount) || 0);

        res.json({
            labels,
            datasets: [{
                label: '每日送货总金额(元)',
                data,
                borderWidth: 2,
                tension: 0.4
            }]
        });
    } catch (error) {
        console.error('获取每日送货总金额失败:', error);
        res.status(500).json({ error: '获取统计数据失败' });
    }
});


// 获取每个店铺每日的送货次数
router.get('/statistics/daily-deliveries', async (req, res) => {
    try {
        const days = req.query.days ? parseInt(req.query.days) : null;
        const storeId = req.query.storeId;

        let sql = `
            SELECT
                DATE(dr.created_at) AS delivery_date,
                dr.store_id,
                COUNT(*) AS delivery_count
            FROM delivery_records dr
        `;
        let params = [];
        let whereClauses = [];

        // 如果提供了天数参数，添加日期过滤
        if (days) {
            whereClauses.push('dr.created_at >= ?');
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - days);
            params.push(startDate.toISOString().split('T')[0]);
        }
        
        // 如果提供了店铺ID, 添加店铺过滤
        if (storeId) {
            whereClauses.push('dr.store_id = ?');
            params.push(storeId);
        }

        if (whereClauses.length > 0) {
            sql += ' WHERE ' + whereClauses.join(' AND ');
        }

        sql += ' GROUP BY DATE(dr.created_at), dr.store_id ORDER BY delivery_date ASC';
        
        const [results] = await db.query(sql, params);
        const [stores] = await db.query('SELECT id, name FROM stores');
        const storeMap = stores.reduce((acc, store) => ({...acc, [store.id]: store.name }), {});

        if (storeId) {
            // 单个店铺的数据处理
            const storeName = storeMap[storeId] || `店铺 ${storeId}`;
            const labels = results.map(r => new Date(r.delivery_date).toLocaleDateString('zh-CN', {month: 'numeric', day: 'numeric'}));
            const data = results.map(r => r.delivery_count);
            res.json({
                labels,
                datasets: [{ label: storeName, data, tension: 0.4 }]
            });
        } else {
            // 所有店铺的数据处理
            if (results.length === 0) {
                return res.json({ labels: [], datasets: [] });
            }
            
            const allDates = [...new Set(results.map(r => r.delivery_date.toISOString().split('T')[0]))].sort();
            
            const datasetsByStore = {};

            results.forEach(r => {
                if (!datasetsByStore[r.store_id]) {
                    datasetsByStore[r.store_id] = {
                        label: storeMap[r.store_id] || `店铺 ${r.store_id}`,
                        data: Array(allDates.length).fill(0),
                        tension: 0.4
                    };
                }
                const dateIndex = allDates.indexOf(r.delivery_date.toISOString().split('T')[0]);
                if (dateIndex > -1) {
                    datasetsByStore[r.store_id].data[dateIndex] = r.delivery_count;
                }
            });
            
            res.json({ 
                labels: allDates.map(d => new Date(d).toLocaleDateString('zh-CN', {month: 'numeric', day: 'numeric'})), 
                datasets: Object.values(datasetsByStore) 
            });
        }
    } catch (error) {
        console.error('获取每日送货次数失败:', error);
        res.status(500).json({ error: '获取统计数据失败' });
    }
});


// 获取综合统计数据
router.get('/statistics', async (req, res) => {
    try {
        const [storeStats] = await db.query(`
            SELECT s.name as store_name, COUNT(r.id) as count
            FROM delivery_records r JOIN stores s ON r.store_id = s.id
            GROUP BY r.store_id ORDER BY count DESC LIMIT 10
        `);
        const [addressStats] = await db.query(`
            SELECT a.address, COUNT(r.id) as count
            FROM delivery_records r JOIN addresses a ON r.address_id = a.id
            GROUP BY r.address_id ORDER BY count DESC LIMIT 10
        `);
        const [dailyStats] = await db.query(`
            SELECT DATE(created_at) as date, COUNT(*) as count
            FROM delivery_records
            GROUP BY DATE(created_at) ORDER BY date ASC
        `);
        res.json({ storeStats, addressStats, dailyStats });
    } catch (error) {
        console.error('获取统计数据失败:', error);
        res.status(500).json({ error: '获取统计数据失败' });
    }
});

// 检查配件数据状态
router.get('/parts/check', async (req, res) => {
    try {
        const [results] = await db.query(`
            SELECT 
                COUNT(*) as total_parts,
                COUNT(CASE WHEN price > 0 AND quantity > 0 THEN 1 END) as valid_parts,
                AVG(price) as avg_price,
                MAX(price) as max_price
            FROM parts
        `);
        const [recentResults] = await db.query(`
            SELECT COUNT(*) as recent_records
            FROM delivery_records
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        `);
        res.json({
            ...results[0],
            recentRecords: recentResults[0].recent_records
        });
    } catch (error) {
        console.error('检查配件数据失败:', error);
        res.status(500).json({ error: '检查配件数据失败' });
    }
});


// ====================================
// 导入与导出 (Import & Export)
// ====================================

// 导出数据
router.get('/export', async (req, res) => {
    try {
        const [records] = await db.query(`
            SELECT r.id, s.name as store_name, a.address, r.notes, r.created_at, r.image_url
            FROM delivery_records r
            JOIN stores s ON r.store_id = s.id
            JOIN addresses a ON r.address_id = a.id
            ORDER BY r.created_at DESC
        `);
        
        for (const record of records) {
            const [parts] = await db.query('SELECT * FROM parts WHERE record_id = ?', [record.id]);
            let total = parts.reduce((sum, p) => sum + (parseFloat(p.price || 0) * parseInt(p.quantity || 1)), 0);
            record.parts = parts.map(p => `${p.type || 'N/A'}: ${p.model || 'N/A'} × ${p.quantity || 1} (¥${parseFloat(p.price || 0).toFixed(2)})`).join('; ');
            record.total_price = `¥${total.toFixed(2)}`;
        }
        
        const escapeCSV = (field) => {
            const str = String(field == null ? '' : field);
            return `"${str.replace(/"/g, '""')}"`;
        };

        let csvData = '记录ID,店铺名称,送货地点,备注,创建时间,配件信息,总价,图片URL\n';
        records.forEach(r => {
            csvData += [
                escapeCSV(r.id), escapeCSV(r.store_name), escapeCSV(r.address),
                escapeCSV(r.notes), escapeCSV(r.created_at), escapeCSV(r.parts),
                escapeCSV(r.total_price), escapeCSV(r.image_url)
            ].join(',') + '\n';
        });

        res.setHeader('Content-Type', 'text/csv; charset=utf-8');
        res.setHeader('Content-Disposition', 'attachment; filename=delivery_records.csv');
        res.send(Buffer.from(csvData));
    } catch (error) {
        console.error('导出数据失败:', error);
        res.status(500).json({ error: '导出数据失败' });
    }
});

// 导入数据
router.post('/import', upload.single('file'), async (req, res) => {
    if (!req.file) {
        return res.status(400).json({ error: '没有上传文件' });
    }
    const conn = await db.getConnection();
    try {
        await conn.beginTransaction();
        const fileContent = fs.readFileSync(req.file.path, 'utf8');
        const lines = fileContent.split('\n').slice(1); // Skip header
        let importedCount = 0;

        for (const line of lines) {
            if (!line.trim()) continue;
            const values = line.split(','); // Simplified CSV parsing
            const [id, store_name, address, notes, created_at, parts_info, total_price, image_url] = values;

            // Find or create store
            let [stores] = await conn.query('SELECT id FROM stores WHERE name = ?', [store_name]);
            let storeId;
            if (stores.length === 0) {
                const [result] = await conn.query('INSERT INTO stores (name) VALUES (?)', [store_name]);
                storeId = result.insertId;
            } else {
                storeId = stores[0].id;
            }

            // Find or create address
            let [addresses] = await conn.query('SELECT id FROM addresses WHERE address = ?', [address]);
            let addressId;
            if (addresses.length === 0) {
                const [result] = await conn.query('INSERT INTO addresses (address) VALUES (?)', [address]);
                addressId = result.insertId;
            } else {
                addressId = addresses[0].id;
            }

            // Create delivery record
            const [recordResult] = await conn.query(
                'INSERT INTO delivery_records (store_id, address_id, notes, created_at) VALUES (?, ?, ?, ?)',
                [storeId, addressId, notes, new Date(created_at)]
            );
            importedCount++;
        }
        
        await conn.commit();
        fs.unlinkSync(req.file.path); // Delete temp file
        res.json({ message: `成功导入 ${importedCount} 条记录` });
    } catch (error) {
        await conn.rollback();
        if (req.file && fs.existsSync(req.file.path)) {
            fs.unlinkSync(req.file.path);
        }
        console.error('导入数据失败:', error);
        res.status(500).json({ error: '导入数据失败' });
    } finally {
        conn.release();
    }
});


module.exports = router;