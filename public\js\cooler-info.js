// 检测浏览器是否支持WebP


// 默认图片路径
const DEFAULT_COOLER_IMAGE = '/images/default-cooler.png';



// 页面加载时检测WebP支持
document.addEventListener('DOMContentLoaded', function() {
  // 检测浏览器是否支持WebP
  checkWebPSupport();
  
  // 添加颜色标签的CSS样式
  const style = document.createElement('style');
  style.textContent = `
    .spec-tag.color {
      background-color: rgba(124, 58, 237, 0.1) !important;
      color: #7c3aed !important;
      border: 1px solid rgba(124, 58, 237, 0.2) !important;
    }
    
    /* 添加表格固定布局样式 */
    table.cooler-table {
      table-layout: fixed;
      width: 100%;
    }
    
    /* 添加列宽度样式 */
    .cooler-table th.col-model {
      width: 200px;
      max-width: 200px;
    }
    
    .cooler-table th.col-brand {
      width: 120px;
    }
    
    .cooler-table th.col-type {
      width: 80px;
    }
    
    .cooler-table th.col-tdp {
      width: 80px;
    }
    
    .cooler-table th.col-color {
      width: 80px;
      max-width: 80px;
    }
    
    .cooler-table th.col-height {
      width: 80px;
    }
    
    .cooler-table th.col-fan {
      width: 65px;
    }
    
    .cooler-table th.col-pipe {
      width: 65px;
    }
    
    .cooler-table th.col-action {
      width: 130px;
      min-width: 130px;
    }
    
    /* 添加文本截断和省略号样式 */
    .cell-truncate {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 100%;
    }
  `;
  document.head.appendChild(style);
  
  // 启动图片点击事件绑定
  attachImageClickHandlers();
  // 不再需要定期检查
  // setInterval(attachImageClickHandlers, 1000);
});

// 在全局变量下添加API端点常量
const API_ENDPOINTS = {
    COOLERS: '/api/coolers',
    COOLER: (id) => `/api/coolers/${id}`
};

// 全屏图片预览功能
function openImageFullscreen(src) {
    console.log('Opening image:', src);
    
    // 安全检查：如果没有src或src不是字符串，使用默认图片
    if (!src || typeof src !== 'string') {
        console.error('Invalid image source:', src);
        src = '/images/default-cooler.png';
    }
    
    try {
        // 创建一个临时的图片容器
        const container = document.createElement('div');
        container.className = 'viewer-container';
        container.style.display = 'none';
        document.body.appendChild(container);

        // 创建图片元素
        const img = document.createElement('img');
        img.src = src;
        
        // 添加加载失败处理
        img.onerror = function() {
            console.error('Failed to load image:', src);
            this.src = '/images/default-cooler.png';
        };
        
        container.appendChild(img);

        // 检测是否为移动设备
        const isMobile = window.innerWidth < 640;
        
        // 初始化 Viewer
        const viewer = new Viewer(img, {
            backdrop: true,          // 启用背景遮罩
            button: true,           // 显示关闭按钮
            navbar: false,          // 隐藏底部导航栏（只有一张图片时不需要）
            title: false,           // 不显示标题
            toolbar: {              // 自定义工具栏
                zoomIn: true,       // 放大按钮
                zoomOut: true,      // 缩小按钮
                oneToOne: true,     // 1:1 尺寸按钮
                reset: true,        // 重置按钮
                prev: false,        // 上一张（隐藏，因为只有一张图片）
                play: false,        // 播放按钮（隐藏）
                next: false,        // 下一张（隐藏）
                rotateLeft: true,   // 向左旋转
                rotateRight: true,  // 向右旋转
                flipHorizontal: true, // 水平翻转
                flipVertical: true,  // 垂直翻转
            },
            initialViewIndex: 0,    // 初始视图索引
            viewed() {
                // 图片加载完成后自动打开查看器
                // 移动端使用较小的缩放比例，避免自动放大
                if (isMobile) {
                    viewer.zoomTo(0.2);  // 移动设备使用较小的初始缩放比例
                } else {
                    viewer.zoomTo(1);    // 桌面设备使用正常的缩放比例
                }
                console.log('Image viewed successfully');
            },
            hidden() {
                // 查看器关闭后移除临时元素
                console.log('Viewer closed, cleaning up');
                viewer.destroy();
                document.body.removeChild(container);
            },
            maxZoomRatio: 5,        // 最大缩放比例
            minZoomRatio: 0.1,      // 最小缩放比例
            transition: true,       // 启用过渡效果
            keyboard: true,         // 启用键盘支持
        });

        // 显示查看器
        viewer.show();
        console.log('Viewer show() called');
    } catch (error) {
        console.error('Error in openImageFullscreen:', error);
    }
}

// 图片点击事件处理函数
function attachImageClickHandlers() {
    // 为页面添加全局事件委托
    document.removeEventListener('click', handleImageClick); // 先移除已有监听器
    document.addEventListener('click', handleImageClick);
    
    // 查找所有具有data-image-src属性的图片和容器
    const imageElements = document.querySelectorAll('[data-image-src]');
    imageElements.forEach(el => {
        if (!el.hasAttribute('data-viewer-attached')) {
            el.setAttribute('data-viewer-attached', 'true');
            el.style.cursor = 'pointer';
        }
    });
}

// 图片点击处理函数
function handleImageClick(event) {
    // 查找最近的具有data-image-src属性的祖先元素
    let target = event.target;
    while (target && !target.getAttribute('data-image-src')) {
        if (target.parentElement) {
            target = target.parentElement;
        } else {
            // 没有找到具有data-image-src的元素
            return;
        }
    }
    
    // 如果找到了元素并且有data-image-src属性
    const imageSrc = target.getAttribute('data-image-src');
    if (imageSrc) {
        event.preventDefault();
        event.stopPropagation();
        console.log('图片点击事件:', imageSrc);
        openImageFullscreen(imageSrc);
    }
}

// 全局变量
let currentPage = 1;
const pageSize = 10;
let totalRecords = 0;
let coolers = [];
let currentCoolerId = null;
let isEditing = false;

// 增强移动端模态框按钮
function enhanceMobileModalButtons() {
    const isMobile = window.innerWidth < 640;
    
    // 获取所有模态框按钮
    const editBtn = document.getElementById('editBtn');
    const deleteBtn = document.getElementById('deleteBtn');
    const closeModalBtn = document.getElementById('closeModalBtn');
    
    if (isMobile) {
        // 在移动端增强按钮样式
        const modalButtons = [editBtn, deleteBtn, closeModalBtn];
        
        modalButtons.forEach(btn => {
            if (btn) {
                // 确保按钮有足够大的点击区域
                btn.style.minHeight = '50px';
                btn.style.marginBottom = '8px';
                btn.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
                btn.style.display = 'flex';
                btn.style.alignItems = 'center';
                btn.style.justifyContent = 'center';
                btn.style.fontWeight = '500';
                btn.style.position = 'relative';
                
                // 添加触摸反馈效果
                btn.addEventListener('touchstart', function() {
                    this.style.transform = 'translateY(2px)';
                    this.style.boxShadow = '0 1px 2px rgba(0,0,0,0.1)';
                }, { passive: true });
                
                btn.addEventListener('touchend', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
                }, { passive: true });
            }
        });
    }
}

// 全局定义函数，解决作用域问题
function viewCoolerDetails(id) {
    console.log('查看散热器详情:', id);

    if (!id) {
        showErrorMessage('无效的散热器ID');
        return;
    }

    // 显示加载状态
    const coolerDetails = document.getElementById('coolerDetails');
    if (coolerDetails) {
        coolerDetails.innerHTML = `
            <div class="flex justify-center items-center h-40">
                <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
            </div>
        `;
    }

    const coolerModal = document.getElementById('coolerModal');
    if (coolerModal) {
        coolerModal.classList.remove('hidden');
        coolerModal.style.display = 'flex'; // 修复：强制显示模态框
    }

    currentCoolerId = id;

    const showDetails = (cooler) => {
        renderCoolerDetails(cooler);
        // 检查用户权限并处理详情页面的按钮
        isAdmin().then(isAdminUser => {
            const modalEditBtn = document.getElementById('editBtn');
            const modalDeleteBtn = document.getElementById('deleteBtn');
            if (modalEditBtn) {
                modalEditBtn.style.display = isAdminUser ? 'inline-flex' : 'none';
            }
            if (modalDeleteBtn) {
                modalDeleteBtn.style.display = isAdminUser ? 'inline-flex' : 'none';
            }
        });
    };

    // 从已加载的数据中查找
    const cooler = coolers.find(c => c.id === id || c.id === parseInt(id));
    if (cooler) {
        showDetails(cooler);
        return;
    }

    // 如果本地没有，从API获取
    fetchWithAuth(`${API_ENDPOINTS.COOLER(id)}`)
        .then(response => {
            if (!response || !response.ok) {
                throw new Error('获取散热器详情失败');
            }
            return response.json();
        })
        .then(coolerData => {
            showDetails(coolerData);
        })
        .catch(error => {
            console.error('Error:', error);
            showErrorMessage(error.message);
            if (coolerModal) {
                coolerModal.classList.add('hidden');
                coolerModal.style.display = 'none';
            }
        });
}

// 编辑散热器信息
function editCooler(id) {
    if (!id) return;
    
    // 重要：在开始编辑前，立即清除任何缓存的图片文件
    imageFile = null;
    
    fetchWithAuth(`${API_ENDPOINTS.COOLER(id)}`)
        .then(response => {
            if (!response || !response.ok) {
                throw new Error('获取散热器信息失败');
            }
            return response.json();
        })
        .then(cooler => {
            resetForm();
            
            // 重要：确保在编辑模式开始时，imageFile为null
            imageFile = null;
            
            currentCoolerId = cooler.id;
            isEditing = true;  // 设置为编辑模式
            
            console.log('加载编辑数据:', cooler);
            console.log('RGB灯效值:', cooler.rgb_lighting);
            
            // 获取表单元素
            const brand = document.getElementById('brand');
            const model = document.getElementById('model');
            const type = document.getElementById('type');
            const socket_support = document.getElementById('socket_support');
            const tdp = document.getElementById('tdp');
            const fanCount = document.getElementById('fanCount');
            const heatpipeCount = document.getElementById('heatpipeCount');
            const towerCount = document.getElementById('towerCount');
            const fanSize = document.getElementById('fanSize');
            const radiatorSize = document.getElementById('radiatorSize');
            const noiseLevel = document.getElementById('noiseLevel');
            const height = document.getElementById('height');
            const rgbLighting = document.getElementById('rgbLighting');
            const color = document.getElementById('color');
            const material = document.getElementById('material');
            const warranty = document.getElementById('warranty');
            const price = document.getElementById('price');
            const notes = document.getElementById('notes');
            const imagePreview = document.getElementById('imagePreview');
            const imagePreviewContainer = document.getElementById('imagePreviewContainer');
            const coolerForm = document.getElementById('coolerForm');
            
            // 填充表单
            if (brand) brand.value = cooler.brand || '';
            if (model) model.value = cooler.model || '';
            
            // 处理散热器类型
            if (type) {
                console.log('原始类型值:', cooler.type);
                if (cooler.type && typeof cooler.type === 'string') {
                    const lowerType = cooler.type.toLowerCase();
                    if (lowerType.includes('风冷')) {
                        type.value = '风冷';
                    } else if (lowerType.includes('aio')) {
                        type.value = 'AIO';
                    } else if (lowerType.includes('水冷')) {
                        type.value = '水冷';
                    } else if (lowerType.includes('半导体')) {
                        type.value = '半导体';
                    } else if (lowerType.includes('被动')) {
                        type.value = '被动散热';
                    } else {
                        // 默认为风冷
                        type.value = '风冷';
                    }
                } else {
                    type.value = '';
                }
                console.log('设置类型下拉框值:', type.value);
            }
            
            if (socket_support) socket_support.value = cooler.socket_support || '';
            if (tdp) tdp.value = cooler.tdp || '';
            if (fanCount) fanCount.value = cooler.fan_count || '';
            if (heatpipeCount) heatpipeCount.value = cooler.heatpipe_count || '';
            if (towerCount) towerCount.value = cooler.tower_count || '';
            if (fanSize) fanSize.value = cooler.fan_size || '';
            if (radiatorSize) radiatorSize.value = cooler.radiator_size || '';
            if (noiseLevel) noiseLevel.value = cooler.noise_level || '';
            if (height) height.value = cooler.height || '';
            
            // 处理RGB灯效值
            if (rgbLighting) {
                if (cooler.rgb_lighting === '有' || cooler.rgb_lighting === '1' || cooler.rgb_lighting === 1) {
                    rgbLighting.value = '1';
                } else {
                    rgbLighting.value = '0';
                }
                console.log('设置RGB灯效下拉框值:', rgbLighting.value);
            }
            
            if (color) color.value = cooler.color || '';
            if (material) material.value = cooler.material || '';
            if (warranty) warranty.value = cooler.warranty || '';
            if (price) price.value = cooler.price || '';
            if (notes) notes.value = cooler.notes || '';
            
            if (cooler.image_url && imagePreview) {
                imagePreview.src = cooler.image_url;
                if (imagePreviewContainer) {
                    imagePreviewContainer.classList.remove('hidden');
                }
            }
            
            imageFile = null; // 修复：确保在编辑模式开始时，清除任何缓存的图片文件

            // 更改提交按钮文本
            if (coolerForm) {
                const submitBtn = coolerForm.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.innerHTML = '<i class="fas fa-save mr-1"></i> 更新散热器信息';
                }
                
                // 滚动到表单区域
                coolerForm.scrollIntoView({ behavior: 'smooth' });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showErrorMessage(error.message);
        });
}

// 确认删除散热器
function confirmDeleteCooler(id) {
    if (!id) return;
    
    // 创建删除确认对话框
    showDeleteConfirmDialog(id);
}

// 显示删除确认对话框
function showDeleteConfirmDialog(id) {
    // 检查是否已存在确认对话框，如果存在则先移除
    const existingDialog = document.getElementById('deleteConfirmDialog');
    if (existingDialog) {
        document.body.removeChild(existingDialog);
    }
    
    // 检测是否为移动设备
    const isMobile = window.innerWidth < 640;
    
    // 检测是否为暗黑模式
    const isDarkMode = document.body.classList.contains('dark-mode');
    
    // 创建确认对话框元素
    const dialogContainer = document.createElement('div');
    dialogContainer.id = 'deleteConfirmDialog';
    dialogContainer.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    dialogContainer.style.animation = 'fadeIn 0.25s ease-out';
    
    // 对话框内容 - 现代设计
    if (isMobile) {
        // 移动端专用布局
        dialogContainer.innerHTML = `
            <div style="width: 90%; max-width: 320px; background-color: ${isDarkMode ? '#252525' : '#fff'}; border-radius: 16px; box-shadow: 0 10px 25px rgba(0,0,0,${isDarkMode ? '0.5' : '0.25'}); overflow: hidden; animation: scaleIn 0.2s ease-out; margin: 0 auto; transform: translateY(-10px); border: ${isDarkMode ? '1px solid rgba(255,255,255,0.1)' : 'none'};">
                <div style="padding: 24px 20px; display: flex; flex-direction: column; align-items: center;">
                    <div style="width: 70px; height: 70px; border-radius: 50%; background-color: rgba(220, 38, 38, ${isDarkMode ? '0.15' : '0.1'}); display: flex; align-items: center; justify-content: center; margin-bottom: 20px; border: 2px solid rgba(220, 38, 38, ${isDarkMode ? '0.3' : '0.2'});">
                        <i class="fas fa-trash-alt" style="color: ${isDarkMode ? '#ef4444' : '#dc2626'}; font-size: 28px;"></i>
                    </div>
                    
                    <h3 style="font-size: 20px; font-weight: 700; color: ${isDarkMode ? '#f3f4f6' : '#111827'}; margin-bottom: 12px; text-align: center;">确认删除</h3>
                    <p style="font-size: 15px; color: ${isDarkMode ? '#d1d5db' : '#4b5563'}; text-align: center; margin-bottom: 24px; line-height: 1.5;">您确定要删除这个散热器吗？<br>此操作<span style="color: ${isDarkMode ? '#f87171' : '#dc2626'}; font-weight: 600;">不可撤销</span>。</p>
                    
                    <div style="width: 100%; display: flex; flex-direction: column; gap: 12px;">
                        <button id="confirmDeleteBtn" style="width: 100%; height: 50px; padding: 0; background-color: ${isDarkMode ? '#b91c1c' : '#dc2626'}; color: white; border: none; border-radius: 12px; font-size: 17px; font-weight: 600; text-align: center; cursor: pointer; display: flex; align-items: center; justify-content: center; box-shadow: 0 4px 12px rgba(220, 38, 38, ${isDarkMode ? '0.15' : '0.25'}); transition: all 0.2s ease;">
                            <i class="fas fa-trash-alt mr-2" style="font-size: 16px;"></i>
                            <span style="pointer-events: none;">确认删除</span>
                        </button>
                        <button id="cancelDeleteBtn" style="width: 100%; height: 50px; padding: 0; background-color: ${isDarkMode ? '#374151' : '#f3f4f6'}; color: ${isDarkMode ? '#d1d5db' : '#4b5563'}; border: none; border-radius: 12px; font-size: 17px; font-weight: 600; text-align: center; cursor: pointer; display: flex; align-items: center; justify-content: center; transition: all 0.2s ease;">
                            <span style="pointer-events: none;">取消</span>
                        </button>
                    </div>
                </div>
            </div>
        `;
    } else {
        // 桌面端现代布局
        dialogContainer.innerHTML = `
            <div class="bg-${isDarkMode ? 'gray-800' : 'white'} rounded-lg shadow-xl p-6 max-w-md w-full transform transition-transform" style="animation: scaleIn 0.15s ease-out; ${isDarkMode ? 'border: 1px solid rgba(255,255,255,0.1);' : ''}">
                <div class="text-center">
                    <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full ${isDarkMode ? 'bg-red-900/30' : 'bg-red-100'} mb-4 border-2 ${isDarkMode ? 'border-red-800/50' : 'border-red-200'}">
                        <i class="fas fa-trash-alt ${isDarkMode ? 'text-red-400' : 'text-red-600'} text-xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold ${isDarkMode ? 'text-gray-100' : 'text-gray-900'} mb-2">确认删除</h3>
                    <p class="${isDarkMode ? 'text-gray-300' : 'text-gray-500'} mb-6">您确定要删除这个散热器吗？此操作<span class="${isDarkMode ? 'text-red-400' : 'text-red-600'} font-medium">不可撤销</span>。</p>
                </div>
                <div class="flex justify-center gap-4">
                    <button id="cancelDeleteBtn" class="px-5 py-2 ${isDarkMode ? 'bg-gray-700 text-gray-200 hover:bg-gray-600' : 'bg-gray-200 text-gray-800 hover:bg-gray-300'} rounded-md transition-colors font-medium" style="min-width: 110px;">
                        取消
                    </button>
                    <button id="confirmDeleteBtn" class="px-5 py-2 ${isDarkMode ? 'bg-red-800 hover:bg-red-700' : 'bg-red-600 hover:bg-red-700'} text-white rounded-md transition-colors font-medium" style="min-width: 110px;">
                        <i class="fas fa-trash-alt mr-2"></i>确认删除
                    </button>
                </div>
            </div>
        `;
    }
    
    // 添加到DOM
    document.body.appendChild(dialogContainer);
    
    // 添加CSS动画样式
    const style = document.createElement('style');
    style.textContent = `
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        @keyframes scaleIn {
            from { transform: scale(0.95); opacity: 0; }
            to { transform: scale(1); opacity: 1; }
        }
        
        /* 添加暗夜模式支持 */
        body.dark-mode #deleteConfirmDialog > div {
            background-color: #252525 !important;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        body.dark-mode #deleteConfirmDialog h3 {
            color: #e5e7eb !important;
        }
        body.dark-mode #deleteConfirmDialog p {
            color: #9ca3af !important;
        }
        body.dark-mode #cancelDeleteBtn {
            background-color: #374151 !important;
            color: #d1d5db !important;
        }
        /* 红色按钮在暗夜模式下保持红色，但稍微调暗 */
        body.dark-mode #confirmDeleteBtn {
            background-color: #b91c1c !important;
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.15) !important;
        }
    `;
    document.head.appendChild(style);
    
    // 事件处理
    const cancelBtn = document.getElementById('cancelDeleteBtn');
    const confirmBtn = document.getElementById('confirmDeleteBtn');
    
    // === 增强版：为移动端按钮强制设置样式，防止被覆盖 ===
    if (isMobile) {
        [cancelBtn, confirmBtn].forEach(btn => {
            if (btn) {
                btn.style.setProperty('display', 'flex', 'important');
                btn.style.setProperty('align-items', 'center', 'important');
                btn.style.setProperty('justify-content', 'center', 'important');
                btn.style.setProperty('width', '100%', 'important');
                btn.style.setProperty('height', '50px', 'important'); 
                btn.style.setProperty('border-radius', '12px', 'important');
                btn.style.setProperty('font-size', '17px', 'important');
                btn.style.setProperty('font-weight', '600', 'important');
            }
        });
        
        // 确认按钮特殊样式
        if (confirmBtn) {
            confirmBtn.style.setProperty('background-color', '#dc2626', 'important');
            confirmBtn.style.setProperty('color', 'white', 'important');
            confirmBtn.style.setProperty('box-shadow', '0 4px 12px rgba(220, 38, 38, 0.25)', 'important');
        }
        
        // 取消按钮特殊样式
        if (cancelBtn) {
            cancelBtn.style.setProperty('background-color', '#f3f4f6', 'important');
            cancelBtn.style.setProperty('color', '#4b5563', 'important');
        }
    }
    
    // 增强按钮交互效果
    if (cancelBtn) {
        cancelBtn.style.cursor = 'pointer';
        cancelBtn.style.userSelect = 'none';
        cancelBtn.style.WebkitTapHighlightColor = 'transparent';
        
        if (isMobile) {
            // 移动端增强触摸反馈
            cancelBtn.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.98)';
                this.style.opacity = '0.95';
            }, { passive: true });
            
            cancelBtn.addEventListener('touchend', function() {
                this.style.transform = 'scale(1)';
                this.style.opacity = '1';
            }, { passive: true });
        }
    }
    
    if (confirmBtn) {
        confirmBtn.style.cursor = 'pointer';
        confirmBtn.style.userSelect = 'none';
        confirmBtn.style.WebkitTapHighlightColor = 'transparent';
        
        if (isMobile) {
            // 移动端增强触摸反馈
            confirmBtn.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.98)';
                this.style.opacity = '0.95';
            }, { passive: true });
            
            confirmBtn.addEventListener('touchend', function() {
                this.style.transform = 'scale(1)';
                this.style.opacity = '1';
            }, { passive: true });
        }
    }
    
    // 点击取消按钮
    cancelBtn.addEventListener('click', () => {
        closeDeleteConfirmDialog();
    });
    
    // 点击确认按钮
    confirmBtn.addEventListener('click', () => {
        closeDeleteConfirmDialog();
        deleteCooler(id);
    });
    
    // 点击背景关闭
    dialogContainer.addEventListener('click', (e) => {
        if (e.target === dialogContainer) {
            closeDeleteConfirmDialog();
        }
    });
}

// 关闭删除确认对话框
function closeDeleteConfirmDialog() {
    const dialog = document.getElementById('deleteConfirmDialog');
    if (dialog) {
        // 添加淡出动画
        dialog.style.animation = 'fadeOut 0.3s ease-out forwards';
        
        // 动画中缩小确认框
        const dialogContent = dialog.querySelector('div > div');
        if (dialogContent) {
            dialogContent.style.animation = 'scaleOut 0.25s ease-out forwards';
        }
        
        // 动画结束后移除对话框
        setTimeout(() => {
            if (dialog.parentNode) {
                dialog.parentNode.removeChild(dialog);
            }
        }, 300);
    }
}

// 添加淡出动画样式
const fadeOutStyle = document.createElement('style');
fadeOutStyle.textContent = `
    @keyframes fadeOut {
        from { opacity: 1; }
        to { opacity: 0; }
    }
    
    @keyframes scaleOut {
        from { transform: scale(1); opacity: 1; }
        to { transform: scale(0.95); opacity: 0; }
    }
`;
document.head.appendChild(fadeOutStyle);

// 删除散热器
function deleteCooler(id) {
    if (!id) return;
    closeDeleteConfirmDialog(); // 先关闭确认对话框
    
    fetchWithAuth(API_ENDPOINTS.COOLER(id), {
        method: 'DELETE',
    })
    .then(response => {
        // 检查状态码，204表示成功且无内容
        if (response.status === 204) {
            return null; // 返回null表示成功，以便then继续处理
        }
        if (response.ok) {
            return response.json(); // 如果有内容，则解析
        }
        // 处理错误情况
        return response.json().then(err => { 
            throw new Error(err.message || '删除失败，服务器返回错误'); 
        });
    })
    .then(() => {
        showSuccessMessage('散热器删除成功！');
        
        // 尝试从UI中移除元素
        const row = document.getElementById(`cooler-row-${id}`);
        if (row) {
            row.remove();
        }
        
        // 重新加载数据以更新分页和总数
        loadCoolers();
    })
    .catch(error => {
        console.error('删除散热器失败:', error);
        showErrorMessage(error.message || '删除失败，请检查网络或联系管理员');
    });
}

// 移除openImageFullscreen函数，因为不再需要图片放大功能

// 显示错误消息
function showErrorMessage(message) {
    const alertElement = document.createElement('div');
    alertElement.className = 'fixed top-4 right-4 bg-red-50 border-l-4 border-red-500 p-4 opacity-0 transition-opacity duration-300 shadow-md fade-in';
    alertElement.innerHTML = `
        <div class="flex items-center">
            <div class="flex-shrink-0 text-red-500">
                <i class="fas fa-exclamation-circle"></i>
            </div>
            <div class="ml-3">
                <p class="text-sm text-red-700">${message}</p>
            </div>
        </div>
    `;
    document.body.appendChild(alertElement);
    
    setTimeout(() => alertElement.classList.add('opacity-100'), 10);
    setTimeout(() => {
        alertElement.classList.remove('opacity-100');
        setTimeout(() => document.body.removeChild(alertElement), 300);
    }, 3000);
}



// 显示成功消息
function showSuccessMessage(message) {
    const alertElement = document.createElement('div');
    alertElement.className = 'fixed top-4 right-4 bg-green-50 border-l-4 border-green-500 p-4 opacity-0 transition-opacity duration-300 shadow-md fade-in';
    alertElement.innerHTML = `
        <div class="flex items-center">
            <div class="flex-shrink-0 text-green-500">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="ml-3">
                <p class="text-sm text-green-700">${message}</p>
            </div>
        </div>
    `;
    document.body.appendChild(alertElement);
    
    setTimeout(() => alertElement.classList.add('opacity-100'), 10);
    setTimeout(() => {
        alertElement.classList.remove('opacity-100');
        setTimeout(() => document.body.removeChild(alertElement), 300);
    }, 3000);
}

// 加载散热器列表
function loadCoolers() {
    const coolerSearch = document.getElementById('coolerSearch');
    const brandFilter = document.getElementById('brandFilter');
    const typeFilter = document.getElementById('typeFilter');
    const totalCount = document.getElementById('totalCount');
    const coolerTableBody = document.getElementById('coolerTableBody');
    
    const searchTerm = coolerSearch ? coolerSearch.value.trim() : '';
    const selectedBrand = brandFilter ? brandFilter.value : 'all';
    const selectedType = typeFilter ? typeFilter.value : 'all';
    
    // 显示加载状态
    if (coolerTableBody) {
        coolerTableBody.innerHTML = `
            <tr>
                <td colspan="8" class="px-3 py-4 text-center">
                    <div class="flex items-center justify-center">
                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-cyan-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span>加载中...</span>
                    </div>
                </td>
            </tr>
        `;
    }
    
    let url = `${API_ENDPOINTS.COOLERS}?page=${currentPage}&limit=${pageSize}`;
    
    // 关键修正：将搜索词添加到API请求中
    if (searchTerm) {
        url += `&search=${encodeURIComponent(searchTerm)}`;
    }
    
    // 常规API参数处理
    if (selectedBrand && selectedBrand !== 'all') {
        url += `&brand=${encodeURIComponent(selectedBrand)}`;
    }

    if (selectedType && selectedType !== 'all') {
        url += `&type=${encodeURIComponent(selectedType)}`;
    }
    
    console.log('API请求URL:', url);
    
    const fetchCoolersPromise = fetchWithAuth(url);
    const isAdminPromise = isAdmin();

    Promise.all([fetchCoolersPromise, isAdminPromise])
        .then(([response, isAdminUser]) => {
            if (!response || !response.ok) {
                return response.json().then(err => {
                    throw new Error(err.message || '加载散热器列表失败');
                }).catch(() => {
                    throw new Error('加载散热器列表失败');
                });
            }
            return response.json().then(data => ({ data, isAdminUser }));
        })
        .then(({ data, isAdminUser }) => {
            console.log('API返回数据:', data);
            coolers = data.coolers || [];
            totalRecords = data.totalRecords || 0;

            renderCoolerTable(coolers, isAdminUser);
            updatePagination();
        })
        .catch(error => {
            console.error('Error:', error);
            if (coolerTableBody) {
                coolerTableBody.innerHTML = `
                    <tr>
                        <td colspan="8" class="px-3 py-4 text-center text-red-500">
                            <div class="flex items-center justify-center">
                                <i class="fas fa-exclamation-circle mr-2"></i>
                                ${error.message || '加载失败，请稍后再试'}
                            </div>
                        </td>
                    </tr>
                `;
            }
            showErrorMessage(error.message || '加载散热器列表失败');
        });
}

    // 渲染散热器表格
    function renderCoolerTable(data, isAdminUser) {
        const coolerTableBody = document.getElementById('coolerTableBody');
        const totalCount = document.getElementById('totalCount');
        
        if (!coolerTableBody || !data.length) {
            if (coolerTableBody) {
                coolerTableBody.innerHTML = `
                    <tr>
                        <td colspan="8" class="px-3 py-4 text-center text-gray-500">暂无数据</td>
                    </tr>
                `;
            }
            if (totalCount) {
                totalCount.textContent = `共 0 条记录`;
            }
            return;
        }

        // 检测是否为移动设备
        const isMobile = window.innerWidth < 640;
        
        // 为每个散热器添加默认图片路径处理
        data.forEach(cooler => {
            if (!cooler.image_url) {
                // 设置默认图片路径为系统提供的默认图片
                cooler.is_default_image = true;
                cooler.image_url = DEFAULT_COOLER_IMAGE;
            }
        });
        
        // 根据设备类型选择渲染方法
        if (isMobile) {
            renderMobileCards(data, isAdminUser);
        } else {
            renderDesktopTable(data, isAdminUser);
        }
        
        // 更新总记录数
        if (totalCount) {
            const recordCountEl = document.getElementById('recordCount');
            if (recordCountEl) {
                recordCountEl.textContent = totalRecords;
            } else {
                totalCount.textContent = `共 ${totalRecords} 条记录`;
            }
        }
        
        // 在渲染表格后调用图片点击事件处理
        attachImageClickHandlers();
    }
    
    // 移动端卡片式布局渲染
    function renderMobileCards(data, isAdminUser) {
        const coolerTableBody = document.getElementById('coolerTableBody');
        if (!coolerTableBody) return;
        
        coolerTableBody.innerHTML = '';
        
        // 修改表格样式为区块显示
        const tableElement = coolerTableBody.closest('table');
        if (tableElement) {
            tableElement.style.display = 'block';
            tableElement.style.width = '100%';
            tableElement.style.maxWidth = '100%';
            tableElement.style.borderCollapse = 'collapse';
            tableElement.style.borderSpacing = '0';
            const theadElement = tableElement.querySelector('thead');
            if (theadElement) {
                theadElement.style.display = 'none';
            }
        }
        
        coolerTableBody.style.display = 'block';
        coolerTableBody.style.width = '100%';
        coolerTableBody.style.maxWidth = '100%';
        
        data.forEach((cooler, index) => {
            const cardOuterContainer = document.createElement('div');
            cardOuterContainer.className = 'cooler-card-outer-container';
            cardOuterContainer.style.width = '100%';
            cardOuterContainer.style.maxWidth = '100vw';
            cardOuterContainer.style.boxSizing = 'border-box';
            cardOuterContainer.style.padding = '0 4px';
            cardOuterContainer.style.marginBottom = '12px';
            cardOuterContainer.style.position = 'relative';
            cardOuterContainer.style.animation = `fadeIn 0.3s ease-in-out ${index * 0.05}s both`;
            
            const card = document.createElement('div');
            card.className = 'cooler-card-new';
            card.style.width = '100%';
            card.style.borderRadius = '12px';
            card.style.overflow = 'hidden';
            card.style.backgroundColor = 'white';
            card.style.boxShadow = '0 5px 15px rgba(0,0,0,0.07)';
            card.style.display = 'flex';
            card.style.flexDirection = 'column';
            card.style.minWidth = '0';
            card.style.transition = 'transform 0.2s ease, box-shadow 0.2s ease';
            
            card.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.99)';
                this.style.boxShadow = '0 2px 8px rgba(0,0,0,0.05)';
            });
            
            card.addEventListener('touchend', function() {
                this.style.transform = 'scale(1)';
                this.style.boxShadow = '0 5px 15px rgba(0,0,0,0.07)';
            });
            
            let headerBgColor = '#f8f9fa'; // 默认标题背景
            let brandColor = '#4A5568'; // 默认品牌颜色
            let lightBrandColor = 'rgba(226, 232, 240, 0.5)';
            let borderBrandColor = 'rgba(203, 213, 225, 0.5)';

            // 获取品牌颜色配置的函数
            function getBrandColors(brand) {
                if (!brand) return {
                    brandColor: '#4A5568',
                    lightBrandColor: 'rgba(226, 232, 240, 0.5)',
                    borderBrandColor: 'rgba(203, 213, 225, 0.5)',
                    headerBgColor: '#f8f9fa'
                };

                const brandLower = brand.toLowerCase();

                if (brandLower.includes('猫头鹰') || brandLower.includes('noctua')) {
                    return {
                        brandColor: '#854d0e',
                        lightBrandColor: 'rgba(161, 98, 7, 0.08)',
                        borderBrandColor: 'rgba(161, 98, 7, 0.15)',
                        headerBgColor: 'rgba(161, 98, 7, 0.05)'
                    };
                } else if (brandLower.includes('九州风神') || brandLower.includes('deepcool')) {
                    return {
                        brandColor: '#0284c7',
                        lightBrandColor: 'rgba(2, 132, 199, 0.08)',
                        borderBrandColor: 'rgba(2, 132, 199, 0.15)',
                        headerBgColor: 'rgba(2, 132, 199, 0.05)'
                    };
                } else if (brandLower.includes('酷冷至尊') || brandLower.includes('cooler master') || brandLower.includes('coolermaster')) {
                    return {
                        brandColor: '#7c3aed',
                        lightBrandColor: 'rgba(124, 58, 237, 0.08)',
                        borderBrandColor: 'rgba(124, 58, 237, 0.15)',
                        headerBgColor: 'rgba(124, 58, 237, 0.05)'
                    };
                } else if (brandLower.includes('海盗船') || brandLower.includes('美商海盗船') || brandLower.includes('corsair')) {
                    return {
                        brandColor: '#a16207',
                        lightBrandColor: 'rgba(234, 179, 8, 0.08)',
                        borderBrandColor: 'rgba(234, 179, 8, 0.15)',
                        headerBgColor: 'rgba(234, 179, 8, 0.05)'
                    };
                } else if (brandLower.includes('利民') || brandLower.includes('thermalright')) {
                    return {
                        brandColor: '#dc2626',
                        lightBrandColor: 'rgba(239, 68, 68, 0.08)',
                        borderBrandColor: 'rgba(239, 68, 68, 0.15)',
                        headerBgColor: 'rgba(239, 68, 68, 0.05)'
                    };
                } else if (brandLower.includes('id-cooling') || brandLower.includes('idcooling')) {
                    return {
                        brandColor: '#059669',
                        lightBrandColor: 'rgba(16, 185, 129, 0.08)',
                        borderBrandColor: 'rgba(16, 185, 129, 0.15)',
                        headerBgColor: 'rgba(16, 185, 129, 0.05)'
                    };
                } else if (brandLower.includes('快睿') || brandLower.includes('scythe')) {
                    return {
                        brandColor: '#8b5cf6',
                        lightBrandColor: 'rgba(139, 92, 246, 0.08)',
                        borderBrandColor: 'rgba(139, 92, 246, 0.15)',
                        headerBgColor: 'rgba(139, 92, 246, 0.05)'
                    };
                } else if (brandLower.includes('arctic') || brandLower.includes('北极')) {
                    return {
                        brandColor: '#0891b2',
                        lightBrandColor: 'rgba(6, 182, 212, 0.08)',
                        borderBrandColor: 'rgba(6, 182, 212, 0.15)',
                        headerBgColor: 'rgba(6, 182, 212, 0.05)'
                    };
                } else if (brandLower.includes('酷马') || brandLower.includes('thermaltake') || brandLower.includes('amd')) {
                    // 酷马和AMD使用红色系
                    return {
                        brandColor: '#dc2626',
                        lightBrandColor: 'rgba(239, 68, 68, 0.08)',
                        borderBrandColor: 'rgba(239, 68, 68, 0.15)',
                        headerBgColor: 'rgba(239, 68, 68, 0.05)'
                    };
                } else if (brandLower.includes('超频三') || brandLower.includes('pccooler') || brandLower.includes('英特尔') || brandLower.includes('intel')) {
                    // 超频三和Intel使用蓝色系
                    return {
                        brandColor: '#0284c7',
                        lightBrandColor: 'rgba(2, 132, 199, 0.08)',
                        borderBrandColor: 'rgba(2, 132, 199, 0.15)',
                        headerBgColor: 'rgba(2, 132, 199, 0.05)'
                    };
                } else {
                    return {
                        brandColor: '#4A5568',
                        lightBrandColor: 'rgba(107, 114, 128, 0.08)',
                        borderBrandColor: 'rgba(107, 114, 128, 0.15)',
                        headerBgColor: 'rgba(107, 114, 128, 0.05)'
                    };
                }
            }

            // 根据品牌设置颜色
            const colors = getBrandColors(cooler.brand);
            brandColor = colors.brandColor;
            lightBrandColor = colors.lightBrandColor;
            borderBrandColor = colors.borderBrandColor;
            headerBgColor = colors.headerBgColor;
            
            // 卡片头部
            const cardHeader = document.createElement('div');
            cardHeader.style.padding = '10px 14px';
            cardHeader.style.backgroundColor = headerBgColor;
            cardHeader.style.borderBottom = `1px solid ${borderBrandColor}`;
            cardHeader.style.display = 'flex';
            cardHeader.style.justifyContent = 'space-between';
            cardHeader.style.alignItems = 'center';
            
            const modelName = document.createElement('div');
            modelName.textContent = cooler.model || '未知型号';
            modelName.style.fontSize = '1rem';
            modelName.style.fontWeight = 'bold';
            modelName.style.color = '#1a202c';
            modelName.style.wordBreak = 'break-word';
            modelName.style.flexGrow = '1';
            modelName.style.marginRight = '8px';
            
            // 创建品牌标签
            const brandBadge = document.createElement('span');
            brandBadge.textContent = cooler.brand || '未知品牌';
            brandBadge.style.padding = '3px 8px';
            brandBadge.style.fontSize = '0.7rem';
            brandBadge.style.fontWeight = 'bold';
            brandBadge.style.borderRadius = '4px';
            brandBadge.style.color = 'white';
            brandBadge.style.backgroundColor = brandColor;
            brandBadge.style.flexShrink = '0';
            brandBadge.style.lineHeight = '1';
            
            cardHeader.appendChild(modelName);
            cardHeader.appendChild(brandBadge);
            
            // 卡片内容
            const cardBody = document.createElement('div');
            cardBody.style.padding = '12px 14px';
            cardBody.style.backgroundColor = 'white';
            cardBody.style.display = 'flex';
            cardBody.style.gap = '12px';
            
            // 图片容器
            const imgContainer = document.createElement('div');
            imgContainer.className = 'flex-shrink-0 relative group';
            imgContainer.style.width = '60px';
            imgContainer.style.height = '60px';
            imgContainer.style.borderRadius = '6px';
            imgContainer.style.border = `1px solid ${borderBrandColor}`;
            imgContainer.style.display = 'flex';
            imgContainer.style.alignItems = 'center';
            imgContainer.style.justifyContent = 'center';
            imgContainer.style.overflow = 'hidden';
            
            const imgElement = document.createElement('img');
            imgElement.alt = cooler.model || '散热器图片';
            imgElement.style.width = '100%';
            imgElement.style.height = '100%';
            imgElement.style.objectFit = 'contain';
            imgElement.style.transition = 'transform 0.2s ease-out';
            
            if (cooler.image_url) {
                imgElement.src = cooler.image_url;
                
                // 添加图片预览功能
                imgElement.style.cursor = 'pointer';
                imgElement.setAttribute('data-image-src', cooler.image_url);
                
                imgContainer.appendChild(imgElement);
                
                // 如果是默认图片，添加标记
                if (cooler.is_default_image) {
                    const defaultBadge = document.createElement('div');
                    defaultBadge.className = 'absolute bottom-0 right-0 bg-gray-700 text-white text-xs px-0.5 rounded-tl-md';
                    defaultBadge.style.fontSize = '0.6rem';
                    defaultBadge.textContent = '默认';
                    imgContainer.appendChild(defaultBadge);
                }
            } else {
                // 这种情况不应该再出现，因为我们已经为所有没有图片的散热器设置了默认图片
                imgElement.src = DEFAULT_COOLER_IMAGE;
                imgElement.style.cursor = 'pointer';
                imgElement.setAttribute('data-image-src', DEFAULT_COOLER_IMAGE);
                
                // 添加默认图片标记
                const defaultBadge = document.createElement('div');
                defaultBadge.className = 'absolute bottom-0 right-0 bg-gray-700 text-white text-xs px-0.5 rounded-tl-md';
                defaultBadge.style.fontSize = '0.6rem';
                defaultBadge.textContent = '默认';
                
                imgContainer.appendChild(imgElement);
                imgContainer.appendChild(defaultBadge);
            }
            
            imgElement.onerror = function() {
                this.onerror = null;
                imgContainer.innerHTML = '';
                const placeholderText = document.createElement('span');
                placeholderText.textContent = '散热器图片';
                placeholderText.style.fontSize = '0.75rem';
                placeholderText.style.color = '#6b7280';
                placeholderText.style.textAlign = 'center';
                imgContainer.appendChild(placeholderText);
                this.style.display = 'none';
            };
            
            // 添加图片预览功能
            if (cooler.image_url) {
                imgElement.style.cursor = 'pointer';
                imgElement.setAttribute('data-image-src', cooler.image_url);
                
                imgContainer.style.cursor = 'pointer';
                imgContainer.setAttribute('data-image-src', cooler.image_url);
            }
            
            // 添加触摸时的效果
            imgContainer.addEventListener('touchstart', function() {
                if (imgElement.src) {
                    imgElement.style.transform = 'scale(1.1)';
                }
            }, { passive: true });
            
            imgContainer.addEventListener('touchend', function() {
                if (imgElement.src) {
                    imgElement.style.transform = 'scale(1)';
                }
            }, { passive: true });
            
            if (cooler.image_url) {
                imgContainer.appendChild(imgElement);
            }
            
            // 信息容器
            const infoContainer = document.createElement('div');
            infoContainer.style.flexGrow = '1';
            infoContainer.style.display = 'flex';
            infoContainer.style.flexDirection = 'column';
            infoContainer.style.justifyContent = 'center';
            infoContainer.style.gap = '4px';
            
            // 类型标签
            const typeElement = document.createElement('div');
            typeElement.innerHTML = `<span style="font-weight: bold; color: ${brandColor}; font-size: 0.95rem;">${cooler.type || '未知类型'}</span>`;
            infoContainer.appendChild(typeElement);
            
            // 添加TDP信息
            if (cooler.tdp) {
                const tdpText = document.createElement('div');
                tdpText.style.color = '#374151';
                tdpText.style.fontSize = '0.8rem';
                tdpText.style.lineHeight = '1.4';
                tdpText.textContent = `散热功率: ${cooler.tdp}W`;
                infoContainer.appendChild(tdpText);
            }
            
            // 添加颜色信息
            if (cooler.color) {
                const colorText = document.createElement('div');
                colorText.style.color = '#374151';
                colorText.style.fontSize = '0.8rem';
                colorText.style.lineHeight = '1.4';
                colorText.innerHTML = `颜色: <span style="color: #7c3aed;">${cooler.color}</span>`;
                infoContainer.appendChild(colorText);
            }
            
            // 创建标签组
            const tagGroup = document.createElement('div');
            tagGroup.className = 'flex flex-wrap gap-1.5 mt-1';
            
            // 添加规格标签
            if (cooler.height) {
                const heightTag = document.createElement('span');
                heightTag.className = 'inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium';
                heightTag.style.backgroundColor = 'rgba(16, 185, 129, 0.1)';
                heightTag.style.color = '#10b981';
                heightTag.style.fontSize = '0.7rem';
                heightTag.textContent = `${cooler.height}mm`;
                tagGroup.appendChild(heightTag);
            }
            
            if (cooler.fan_count) {
                const fanTag = document.createElement('span');
                fanTag.className = 'inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium';
                fanTag.style.backgroundColor = 'rgba(219, 39, 119, 0.1)';
                fanTag.style.color = '#db2777';
                fanTag.style.fontSize = '0.7rem';
                fanTag.textContent = `${cooler.fan_count}风扇`;
                tagGroup.appendChild(fanTag);
            }
            
            if (cooler.heatpipe_count) {
                const heatpipeTag = document.createElement('span');
                heatpipeTag.className = 'inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium';
                heatpipeTag.style.backgroundColor = 'rgba(217, 119, 6, 0.1)';
                heatpipeTag.style.color = '#d97706';
                heatpipeTag.style.fontSize = '0.7rem';
                heatpipeTag.textContent = `${cooler.heatpipe_count}铜管`;
                tagGroup.appendChild(heatpipeTag);
            }
            
            // 噪音标签已移除
            
            if (tagGroup.children.length > 0) {
                infoContainer.appendChild(tagGroup);
            }
            
            cardBody.appendChild(imgContainer);
            cardBody.appendChild(infoContainer);
            
            // 卡片底部
            const cardFooter = document.createElement('div');
            cardFooter.style.padding = '8px 14px';
            cardFooter.style.backgroundColor = 'white';
            cardFooter.style.borderTop = `1px solid #e5e7eb`;
            cardFooter.style.display = 'flex';
            cardFooter.style.justifyContent = 'space-around';
            cardFooter.style.alignItems = 'center';
            cardFooter.style.gap = '8px';
            
            // 查看按钮
            const viewButton = document.createElement('button');
            viewButton.style.padding = '4px 8px';
            viewButton.style.borderRadius = '6px';
            viewButton.style.display = 'inline-flex';
            viewButton.style.alignItems = 'center';
            viewButton.style.justifyContent = 'center';
            viewButton.style.backgroundColor = 'transparent';
            viewButton.style.color = '#3B82F6';
            viewButton.style.fontSize = '0.8rem';
            viewButton.style.fontWeight = '500';
            viewButton.style.transition = 'background-color 0.15s ease-out, color 0.15s ease-out';
            viewButton.style.visibility = 'visible';
            viewButton.style.opacity = '1';
            viewButton.innerHTML = '<i class="fas fa-eye mr-1"></i>查看';
            viewButton.title = '查看详情';
            viewButton.addEventListener('click', () => viewCoolerDetails(cooler.id));
            viewButton.addEventListener('mouseenter', function() { this.style.backgroundColor = `rgba(59, 130, 246, 0.1)`; });
            viewButton.addEventListener('mouseleave', function() { this.style.backgroundColor = 'transparent'; });
            viewButton.addEventListener('touchstart', function() { this.style.backgroundColor = `rgba(59, 130, 246, 0.2)`; });
            viewButton.addEventListener('touchend', function() { this.style.backgroundColor = 'transparent'; });
            
            // 编辑按钮
            const editButton = document.createElement('button');
            editButton.style.padding = '4px 8px';
            editButton.style.borderRadius = '6px';
            editButton.style.display = 'inline-flex';
            editButton.style.alignItems = 'center';
            editButton.style.justifyContent = 'center';
            editButton.style.backgroundColor = 'transparent';
            editButton.style.color = '#10B981';
            editButton.style.fontSize = '0.8rem';
            editButton.style.fontWeight = '500';
            editButton.style.transition = 'background-color 0.15s ease-out, color 0.15s ease-out';
            editButton.style.visibility = 'visible';
            editButton.style.opacity = '1';
            editButton.innerHTML = '<i class="fas fa-edit mr-1"></i>编辑';
            editButton.title = '编辑';
            editButton.addEventListener('click', () => editCooler(cooler.id));
            editButton.addEventListener('mouseenter', function() { this.style.backgroundColor = `rgba(16, 185, 129, 0.1)`; });
            editButton.addEventListener('mouseleave', function() { this.style.backgroundColor = 'transparent'; });
            editButton.addEventListener('touchstart', function() { this.style.backgroundColor = `rgba(16, 185, 129, 0.2)`; });
            editButton.addEventListener('touchend', function() { this.style.backgroundColor = 'transparent'; });
            
            // 删除按钮
            const deleteButton = document.createElement('button');
            deleteButton.style.padding = '4px 8px';
            deleteButton.style.borderRadius = '6px';
            deleteButton.style.display = 'inline-flex';
            deleteButton.style.alignItems = 'center';
            deleteButton.style.justifyContent = 'center';
            deleteButton.style.backgroundColor = 'transparent';
            deleteButton.style.color = '#EF4444';
            deleteButton.style.fontSize = '0.8rem';
            deleteButton.style.fontWeight = '500';
            deleteButton.style.transition = 'background-color 0.15s ease-out, color 0.15s ease-out';
            deleteButton.style.visibility = 'visible';
            deleteButton.style.opacity = '1';
            deleteButton.innerHTML = '<i class="fas fa-trash mr-1"></i>删除';
            deleteButton.title = '删除';
            deleteButton.addEventListener('click', () => {
                confirmDeleteCooler(cooler.id);
            });
            deleteButton.addEventListener('mouseenter', function() {
                this.style.backgroundColor = `rgba(239, 68, 68, 0.1)`;
            });
            deleteButton.addEventListener('mouseleave', function() {
                this.style.backgroundColor = 'transparent';
            });
            deleteButton.addEventListener('touchstart', function() {
                this.style.backgroundColor = `rgba(239, 68, 68, 0.2)`;
            });
            deleteButton.addEventListener('touchend', function() {
                this.style.backgroundColor = 'transparent';
            });
            
            cardFooter.appendChild(viewButton);
            if (isAdminUser) {
                cardFooter.appendChild(editButton);
                cardFooter.appendChild(deleteButton);
            }
            
            card.appendChild(cardHeader);
            card.appendChild(cardBody);
            card.appendChild(cardFooter);
            cardOuterContainer.appendChild(card);
            coolerTableBody.appendChild(cardOuterContainer);
        });
    }

    // PC端表格布局渲染
    function renderDesktopTable(data, isAdminUser) {
        const coolerTableBody = document.getElementById('coolerTableBody');
        if (!coolerTableBody) return;
        
        // 添加表格固定布局样式，但只为型号列设置固定宽度
        const tableElement = coolerTableBody.closest('table');
        if (tableElement) {
            tableElement.style.tableLayout = 'fixed';
            tableElement.style.width = '100%';
        }
        
        coolerTableBody.innerHTML = '';
        
        data.forEach((cooler, index) => {
            const row = document.createElement('tr');
            // 添加淡入动画和悬停效果
            row.className = 'hover:bg-gray-50 transition-colors border-b border-gray-200';
            row.style.animation = 'fadeIn 0.3s ease-in-out';
            row.style.animationFillMode = 'both';
            row.style.animationDelay = `${index * 0.05}s`;
            
            // 型号列 - 应用宽度限制和文本截断
            const modelCell = document.createElement('td');
            modelCell.className = 'px-3 py-2 sm:px-4 sm:py-3';
            modelCell.style.maxWidth = '200px'; // 限制型号列宽度
            modelCell.style.width = '200px';
            
            const modelContainer = document.createElement('div');
            modelContainer.className = 'flex items-center';
            
            // 如果有图片则显示
            if (cooler.image_url) {
                const imgContainer = document.createElement('div');
                imgContainer.className = 'w-10 h-10 relative group mr-2 flex-shrink-0'; // 添加flex-shrink-0防止图片被压缩
                
                const imgElement = document.createElement('img');
                imgElement.src = cooler.image_url;
                imgElement.alt = cooler.model || '散热器图片';
                imgElement.className = 'w-10 h-10 object-contain rounded transition-transform group-hover:scale-110';
                // 不直接绑定事件，仅添加data属性和样式
                imgElement.style.cursor = 'pointer';
                imgElement.setAttribute('data-image-src', cooler.image_url);
                
                // 添加查看图标提示
                const viewOverlay = document.createElement('div');
                viewOverlay.className = 'absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 rounded-md flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity';
                viewOverlay.innerHTML = '<i class="fas fa-search-plus text-white text-opacity-80"></i>';
                viewOverlay.style.pointerEvents = 'none'; // 确保覆盖层不会阻止点击事件传递到下面的图片
                
                imgContainer.appendChild(imgElement);
                imgContainer.appendChild(viewOverlay);
                
                // 如果是默认图片，添加标记
                if (cooler.is_default_image) {
                    const defaultBadge = document.createElement('div');
                    defaultBadge.className = 'absolute bottom-0 right-0 bg-gray-700 text-white text-xs px-0.5 rounded-tl-md';
                    defaultBadge.style.fontSize = '0.6rem';
                    defaultBadge.textContent = '默认';
                    imgContainer.appendChild(defaultBadge);
                }
                
                // 不直接绑定事件，添加data属性让全局处理器处理
                imgContainer.style.cursor = 'pointer';
                imgContainer.setAttribute('data-image-src', cooler.image_url);
                
                modelContainer.appendChild(imgContainer);
            }
            
            const infoContainer = document.createElement('div');
            infoContainer.className = 'overflow-hidden flex-grow min-w-0'; // 添加min-w-0让子元素可以正确截断
            
            const modelText = document.createElement('div');
            modelText.className = 'text-sm font-medium text-gray-900 model-text';
            modelText.textContent = cooler.model || '-';
            modelText.title = cooler.model || '-'; // 保留title属性用于悬停提示
            infoContainer.appendChild(modelText);
            
            // 移动端显示的额外信息
            const brandMobile = document.createElement('div');
            brandMobile.className = 'text-xs text-gray-500 mt-1 sm:hidden truncate';
            brandMobile.textContent = `品牌: ${cooler.brand || '-'}`;
            infoContainer.appendChild(brandMobile);
            
            const tdpMobile = document.createElement('div');
            tdpMobile.className = 'text-xs text-gray-500 mt-1 sm:hidden truncate';
            tdpMobile.textContent = `TDP: ${cooler.tdp ? `${cooler.tdp}W` : '-'}`;
            infoContainer.appendChild(tdpMobile);
            
            modelContainer.appendChild(infoContainer);
            modelCell.appendChild(modelContainer);
            row.appendChild(modelCell);
            
            // 品牌列 - 移除固定宽度
            const brandCell = document.createElement('td');
            brandCell.className = 'px-3 py-2 sm:px-4 sm:py-3 hidden sm:table-cell';
            
            // 使用带颜色的标签显示品牌
            const brandBadge = document.createElement('span');
            let brandClass = 'cooler-brand-other';

            // 获取品牌类别的函数
            function getBrandClass(brand) {
                if (!brand) return 'cooler-brand-other';

                const brandLower = brand.toLowerCase();

                if (brandLower.includes('猫头鹰') || brandLower.includes('noctua')) {
                    return 'cooler-brand-noctua';
                } else if (brandLower.includes('九州风神') || brandLower.includes('deepcool')) {
                    return 'cooler-brand-deepcool';
                } else if (brandLower.includes('酷冷至尊') || brandLower.includes('cooler master') || brandLower.includes('coolermaster')) {
                    return 'cooler-brand-coolermaster';
                } else if (brandLower.includes('海盗船') || brandLower.includes('美商海盗船') || brandLower.includes('corsair')) {
                    return 'cooler-brand-corsair';
                } else if (brandLower.includes('利民') || brandLower.includes('thermalright')) {
                    return 'cooler-brand-thermalright';
                } else if (brandLower.includes('id-cooling') || brandLower.includes('idcooling')) {
                    return 'cooler-brand-idcooling';
                } else if (brandLower.includes('快睿') || brandLower.includes('scythe')) {
                    return 'cooler-brand-scythe';
                } else if (brandLower.includes('arctic') || brandLower.includes('北极')) {
                    return 'cooler-brand-arctic';
                } else if (brandLower.includes('航嘉') || brandLower.includes('huntkey')) {
                    return 'cooler-brand-huntkey';
                } else if (brandLower.includes('酷马') || brandLower.includes('thermaltake')) {
                    return 'cooler-brand-thermaltake';
                } else if (brandLower.includes('爱国者') || brandLower.includes('aigo')) {
                    return 'cooler-brand-aigo';
                } else if (brandLower.includes('超频三') || brandLower.includes('pccooler')) {
                    return 'cooler-brand-deepcool'; // 超频三使用蓝色系
                } else if (brandLower.includes('安钛克') || brandLower.includes('antec')) {
                    return 'cooler-brand-other';
                } else if (brandLower.includes('银欣') || brandLower.includes('silverstone')) {
                    return 'cooler-brand-other';
                } else if (brandLower.includes('amd')) {
                    return 'cooler-brand-amd';
                } else if (brandLower.includes('英特尔') || brandLower.includes('intel')) {
                    return 'cooler-brand-intel';
                } else if (brandLower.includes('半岛铁盒')) {
                    return 'cooler-brand-peninsula';
                } else if (brandLower.includes('微星') || brandLower.includes('msi')) {
                    return 'cooler-brand-msi';
                } else if (brandLower.includes('aoc')) {
                    return 'cooler-brand-aoc';
                } else if (brandLower.includes('瓦尔基里') || brandLower.includes('valkyrie')) {
                    return 'cooler-brand-valkyrie';
                } else if (brandLower.includes('先马') || brandLower.includes('segotep')) {
                    return 'cooler-brand-segotep';
                } else if (brandLower.includes('钛钽') || brandLower.includes('tantalum')) {
                    return 'cooler-brand-tantalum';
                } else if (brandLower.includes('创氪星系') || brandLower.includes('krypton')) {
                    return 'cooler-brand-krypton';
                } else if (brandLower.includes('酷里奥') || brandLower.includes('coolio')) {
                    return 'cooler-brand-coolio';
                } else if (brandLower.includes('乔思伯') || brandLower.includes('jonsbo')) {
                    return 'cooler-brand-jonsbo';
                } else if (brandLower.includes('联力') || brandLower.includes('lian li')) {
                    return 'cooler-brand-lianli';
                } else if (brandLower.includes('几何未来') || brandLower.includes('geometric')) {
                    return 'cooler-brand-geometric';
                } else {
                    return 'cooler-brand-other';
                }
            }

            brandClass = getBrandClass(cooler.brand);
            brandBadge.className = `cooler-badge ${brandClass} transition-transform hover:scale-105`;
            brandBadge.textContent = cooler.brand || '-';
            brandCell.appendChild(brandBadge);
            row.appendChild(brandCell);
            
            // 类型列 - 移除固定宽度
            const typeCell = document.createElement('td');
            typeCell.className = 'px-3 py-2 sm:px-4 sm:py-3 hidden sm:table-cell';
            
            const typeTag = document.createElement('span');
            let typeClass = '';
            
            if (cooler.type === '风冷' || cooler.type === 'Air') {
                typeClass = 'cooler-type-air';
            } else if (cooler.type === '水冷') {
                typeClass = 'cooler-type-water';
            } else if (cooler.type === 'AIO' || cooler.type === 'AIO一体式水冷') {
                typeClass = 'cooler-type-aio';
            }
            
            typeTag.className = `cooler-type ${typeClass}`;
            typeTag.textContent = cooler.type || '-';
            typeCell.appendChild(typeTag);
            row.appendChild(typeCell);

            // 塔数
            const towerCell = document.createElement('td');
            towerCell.className = 'px-3 py-2 sm:px-4 sm:py-3 hidden sm:table-cell';
            const towerTag = document.createElement('span');
            towerTag.className = 'spec-tag tower';
            towerTag.textContent = cooler.tower_count || '-';
            towerCell.appendChild(towerTag);
            row.appendChild(towerCell);

            // TDP列 - 移除固定宽度
            const tdpCell = document.createElement('td');
            tdpCell.className = 'px-3 py-2 sm:px-4 sm:py-3 hidden sm:table-cell';
            
            const tdpIcon = document.createElement('span');
            tdpIcon.className = 'mr-1 text-amber-600';
            tdpIcon.innerHTML = '<i class="fas fa-bolt"></i>';
            
            const tdpTag = document.createElement('span');
            tdpTag.className = 'spec-tag tdp inline-flex items-center transition-all hover:shadow-sm';
            tdpTag.appendChild(tdpIcon);
            
            const tdpText = document.createElement('span');
            tdpText.textContent = cooler.tdp ? `${cooler.tdp}W` : '-';
            tdpTag.appendChild(tdpText);
            
            tdpCell.appendChild(tdpTag);
            row.appendChild(tdpCell);
            
            // 颜色列 - 移除固定宽度
            const colorCell = document.createElement('td');
            colorCell.className = 'px-3 py-2 sm:px-4 sm:py-3 hidden sm:table-cell';
            
            const colorIcon = document.createElement('span');
            colorIcon.className = 'mr-1 text-purple-600';
            colorIcon.innerHTML = '<i class="fas fa-palette"></i>';
            
            const colorTag = document.createElement('span');
            colorTag.className = 'spec-tag color inline-flex items-center transition-all hover:shadow-sm';
            colorTag.appendChild(colorIcon);
            
            const colorText = document.createElement('span');
            colorText.textContent = cooler.color || '-';
            colorTag.appendChild(colorText);
            
            colorCell.appendChild(colorTag);
            row.appendChild(colorCell);
            
            // 高度列 - 移除固定宽度
            const heightCell = document.createElement('td');
            heightCell.className = 'px-3 py-2 sm:px-4 sm:py-3 hidden sm:table-cell';
            
            const heightIcon = document.createElement('span');
            heightIcon.className = 'mr-1 text-emerald-600';
            heightIcon.innerHTML = '<i class="fas fa-arrows-alt-v"></i>';
            
            const heightTag = document.createElement('span');
            heightTag.className = 'spec-tag height inline-flex items-center transition-all hover:shadow-sm';
            heightTag.appendChild(heightIcon);
            
            const heightText = document.createElement('span');
            heightText.textContent = cooler.height ? `${cooler.height}mm` : '-';
            heightTag.appendChild(heightText);
            
            heightCell.appendChild(heightTag);
            row.appendChild(heightCell);
            
            // 风扇数量列 - 移除固定宽度
            const fanCountCell = document.createElement('td');
            fanCountCell.className = 'px-3 py-2 sm:px-4 sm:py-3 hidden sm:table-cell';
            
            const fanIcon = document.createElement('span');
            fanIcon.className = 'mr-1 text-pink-600';
            fanIcon.innerHTML = '<i class="fas fa-fan"></i>';
            
            const fanTag = document.createElement('span');
            fanTag.className = 'spec-tag fan inline-flex items-center transition-all hover:shadow-sm';
            fanTag.appendChild(fanIcon);
            
            const fanText = document.createElement('span');
            fanText.textContent = cooler.fan_count || '-';
            fanTag.appendChild(fanText);
            
            fanCountCell.appendChild(fanTag);
            row.appendChild(fanCountCell);

            // 铜管数量列 - 移除固定宽度
            const heatpipeCountCell = document.createElement('td');
            heatpipeCountCell.className = 'px-3 py-2 sm:px-4 sm:py-3 hidden sm:table-cell';
            
            const heatpipeIcon = document.createElement('span');
            heatpipeIcon.className = 'mr-1 text-amber-600';
            heatpipeIcon.innerHTML = '<i class="fas fa-grip-lines"></i>';
            
            const heatpipeTag = document.createElement('span');
            heatpipeTag.className = 'spec-tag heatpipe inline-flex items-center transition-all hover:shadow-sm';
            heatpipeTag.appendChild(heatpipeIcon);
            
            const heatpipeText = document.createElement('span');
            heatpipeText.textContent = cooler.heatpipe_count || '-';
            heatpipeTag.appendChild(heatpipeText);
            
            heatpipeCountCell.appendChild(heatpipeTag);
            row.appendChild(heatpipeCountCell);
            
            // 操作列
            const actionCell = document.createElement('td');
            actionCell.className = 'px-3 py-2 sm:px-4 sm:py-3 text-center action-column';
            actionCell.style.width = '130px';
            actionCell.style.minWidth = '130px';
            
            // 创建操作按钮容器
            const actionBtns = document.createElement('div');
            actionBtns.className = 'flex justify-center gap-2';
            actionBtns.style.display = 'flex';
            actionBtns.style.visibility = 'visible';
            
            // 创建查看按钮
            const viewButton = document.createElement('button');
            viewButton.type = 'button';
            viewButton.className = 'action-btn view-btn p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-full flex items-center justify-center';
            viewButton.innerHTML = '<i class="fas fa-eye"></i>';
            viewButton.title = '查看详情';
            viewButton.style.width = '30px';
            viewButton.style.height = '30px';
            viewButton.style.visibility = 'visible';
            viewButton.style.display = 'flex';
            viewButton.onclick = function() { viewCoolerDetails(cooler.id); };
            
            // 创建编辑按钮
            const editButton = document.createElement('button');
            editButton.type = 'button';
            editButton.className = 'action-btn edit-btn p-1 text-green-600 hover:text-green-800 hover:bg-green-50 rounded-full flex items-center justify-center';
            editButton.innerHTML = '<i class="fas fa-edit"></i>';
            editButton.title = '编辑';
            editButton.style.width = '30px';
            editButton.style.height = '30px';
            editButton.style.visibility = 'visible';
            editButton.style.display = 'flex';
            editButton.onclick = function() { editCooler(cooler.id); };
            
            // 创建删除按钮
            const deleteButton = document.createElement('button');
            deleteButton.type = 'button';
            deleteButton.className = 'action-btn delete-btn p-1 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-full flex items-center justify-center';
            deleteButton.innerHTML = '<i class="fas fa-trash"></i>';
            deleteButton.title = '删除';
            deleteButton.style.width = '30px';
            deleteButton.style.height = '30px';
            deleteButton.style.visibility = 'visible';
            deleteButton.style.display = 'flex'; // 确保按钮显示
            deleteButton.onclick = function() { confirmDeleteCooler(cooler.id); };
            
            // 添加按钮到容器
            actionBtns.appendChild(viewButton);
            if (isAdminUser) {
                actionBtns.appendChild(editButton);
                actionBtns.appendChild(deleteButton);
            }
            
            // 添加按钮容器到单元格
            actionCell.appendChild(actionBtns);
            row.appendChild(actionCell);
            
            // 添加行到表格
            coolerTableBody.appendChild(row);
        });
    }

    // 更新分页信息
    function updatePagination() {
        const currentPageDisplay = document.getElementById('currentPageDisplay');
        const totalPagesDisplay = document.getElementById('totalPagesDisplay');
        const pageNumbers = document.getElementById('pageNumbers');
        const firstPageBtn = document.getElementById('firstPage');
        const prevPageBtn = document.getElementById('prevPage');
        const nextPageBtn = document.getElementById('nextPage');
        const lastPageBtn = document.getElementById('lastPage');
        const pageJumpInput = document.getElementById('pageJump');
        
        const totalPages = Math.ceil(totalRecords / pageSize) || 1;
        
        // 更新当前页和总页数显示
        if (currentPageDisplay) currentPageDisplay.textContent = currentPage;
        if (totalPagesDisplay) totalPagesDisplay.textContent = totalPages;
        
        const totalCount = document.getElementById('totalCount');
        if (totalCount) totalCount.textContent = `共 ${totalRecords} 条记录`;
        
        // 启用或禁用页码按钮
        if (prevPageBtn) {
            prevPageBtn.disabled = currentPage <= 1;
            prevPageBtn.classList.toggle('opacity-50', currentPage <= 1);
        }
        
        if (nextPageBtn) {
            nextPageBtn.disabled = currentPage >= totalPages;
            nextPageBtn.classList.toggle('opacity-50', currentPage >= totalPages);
        }
        
        if (firstPageBtn) {
            firstPageBtn.disabled = currentPage <= 1;
            firstPageBtn.classList.toggle('opacity-50', currentPage <= 1);
        }
        
        if (lastPageBtn) {
            lastPageBtn.disabled = currentPage >= totalPages;
            lastPageBtn.classList.toggle('opacity-50', currentPage >= totalPages);
        }
        
        if (pageJumpInput) {
            pageJumpInput.max = totalPages;
        }
        
        // 生成页码按钮
        if (pageNumbers) {
            pageNumbers.innerHTML = '';
            
            // 定义最大显示的页码按钮数
            const maxPageButtons = 5;
            
            // 确定要显示的页码范围
            let startPage = Math.max(1, currentPage - Math.floor(maxPageButtons / 2));
            let endPage = startPage + maxPageButtons - 1;
            
            if (endPage > totalPages) {
                endPage = totalPages;
                startPage = Math.max(1, endPage - maxPageButtons + 1);
            }
            
            // 生成页码按钮
            for (let i = startPage; i <= endPage; i++) {
                const pageButton = document.createElement('button');
                pageButton.type = 'button';
                pageButton.className = `px-3 py-1 border rounded-md text-sm ${
                    i === currentPage
                        ? 'bg-cyan-600 text-white border-cyan-600'
                        : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                }`;
                pageButton.textContent = i;
                pageButton.addEventListener('click', () => changePage(i));
                
                pageNumbers.appendChild(pageButton);
            }
        }
    }

    // 更改页面
    function changePage(newPage) {
        const totalPages = Math.ceil(totalRecords / pageSize) || 1;
        
        if (newPage < 1 || newPage > totalPages) {
            return;
        }
        
        currentPage = newPage;
        loadCoolers();
    }
    
// 处理编辑按钮点击
function handleEdit() {
    if (currentCoolerId) {
        editCooler(currentCoolerId);
        
        // 关闭模态框
        const coolerModal = document.getElementById('coolerModal');
        if (coolerModal) {
            coolerModal.classList.add('hidden');
            coolerModal.style.display = 'none';
            coolerModal.setAttribute('style', 'display: none !important');
            document.body.style.overflow = 'auto';
            console.log('编辑按钮关闭模态框');
        }
    }
}

// 显示消息提示
function showToast(message, color = '#4F46E5') {
    // 检查是否已存在toast，如果存在则移除
    const existingToast = document.getElementById('toast');
    if (existingToast) {
        existingToast.remove();
    }
    
    // 创建toast元素
    const toast = document.createElement('div');
    toast.id = 'toast';
    toast.className = 'fixed top-16 left-1/2 transform -translate-x-1/2 px-4 py-2 rounded-md text-white z-50 shadow-lg transition-all duration-300 ease-in-out';
    toast.style.backgroundColor = color;
    toast.style.opacity = '0';
    toast.textContent = message;
    
    // 添加到DOM
    document.body.appendChild(toast);
    
    // 显示动画
    setTimeout(() => {
        toast.style.opacity = '1';
    }, 10);
    
    // 3秒后隐藏
    setTimeout(() => {
        toast.style.opacity = '0';
        setTimeout(() => {
            if (toast.parentNode) toast.parentNode.removeChild(toast);
        }, 300);
    }, 3000);
    }

    // 渲染散热器详情
    function renderCoolerDetails(cooler) {
        const coolerDetails = document.getElementById('coolerDetails');
        if (!coolerDetails) return;
        
        // 添加调试信息
        console.log('详情数据:', cooler);
        console.log('RGB灯效值:', cooler.rgb_lighting);
        
        // 更新标题
        const detailTitle = document.getElementById('detailTitle');
        if (detailTitle) {
            detailTitle.textContent = `${cooler.brand} ${cooler.model}`;
        }
        
        // 设置默认图片处理
        if (!cooler.image_url) {
            cooler.is_default_image = true;
            cooler.image_url = DEFAULT_COOLER_IMAGE;
        }
        
        // 检测是否为深色模式
        const isDarkMode = document.body.classList.contains('dark-mode');
        
        // 创建详情页面 - 全新设计
        const detailHTML = `
            <!-- 头部区域：型号、品牌和价格 -->
            <div class="flex flex-col md:flex-row items-center justify-between ${isDarkMode ? 'bg-gradient-to-r from-gray-800 to-gray-700' : 'bg-gradient-to-r from-cyan-50 to-blue-50'} p-4 rounded-lg mb-5">
                <div class="flex flex-col items-start">
                    <h2 class="text-xl md:text-2xl font-bold ${isDarkMode ? 'text-gray-100' : 'text-gray-800'}">${cooler.model || '未知型号'}</h2>
                    <div class="flex items-center mt-1">
                        <span class="cooler-badge ${getBrandClass(cooler.brand)}">${cooler.brand || '-'}</span>
                        ${cooler.type ? `<span class="ml-2 cooler-type ${getTypeClass(cooler.type)}">${cooler.type}</span>` : ''}
                    </div>
                </div>
                <div class="mt-3 md:mt-0 text-right">
                    <p class="text-2xl font-bold text-red-500">${cooler.price ? `¥${cooler.price}` : '价格未知'}</p>
                    <p class="text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-500'}">保修期: ${cooler.warranty ? `${cooler.warranty}年` : '未知'}</p>
                </div>
            </div>

            <!-- 主内容区：图片和关键规格 -->
            <div class="flex flex-col md:flex-row gap-6 mb-6">
                <!-- 左侧散热器图片 -->
                <div class="w-full md:w-2/5 flex flex-col">
                    <div class="${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-gray-50 border-gray-200'} border rounded-lg p-3 h-[250px] flex items-center justify-center relative">
                        ${cooler.image_url ? 
                            `<img src="${cooler.image_url}" alt="${cooler.model}" class="max-h-full max-w-full rounded object-contain cursor-pointer hover:opacity-90 preview-image" data-image-src="${cooler.image_url}">
                            ${cooler.is_default_image ? `<div class="absolute bottom-2 right-2 bg-gray-700 text-white text-xs px-1.5 py-0.5 rounded-md opacity-80">默认图片</div>` : ''}` : 
                            `<div class="flex flex-col items-center justify-center ${isDarkMode ? 'text-gray-500' : 'text-gray-400'}">
                                <i class="fas fa-image text-5xl mb-2"></i>
                                <span class="text-sm">暂无图片</span>
                             </div>`
                        }
                    </div>
                    <p class="text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'} mt-1 text-center">
                        ${cooler.image_url ? '<i class="fas fa-search-plus mr-1"></i>点击图片可放大查看' : ''}
                    </p>
                </div>
                
                <!-- 右侧核心规格卡片 -->
                <div class="w-full md:w-3/5 grid grid-cols-2 gap-3">
                    <!-- 散热功率卡片 -->
                    <div class="${isDarkMode ? 'bg-blue-900/30 border-blue-800/50' : 'bg-gradient-to-br from-blue-50 to-cyan-50 border-blue-100'} p-4 rounded-lg border flex flex-col justify-between">
                        <div class="flex items-center ${isDarkMode ? 'text-blue-300' : 'text-blue-700'} mb-1">
                            <i class="fas fa-thermometer-three-quarters mr-2"></i>
                            <h3 class="font-medium">散热功率</h3>
                        </div>
                        <div class="mt-1">
                            <p class="text-2xl font-bold ${isDarkMode ? 'text-blue-300' : 'text-blue-700'}">${cooler.tdp || '-'} <span class="text-sm font-normal">W</span></p>
                        </div>
                    </div>
                    
                    <!-- 高度卡片 -->
                    <div class="${isDarkMode ? 'bg-green-900/30 border-green-800/50' : 'bg-gradient-to-br from-emerald-50 to-green-50 border-emerald-100'} p-4 rounded-lg border flex flex-col justify-between">
                        <div class="flex items-center ${isDarkMode ? 'text-green-300' : 'text-emerald-700'} mb-1">
                            <i class="fas fa-arrows-alt-v mr-2"></i>
                            <h3 class="font-medium">散热器高度</h3>
                        </div>
                        <div class="mt-1">
                            <p class="text-2xl font-bold ${isDarkMode ? 'text-green-300' : 'text-emerald-700'}">${cooler.height || '-'} <span class="text-sm font-normal">mm</span></p>
                        </div>
                    </div>
                    
                    <!-- 风扇配置卡片 -->
                    <div class="${isDarkMode ? 'bg-purple-900/30 border-purple-800/50' : 'bg-gradient-to-br from-purple-50 to-indigo-50 border-purple-100'} p-4 rounded-lg border flex flex-col justify-between">
                        <div class="flex items-center ${isDarkMode ? 'text-purple-300' : 'text-purple-700'} mb-1">
                            <i class="fas fa-fan mr-2"></i>
                            <h3 class="font-medium">风扇配置</h3>
                        </div>
                        <div class="mt-1">
                            <p class="text-xl font-bold ${isDarkMode ? 'text-purple-300' : 'text-purple-700'}">${cooler.fan_count || '-'} <span class="text-sm font-normal">个</span></p>
                            <p class="text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}">${cooler.fan_size || '尺寸未知'}</p>
                        </div>
                    </div>
                    
                    <!-- 噪音级别卡片 -->
                    <div class="${isDarkMode ? 'bg-amber-900/30 border-amber-800/50' : 'bg-gradient-to-br from-amber-50 to-yellow-50 border-amber-100'} p-4 rounded-lg border flex flex-col justify-between">
                        <div class="flex items-center ${isDarkMode ? 'text-amber-300' : 'text-amber-700'} mb-1">
                            <i class="fas fa-volume-down mr-2"></i>
                            <h3 class="font-medium">噪音级别</h3>
                        </div>
                        <div class="mt-1">
                            <p class="text-xl font-bold ${isDarkMode ? 'text-amber-300' : 'text-amber-700'}">${cooler.noise_level || '-'} <span class="text-sm font-normal">dBA</span></p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 详细规格区域 - 选项卡式布局 -->
            <div class="${isDarkMode ? 'border-gray-700' : 'border-gray-200'} border rounded-lg overflow-hidden mb-4">
                <!-- 选项卡标题 -->
                <div class="flex ${isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'} border-b">
                    <div class="px-4 py-3 ${isDarkMode ? 'text-cyan-400 border-cyan-400' : 'text-cyan-700 border-cyan-500'} font-medium border-b-2">详细规格</div>
                </div>
                
                <!-- 选项卡内容 -->
                <div class="p-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-y-2 gap-x-6">
                        <div class="grid grid-cols-3 py-2 ${isDarkMode ? 'border-gray-700' : 'border-gray-100'} border-b">
                            <span class="${isDarkMode ? 'text-gray-400' : 'text-gray-600'} col-span-1">CPU接口</span>
                            <span class="font-medium col-span-2">${cooler.socket_support || '-'}</span>
                        </div>
                        <div class="grid grid-cols-3 py-2 ${isDarkMode ? 'border-gray-700' : 'border-gray-100'} border-b">
                            <span class="${isDarkMode ? 'text-gray-400' : 'text-gray-600'} col-span-1">散热功率</span>
                            <span class="font-medium col-span-2">${cooler.tdp ? `${cooler.tdp}W` : '-'}</span>
                        </div>
                        <div class="grid grid-cols-3 py-2 ${isDarkMode ? 'border-gray-700' : 'border-gray-100'} border-b">
                            <span class="${isDarkMode ? 'text-gray-400' : 'text-gray-600'} col-span-1">散热器高度</span>
                            <span class="font-medium col-span-2">${cooler.height ? `${cooler.height}mm` : '-'}</span>
                        </div>
                        <div class="grid grid-cols-3 py-2 ${isDarkMode ? 'border-gray-700' : 'border-gray-100'} border-b">
                            <span class="${isDarkMode ? 'text-gray-400' : 'text-gray-600'} col-span-1">风扇数量</span>
                            <span class="font-medium col-span-2">${cooler.fan_count || '-'}</span>
                        </div>
                        <div class="grid grid-cols-3 py-2 ${isDarkMode ? 'border-gray-700' : 'border-gray-100'} border-b">
                            <span class="${isDarkMode ? 'text-gray-400' : 'text-gray-600'} col-span-1">铜管数量</span>
                            <span class="font-medium col-span-2">${cooler.heatpipe_count || '-'}</span>
                        </div>
                        <div class="grid grid-cols-3 py-2 ${isDarkMode ? 'border-gray-700' : 'border-gray-100'} border-b">
                            <span class="${isDarkMode ? 'text-gray-400' : 'text-gray-600'} col-span-1">风扇尺寸</span>
                            <span class="font-medium col-span-2">${cooler.fan_size || '-'}</span>
                        </div>
                        <div class="grid grid-cols-3 py-2 ${isDarkMode ? 'border-gray-700' : 'border-gray-100'} border-b">
                            <span class="${isDarkMode ? 'text-gray-400' : 'text-gray-600'} col-span-1">散热器尺寸</span>
                            <span class="font-medium col-span-2">${cooler.radiator_size || '-'}</span>
                        </div>
                        <div class="grid grid-cols-3 py-2 ${isDarkMode ? 'border-gray-700' : 'border-gray-100'} border-b">
                            <span class="${isDarkMode ? 'text-gray-400' : 'text-gray-600'} col-span-1">噪音级别</span>
                            <span class="font-medium col-span-2">${cooler.noise_level || '-'}</span>
                        </div>
                        <div class="grid grid-cols-3 py-2 ${isDarkMode ? 'border-gray-700' : 'border-gray-100'} border-b">
                            <span class="${isDarkMode ? 'text-gray-400' : 'text-gray-600'} col-span-1">RGB灯效</span>
                            <span class="font-medium col-span-2">${cooler.rgb_lighting === '有' || cooler.rgb_lighting === '1' || cooler.rgb_lighting === 1 ? '有' : '无'}</span>
                        </div>
                        <div class="grid grid-cols-3 py-2 ${isDarkMode ? 'border-gray-700' : 'border-gray-100'} border-b">
                            <span class="${isDarkMode ? 'text-gray-400' : 'text-gray-600'} col-span-1">材质</span>
                            <span class="font-medium col-span-2">${cooler.material || '-'}</span>
                        </div>
                        <div class="grid grid-cols-3 py-2 ${isDarkMode ? 'border-gray-700' : 'border-gray-100'} border-b">
                            <span class="${isDarkMode ? 'text-gray-400' : 'text-gray-600'} col-span-1">颜色</span>
                            <span class="font-medium col-span-2">${cooler.color || '-'}</span>
                        </div>
                        <div class="grid grid-cols-3 py-2 ${isDarkMode ? 'border-gray-700' : 'border-gray-100'} border-b">
                            <span class="${isDarkMode ? 'text-gray-400' : 'text-gray-600'} col-span-1">价格</span>
                            <span class="font-medium col-span-2">${cooler.price ? `¥${cooler.price}` : '-'}</span>
                        </div>
                    </div>
                    
                    ${cooler.notes ? 
                        `<div class="mt-4 ${isDarkMode ? 'bg-gray-800' : 'bg-gray-50'} p-3 rounded-md">
                            <h4 class="font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'} mb-1">备注信息</h4>
                            <p class="${isDarkMode ? 'text-gray-400' : 'text-gray-600'}">${cooler.notes}</p>
                         </div>` : ''
                    }
                    
                    <div class="mt-4 text-xs ${isDarkMode ? 'text-gray-500' : 'text-gray-500'} flex justify-between">
                        <span>添加时间: ${cooler.created_at ? new Date(cooler.created_at).toLocaleString() : '-'}</span>
                        <span>最后更新: ${cooler.updated_at ? new Date(cooler.updated_at).toLocaleString() : '-'}</span>
                    </div>
                </div>
            </div>
        `;
        
        // 更新详情内容
        coolerDetails.innerHTML = detailHTML;
        
        // 在渲染详情后调用图片点击事件处理
        attachImageClickHandlers();
    }
    
    // 获取品牌对应的CSS类名
    function getBrandClass(brand) {
        if (!brand) return 'cooler-brand-other';

        const brandLower = brand.toLowerCase();

        if (brandLower.includes('猫头鹰') || brandLower.includes('noctua')) {
            return 'cooler-brand-noctua';
        } else if (brandLower.includes('九州风神') || brandLower.includes('deepcool')) {
            return 'cooler-brand-deepcool';
        } else if (brandLower.includes('酷冷至尊') || brandLower.includes('cooler master') || brandLower.includes('coolermaster')) {
            return 'cooler-brand-coolermaster';
        } else if (brandLower.includes('海盗船') || brandLower.includes('美商海盗船') || brandLower.includes('corsair')) {
            return 'cooler-brand-corsair';
        } else if (brandLower.includes('利民') || brandLower.includes('thermalright')) {
            return 'cooler-brand-thermalright';
        } else if (brandLower.includes('id-cooling') || brandLower.includes('idcooling')) {
            return 'cooler-brand-idcooling';
        } else if (brandLower.includes('快睿') || brandLower.includes('scythe')) {
            return 'cooler-brand-scythe';
        } else if (brandLower.includes('arctic') || brandLower.includes('北极')) {
            return 'cooler-brand-arctic';
        } else if (brandLower.includes('航嘉') || brandLower.includes('huntkey')) {
            return 'cooler-brand-huntkey';
        } else if (brandLower.includes('酷马') || brandLower.includes('thermaltake')) {
            return 'cooler-brand-thermaltake';
        } else if (brandLower.includes('爱国者') || brandLower.includes('aigo')) {
            return 'cooler-brand-aigo';
        } else if (brandLower.includes('超频三') || brandLower.includes('pccooler')) {
            return 'cooler-brand-deepcool'; // 超频三使用蓝色系
        } else if (brandLower.includes('安钛克') || brandLower.includes('antec')) {
            return 'cooler-brand-other';
        } else if (brandLower.includes('银欣') || brandLower.includes('silverstone')) {
            return 'cooler-brand-other';
        } else if (brandLower.includes('amd')) {
            return 'cooler-brand-amd';
        } else if (brandLower.includes('英特尔') || brandLower.includes('intel')) {
            return 'cooler-brand-intel';
        } else if (brandLower.includes('半岛铁盒')) {
            return 'cooler-brand-peninsula';
        } else if (brandLower.includes('微星') || brandLower.includes('msi')) {
            return 'cooler-brand-msi';
        } else if (brandLower.includes('aoc')) {
            return 'cooler-brand-aoc';
        } else if (brandLower.includes('瓦尔基里') || brandLower.includes('valkyrie')) {
            return 'cooler-brand-valkyrie';
        } else if (brandLower.includes('先马') || brandLower.includes('segotep')) {
            return 'cooler-brand-segotep';
        } else if (brandLower.includes('钛钽') || brandLower.includes('tantalum')) {
            return 'cooler-brand-tantalum';
        } else if (brandLower.includes('创氪星系') || brandLower.includes('krypton')) {
            return 'cooler-brand-krypton';
        } else if (brandLower.includes('酷里奥') || brandLower.includes('coolio')) {
            return 'cooler-brand-coolio';
        } else if (brandLower.includes('乔思伯') || brandLower.includes('jonsbo')) {
            return 'cooler-brand-jonsbo';
        } else if (brandLower.includes('联力') || brandLower.includes('lian li')) {
            return 'cooler-brand-lianli';
        } else if (brandLower.includes('几何未来') || brandLower.includes('geometric')) {
            return 'cooler-brand-geometric';
        } else {
            return 'cooler-brand-other';
        }
    }
    
    // 获取散热器类型对应的CSS类名
    function getTypeClass(type) {
        if (!type) return '';
        
        if (type.includes('风冷') || type.includes('Air')) {
            return 'cooler-type-air';
        } else if (type.includes('水冷') || type.includes('Water')) {
            return 'cooler-type-water';
        } else if (type.includes('AIO')) {
            return 'cooler-type-aio';
        }
        
        return '';
    }

// 重置表单
function resetForm() {
    const coolerForm = document.getElementById('coolerForm');
    const coolerImage = document.getElementById('coolerImage');
    const imagePreview = document.getElementById('imagePreview');
    const imagePreviewContainer = document.getElementById('imagePreviewContainer');
    
    if (coolerForm) {
        coolerForm.reset();
        if (imagePreview) {
            imagePreview.src = '';
        }
        if (imagePreviewContainer) {
            imagePreviewContainer.classList.add('hidden');
        }
        if (coolerImage) {
            coolerImage.value = '';
        }
        
        // 重要：确保重置imageFile变量
        imageFile = null;
        
        isEditing = false;
        currentCoolerId = null;
        
        // 更改提交按钮文本
        const submitBtn = coolerForm.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.innerHTML = '<i class="fas fa-save mr-1 sm:mr-2"></i> 保存散热器信息';
        }
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // DOM元素
    const coolerForm = document.getElementById('coolerForm');
    const brand = document.getElementById('brand');
    const model = document.getElementById('model');
    const type = document.getElementById('type');
    const socket_support = document.getElementById('socket_support');
    const tdp = document.getElementById('tdp');
    const fanCount = document.getElementById('fanCount');
    const heatpipeCount = document.getElementById('heatpipeCount');
    const towerCount = document.getElementById('towerCount');
    const fanSize = document.getElementById('fanSize');
    const radiatorSize = document.getElementById('radiatorSize');
    const noiseLevel = document.getElementById('noiseLevel');
    const height = document.getElementById('height');
    const rgbLighting = document.getElementById('rgbLighting');
    const color = document.getElementById('color');
    const material = document.getElementById('material');
    const warranty = document.getElementById('warranty');
    const price = document.getElementById('price');
    const coolerImage = document.getElementById('coolerImage');
    const imagePreview = document.getElementById('imagePreview');
    const imagePreviewContainer = document.getElementById('imagePreviewContainer');
    const removeImageBtn = document.getElementById('removeImageBtn');
    const notes = document.getElementById('notes');
    const coolerTableBody = document.getElementById('coolerTableBody');
    const coolerSearch = document.getElementById('coolerSearch');
    const brandFilter = document.getElementById('brandFilter');
    const typeFilter = document.getElementById('typeFilter');
    const resetFilterBtn = document.getElementById('resetFilterBtn');
    const totalCount = document.getElementById('totalCount');
    const prevPage = document.getElementById('prevPage');
    const nextPage = document.getElementById('nextPage');
    const pageInfo = document.getElementById('pageInfo');
    const coolerModal = document.getElementById('coolerModal');
    const closeModal = document.getElementById('closeModal');
    const closeModalBtn = document.getElementById('closeModalBtn');
    const coolerDetails = document.getElementById('coolerDetails');
    const editBtn = document.getElementById('editBtn');
    const deleteBtn = document.getElementById('deleteBtn');
    const smartInput = document.getElementById('smartInput');
    const autoFillBtn = document.getElementById('autoFillBtn');
    const darkModeToggle = document.getElementById('darkModeToggle');
    
    // 重置状态，防止任何缓存导致的意外行为
    currentCoolerId = null;
    isEditing = false;
    
    // 修复bug：确保模态框始终是隐藏状态开始，防止在移动端自动显示
    if (coolerModal) {
        coolerModal.classList.add('hidden');
        coolerModal.style.display = 'none'; 
        coolerModal.setAttribute('style', 'display: none !important');
        
        // 检查是否是移动设备
        const isMobile = window.innerWidth < 640;
        console.log('当前设备是移动端:', isMobile);
        
        // 检查模态框是否显示的调试日志
        console.log('模态框初始状态:', coolerModal.classList.contains('hidden') ? '隐藏' : '显示');
    }

    // 局部变量
    let imageFile = null;
    let isDarkMode = localStorage.getItem('darkMode') === 'true'; // 默认为亮色模式，只有明确设置为true时才使用暗夜模式

    // 初始化
    init();

    // 初始化函数
    function init() {
        // 先应用安全初始化
        safePageInit();
        
        // 然后设置事件监听和加载数据
        setupEventListeners();
        loadCoolers();
        initDarkMode();
        initPermissions(); // 添加权限初始化
        
        // 将图片预览函数暴露到全局作用域，便于内联onclick使用
        window.openImageFullscreen = openImageFullscreen;
    }
    
    // 安全初始化页面，防止自动打开模态框
    function safePageInit() {
        // 重置状态变量
        currentCoolerId = null;
        isEditing = false;
        
        // 确保模态框隐藏
        const coolerModal = document.getElementById('coolerModal');
        if (coolerModal) {
            coolerModal.classList.add('hidden');
            coolerModal.style.display = 'none';
            coolerModal.setAttribute('style', 'display: none !important');
            document.body.style.overflow = 'auto';
        }
        
        // 增强移动端模态框按钮
        enhanceMobileModalButtons();
        
        // 检查地址栏是否有异常参数
        const url = new URL(window.location.href);
        // 清除可能影响页面状态的参数
        if (url.searchParams.has('id') || url.searchParams.has('view') || url.searchParams.has('modal')) {
            url.searchParams.delete('id');
            url.searchParams.delete('view');
            url.searchParams.delete('modal');
            // 使用history API替换当前URL，不会引起页面刷新
            window.history.replaceState({}, document.title, url.toString());
        }
    }
    

    
    // 初始化暗夜模式
    function initDarkMode() {
        // 检查本地存储中的暗夜模式设置
        if (isDarkMode) {
            document.body.classList.add('dark-mode');
            updateDarkModeIcon(true);
        } else {
            document.body.classList.remove('dark-mode');
            updateDarkModeIcon(false);
        }
    }
    
    // 更新暗夜模式图标
    function updateDarkModeIcon(isDark) {
        if (darkModeToggle) {
            if (isDark) {
                darkModeToggle.innerHTML = '<i class="fas fa-sun"></i>';
                darkModeToggle.classList.remove('bg-gray-200', 'hover:bg-gray-300', 'text-gray-700');
                darkModeToggle.classList.add('bg-yellow-400', 'hover:bg-yellow-500', 'text-yellow-900');
                darkModeToggle.title = '切换至亮色模式';
                darkModeToggle.style.boxShadow = '0 0 10px rgba(251, 191, 36, 0.5)';
            } else {
                darkModeToggle.innerHTML = '<i class="fas fa-moon"></i>';
                darkModeToggle.classList.remove('bg-yellow-400', 'hover:bg-yellow-500', 'text-yellow-900');
                darkModeToggle.classList.add('bg-gray-200', 'hover:bg-gray-300', 'text-gray-700');
                darkModeToggle.title = '切换至暗夜模式';
                darkModeToggle.style.boxShadow = 'none';
            }
        }
    }
    
    // 切换暗夜模式
    function toggleDarkMode() {
        isDarkMode = !isDarkMode;
        localStorage.setItem('darkMode', isDarkMode);
        
        if (isDarkMode) {
            document.body.classList.add('dark-mode');
        } else {
            document.body.classList.remove('dark-mode');
        }
        
        updateDarkModeIcon(isDarkMode);
    }

    // 添加debounce函数
    function debounce(func, wait) {
        let timeout;
        return function() {
            const context = this;
            const args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(function() {
                func.apply(context, args);
            }, wait);
        };
    }

    // 设置事件监听器
    function setupEventListeners() {
        // 暗夜模式切换
        if (darkModeToggle) {
            darkModeToggle.addEventListener('click', toggleDarkMode);
        }
        
        // 智能解析按钮
        if (autoFillBtn) {
            autoFillBtn.addEventListener('click', parseCoolerInfo);
        }
        
        // 图片上传
        if (coolerImage) {
            coolerImage.addEventListener('change', handleImageUpload);
        }
        
        if (removeImageBtn) {
            removeImageBtn.addEventListener('click', removeImage);
        }

        // 表单提交
            if (coolerForm) {
            coolerForm.addEventListener('submit', handleFormSubmit);
        }

        // 搜索和筛选
        if (coolerSearch) {
            coolerSearch.addEventListener('input', debounce(() => {
                currentPage = 1;
                loadCoolers();
            }, 300));
        }
        
        if (brandFilter) {
            brandFilter.addEventListener('change', () => {
                currentPage = 1;
                loadCoolers();
            });
        }

        if (typeFilter) {
            typeFilter.addEventListener('change', () => {
                currentPage = 1;
                loadCoolers();
            });
        }

        if (resetFilterBtn) {
            resetFilterBtn.addEventListener('click', () => {
                if (coolerSearch) coolerSearch.value = '';
                if (brandFilter) brandFilter.value = 'all';
                if (typeFilter) typeFilter.value = 'all';
                currentPage = 1;
                loadCoolers();
            });
        }

        // 分页
        if (prevPage) {
            prevPage.addEventListener('click', () => changePage(currentPage - 1));
        }
        
        if (nextPage) {
            nextPage.addEventListener('click', () => changePage(currentPage + 1));
        }

        // 首页和尾页
        const firstPageBtn = document.getElementById('firstPage');
        if (firstPageBtn) {
            firstPageBtn.addEventListener('click', () => changePage(1));
        }

        const lastPageBtn = document.getElementById('lastPage');
        if (lastPageBtn) {
            lastPageBtn.addEventListener('click', () => {
                const totalPages = Math.ceil(totalRecords / pageSize) || 1;
                changePage(totalPages);
            });
        }

        // 页码跳转
        const goToPageBtn = document.getElementById('goToPage');
        if (goToPageBtn) {
            goToPageBtn.addEventListener('click', () => {
                const pageJumpInput = document.getElementById('pageJump');
                if (pageJumpInput) {
                    let pageNum = parseInt(pageJumpInput.value);
                    const totalPages = Math.ceil(totalRecords / pageSize) || 1;
                    
                    if (isNaN(pageNum) || pageNum < 1) {
                        pageNum = 1;
                    } else if (pageNum > totalPages) {
                        pageNum = totalPages;
                    }
                    
                    changePage(pageNum);
                    pageJumpInput.value = '';
                }
            });
        }

        // 监听页码输入框的回车事件
        const pageJumpInput = document.getElementById('pageJump');
        if (pageJumpInput) {
            pageJumpInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    const goToPageBtn = document.getElementById('goToPage');
                    if (goToPageBtn) {
                        goToPageBtn.click();
                    }
                }
            });
        }

        // 模态框
        if (closeModal) {
            // 移除之前的监听器，防止重复添加
            const newCloseModal = closeModal.cloneNode(true);
            if (closeModal.parentNode) {
                closeModal.parentNode.replaceChild(newCloseModal, closeModal);
            }
            
            newCloseModal.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                if (coolerModal) {
                    coolerModal.classList.add('hidden');
                    coolerModal.style.display = 'none';
                    // 移除内联样式，确保隐藏生效
                    coolerModal.setAttribute('style', 'display: none !important');
                    document.body.style.overflow = 'auto'; // 恢复滚动
                    console.log('点击X按钮关闭模态框');
                }
            });
            
            // 添加触摸事件支持
            newCloseModal.addEventListener('touchend', (e) => {
                e.preventDefault();
                e.stopPropagation();
                if (coolerModal) {
                    coolerModal.classList.add('hidden');
                    coolerModal.style.display = 'none';
                    coolerModal.setAttribute('style', 'display: none !important');
                    document.body.style.overflow = 'auto';
                    console.log('触摸X按钮关闭模态框');
                }
            });
            
            // 确保在移动设备上有足够大的点击区域
            newCloseModal.style.padding = '15px';
            newCloseModal.style.cursor = 'pointer';
            newCloseModal.style.zIndex = '60';
            newCloseModal.style.position = 'absolute';
            newCloseModal.style.right = '5px';
            newCloseModal.style.top = '5px';
        }
        
        if (closeModalBtn) {
            // 移除之前的监听器，防止重复添加
            const newCloseModalBtn = closeModalBtn.cloneNode(true);
            if (closeModalBtn.parentNode) {
                closeModalBtn.parentNode.replaceChild(newCloseModalBtn, closeModalBtn);
            }
            
            newCloseModalBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                if (coolerModal) {
                    coolerModal.classList.add('hidden');
                    coolerModal.style.display = 'none';
                    // 移除内联样式，确保隐藏生效
                    coolerModal.setAttribute('style', 'display: none !important');
                    document.body.style.overflow = 'auto'; // 恢复滚动
                    console.log('点击关闭按钮关闭模态框');
                }
            });
            
            // 添加触摸事件支持
            newCloseModalBtn.addEventListener('touchend', (e) => {
                e.preventDefault();
                e.stopPropagation();
                if (coolerModal) {
                    coolerModal.classList.add('hidden');
                    coolerModal.style.display = 'none';
                    coolerModal.setAttribute('style', 'display: none !important');
                    document.body.style.overflow = 'auto';
                    console.log('触摸关闭按钮关闭模态框');
                }
            });
            
            newCloseModalBtn.style.cursor = 'pointer';
            newCloseModalBtn.style.userSelect = 'none';
            newCloseModalBtn.style.WebkitTapHighlightColor = 'transparent';
            newCloseModalBtn.style.minWidth = '80px';
            newCloseModalBtn.style.padding = '8px 16px';
        }
        
        if (editBtn) {
            // 移除之前的监听器，防止重复添加
            const newEditBtn = editBtn.cloneNode(true);
            if (editBtn.parentNode) {
                editBtn.parentNode.replaceChild(newEditBtn, editBtn);
            }
            
            newEditBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                handleEdit();
            });
            
            // 添加触摸事件支持
            newEditBtn.addEventListener('touchend', function(e) {
                e.preventDefault();
                e.stopPropagation();
                handleEdit();
                console.log('触摸编辑按钮');
            });
            
            newEditBtn.style.cursor = 'pointer';
            newEditBtn.style.userSelect = 'none';
            newEditBtn.style.WebkitTapHighlightColor = 'transparent';
        }
        
        if (deleteBtn) {
            // 移除之前的监听器，防止重复添加
            const newDeleteBtn = deleteBtn.cloneNode(true);
            if (deleteBtn.parentNode) {
                deleteBtn.parentNode.replaceChild(newDeleteBtn, deleteBtn);
            }
            
            newDeleteBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                if (currentCoolerId) {
                    confirmDeleteCooler(currentCoolerId);
                    
                    // 关闭详情模态框
                    if (coolerModal) {
                        coolerModal.classList.add('hidden');
                        coolerModal.style.display = 'none';
                        coolerModal.setAttribute('style', 'display: none !important');
                        document.body.style.overflow = 'auto';
                        console.log('删除按钮关闭模态框');
                    }
                }
            });
            
            // 添加触摸事件支持
            newDeleteBtn.addEventListener('touchend', function(e) {
                e.preventDefault();
                e.stopPropagation();
                if (currentCoolerId) {
                    confirmDeleteCooler(currentCoolerId);
                    
                    // 关闭详情模态框
                    if (coolerModal) {
                        coolerModal.classList.add('hidden');
                        coolerModal.style.display = 'none';
                        coolerModal.setAttribute('style', 'display: none !important');
                        document.body.style.overflow = 'auto';
                        console.log('触摸删除按钮关闭模态框');
                    }
                }
            });
            
            newDeleteBtn.style.cursor = 'pointer';
            newDeleteBtn.style.userSelect = 'none';
            newDeleteBtn.style.WebkitTapHighlightColor = 'transparent';
        }
    }

    // 解析散热器信息
    function parseCoolerInfo() {
        const smartInput = document.getElementById('smartInput');
        if (!smartInput || !smartInput.value.trim()) {
            showErrorMessage('请输入散热器参数信息');
            return;
        }

        const text = smartInput.value.trim();
        console.log('开始解析文本:', text);

        // 提取参数对
        const patterns = {
            model: /散热器型号\s*[:：]\s*([^、，,]+)/i,
            brand: /品牌\s*[:：]\s*([^、，,]+)/i,
            type: /类型\s*[:：]\s*([^、，,]+)/i,
            socket_support: /支持的CPU接口\s*[:：]\s*([^、，,]+|[^、，,]+(?:[^散])+)/i,
            tdp: /散热功率\s*\(?W\)?\s*[:：]\s*([^、，,]+)/i,
            fan_count: /风扇数量\s*[:：]\s*(\d+)[个]?/i,
            heatpipe_count: /铜管数量\s*[:：]\s*(\d+)[根个]?/i,
            tower_count: /塔数\s*[:：]\s*([^、，,]+)/i,
            fan_size: /风扇尺寸\s*\(?mm\)?\s*[:：]\s*([^、，,]+)/i,
            radiator_size: /散热器尺寸[\s\S]*?[:：]\s*([^、，,]+)/i,
            noise_level: /噪音级别\s*\(?dBA\)?\s*[:：]\s*([^、，,]+)/i,
            height: /散热器高度\s*\(?mm\)?\s*[:：]\s*([^、，,]+)/i,
            rgb_lighting: /RGB灯效\s*[:：]\s*([^、，,；;]*)/i,
            color: /颜色\s*[:：]\s*([^、，,；;]*)/i,
            material: /材质\s*[:：]\s*([^、，,]+|[^、，,]+(?:[^保])+)/i,
            warranty: /保修期\s*\(?年\)?\s*[:：]\s*([^、，,]+)/i,
            price: /价格\s*[:：]\s*([^、，,]+)/i,
            notes: /备注\s*[:：]\s*(.+)$/i
        };

        const results = {};
        
        // 尝试从文本中提取每个属性
        for (const [key, pattern] of Object.entries(patterns)) {
            const match = text.match(pattern);
            if (match && match[1]) {
                results[key] = match[1].trim();
                console.log(`提取 ${key}:`, results[key]);
            }
        }

        // 特殊处理
        // 处理风扇数量提取
        if (!results.fan_count && text.includes('风扇数量')) {
            const match = text.match(/风扇数量\s*[:：]\s*([^、，,]+)/i);
            if (match && match[1]) {
                const count = match[1].match(/(\d+)/);
                if (count && count[1]) {
                    results.fan_count = count[1];
                }
            }
        }
        
        // 处理铜管数量提取
        if (!results.heatpipe_count) {
            // 尝试匹配"6根铜管"或"六铜管"等格式
            const heatpipeMatch = text.match(/(\d+)[根个]?[铜]+管/i) || 
                                  text.match(/(\d+)\s*heat\s*pipes?/i) ||
                                  text.match(/[铜]+管\s*(\d+)[根个]?/i);
            if (heatpipeMatch && heatpipeMatch[1]) {
                results.heatpipe_count = heatpipeMatch[1];
                console.log('提取铜管数量:', results.heatpipe_count);
            }
        }

        // 处理散热器高度
        if (results.height) {
            const heightMatch = results.height.match(/(\d+)/);
            if (heightMatch && heightMatch[1]) {
                results.height = heightMatch[1];
            }
        }

        // 处理价格
        if (results.price) {
            const priceMatch = results.price.match(/(\d+(?:\.\d+)?)/);
            if (priceMatch && priceMatch[1]) {
                results.price = priceMatch[1];
            }
        }

        // 处理保修期
        if (results.warranty) {
            const warrantyMatch = results.warranty.match(/(\d+)/);
            if (warrantyMatch && warrantyMatch[1]) {
                results.warranty = warrantyMatch[1];
            }
        }

        // 处理RGB灯效
        if (results.rgb_lighting) {
            const rgbValue = results.rgb_lighting.toLowerCase();
            if (rgbValue.includes('是') || rgbValue.includes('有') || rgbValue.includes('yes') || rgbValue === '1' || rgbValue === 'true') {
                results.rgb_lighting = '有';
            } else {
                results.rgb_lighting = '无';
            }
        } else if (text.includes('RGB灯效')) {
            // 尝试特殊处理一下RGB灯效
            if (text.toLowerCase().includes('rgb灯效：无') || text.toLowerCase().includes('rgb灯效:无')) {
                results.rgb_lighting = '无';
                console.log('特殊处理RGB灯效: 无');
            } else if (text.toLowerCase().includes('rgb灯效：有') || text.toLowerCase().includes('rgb灯效:有')) {
                results.rgb_lighting = '有';
                console.log('特殊处理RGB灯效: 有');
            }
        }

        // 填充表单
        if (results.brand) document.getElementById('brand').value = results.brand;
        if (results.model) document.getElementById('model').value = results.model;
        
        // 类型映射
        if (results.type) {
            const typeElement = document.getElementById('type');
            const typeValue = results.type.toLowerCase();
            
            if (typeValue.includes('风冷') || typeValue.includes('塔式')) {
                typeElement.value = '风冷';
            } else if (typeValue.includes('水冷') || typeValue.includes('一体式') || typeValue.includes('aio')) {
                typeElement.value = '水冷';
            } else if (typeValue.includes('半导体')) {
                typeElement.value = '半导体';
            } else if (typeValue.includes('被动')) {
                typeElement.value = '被动散热';
            } else {
                typeElement.value = '风冷'; // 默认
            }
        }
        
        if (results.socket_support) document.getElementById('socket_support').value = results.socket_support;
        if (results.tdp) document.getElementById('tdp').value = results.tdp.replace(/[^\d.]/g, '');
        if (results.fan_count) document.getElementById('fanCount').value = results.fan_count;
        if (results.heatpipe_count) document.getElementById('heatpipeCount').value = results.heatpipe_count;
        if (results.tower_count) {
            const towerValue = results.tower_count;
            const towerElement = document.getElementById('towerCount');
            if (towerValue.includes('单') || towerValue === '1') {
                towerElement.value = '单塔';
            } else if (towerValue.includes('双') || towerValue === '2') {
                towerElement.value = '双塔';
            }
        }
        if (results.fan_size) document.getElementById('fanSize').value = results.fan_size;
        if (results.radiator_size) document.getElementById('radiatorSize').value = results.radiator_size;
        if (results.noise_level) document.getElementById('noiseLevel').value = results.noise_level;
        if (results.height) document.getElementById('height').value = results.height;
        
        // RGB灯效映射
        if (results.rgb_lighting) {
            const rgbElement = document.getElementById('rgbLighting');
            if (results.rgb_lighting === '有') {
                rgbElement.value = '1';
            } else {
                rgbElement.value = '0';
            }
            console.log('设置RGB灯效下拉框值:', rgbElement.value);
        }
        
        if (results.color) document.getElementById('color').value = results.color;
        if (results.material) document.getElementById('material').value = results.material;
        if (results.warranty) document.getElementById('warranty').value = results.warranty;
        if (results.price) document.getElementById('price').value = results.price;
        if (results.notes) document.getElementById('notes').value = results.notes;

        showSuccessMessage('散热器信息已解析并填充到表单');
    }

    // 处理图片上传
    function handleImageUpload(e) {
        const file = e.target.files[0];
        if (!file) return;

        // 验证文件类型
        const validTypes = ['image/jpeg', 'image/png', 'image/gif'];
        if (!validTypes.includes(file.type)) {
            alert('请上传JPG、PNG或GIF格式的图片');
            return;
        }

        // 验证文件大小
        if (file.size > 5 * 1024 * 1024) { // 5MB
            alert('图片大小不能超过5MB');
            return;
        }

        imageFile = file;
        const reader = new FileReader();
        reader.onload = function(event) {
            imagePreview.src = event.target.result;
            imagePreviewContainer.classList.remove('hidden');
            
            // 添加WebP转换信息提示
            const formatInfo = document.createElement('div');
            formatInfo.className = 'mt-2 text-xs text-gray-600';
            formatInfo.id = 'webpInfo';
            
            // 估算WebP压缩比例
            let compressionRate;
            if (file.type === 'image/jpeg') {
                compressionRate = '约30-40%'; // JPEG转WebP的一般优化比例
            } else if (file.type === 'image/png') {
                compressionRate = '约45-65%'; // PNG转WebP的一般优化比例
            } else {
                compressionRate = '约25-50%'; // 其他格式的一般优化比例
            }
            
            const originalSizeKB = Math.round(file.size / 1024);
            const estimatedSizeKB = Math.round(originalSizeKB * (1 - 0.4)); // 假设平均节省40%
            
            // 检测WebP支持并生成信息
            const infoHTML = `
                <div class="flex flex-col space-y-1">
                    <span><i class="fas fa-info-circle text-cyan-600 mr-1"></i> 原始图片: ${file.name} (${originalSizeKB}KB)</span>
                    <span><i class="fas fa-sync text-cyan-600 mr-1"></i> 上传后将自动转换为WebP格式，预计大小约${estimatedSizeKB}KB</span>
                    <span><i class="fas fa-compress-arrows-alt text-cyan-600 mr-1"></i> WebP格式可节省${compressionRate}的文件大小</span>
                    ${supportWebP ? 
                        '<span class="text-green-600"><i class="fas fa-check-circle mr-1"></i> 您的浏览器支持WebP格式</span>' : 
                        '<span class="text-orange-500"><i class="fas fa-exclamation-triangle mr-1"></i> 您的浏览器不完全支持WebP格式，但不影响上传</span>'}
                </div>
            `;
            
            // 移除旧的提示（如果存在）
            const oldInfo = document.getElementById('webpInfo');
            if (oldInfo) {
                oldInfo.remove();
            }
            
            // 添加新的提示
            formatInfo.innerHTML = infoHTML;
            imagePreviewContainer.appendChild(formatInfo);
        };
        reader.readAsDataURL(file);
    }

    // 移除图片
    function removeImage() {
        coolerImage.value = '';
        imagePreview.src = '';
        imagePreviewContainer.classList.add('hidden');
        imageFile = null;
    }

    // 处理表单提交
    function handleFormSubmit(e) {
        e.preventDefault();

        // 表单验证
        if (!model.value.trim()) {
            showErrorMessage('请输入散热器型号');
            model.focus();
            return;
        }

        if (!brand.value) {
            showErrorMessage('请选择散热器品牌');
            brand.focus();
            return;
        }

        if (!type.value) {
            showErrorMessage('请选择散热器类型');
            type.focus();
            return;
        }

        // 创建FormData对象
        const formData = new FormData();
        formData.append('brand', brand.value);
        formData.append('model', model.value.trim());
        formData.append('type', type.value);
        formData.append('socket_support', socket_support.value.trim());
        formData.append('tdp', tdp.value);
        formData.append('fan_count', fanCount.value);
        // 确保heatpipe_count不为空字符串，如果为空则传0
        formData.append('heatpipe_count', heatpipeCount.value === '' ? '0' : heatpipeCount.value);
        formData.append('tower_count', towerCount.value);
        formData.append('fan_size', fanSize.value.trim());
        formData.append('radiator_size', radiatorSize.value.trim());
        formData.append('noise_level', noiseLevel.value.trim());
        formData.append('height', height.value);
        formData.append('rgb_lighting', rgbLighting.value);
        formData.append('color', color.value.trim());
        formData.append('material', material.value.trim());
        formData.append('warranty', warranty.value);
        formData.append('price', price.value);
        formData.append('notes', notes.value.trim());

        // 修复：仅当 imageFile 存在（即用户新上传了图片）时才附加
        if (imageFile) {
            formData.append('image', imageFile);
        } else {
            // 如果用户未上传图片，告知后端使用默认图片
            console.log('[DEBUG] 用户未上传图片，将使用默认图片');
            formData.append('use_default_image', 'true');
        }

        // 添加调试信息
        console.log('表单提交数据:');
        console.log('RGB灯效:', rgbLighting.value);
        console.log('颜色:', color.value);
        
        // 将FormData内容打印出来便于检查
        for (let pair of formData.entries()) {
            console.log(pair[0] + ': ' + pair[1]);
        }

        // 提交表单
        const url = isEditing ? `/api/coolers/${currentCoolerId}` : '/api/coolers';
        const method = isEditing ? 'PUT' : 'POST';
        
        console.log('提交请求:', method, url);

        // 关键修复：在编辑模式下，明确告诉后端是否需要更新图片
        if (isEditing) {
            // 如果用户没有选择新图片，添加一个标记告诉后端保留原图片
            if (!imageFile) {
                formData.append('keep_original_image', 'true');
            } else {
                // 用户上传了新图片，添加标记告诉后端更新图片
                formData.append('update_image', 'true');
            }
        }

        fetchWithAuth(url, {
            method: method,
            body: formData
        })
        .then(response => {
            console.log('响应状态:', response.status);
            if (!response || !response.ok) {
                return response.json().then(data => {
                    console.error('错误响应:', data);
                    throw new Error(data.message || '操作失败');
                });
            }
            return response.json();
        })
        .then(data => {
            console.log('成功响应:', data);
            showSuccessMessage(isEditing ? '散热器信息更新成功' : '散热器信息添加成功');
            
            // 重要：确保清空imageFile变量，避免缓存问题
            imageFile = null;
            
            resetForm();
            loadCoolers();
        })
        .catch(error => {
            console.error('请求错误:', error);
            showErrorMessage(error.message || '操作失败');
        });
    }

    // 图片预览功能已禁用
});










// 初始化权限控制
function initPermissions() {
    console.log('初始化权限控制...');
    
    // 如果没有权限控制模块，发出警告
    if (typeof setupPermissionBasedUI !== 'function') {
        console.warn('警告：权限控制模块未加载！请确保已引入permission-helper.js');
        return;
    }
    
    // 执行权限控制初始化
    setupPermissionBasedUI().then(() => {
        // 根据用户角色处理按钮
        isAdmin().then(isAdminUser => {
            if (!isAdminUser) {
                // 禁用表单
                const form = document.querySelector('form');
                if (form) {
                    const inputs = form.querySelectorAll('input, select, textarea');
                    inputs.forEach(input => {
                        input.setAttribute('disabled', 'disabled');
                    });
                    
                    const submitBtn = form.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        submitBtn.setAttribute('disabled', 'disabled');
                        submitBtn.title = '只有管理员可以添加或修改数据';
                    }
                }
                
                // 为所有删除操作添加权限验证拦截器
                interceptDeleteOperations();
            } else {
                // 添加管理员标识
                const header = document.querySelector('h1, h2');
                if (header) {
                    // 检查是否已经添加过徽章
                    if (!header.querySelector('.admin-badge')) {
                        const adminBadge = document.createElement('span');
                        adminBadge.className = 'bg-green-500 text-white text-xs px-2 py-1 rounded ml-2 admin-badge';
                        adminBadge.innerText = '管理员';
                        header.appendChild(adminBadge);
                    }
                }
            }
        });
    });
}

// 拦截所有删除操作的请求
function interceptDeleteOperations() {
    // 保存原始的fetch函数
    const originalFetch = window.fetch;
    
    // 重写fetch函数以拦截删除请求
    window.fetch = async function(url, options) {
        // 检查是否为删除操作
        if (options && options.method === 'DELETE') {
            console.log('拦截到删除操作请求:', url);
            
            // 检查用户权限
            const isAdminUser = await isAdmin();
            if (!isAdminUser) {
                console.warn('权限不足，普通用户无法执行删除操作');
                // 返回权限错误响应
                return Promise.resolve(new Response(JSON.stringify({
                    error: '权限不足',
                    message: '只有管理员可以删除数据'
                }), {
                    status: 403,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }));
            }
        }
        
        // 对于非删除请求或管理员的删除请求，正常执行
        return originalFetch.apply(this, arguments);
    };
    
    // 添加全局点击事件拦截
    document.addEventListener('click', async function(event) {
        // 查找可能触发删除操作的元素
        const deleteButton = event.target.closest('.btn-delete, [data-action="delete"]');
        if (deleteButton) {
            const isAdminUser = await isAdmin();
            if (!isAdminUser) {
                // 阻止事件传播
                event.preventDefault();
                event.stopPropagation();
                console.warn('权限不足，普通用户无法执行删除操作');
                
                // 可选：显示提示消息（使用toast或alert）
                if (typeof showToast === 'function') {
                    showToast('只有管理员可以删除数据', 'error');
                } else {
                    console.error('权限不足，只有管理员可以删除数据');
                }
                
                return false;
            }
        }
    }, true);
}

// 移除多余的事件监听器，因为我们已经在init函数中调用了initPermissions
// document.addEventListener('DOMContentLoaded', initPermissions);
