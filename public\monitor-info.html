<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>显示器管理 - 电脑配件综合管理系统</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/viewerjs/1.11.3/viewer.min.css">
    <link rel="stylesheet" href="/css/monitor-info.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/viewerjs/1.11.3/viewer.min.js"></script>
    <script src="/js/main.js"></script>
    <script src="/js/permission-helper.js"></script>
</head>

<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-8">
        <header class="mb-8 bg-card shadow-md p-4 rounded-lg">
            <div class="flex justify-between items-center">
                <h1 class="text-2xl font-bold highlight-text flex items-center">
                    <i class="fas fa-desktop fa-lg mr-3"></i> 显示器管理
                </h1>
                <div class="flex items-center space-x-2">
                    <button id="darkModeToggle" title="切换模式"
                        class="text-muted hover:text-color w-10 h-10 rounded-full flex items-center justify-center transition-colors">
                        <i class="fas fa-moon"></i>
                    </button>
                    <a href="/pc-components.html"
                        class="highlight-bg text-white py-2 px-4 rounded-md transition duration-300 ease-in-out">
                        <i class="fas fa-arrow-left mr-1"></i> 返回
                    </a>
                </div>
            </div>
        </header>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Left Form -->
            <div class="lg:col-span-1">
                <div class="bg-card shadow-md rounded-lg p-6">
                    <h2 class="text-xl font-semibold mb-4 highlight-text flex items-center">
                        <i class="fas fa-plus-circle mr-2"></i> 添加显示器
                    </h2>
                    <form id="monitorForm" class="space-y-4">
                        <input type="hidden" id="monitorId" name="monitorId">
                        
                        <div>
                            <h3 class="text-lg font-medium leading-6 text-color border-b border-color pb-2 mb-4">智能解析</h3>
                            <div class="space-y-2">
                                <label for="smartParseInput" class="block text-sm font-medium text-muted">粘贴显示器规格文本</label>
                                <textarea id="smartParseInput" rows="4" class="mt-1 block w-full px-3 py-2 border border-color rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm" placeholder="将产品名称或规格描述粘贴到此处，系统将尝试自动填充表单。"></textarea>
                                <button type="button" id="parseButton" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md transition duration-300 ease-in-out flex items-center justify-center">
                                    <i class="fas fa-magic mr-2"></i> 解析
                                </button>
                            </div>
                        </div>

                        <div class="space-y-6">
                            <div>
                                <h3 class="text-lg font-medium leading-6 text-color border-b border-color pb-2 mb-4">基本信息</h3>
                                <div class="grid grid-cols-1 gap-y-4 gap-x-4 sm:grid-cols-2">
                                    <div>
                                        <label for="brand" class="block text-sm font-medium text-muted">品牌</label>
                                        <input type="text" id="brand" name="brand" required class="mt-1 block w-full px-3 py-2 border border-color rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm">
                                    </div>
                                    <div>
                                        <label for="model" class="block text-sm font-medium text-muted">型号</label>
                                        <input type="text" id="model" name="model" required class="mt-1 block w-full px-3 py-2 border border-color rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm">
                                    </div>
                                </div>
                            </div>

                            <div>
                                <h3 class="text-lg font-medium leading-6 text-color border-b border-color pb-2 mb-4">核心规格</h3>
                                <div class="grid grid-cols-1 gap-y-4 gap-x-4 sm:grid-cols-2">
                                    <div>
                                        <label for="screen_size" class="block text-sm font-medium text-muted">屏幕尺寸 (英寸)</label>
                                        <input type="number" step="0.1" id="screen_size" name="screen_size" class="mt-1 block w-full px-3 py-2 border border-color rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm">
                                    </div>
                                     <div>
                                        <label for="resolution" class="block text-sm font-medium text-muted">分辨率</label>
                                        <input type="text" id="resolution" name="resolution" placeholder="例如: 1920x1080" class="mt-1 block w-full px-3 py-2 border border-color rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm">
                                    </div>
                                    <div>
                                        <label for="panel_type" class="block text-sm font-medium text-muted">面板类型</label>
                                        <select id="panel_type" name="panel_type" class="mt-1 block w-full px-3 py-2 border border-color rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm">
                                            <option value="">请选择</option>
                                            <option value="IPS">IPS</option>
                                            <option value="VA">VA</option>
                                            <option value="TN">TN</option>
                                            <option value="OLED">OLED</option>
                                            <option value="Mini-LED">Mini-LED</option>
                                            <option value="Other">其他</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label for="refresh_rate" class="block text-sm font-medium text-muted">刷新率 (Hz)</label>
                                        <input type="number" id="refresh_rate" name="refresh_rate" class="mt-1 block w-full px-3 py-2 border border-color rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm">
                                    </div>
                                    <div>
                                        <label for="response_time" class="block text-sm font-medium text-muted">响应时间 (ms)</label>
                                        <input type="number" step="0.1" id="response_time" name="response_time" class="mt-1 block w-full px-3 py-2 border border-color rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm">
                                    </div>
                                    <div>
                                        <label for="aspect_ratio" class="block text-sm font-medium text-muted">屏幕比例</label>
                                        <input type="text" id="aspect_ratio" name="aspect_ratio" placeholder="例如: 16:9" class="mt-1 block w-full px-3 py-2 border border-color rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm">
                                    </div>
                                </div>
                            </div>

                            <div>
                                <h3 class="text-lg font-medium leading-6 text-color border-b border-color pb-2 mb-4">显示特性</h3>
                                <div class="grid grid-cols-1 gap-y-4 gap-x-4 sm:grid-cols-2">
                                    <div>
                                        <label for="brightness" class="block text-sm font-medium text-muted">亮度 (cd/m²)</label>
                                        <input type="number" id="brightness" name="brightness" class="mt-1 block w-full px-3 py-2 border border-color rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm">
                                    </div>
                                    <div>
                                        <label for="contrast_ratio" class="block text-sm font-medium text-muted">对比度</label>
                                        <input type="text" id="contrast_ratio" name="contrast_ratio" placeholder="例如: 1000:1" class="mt-1 block w-full px-3 py-2 border border-color rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm">
                                    </div>
                                    <div>
                                        <label for="color_gamut" class="block text-sm font-medium text-muted">色域</label>
                                        <input type="text" id="color_gamut" name="color_gamut" placeholder="例如: 99% sRGB" class="mt-1 block w-full px-3 py-2 border border-color rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm">
                                    </div>
                                    <div>
                                        <label for="hdr_support" class="block text-sm font-medium text-muted">HDR 支持</label>
                                        <input type="text" id="hdr_support" name="hdr_support" placeholder="例如: HDR10" class="mt-1 block w-full px-3 py-2 border border-color rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm">
                                    </div>
                                </div>
                            </div>
                            
                            <div>
                                <h3 class="text-lg font-medium leading-6 text-color border-b border-color pb-2 mb-4">其他信息</h3>
                                <div class="grid grid-cols-1 gap-y-4 gap-x-4 sm:grid-cols-2">
                                    <div>
                                        <label for="speakers" class="block text-sm font-medium text-muted">内置扬声器</label>
                                        <select id="speakers" name="speakers" class="mt-1 block w-full px-3 py-2 border border-color rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm">
                                            <option value="">不确定</option>
                                            <option value="1">是</option>
                                            <option value="0">否</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label for="vesa_mount" class="block text-sm font-medium text-muted">VESA 挂载</label>
                                        <input type="text" id="vesa_mount" name="vesa_mount" placeholder="例如: 100x100mm" class="mt-1 block w-full px-3 py-2 border border-color rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm">
                                    </div>
                                    <div class="sm:col-span-2">
                                        <label for="connectivity" class="block text-sm font-medium text-muted">接口</label>
                                        <textarea id="connectivity" name="connectivity" rows="2" placeholder="例如: HDMI 2.0, DP 1.4" class="mt-1 block w-full px-3 py-2 border border-color rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm"></textarea>
                                    </div>
                                    <div class="sm:col-span-2">
                                        <label for="adjustable_stand" class="block text-sm font-medium text-muted">支架调节</label>
                                        <input type="text" id="adjustable_stand" name="adjustable_stand" placeholder="例如: 升降, 旋转" class="mt-1 block w-full px-3 py-2 border border-color rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm">
                                    </div>
                                     <div>
                                        <label for="price" class="block text-sm font-medium text-muted">价格 (¥)</label>
                                        <input type="number" step="0.01" id="price" name="price" class="mt-1 block w-full px-3 py-2 border border-color rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm">
                                    </div>
                                </div>
                            </div>

                            <div class="space-y-2">
                                <label for="notes" class="block text-sm font-medium text-muted">备注</label>
                                <textarea id="notes" name="notes" rows="3" class="mt-1 block w-full px-3 py-2 border border-color rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm"></textarea>
                            </div>

                            <div class="space-y-2">
                                <label for="monitorImage" class="block text-sm font-medium text-muted">图片</label>
                                <input type="file" id="monitorImage" name="image" accept="image/*" class="mt-1 block w-full text-sm text-muted file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-red-50 file:text-red-700 hover:file:bg-red-100"/>

                                <!-- 上传进度条 -->
                                <div id="uploadProgressContainer" class="mt-2 hidden">
                                    <div class="bg-gray-200 dark:bg-gray-700 rounded-full h-3 mb-2 overflow-hidden">
                                        <div id="uploadProgressBar" class="bg-gradient-to-r from-red-500 to-red-600 dark:from-red-400 dark:to-red-500 h-3 rounded-full transition-all duration-500 ease-out relative" style="width: 0%">
                                            <!-- 进度条光泽效果 -->
                                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-20 animate-pulse"></div>
                                        </div>
                                    </div>
                                    <div class="flex justify-between items-center text-xs">
                                        <span id="uploadProgressText" class="text-gray-600 dark:text-gray-400 flex items-center">
                                            <i id="uploadProgressIcon" class="fas fa-upload mr-1"></i>
                                            准备上传...
                                        </span>
                                        <span id="uploadProgressPercent" class="text-red-600 dark:text-red-400 font-medium">0%</span>
                                    </div>
                                </div>

                                <div id="imagePreviewContainer" class="hidden mt-2 relative w-32">
                                    <img id="imagePreview" src="#" alt="图片预览" class="h-32 w-32 object-cover rounded-md monitor-img-hover">
                                    <button type="button" id="removeImageBtn" class="absolute top-0 right-0 bg-red-500 text-white rounded-full h-6 w-6 flex items-center justify-center -mt-2 -mr-2 text-lg">&times;</button>
                                </div>
                            </div>
                        </div>

                        <div class="pt-4">
                            <button type="submit" id="submitBtn" class="w-full highlight-bg text-white py-2 px-4 rounded-md transition duration-300 ease-in-out flex items-center justify-center">
                                <i class="fas fa-save mr-2"></i> <span id="submitBtnText">保存</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Right List -->
            <div class="lg:col-span-2">
                <div class="bg-card shadow-md rounded-lg p-6">
                    <div class="flex flex-col md:flex-row justify-between items-center mb-4">
                        <h2 class="text-xl font-semibold highlight-text mb-2 md:mb-0 flex items-center">
                            <i class="fas fa-list mr-2"></i> 显示器列表
                            <div id="loadingIndicator" class="hidden ml-2">
                                <svg class="animate-spin h-5 w-5 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            </div>
                        </h2>
                        <div class="flex items-center space-x-2">
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-search text-gray-400"></i>
                                </div>
                                <input type="text" id="monitorSearch" placeholder="搜索显示器..." class="w-full md:w-auto pl-10 px-3 py-2 border border-color rounded-md focus:outline-none focus:ring-2 focus:ring-red-500">
                            </div>
                            <select id="brandFilter" class="w-full md:w-auto px-3 py-2 border border-color rounded-md focus:outline-none focus:ring-2 focus:ring-red-500">
                                <option value="all">所有品牌</option>
                            </select>
                            <button id="resetFilterBtn" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="w-full monitor-table">
                            <thead>
                                <tr class="text-left text-xs font-medium text-muted uppercase tracking-wider">
                                    <th class="px-3 py-3 w-16">图片</th>
                                    <th class="px-3 py-3">品牌</th>
                                    <th class="px-3 py-3">型号</th>
                                    <th class="px-3 py-3">尺寸</th>
                                    <th class="px-3 py-3">分辨率</th>
                                    <th class="px-3 py-3 hidden md:table-cell">面板</th>
                                    <th class="px-3 py-3">刷新率</th>
                                    <th class="px-3 py-3 text-center">操作</th>
                                </tr>
                            </thead>
                            <tbody id="monitorTableBody" class="divide-y border-color">
                                <!-- JS-loaded content -->
                                <tr>
                                    <td colspan="5" class="px-3 py-4 text-center">
                                        <div class="flex items-center justify-center">
                                            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                            </svg>
                                            <span>加载中...</span>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-4 flex flex-col sm:flex-row justify-between items-center text-sm text-muted">
                        <div id="totalCount">共 0 条记录</div>
                        <div class="flex items-center space-x-1 mt-2 sm:mt-0">
                             <button id="firstPage" class="px-3 py-1 border border-color rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50">&laquo;</button>
                            <button id="prevPage" class="px-3 py-1 border border-color rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50">&lsaquo;</button>
                            <div id="pageNumbers" class="flex items-center space-x-1"></div>
                            <button id="nextPage" class="px-3 py-1 border border-color rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50">&rsaquo;</button>
                            <button id="lastPage" class="px-3 py-1 border border-color rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50">&raquo;</button>
                            <span class="ml-2">第 <span id="currentPageDisplay">1</span> / <span id="totalPagesDisplay">1</span> 页</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Details Modal -->
    <div id="monitorDetailModal" class="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 hidden">
        <div class="bg-card rounded-lg shadow-xl max-w-2xl lg:max-w-4xl w-full m-4 modal-card overflow-hidden">
            <div class="flex flex-col h-full">
                <div class="p-4 md:p-6 modal-header">
                    <div class="flex justify-between items-center mb-2">
                        <h2 class="text-lg md:text-2xl font-bold highlight-text">显示器详情</h2>
                        <button id="closeDetailModal" class="text-muted hover:text-color"><i class="fas fa-times fa-lg"></i></button>
                    </div>
                </div>
                
                <div class="modal-content-wrapper flex-1">
                    <div id="monitorDetailContent" class="text-color p-4 md:p-6 pt-0 md:pt-0"></div>
                </div>
                
                <div class="p-3 md:p-4 flex space-x-2 md:space-x-3 modal-footer">
                    <button id="detailEditBtn" class="bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-md transition-all duration-300 text-sm md:text-base btn-action">
                        <i class="fas fa-edit mr-1"></i> 编辑
                    </button>
                    <button id="detailDeleteBtn" class="bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-md transition-all duration-300 text-sm md:text-base btn-action">
                        <i class="fas fa-trash mr-1"></i> 删除
                    </button>
                    <button id="closeDetailModalBtn" class="bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 px-4 rounded-md transition-all duration-300 text-sm md:text-base btn-action">关闭</button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="js/monitor-info.js"></script>
</body>

</html>
