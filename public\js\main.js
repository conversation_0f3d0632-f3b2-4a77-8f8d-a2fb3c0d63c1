// 检测浏览器是否支持WebP
let supportWebP = false;
function checkWebPSupport() {
  return new Promise(resolve => {
    const webP = new Image();
    webP.onload = webP.onerror = function() {
      supportWebP = webP.height === 2;
      resolve(supportWebP);
    };
    webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
  });
}

// 创建通用的用户菜单
function createUserMenu() {
    // 检查是否已登录
    const token = localStorage.getItem('token');
    const username = localStorage.getItem('username');
    
    if (!token || !username) {
        console.log('用户未登录，跳过创建用户菜单');
        return;
    }
    
    // 检查页面上是否已经有用户菜单
    if (document.getElementById('userMenuContainer')) {
        return;
    }
    
    // 创建用户菜单容器
    const userMenuContainer = document.createElement('div');
    userMenuContainer.id = 'userMenuContainer';
    userMenuContainer.className = 'fixed top-0 right-0 bg-white shadow-md p-2 rounded-bl-lg z-50 flex items-center';
    
    // 解析token获取角色信息
    let role = 'user';
    try {
        const tokenParts = token.split('.');
        if (tokenParts.length === 3) {
            const payload = JSON.parse(atob(tokenParts[1]));
            role = payload.role || 'user';
        }
    } catch (e) {
        console.error('解析token失败:', e);
    }
    
    // 创建角色标识
    const roleBadge = document.createElement('span');
    roleBadge.className = role === 'admin' ? 
        'bg-red-100 text-red-800 text-xs font-semibold px-2 py-1 rounded mr-2' : 
        'bg-blue-100 text-blue-800 text-xs font-semibold px-2 py-1 rounded mr-2';
    roleBadge.textContent = role === 'admin' ? '管理员' : '普通用户';
    
    // 创建用户名显示
    const userDisplay = document.createElement('span');
    userDisplay.className = 'text-gray-700 mr-3';
    userDisplay.innerHTML = `<i class="fas fa-user mr-1"></i> ${username}`;
    
    // 创建退出按钮
    const logoutBtn = document.createElement('button');
    logoutBtn.className = 'text-gray-600 hover:text-red-600 transition-colors';
    logoutBtn.innerHTML = '<i class="fas fa-sign-out-alt"></i>';
    logoutBtn.title = '退出登录';
    logoutBtn.addEventListener('click', function() {
        // 确认退出登录
        if (confirm('确定要退出登录吗？')) {
            // 清除token和用户名
            localStorage.removeItem('token');
            localStorage.removeItem('username');
            
            // 清除cookie
            document.cookie = "token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
            
            // 显示退出成功信息
            alert('已成功退出登录');
            
            // 跳转到登录页面，带上当前页面作为重定向参数
            const currentUrl = encodeURIComponent(window.location.pathname + window.location.search);
            window.location.href = `/login.html?redirect=${currentUrl}`;
        }
    });
    
    // 组合菜单
    userMenuContainer.appendChild(roleBadge);
    userMenuContainer.appendChild(userDisplay);
    userMenuContainer.appendChild(logoutBtn);
    
    // 添加到页面
    document.body.appendChild(userMenuContainer);
}

// 页面加载时检测WebP支持
document.addEventListener('DOMContentLoaded', function() {
  checkWebPSupport();
});

// 添加token过期检查逻辑
function checkTokenExpiration() {
    // 登录页和客服问答页不需要检查
    if (window.location.pathname.endsWith('/login.html') || window.location.pathname.endsWith('/customer-service-qa.html')) {
        console.log('[DEBUG] 当前是登录页或客服问答页，跳过token检查');
        return;
    }
    
    const token = localStorage.getItem('token');
    if (!token) {
        console.log('[DEBUG] localStorage中没有token，需要重定向到登录页');
        return;
    }
    
    console.log('[DEBUG] 开始验证token');
    
    // 发起API请求验证token
    fetch('/api/validate-token', {
        headers: {
            'Authorization': `Bearer ${token}`
        }
    })
    .then(response => {
        console.log('[DEBUG] token验证响应状态:', response.status);
        
        if (!response.ok) {
            // token无效或过期，清除localStorage并重定向到登录页
            if (response.status === 401 || response.status === 403) {
                console.log('[DEBUG] token无效或过期，清除localStorage并重定向到登录页');
                localStorage.removeItem('token');
                localStorage.removeItem('username');
                const currentUrl = encodeURIComponent(window.location.pathname + window.location.search);
                window.location.href = `/login.html?redirect=${currentUrl}`;
            }
        } else {
            console.log('[DEBUG] token验证成功');
        }
    })
    .catch(error => {
        console.error('[DEBUG] 验证token时出错:', error);
    });
}

// 创建一个带授权的fetch函数，供所有页面使用
async function fetchWithAuth(url, options = {}) {
    const token = localStorage.getItem('token');
    
    // 区分API请求和页面访问
    const isApiRequest = url.includes('/api/');
    const isRamApiRequest = url.includes('/api/rams');
    const isCustomerServicePage = window.location.pathname.endsWith('/customer-service-qa.html');
    
    if (!token) {
        // 如果是RAM API请求或客服问答页面的API请求，则不需要token
        if (isRamApiRequest || isCustomerServicePage) {
            return fetch(url, options);
        }
        // 如果没有token，重定向到登录页
        const currentUrl = encodeURIComponent(window.location.pathname + window.location.search);
        window.location.href = `/login.html?redirect=${currentUrl}`;
        return null;
    }
    
    // 合并选项，确保headers存在
    const mergedOptions = {
        ...options,
        headers: {
            ...options.headers,
            'Authorization': `Bearer ${token}`
        }
    };
    
    try {
        const response = await fetch(url, mergedOptions);
        
        // 处理401或403错误
        if (response.status === 401 || response.status === 403) {
            // 如果是RAM API请求或客服问答页面的API请求，则不重定向
            if (isRamApiRequest || isCustomerServicePage) {
                return response;
            }
            localStorage.removeItem('token');
            localStorage.removeItem('username');
            const currentUrl = encodeURIComponent(window.location.pathname + window.location.search);
            window.location.href = `/login.html?redirect=${currentUrl}`;
            return null;
        }
        
        return response;
    } catch (error) {
        console.error('API请求失败:', error);
        throw error;
    }
}

// 每次页面加载时检查token状态
document.addEventListener('DOMContentLoaded', function() {
    console.log('[DEBUG] 页面加载完成，当前路径:', window.location.pathname);
    console.log('[DEBUG] localStorage中的token:', localStorage.getItem('token') ? '存在' : '不存在');
    console.log('[DEBUG] localStorage中的username:', localStorage.getItem('username'));
    
    // 客服问答页面不需要登录
    const isCustomerServicePage = window.location.pathname.endsWith('/customer-service-qa.html');
    const isLoginPage = window.location.pathname.endsWith('/login.html');
    
    // 创建用户菜单（除了登录页和客服问答页）
    if (!isLoginPage && !isCustomerServicePage) {
        createUserMenu();
    }
    
    if (isLoginPage || isCustomerServicePage) {
        console.log('[DEBUG] 当前是登录页或客服问答页，不需要验证登录状态');
        return;
    }

    // 检查token是否存在，如果不存在则重定向到登录页
    const token = localStorage.getItem('token');
    if (!token) {
        console.log('[DEBUG] 未找到token，重定向到登录页');
        const currentUrl = encodeURIComponent(window.location.pathname + window.location.search);
        window.location.href = `/login.html?redirect=${currentUrl}`;
        return;
    }
    
    // 检查token是否过期
    try {
        const tokenParts = token.split('.');
        if (tokenParts.length === 3) {
            const payload = JSON.parse(atob(tokenParts[1]));
            console.log('[DEBUG] token解析结果:', payload);
            
            if (payload.exp) {
                const expTime = payload.exp * 1000; // 转换为毫秒
                const now = Date.now();
                console.log('[DEBUG] token过期时间:', new Date(expTime).toLocaleString());
                console.log('[DEBUG] 当前时间:', new Date(now).toLocaleString());
                
                if (now > expTime) {
                    console.log('[DEBUG] token已过期，重定向到登录页');
                    localStorage.removeItem('token');
                    localStorage.removeItem('username');
                    const currentUrl = encodeURIComponent(window.location.pathname + window.location.search);
                    window.location.href = `/login.html?redirect=${currentUrl}`;
                    return;
                }
            }
        }
    } catch (e) {
        console.error('[DEBUG] 解析token失败:', e);
    }
    
    console.log('[DEBUG] 开始检查token有效性');
    checkTokenExpiration();
    
    // 显示用户角色信息
    displayUserRole();
});

// 显示用户角色信息
function displayUserRole() {
    const userRoleBadge = document.getElementById('userRoleBadge');
    const userRoleText = document.getElementById('userRoleText');
    
    if (!userRoleBadge || !userRoleText) return;
    
    // 获取用户角色信息
    fetch('/api/validate-token', {
        headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
    })
    .then(response => {
        if (!response.ok) throw new Error('Token验证失败');
        return response.json();
    })
    .then(data => {
        const username = localStorage.getItem('username') || '用户';
        const role = data.user?.role || 'user';
        
        // 调试输出
        console.log('用户角色信息:', {
            username,
            role,
            userData: data.user
        });
        
        // 更新角色显示
        if (role === 'admin') {
            console.log('检测到管理员角色，显示用户管理链接');
            userRoleBadge.classList.remove('hidden', 'bg-blue-100', 'text-blue-800');
            userRoleBadge.classList.add('bg-red-100', 'text-red-800');
            userRoleText.textContent = '管理员';
            
            // 显示用户管理链接（仅管理员可见）
            showUserManagementLink();
        } else {
            console.log('普通用户角色，隐藏用户管理链接');
            userRoleBadge.classList.remove('hidden', 'bg-red-100', 'text-red-800');
            userRoleBadge.classList.add('bg-blue-100', 'text-blue-800');
            userRoleText.textContent = '普通用户';
            
            // 确保用户管理链接隐藏
            hideUserManagementLink();
        }
        
        userRoleBadge.classList.remove('hidden');
    })
    .catch(error => {
        console.error('获取用户角色失败:', error);
    });
}

// 显示用户管理链接（仅管理员可见）
function showUserManagementLink() {
    console.log('用户管理链接已在HTML中直接添加，无需JavaScript控制显示');
}

// 隐藏用户管理链接
function hideUserManagementLink() {
    console.log('用户管理链接已在HTML中直接添加，无需JavaScript控制显示');
}

// 保留旧函数以兼容性，但改为调用新函数
function addUserManagementLink() {
    console.log('用户管理链接已在HTML中直接添加，无需JavaScript控制显示');
} 