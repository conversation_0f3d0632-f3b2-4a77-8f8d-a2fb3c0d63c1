<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="">
    <title>显卡信息管理 - 电脑配件综合管理系统</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="/js/js-pinyin.js"></script>
    <script src="/js/pinyin-wrapper.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/hammer.js/2.0.8/hammer.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/viewerjs/1.11.6/viewer.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/viewerjs/1.11.6/viewer.min.js"></script>
    <link rel="stylesheet" href="/css/gpu-info.css">
    <!-- 添加Viewer.js加载检查脚本 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('检查Viewer.js是否加载:', typeof Viewer !== 'undefined' ? '已加载' : '未加载');
            
            // 尝试修复可能的Viewer加载问题
            if (typeof Viewer === 'undefined') {
                console.warn('Viewer未定义，尝试重新加载Viewer.js...');
                const script = document.createElement('script');
                script.src = 'https://cdnjs.cloudflare.com/ajax/libs/viewerjs/1.11.6/viewer.min.js';
                script.onload = function() {
                    console.log('Viewer.js重新加载成功');
                    // 初始化全局变量以确保可用性
                    window.Viewer = Viewer;
                };
                script.onerror = function() {
                    console.error('Viewer.js重新加载失败');
                    alert('图片预览功能加载失败，部分功能可能无法正常使用');
                };
                document.head.appendChild(script);
            }
        });
    </script>
    <script src="/js/main.js"></script>
    <script src="/js/permission-helper.js"></script>
</head>

<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-2 sm:px-4 py-4 sm:py-8">
        <header class="mb-4 sm:mb-8">
            <h1 class="text-xl sm:text-3xl font-bold text-red-700 flex items-center">
                <i class="fas fa-desktop mr-2 sm:mr-3"></i> 显卡信息管理
            </h1>
            <div class="flex justify-between items-center">
                <div class="flex items-center">
                <p class="text-gray-600 mt-1 sm:mt-2 text-sm sm:text-base">记录和管理显卡配置信息</p>
                    <button id="darkModeToggle" class="ml-2 w-8 h-8 flex items-center justify-center rounded-full bg-gray-200 hover:bg-gray-300 text-gray-700" title="切换至暗夜模式">
                        <i class="fas fa-moon"></i>
                    </button>
                </div>
                <div class="mt-2 flex space-x-2">
                    <a href="/pc-components.html"
                        class="inline-block bg-red-600 text-white py-1 sm:py-2 px-3 sm:px-4 rounded-md hover:bg-red-700 text-sm sm:text-base">
                        <i class="fas fa-arrow-left mr-1"></i> 返回配件列表
                    </a>
                    <a href="/"
                        class="inline-block bg-red-600 text-white py-1 sm:py-2 px-3 sm:px-4 rounded-md hover:bg-red-700 text-sm sm:text-base">
                        <i class="fas fa-home mr-1"></i> 首页
                    </a>
                </div>
            </div>
        </header>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-4 sm:gap-6">
            <!-- 左侧表单区域 -->
            <div class="lg:col-span-1 bg-white p-3 sm:p-6 rounded-lg shadow-md">
                <h2 class="text-lg sm:text-xl font-semibold text-gray-800 mb-3 sm:mb-4 flex items-center">
                    <i class="fas fa-plus-circle mr-1 sm:mr-2 text-red-500"></i> 添加显卡信息
                </h2>

                <form id="gpuForm" class="space-y-3 sm:space-y-4">
                    <!-- 智能识别输入 -->
                    <div class="border border-gray-200 rounded-md p-3 mb-3">
                        <h3 class="text-md font-medium text-gray-700 mb-2">智能识别输入</h3>
                        <div class="bg-red-50 p-2 sm:p-3 rounded-lg border border-red-200">
                            <div class="flex gap-2 sm:flex-row flex-col">
                                <input type="text" id="smartInput"
                                    placeholder="粘贴显卡参数"
                                    class="flex-1 px-2 py-1 sm:px-3 sm:py-2 border rounded-md text-xs sm:text-sm focus:ring-2 focus:ring-red-500">
                                <button type="button" id="autoFillBtn"
                                    class="bg-red-600 text-white rounded-md hover:bg-red-700 text-xs sm:text-sm transition-colors whitespace-nowrap w-[90px] h-[30px] sm:w-[110px] sm:h-[38px] flex items-center justify-center">
                                    <i class="fas fa-robot fa-sm mr-1"></i><span>智能解析</span>
                                </button>
                            </div>
                            <p class="mt-2 text-xs text-red-600">支持格式：品牌 型号 芯片组 显存 位宽 功耗 长度 价格</p>
                        </div>
                    </div>
                    
                    <!-- 1.显卡核心信息 -->
                    <div class="border border-gray-200 rounded-md p-3">
                        <h3 class="text-md font-medium text-gray-700 mb-2">显卡核心信息</h3>

                        <div class="space-y-3">
                            <div>
                                <label for="model" class="block text-sm font-medium text-gray-700 mb-1">显卡型号
                                    <span class="text-red-500">*</span></label>
                                <input type="text" id="model"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 text-sm sm:text-base"
                                    placeholder="如: RTX 4070 SUPER" required>
                            </div>

                            <div>
                                <label for="brand" class="block text-sm font-medium text-gray-700 mb-1">品牌 <span
                                        class="text-red-500">*</span></label>
                                <input type="text" id="brand"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 text-sm sm:text-base"
                                    placeholder="如: NVIDIA RTX 4070, AMD RX 7800XT" required>
                            </div>

                            <div>
                                <label for="chipset" class="block text-sm font-medium text-gray-700 mb-1">芯片组 <span
                                        class="text-red-500">*</span></label>
                                <input type="text" id="chipset"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 text-sm sm:text-base"
                                    placeholder="如: NVIDIA RTX 4070, AMD RX 7800XT" required>
                            </div>
                        </div>
                    </div>

                    <!-- 2.显存和性能 -->
                    <div class="border border-gray-200 rounded-md p-3">
                        <h3 class="text-md font-medium text-gray-700 mb-2">显存和性能</h3>

                        <div class="space-y-3">
                            <div>
                                <label for="memorySize" class="block text-sm font-medium text-gray-700 mb-1">显存大小
                                    (GB)</label>
                                <input type="number" id="memorySize"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 text-sm sm:text-base"
                                    placeholder="如: 12">
                            </div>

                            <div>
                                <label for="memoryType"
                                    class="block text-sm font-medium text-gray-700 mb-1">显存类型</label>
                                <select id="memoryType"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 text-sm sm:text-base">
                                    <option value="">选择显存类型</option>
                                    <option value="GDDR7">GDDR7</option>
                                    <option value="GDDR6X">GDDR6X</option>
                                    <option value="GDDR6">GDDR6</option>
                                    <option value="GDDR5X">GDDR5X</option>
                                    <option value="GDDR5">GDDR5</option>
                                    <option value="HBM3">HBM3</option>
                                    <option value="HBM2">HBM2</option>
                                    <option value="HBM">HBM</option>
                                    <option value="其他">其他</option>
                                </select>
                            </div>

                            <div>
                                <label for="memoryBus" class="block text-sm font-medium text-gray-700 mb-1">位宽</label>
                                <input type="number" id="memoryBus"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 text-sm sm:text-base"
                                    placeholder="如: 192, 256, 384">
                            </div>

                            <div>
                                <label for="coreClock" class="block text-sm font-medium text-gray-700 mb-1">核心频率
                                    (MHz)</label>
                                <input type="number" id="coreClock"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 text-sm sm:text-base"
                                    placeholder="如: 1785">
                            </div>

                            <div>
                                <label for="boostClock" class="block text-sm font-medium text-gray-700 mb-1">加速频率
                                    (MHz)</label>
                                <input type="number" id="boostClock"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 text-sm sm:text-base"
                                    placeholder="如: 2610">
                            </div>
                        </div>
                    </div>

                    <!-- 3.物理规格和功耗 -->
                    <div class="border border-gray-200 rounded-md p-3">
                        <h3 class="text-md font-medium text-gray-700 mb-2">物理规格和功耗</h3>

                        <div class="space-y-3">
                            <div>
                                <label for="tdp" class="block text-sm font-medium text-gray-700 mb-1">功耗 (W)</label>
                                <input type="number" id="tdp"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 text-sm sm:text-base"
                                    placeholder="如: 200">
                            </div>

                            <div>
                                <label for="dimensions" class="block text-sm font-medium text-gray-700 mb-1">尺寸</label>
                                <input type="text" id="dimensions"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 text-sm sm:text-base"
                                    placeholder="如: 189 × 109 × 42">
                            </div>

                            <div>
                                <label for="powerConnectors"
                                    class="block text-sm font-medium text-gray-700 mb-1">电源接口</label>
                                <input type="text" id="powerConnectors"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 text-sm sm:text-base"
                                    placeholder="如: 8-pin x2, 12VHPWR">
                            </div>

                            <div>
                                <label for="recommendedPsu" class="block text-sm font-medium text-gray-700 mb-1">推荐电源
                                    (W)</label>
                                <input type="number" id="recommendedPsu"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 text-sm sm:text-base"
                                    placeholder="如: 650">
                            </div>
                        </div>
                    </div>

                    <!-- 4.接口和外观 -->
                    <div class="border border-gray-200 rounded-md p-3">
                        <h3 class="text-md font-medium text-gray-700 mb-2">接口和外观</h3>

                        <div class="space-y-3">
                            <div>
                                <label for="displayPorts"
                                    class="block text-sm font-medium text-gray-700 mb-1">DisplayPort数量</label>
                                <input type="number" id="displayPorts"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 text-sm sm:text-base"
                                    placeholder="如: 3">
                            </div>

                            <div>
                                <label for="hdmiPorts"
                                    class="block text-sm font-medium text-gray-700 mb-1">HDMI接口数量</label>
                                <input type="number" id="hdmiPorts"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 text-sm sm:text-base"
                                    placeholder="如: 1">
                            </div>
                        </div>
                    </div>

                    <!-- 5.价格与备注 -->
                    <div class="border border-gray-200 rounded-md p-3">
                        <h3 class="text-md font-medium text-gray-700 mb-2">价格与备注</h3>

                        <div class="space-y-3">
                            <div>
                                <label for="price" class="block text-sm font-medium text-gray-700 mb-1">价格</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 sm:text-sm">¥</span>
                                    </div>
                                    <input type="number" id="price"
                                        class="w-full pl-7 px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 text-sm sm:text-base"
                                        placeholder="0.00" min="0" step="0.01">
                                </div>
                            </div>

                            <div>
                                <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">备注</label>
                                <textarea id="notes" rows="3"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 text-sm sm:text-base"
                                    placeholder="其他需要备注的信息..."></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- 6.图片上传 -->
                    <div class="border border-gray-200 rounded-md p-3">
                        <h3 class="text-md font-medium text-gray-700 mb-2">图片上传</h3>

                        <div
                            class="mt-1 flex justify-center px-4 sm:px-6 pt-3 sm:pt-5 pb-4 sm:pb-6 border-2 border-gray-300 border-dashed rounded-md">
                            <div class="space-y-1 text-center">
                                <div class="flex text-sm text-gray-600 items-center justify-center flex-wrap">
                                    <label for="gpuImage"
                                        class="relative cursor-pointer bg-white rounded-md font-medium text-red-600 hover:text-red-500 focus-within:outline-none">
                                        <span>上传图片</span>
                                        <input id="gpuImage" name="gpuImage" type="file" accept="image/*"
                                            class="sr-only">
                                    </label>
                                    <p class="pl-1 mobile-hidden">或将图片拖拽到此处</p>
                                </div>
                                <p class="text-xs text-gray-500">PNG, JPG, GIF 格式，大小不超过5MB（将自动转换为WebP格式以优化加载速度）</p>
                                <p class="text-xs text-gray-500 mt-1">不上传图片时将使用默认图片</p>
                            </div>
                        </div>

                        <!-- 上传进度条 -->
                        <div id="uploadProgressContainer" class="mt-2 hidden">
                            <div class="bg-gray-200 dark:bg-gray-700 rounded-full h-3 mb-2 overflow-hidden">
                                <div id="uploadProgressBar" class="bg-gradient-to-r from-green-500 to-green-600 dark:from-green-400 dark:to-green-500 h-3 rounded-full transition-all duration-500 ease-out relative" style="width: 0%">
                                    <!-- 进度条光泽效果 -->
                                    <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-20 animate-pulse"></div>
                                </div>
                            </div>
                            <div class="flex justify-between items-center text-xs">
                                <span id="uploadProgressText" class="text-gray-600 dark:text-gray-400 flex items-center">
                                    <i id="uploadProgressIcon" class="fas fa-upload mr-1"></i>
                                    准备上传...
                                </span>
                                <span id="uploadProgressPercent" class="text-green-600 dark:text-green-400 font-medium">0%</span>
                            </div>
                        </div>

                        <div id="imagePreviewContainer" class="mt-2 hidden">
                            <div class="relative inline-block">
                                <img id="imagePreview" src="#" alt="预览图"
                                    class="h-24 sm:h-32 rounded-md shadow image-preview">
                                <button type="button" id="removeImageBtn"
                                    class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 sm:w-6 sm:h-6 flex items-center justify-center hover:bg-red-600">
                                    <i class="fas fa-times text-xs"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div>
                        <button type="submit"
                            class="w-full bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-md">
                            <i class="fas fa-save mr-1"></i> 保存显卡信息
                        </button>
                    </div>
                </form>
            </div>

            <!-- 右侧列表区域 -->
            <div class="lg:col-span-3">
                <!-- 搜索和过滤 -->
                <div class="bg-white p-3 sm:p-6 rounded-lg shadow-md mb-4 sm:mb-6">
                    <h2 class="text-xl font-semibold text-red-800 mb-2 flex items-center">
                        <i class="fas fa-desktop mr-2 sm:mr-3"></i> 显卡列表
                        <div id="loadingIndicator" class="hidden ml-2">
                            <svg class="animate-spin h-5 w-5 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                        </div>
                    </h2>
                    <div class="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4">
                        <!-- 搜索框 -->
                        <div class="w-full sm:flex-1">
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-search text-gray-400"></i>
                                </div>
                                <input type="text" id="gpuSearch" placeholder="搜索显卡型号、品牌等..."
                                    class="w-full py-2 pl-10 pr-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500">
                            </div>
                        </div>

                        <!-- 筛选器 -->
                        <div class="flex flex-wrap gap-2 sm:gap-4 items-center sm:flex-none">
                            <select id="brandFilter"
                                class="flex-1 sm:flex-none min-w-[85px] max-w-[120px] sm:max-w-none border border-gray-300 rounded-md shadow-sm py-2 pl-3 pr-6 focus:outline-none focus:ring-red-500 focus:border-red-500 text-sm truncate">
                                <option value="all">所有品牌</option>
                                <!-- 品牌列表将通过JavaScript动态填充 -->
                            </select>

                            <select id="chipsetFilter"
                                class="flex-1 sm:flex-none min-w-[95px] max-w-[130px] sm:max-w-none border border-gray-300 rounded-md shadow-sm py-2 pl-3 pr-6 focus:outline-none focus:ring-red-500 focus:border-red-500 text-sm truncate">
                                <option value="all">所有芯片组</option>
                                <!-- 芯片组列表将通过JavaScript动态填充 -->
                            </select>

                            <button id="resetFilterBtn"
                                class="flex-none bg-red-600 hover:bg-red-700 text-white py-2 px-3 sm:px-4 rounded-md whitespace-nowrap text-sm min-w-[65px]">
                                <i class="fas fa-sync-alt mr-1"></i> 重置
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 显卡列表 -->
                <div class="bg-white rounded-lg shadow-md">
                    <div class="overflow-x-auto sm:mx-0">
                        <div class="inline-block min-w-full align-middle">
                            <table class="min-w-full divide-y divide-gray-200 table-compact sm:table-auto table-modern">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col"
                                            class="px-2 py-2 sm:px-4 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider model-column">
                                        显卡型号
                                    </th>
                                    <th scope="col"
                                            class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell">
                                        品牌
                                    </th>
                                    <th scope="col"
                                            class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell">
                                        芯片组
                                    </th>
                                    <th scope="col"
                                            class="px-2 py-2 sm:px-4 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider memory-column">
                                        显存
                                    </th>
                                    <th scope="col"
                                            class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell">
                                        功耗
                                    </th>
                                    <th scope="col"
                                            class="px-2 py-2 sm:px-4 sm:py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider action-column">
                                        操作
                                    </th>
                                </tr>
                            </thead>
                            <tbody id="gpuTableBody" class="bg-white divide-y divide-gray-200">
                                <!-- 数据将通过JavaScript填充 -->
                                <tr>
                                        <td colspan="6" class="px-4 py-4 text-center text-sm text-gray-500">
                                        加载中...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        </div>
                    </div>

                    <!-- 分页控件 -->
                    <div class="mt-4 px-4 py-4 border-t border-gray-200 sm:px-6 flex flex-wrap justify-between items-center pagination-mobile">
                        <div class="text-sm text-gray-700 mb-2 sm:mb-0" id="totalCount">
                            共 0 条记录
                        </div>
                        <div class="flex items-center space-x-1 sm:space-x-2 page-controls-mobile">
                            <button id="firstPage" title="首页"
                                class="px-2 py-1 border border-gray-300 rounded-md text-sm bg-white text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                <i class="fas fa-angle-double-left"></i>
                            </button>
                            <button id="prevPage" title="上一页"
                                class="px-2 py-1 border border-gray-300 rounded-md text-sm bg-white text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            
                            <div id="pageNumbers" class="hidden sm:flex space-x-1">
                                <!-- 页码将通过JavaScript填充 -->
                            </div>
                            
                            <span id="pageInfo" class="px-2 py-1 text-sm whitespace-nowrap">第 <span id="currentPageDisplay">1</span> / <span id="totalPagesDisplay">1</span> 页</span>
                            
                            <button id="nextPage" title="下一页"
                                class="px-2 py-1 border border-gray-300 rounded-md text-sm bg-white text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                            <button id="lastPage" title="尾页"
                                class="px-2 py-1 border border-gray-300 rounded-md text-sm bg-white text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                <i class="fas fa-angle-double-right"></i>
                            </button>
                            
                            <div class="hidden sm:flex items-center ml-2">
                                <span class="text-sm">跳转到</span>
                                <input type="number" id="pageJump" min="1" class="w-12 ml-1 px-2 py-1 border border-gray-300 rounded-md text-sm">
                                <button id="goToPage" class="ml-1 px-2 py-1 bg-red-600 text-white text-sm rounded-md hover:bg-red-700">
                                    确定
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 显卡详情模态框 -->
        <div id="gpuModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
            <div class="bg-white rounded-lg shadow-xl p-4 sm:p-6 max-w-4xl w-full max-h-[90vh] overflow-y-auto">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg sm:text-xl font-semibold text-gray-800">显卡详情</h3>
                    <button id="closeModal" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div id="gpuDetails" class="space-y-4">
                    <!-- 详情会动态填充 -->
                </div>

                <div class="mt-6 flex justify-end space-x-2">
                    <button id="editBtn"
                        class="px-3 py-1 sm:px-4 sm:py-2 bg-red-600 text-white rounded-md hover:bg-red-700 text-sm sm:text-base">
                        <i class="fas fa-edit mr-1"></i> 编辑
                    </button>
                    <button id="deleteBtn"
                        class="px-3 py-1 sm:px-4 sm:py-2 bg-red-600 text-white rounded-md hover:bg-red-700 text-sm sm:text-base">
                        <i class="fas fa-trash mr-1"></i> 删除
                    </button>
                    <button id="closeModalBtn"
                        class="px-3 py-1 sm:px-4 sm:py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 text-sm sm:text-base">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="/js/gpu-info.js"></script>

    <script src="js/upload-progress.js"></script>
</body>

</html>