const db = require('../config/db');

/**
 * 创建收入管理系统相关的数据库表
 */
async function createRevenueSchema() {
    try {
        console.log('开始创建收入管理系统数据库表...');

        // 创建收入记录表
        await db.query(`
            CREATE TABLE IF NOT EXISTS revenue_records (
                id INT PRIMARY KEY AUTO_INCREMENT,
                shop_name VARCHAR(100) NOT NULL COMMENT '店铺名称',
                order_number VARCHAR(100) NOT NULL UNIQUE COMMENT '订单号，唯一标识',
                product_info TEXT NOT NULL COMMENT '产品信息，格式为"创收金额+具体型号"',
                image_url VARCHAR(500) COMMENT '图片URL，可选',
                revenue_notes TEXT COMMENT '创收备注，用于记录额外说明',
                total_revenue DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '创收金额合计',
                user_id INT NOT NULL COMMENT '关联用户表',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                INDEX idx_shop_name (shop_name),
                INDEX idx_order_number (order_number),
                INDEX idx_user_id (user_id),
                INDEX idx_created_at (created_at),
                INDEX idx_updated_at (updated_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='收入记录表'
        `);
        console.log('创建revenue_records表成功');

        console.log('收入管理系统数据库表创建完成！');

    } catch (error) {
        console.error('创建收入管理系统数据库表失败:', error);
        throw error;
    }
}

/**
 * 检查收入管理系统数据库表是否存在
 */
async function checkRevenueSchema() {
    try {
        const tables = ['revenue_records'];
        const existingTables = [];
        const missingTables = [];

        for (const table of tables) {
            const [rows] = await db.query(`SHOW TABLES LIKE '${table}'`);
            if (rows.length > 0) {
                existingTables.push(table);
            } else {
                missingTables.push(table);
            }
        }

        return {
            allExist: missingTables.length === 0,
            existingTables,
            missingTables
        };
    } catch (error) {
        console.error('检查收入管理系统数据库表失败:', error);
        return {
            allExist: false,
            existingTables: [],
            missingTables: tables
        };
    }
}

/**
 * 删除收入管理系统数据库表（用于开发测试）
 */
async function dropRevenueSchema() {
    try {
        console.log('开始删除收入管理系统数据库表...');
        
        // 删除表（注意外键约束，需要按顺序删除）
        await db.query('DROP TABLE IF EXISTS revenue_records');
        console.log('删除revenue_records表成功');

        console.log('收入管理系统数据库表删除完成！');
        
    } catch (error) {
        console.error('删除收入管理系统数据库表失败:', error);
        throw error;
    }
}

// 如果直接运行此文件，则执行创建
if (require.main === module) {
    createRevenueSchema()
        .then(() => {
            console.log('收入管理系统数据库表创建成功完成');
            process.exit(0);
        })
        .catch(error => {
            console.error('收入管理系统数据库表创建失败:', error);
            process.exit(1);
        });
}

module.exports = {
    createRevenueSchema,
    checkRevenueSchema,
    dropRevenueSchema
};
