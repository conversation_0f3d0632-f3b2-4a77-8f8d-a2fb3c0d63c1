const express = require('express');
const router = express.Router();
const db = require('../config/db');
const { verifyToken, checkAdminRole, checkReadPermission } = require('../middleware/auth');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const sharp = require('sharp');

// =======================================
// 文件上传配置
// =======================================

// 确保上传目录存在
const uploadDir = path.join(__dirname, '..', 'uploads', 'notes');
if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
}

// 配置multer
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, uploadDir);
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
    }
});

const upload = multer({
    storage: storage,
    limits: {
        fileSize: 5 * 1024 * 1024 // 5MB限制
    },
    fileFilter: function (req, file, cb) {
        // 只允许图片文件
        if (file.mimetype.startsWith('image/')) {
            cb(null, true);
        } else {
            cb(new Error('只允许上传图片文件'), false);
        }
    }
});

// 图片处理函数
async function processImage(file) {
    if (!file) return null;

    try {
        const webpFilename = `${path.basename(file.filename, path.extname(file.filename))}.webp`;
        const webpPath = path.join(uploadDir, webpFilename);

        // 只转换格式，不调整尺寸，使用无损压缩
        await sharp(file.path)
            .webp({ quality: 100, lossless: true })
            .toFile(webpPath);

        // 删除原图
        fs.unlinkSync(file.path);

        return `/uploads/notes/${webpFilename}`;
    } catch (error) {
        console.error('图片处理失败:', error);
        // 如果转换失败，返回原始图片路径
        return `/uploads/notes/${file.filename}`;
    }
}

// =======================================
// 笔记分类管理 API
// =======================================

// 获取所有分类（所有用户可访问）
router.get('/categories', checkReadPermission, async (req, res) => {
    try {
        const [categories] = await db.query(`
            SELECT c.*, COUNT(n.id) as note_count 
            FROM note_categories c 
            LEFT JOIN notes n ON c.id = n.category_id 
            GROUP BY c.id 
            ORDER BY c.name
        `);
        
        res.json({
            success: true,
            data: categories
        });
    } catch (error) {
        console.error('获取分类失败:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});

// 创建分类（仅管理员）
router.post('/categories', checkAdminRole, async (req, res) => {
    try {
        const { name, description, color, icon } = req.body;
        
        if (!name) {
            return res.status(400).json({ error: '分类名称不能为空' });
        }
        
        const [result] = await db.query(
            'INSERT INTO note_categories (name, description, color, icon) VALUES (?, ?, ?, ?)',
            [name, description || null, color || '#3B82F6', icon || 'fas fa-folder']
        );
        
        res.status(201).json({
            success: true,
            message: '分类创建成功',
            categoryId: result.insertId
        });
    } catch (error) {
        if (error.code === 'ER_DUP_ENTRY') {
            return res.status(400).json({ error: '分类名称已存在' });
        }
        console.error('创建分类失败:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});

// 更新分类（仅管理员）
router.put('/categories/:id', checkAdminRole, async (req, res) => {
    try {
        const { name, description, color, icon } = req.body;
        const categoryId = req.params.id;
        
        if (!name) {
            return res.status(400).json({ error: '分类名称不能为空' });
        }
        
        const [result] = await db.query(
            'UPDATE note_categories SET name = ?, description = ?, color = ?, icon = ? WHERE id = ?',
            [name, description || null, color || '#3B82F6', icon || 'fas fa-folder', categoryId]
        );
        
        if (result.affectedRows === 0) {
            return res.status(404).json({ error: '分类不存在' });
        }
        
        res.json({
            success: true,
            message: '分类更新成功'
        });
    } catch (error) {
        if (error.code === 'ER_DUP_ENTRY') {
            return res.status(400).json({ error: '分类名称已存在' });
        }
        console.error('更新分类失败:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});

// 删除分类（仅管理员）
router.delete('/categories/:id', checkAdminRole, async (req, res) => {
    try {
        const categoryId = req.params.id;
        
        // 检查是否有笔记使用此分类
        const [notes] = await db.query('SELECT COUNT(*) as count FROM notes WHERE category_id = ?', [categoryId]);
        
        if (notes[0].count > 0) {
            return res.status(400).json({ 
                error: '无法删除分类，该分类下还有笔记',
                noteCount: notes[0].count
            });
        }
        
        const [result] = await db.query('DELETE FROM note_categories WHERE id = ?', [categoryId]);
        
        if (result.affectedRows === 0) {
            return res.status(404).json({ error: '分类不存在' });
        }
        
        res.json({
            success: true,
            message: '分类删除成功'
        });
    } catch (error) {
        console.error('删除分类失败:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});

// =======================================
// 标签管理 API
// =======================================

// 获取所有标签（所有用户可访问）
router.get('/tags', checkReadPermission, async (req, res) => {
    try {
        const [tags] = await db.query(`
            SELECT t.*, COUNT(ntr.note_id) as usage_count 
            FROM note_tags t 
            LEFT JOIN note_tag_relations ntr ON t.id = ntr.tag_id 
            GROUP BY t.id 
            ORDER BY usage_count DESC, t.name
        `);
        
        res.json({
            success: true,
            data: tags
        });
    } catch (error) {
        console.error('获取标签失败:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});

// 创建标签（仅管理员）
router.post('/tags', checkAdminRole, async (req, res) => {
    try {
        const { name, color } = req.body;
        
        if (!name) {
            return res.status(400).json({ error: '标签名称不能为空' });
        }
        
        const [result] = await db.query(
            'INSERT INTO note_tags (name, color) VALUES (?, ?)',
            [name, color || '#6B7280']
        );
        
        res.status(201).json({
            success: true,
            message: '标签创建成功',
            tagId: result.insertId
        });
    } catch (error) {
        if (error.code === 'ER_DUP_ENTRY') {
            return res.status(400).json({ error: '标签名称已存在' });
        }
        console.error('创建标签失败:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});

// 更新标签（仅管理员）
router.put('/tags/:id', checkAdminRole, async (req, res) => {
    try {
        const { name, color } = req.body;
        const tagId = req.params.id;
        
        if (!name) {
            return res.status(400).json({ error: '标签名称不能为空' });
        }
        
        const [result] = await db.query(
            'UPDATE note_tags SET name = ?, color = ? WHERE id = ?',
            [name, color || '#6B7280', tagId]
        );
        
        if (result.affectedRows === 0) {
            return res.status(404).json({ error: '标签不存在' });
        }
        
        res.json({
            success: true,
            message: '标签更新成功'
        });
    } catch (error) {
        if (error.code === 'ER_DUP_ENTRY') {
            return res.status(400).json({ error: '标签名称已存在' });
        }
        console.error('更新标签失败:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});

// 删除标签（仅管理员）
router.delete('/tags/:id', checkAdminRole, async (req, res) => {
    try {
        const tagId = req.params.id;
        
        // 先删除关联关系
        await db.query('DELETE FROM note_tag_relations WHERE tag_id = ?', [tagId]);
        
        // 再删除标签
        const [result] = await db.query('DELETE FROM note_tags WHERE id = ?', [tagId]);
        
        if (result.affectedRows === 0) {
            return res.status(404).json({ error: '标签不存在' });
        }
        
        res.json({
            success: true,
            message: '标签删除成功'
        });
    } catch (error) {
        console.error('删除标签失败:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});

// =======================================
// 笔记管理 API
// =======================================

// 获取笔记列表（支持搜索、分页、过滤）
router.get('/', checkReadPermission, async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 20;
        const offset = (page - 1) * limit;
        const search = req.query.search || '';
        const categoryId = req.query.category_id || '';
        const tagId = req.query.tag_id || '';
        const authorId = req.query.author_id || '';
        const isPinned = req.query.is_pinned || '';
        const sortBy = req.query.sort_by || 'updated_at';
        const sortOrder = req.query.sort_order || 'DESC';
        
        // 构建查询条件
        let whereClause = 'WHERE 1=1';
        let params = [];
        
        if (search) {
            whereClause += ' AND (MATCH(n.title, n.content) AGAINST(? IN NATURAL LANGUAGE MODE) OR n.title LIKE ? OR n.content LIKE ?)';
            params.push(search, `%${search}%`, `%${search}%`);
        }
        
        if (categoryId) {
            whereClause += ' AND n.category_id = ?';
            params.push(categoryId);
        }
        
        if (authorId) {
            whereClause += ' AND n.author_id = ?';
            params.push(authorId);
        }
        
        if (isPinned !== '') {
            whereClause += ' AND n.is_pinned = ?';
            params.push(isPinned === 'true' ? 1 : 0);
        }
        
        if (tagId) {
            whereClause += ' AND n.id IN (SELECT note_id FROM note_tag_relations WHERE tag_id = ?)';
            params.push(tagId);
        }
        
        // 验证排序字段
        const allowedSortFields = ['title', 'created_at', 'updated_at', 'view_count'];
        const validSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'updated_at';
        const validSortOrder = ['ASC', 'DESC'].includes(sortOrder.toUpperCase()) ? sortOrder.toUpperCase() : 'DESC';
        
        // 获取笔记列表
        const [notes] = await db.query(`
            SELECT 
                n.id, n.title, n.content, n.is_public, n.is_pinned, n.view_count,
                n.created_at, n.updated_at,
                c.name as category_name, c.color as category_color, c.icon as category_icon,
                u.username as author_name,
                GROUP_CONCAT(DISTINCT CONCAT(t.id, ':', t.name, ':', t.color) SEPARATOR '|') as tags
            FROM notes n
            LEFT JOIN note_categories c ON n.category_id = c.id
            LEFT JOIN users u ON n.author_id = u.id
            LEFT JOIN note_tag_relations ntr ON n.id = ntr.note_id
            LEFT JOIN note_tags t ON ntr.tag_id = t.id
            ${whereClause}
            GROUP BY n.id
            ORDER BY n.is_pinned DESC, n.${validSortBy} ${validSortOrder}
            LIMIT ? OFFSET ?
        `, [...params, limit, offset]);
        
        // 获取总数
        const [countResult] = await db.query(`
            SELECT COUNT(DISTINCT n.id) as total
            FROM notes n
            LEFT JOIN note_categories c ON n.category_id = c.id
            LEFT JOIN users u ON n.author_id = u.id
            LEFT JOIN note_tag_relations ntr ON n.id = ntr.note_id
            LEFT JOIN note_tags t ON ntr.tag_id = t.id
            ${whereClause}
        `, params);
        
        const total = countResult[0].total;
        
        // 处理标签数据
        const processedNotes = notes.map(note => ({
            ...note,
            content: note.content ? note.content.substring(0, 200) + (note.content.length > 200 ? '...' : '') : '',
            tags: note.tags ? note.tags.split('|').map(tag => {
                const [id, name, color] = tag.split(':');
                return { id: parseInt(id), name, color };
            }) : []
        }));
        
        res.json({
            success: true,
            data: processedNotes,
            pagination: {
                total,
                page,
                limit,
                pages: Math.ceil(total / limit)
            }
        });
    } catch (error) {
        console.error('获取笔记列表失败:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});

// 获取单个笔记详情
router.get('/:id', checkReadPermission, async (req, res) => {
    try {
        const noteId = req.params.id;

        // 获取笔记详情
        const [notes] = await db.query(`
            SELECT
                n.id, n.title, n.content, n.is_public, n.is_pinned, n.view_count,
                n.created_at, n.updated_at,
                c.id as category_id, c.name as category_name, c.color as category_color, c.icon as category_icon,
                u.id as author_id, u.username as author_name
            FROM notes n
            LEFT JOIN note_categories c ON n.category_id = c.id
            LEFT JOIN users u ON n.author_id = u.id
            WHERE n.id = ?
        `, [noteId]);

        if (notes.length === 0) {
            return res.status(404).json({ error: '笔记不存在' });
        }

        const note = notes[0];

        // 获取笔记标签
        const [tags] = await db.query(`
            SELECT t.id, t.name, t.color
            FROM note_tags t
            JOIN note_tag_relations ntr ON t.id = ntr.tag_id
            WHERE ntr.note_id = ?
            ORDER BY t.name
        `, [noteId]);

        note.tags = tags;

        // 获取笔记附件（图片）
        const [attachments] = await db.query(`
            SELECT id, file_name, file_path, file_type, file_size, created_at
            FROM note_attachments
            WHERE note_id = ?
            ORDER BY created_at
        `, [noteId]);

        note.attachments = attachments;
        // 为了兼容前端，也提供images字段
        note.images = attachments.filter(att => att.file_type.startsWith('image/')).map(att => att.file_path);

        // 记录访问日志（异步，不影响响应）
        const userId = req.user ? req.user.id : null;
        const ipAddress = req.ip || req.connection.remoteAddress;
        const userAgent = req.get('User-Agent');

        db.query(
            'INSERT INTO note_access_logs (note_id, user_id, ip_address, user_agent) VALUES (?, ?, ?, ?)',
            [noteId, userId, ipAddress, userAgent]
        ).catch(err => console.error('记录访问日志失败:', err));

        // 更新浏览次数（异步）
        db.query('UPDATE notes SET view_count = view_count + 1 WHERE id = ?', [noteId])
            .catch(err => console.error('更新浏览次数失败:', err));

        res.json({
            success: true,
            data: note
        });
    } catch (error) {
        console.error('获取笔记详情失败:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});

// 创建笔记（所有登录用户）
router.post('/', checkReadPermission, upload.array('images', 10), async (req, res) => {
    try {
        const { title, content, category_id, is_public, is_pinned } = req.body;
        const authorId = req.user.id;
        const isAdmin = req.user.role === 'admin';

        // 处理tag_ids数组（FormData中可能是字符串数组）
        let tag_ids = req.body['tag_ids[]'] || req.body.tag_ids || [];
        if (typeof tag_ids === 'string') {
            tag_ids = [tag_ids];
        }
        tag_ids = tag_ids.filter(id => id && !isNaN(id)).map(id => parseInt(id));

        if (!title) {
            return res.status(400).json({ error: '笔记标题不能为空' });
        }
        
        // 非管理员不能置顶笔记
        const shouldPin = is_pinned === true && isAdmin;

        // 开始事务
        await db.query('START TRANSACTION');

        try {
            // 插入笔记
            const [result] = await db.query(
                'INSERT INTO notes (title, content, category_id, author_id, is_public, is_pinned) VALUES (?, ?, ?, ?, ?, ?)',
                [title, content || '', category_id || null, authorId, is_public !== false, shouldPin]
            );

            const noteId = result.insertId;

            // 添加标签关联
            if (tag_ids && Array.isArray(tag_ids) && tag_ids.length > 0) {
                const tagValues = tag_ids.map(tagId => [noteId, tagId]);
                await db.query(
                    'INSERT INTO note_tag_relations (note_id, tag_id) VALUES ?',
                    [tagValues]
                );
            }

            // 处理上传的图片
            if (req.files && req.files.length > 0) {
                for (const file of req.files) {
                    try {
                        const filePath = await processImage(file);

                        // 保存附件信息到数据库
                        await db.query(
                            'INSERT INTO note_attachments (note_id, file_name, file_path, file_type, file_size) VALUES (?, ?, ?, ?, ?)',
                            [noteId, file.originalname, filePath, file.mimetype, file.size]
                        );
                    } catch (error) {
                        console.error('处理图片失败:', error);
                        // 继续处理其他图片，不中断整个流程
                    }
                }
            }

            await db.query('COMMIT');

            res.status(201).json({
                success: true,
                message: '笔记创建成功',
                noteId: noteId
            });
        } catch (error) {
            await db.query('ROLLBACK');
            throw error;
        }
    } catch (error) {
        console.error('创建笔记失败:', error);
        if (error.code === 'ER_NO_REFERENCED_ROW_2') {
            return res.status(400).json({ error: '分类或标签不存在' });
        }
        res.status(500).json({ error: '服务器错误' });
    }
});

// 更新笔记（所有登录用户可以更新自己的笔记，管理员可以更新所有）- 支持图片上传
router.put('/:id', checkReadPermission, upload.array('images', 10), async (req, res) => {
    try {
        const noteId = req.params.id;
        const { title, content, category_id, is_public, is_pinned } = req.body;
        const currentUserId = req.user.id;
        const isAdmin = req.user.role === 'admin';

        // 处理tag_ids数组（FormData中可能是字符串数组）
        let tag_ids = req.body['tag_ids[]'] || req.body.tag_ids || [];
        if (typeof tag_ids === 'string') {
            tag_ids = [tag_ids];
        }
        tag_ids = tag_ids.filter(id => id && !isNaN(id)).map(id => parseInt(id));

        if (!title) {
            return res.status(400).json({ error: '笔记标题不能为空' });
        }

        // 检查笔记是否存在和权限
        const [existingNotes] = await db.query('SELECT id, author_id FROM notes WHERE id = ?', [noteId]);
        if (existingNotes.length === 0) {
            return res.status(404).json({ error: '笔记不存在' });
        }
        
        // 检查是否有权限修改（管理员或笔记作者）
        const isOwner = String(existingNotes[0].author_id) === String(currentUserId);
        if (!isAdmin && !isOwner) {
            return res.status(403).json({ error: '没有权限修改此笔记' });
        }
        
        // 非管理员不能置顶笔记
        const shouldPin = is_pinned === true && isAdmin;

        // 开始事务
        await db.query('START TRANSACTION');

        try {
            // 更新笔记
            await db.query(
                'UPDATE notes SET title = ?, content = ?, category_id = ?, is_public = ?, is_pinned = ? WHERE id = ?',
                [title, content || '', category_id || null, is_public !== false, shouldPin, noteId]
            );

            // 删除现有标签关联
            await db.query('DELETE FROM note_tag_relations WHERE note_id = ?', [noteId]);

            // 添加新的标签关联
            if (tag_ids && Array.isArray(tag_ids) && tag_ids.length > 0) {
                const tagValues = tag_ids.map(tagId => [noteId, tagId]);
                await db.query(
                    'INSERT INTO note_tag_relations (note_id, tag_id) VALUES ?',
                    [tagValues]
                );
            }

            // 处理要删除的图片
            const deleteImages = req.body['delete_images[]'] || req.body.delete_images || [];
            const imagesToDelete = Array.isArray(deleteImages) ? deleteImages : (deleteImages ? [deleteImages] : []);

            if (imagesToDelete.length > 0) {
                for (const imagePath of imagesToDelete) {
                    try {
                        // 从数据库删除记录
                        await db.query(
                            'DELETE FROM note_attachments WHERE note_id = ? AND file_path = ?',
                            [noteId, imagePath]
                        );

                        // 删除物理文件
                        const fs = require('fs');
                        const path = require('path');
                        const fullPath = path.join(__dirname, '..', 'public', imagePath);
                        if (fs.existsSync(fullPath)) {
                            fs.unlinkSync(fullPath);
                            console.log('删除图片文件:', fullPath);
                        }
                    } catch (error) {
                        console.error('删除图片失败:', error);
                        // 继续处理其他图片，不中断整个流程
                    }
                }
            }

            // 处理上传的图片（如果有）
            if (req.files && req.files.length > 0) {
                for (const file of req.files) {
                    try {
                        const filePath = await processImage(file);

                        // 保存附件信息到数据库
                        await db.query(
                            'INSERT INTO note_attachments (note_id, file_name, file_path, file_type, file_size) VALUES (?, ?, ?, ?, ?)',
                            [noteId, file.originalname, filePath, file.mimetype, file.size]
                        );
                    } catch (error) {
                        console.error('处理图片失败:', error);
                        // 继续处理其他图片，不中断整个流程
                    }
                }
            }

            await db.query('COMMIT');

            res.json({
                success: true,
                message: '笔记更新成功'
            });
        } catch (error) {
            await db.query('ROLLBACK');
            throw error;
        }
    } catch (error) {
        console.error('更新笔记失败:', error);
        if (error.code === 'ER_NO_REFERENCED_ROW_2') {
            return res.status(400).json({ error: '分类或标签不存在' });
        }
        res.status(500).json({ error: '服务器错误' });
    }
});

// 删除笔记（管理员或笔记作者）
router.delete('/:id', checkReadPermission, async (req, res) => {
    try {
        const noteId = req.params.id;
        const currentUserId = req.user.id;
        const isAdmin = req.user.role === 'admin';
        
        // 检查笔记是否存在及权限
        const [existingNotes] = await db.query('SELECT id, author_id FROM notes WHERE id = ?', [noteId]);
        
        if (existingNotes.length === 0) {
            return res.status(404).json({ error: '笔记不存在' });
        }
        
        // 检查是否有权限删除（管理员或笔记作者）
        const isOwner = String(existingNotes[0].author_id) === String(currentUserId);
        if (!isAdmin && !isOwner) {
            return res.status(403).json({ error: '没有权限删除此笔记' });
        }
        
        // 开始事务
        await db.query('START TRANSACTION');

        try {
            // 删除标签关联
            await db.query('DELETE FROM note_tag_relations WHERE note_id = ?', [noteId]);

            // 删除访问日志
            await db.query('DELETE FROM note_access_logs WHERE note_id = ?', [noteId]);

            // 删除笔记
            const [result] = await db.query('DELETE FROM notes WHERE id = ?', [noteId]);

            if (result.affectedRows === 0) {
                await db.query('ROLLBACK');
                return res.status(404).json({ error: '笔记不存在' });
            }

            await db.query('COMMIT');

            res.json({
                success: true,
                message: '笔记删除成功'
            });
        } catch (error) {
            await db.query('ROLLBACK');
            throw error;
        }
    } catch (error) {
        console.error('删除笔记失败:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});

// 切换笔记置顶状态（仅管理员）
router.patch('/:id/pin', checkAdminRole, async (req, res) => {
    try {
        const noteId = req.params.id;

        // 获取当前置顶状态
        const [notes] = await db.query('SELECT is_pinned FROM notes WHERE id = ?', [noteId]);

        if (notes.length === 0) {
            return res.status(404).json({ error: '笔记不存在' });
        }

        const newPinnedStatus = !notes[0].is_pinned;

        // 更新置顶状态
        await db.query('UPDATE notes SET is_pinned = ? WHERE id = ?', [newPinnedStatus, noteId]);

        res.json({
            success: true,
            message: newPinnedStatus ? '笔记已置顶' : '笔记已取消置顶',
            is_pinned: newPinnedStatus
        });
    } catch (error) {
        console.error('切换置顶状态失败:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});

// 搜索笔记（支持全文搜索）
router.get('/search/advanced', checkReadPermission, async (req, res) => {
    try {
        const { q, category, tags, author, date_from, date_to, sort } = req.query;
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const offset = (page - 1) * limit;

        let whereClause = 'WHERE 1=1';
        let params = [];

        // 全文搜索
        if (q) {
            whereClause += ' AND (MATCH(n.title, n.content) AGAINST(? IN NATURAL LANGUAGE MODE) OR n.title LIKE ? OR n.content LIKE ?)';
            params.push(q, `%${q}%`, `%${q}%`);
        }

        // 分类过滤
        if (category) {
            whereClause += ' AND n.category_id = ?';
            params.push(category);
        }

        // 作者过滤
        if (author) {
            whereClause += ' AND n.author_id = ?';
            params.push(author);
        }

        // 日期范围过滤
        if (date_from) {
            whereClause += ' AND n.created_at >= ?';
            params.push(date_from);
        }

        if (date_to) {
            whereClause += ' AND n.created_at <= ?';
            params.push(date_to + ' 23:59:59');
        }

        // 标签过滤
        if (tags) {
            const tagIds = tags.split(',').map(id => parseInt(id)).filter(id => !isNaN(id));
            if (tagIds.length > 0) {
                whereClause += ` AND n.id IN (
                    SELECT note_id FROM note_tag_relations
                    WHERE tag_id IN (${tagIds.map(() => '?').join(',')})
                    GROUP BY note_id
                    HAVING COUNT(DISTINCT tag_id) = ?
                )`;
                params.push(...tagIds, tagIds.length);
            }
        }

        // 排序
        let orderClause = 'ORDER BY n.is_pinned DESC, ';
        switch (sort) {
            case 'title':
                orderClause += 'n.title ASC';
                break;
            case 'created':
                orderClause += 'n.created_at DESC';
                break;
            case 'views':
                orderClause += 'n.view_count DESC';
                break;
            case 'relevance':
                if (q) {
                    orderClause += 'MATCH(n.title, n.content) AGAINST(? IN NATURAL LANGUAGE MODE) DESC';
                    params.push(q);
                } else {
                    orderClause += 'n.updated_at DESC';
                }
                break;
            default:
                orderClause += 'n.updated_at DESC';
        }

        // 执行搜索
        const [notes] = await db.query(`
            SELECT
                n.id, n.title, n.content, n.is_public, n.is_pinned, n.view_count,
                n.created_at, n.updated_at,
                c.name as category_name, c.color as category_color, c.icon as category_icon,
                u.username as author_name,
                GROUP_CONCAT(DISTINCT CONCAT(t.id, ':', t.name, ':', t.color) SEPARATOR '|') as tags
            FROM notes n
            LEFT JOIN note_categories c ON n.category_id = c.id
            LEFT JOIN users u ON n.author_id = u.id
            LEFT JOIN note_tag_relations ntr ON n.id = ntr.note_id
            LEFT JOIN note_tags t ON ntr.tag_id = t.id
            ${whereClause}
            GROUP BY n.id
            ${orderClause}
            LIMIT ? OFFSET ?
        `, [...params, limit, offset]);

        // 获取总数
        const [countResult] = await db.query(`
            SELECT COUNT(DISTINCT n.id) as total
            FROM notes n
            LEFT JOIN note_categories c ON n.category_id = c.id
            LEFT JOIN users u ON n.author_id = u.id
            LEFT JOIN note_tag_relations ntr ON n.id = ntr.note_id
            LEFT JOIN note_tags t ON ntr.tag_id = t.id
            ${whereClause}
        `, params.slice(0, -2)); // 移除 limit 和 offset 参数

        const total = countResult[0].total;

        // 处理结果
        const processedNotes = notes.map(note => ({
            ...note,
            content: note.content ? note.content.substring(0, 200) + (note.content.length > 200 ? '...' : '') : '',
            tags: note.tags ? note.tags.split('|').map(tag => {
                const [id, name, color] = tag.split(':');
                return { id: parseInt(id), name, color };
            }) : []
        }));

        res.json({
            success: true,
            data: processedNotes,
            pagination: {
                total,
                page,
                limit,
                pages: Math.ceil(total / limit)
            }
        });
    } catch (error) {
        console.error('高级搜索失败:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});

module.exports = router;
