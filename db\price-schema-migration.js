const db = require('./index');

/**
 * 价格系统数据库迁移脚本
 * 为现有的价格表添加用户ID字段，并创建缺失的表
 */

// 检查表是否存在
async function checkTableExists(tableName) {
    try {
        const [tables] = await db.query("SHOW TABLES LIKE ?", [tableName]);
        return tables.length > 0;
    } catch (error) {
        console.error(`检查表 ${tableName} 是否存在时出错:`, error);
        return false;
    }
}

// 检查字段是否存在
async function checkColumnExists(tableName, columnName) {
    try {
        const [columns] = await db.query("SHOW COLUMNS FROM ?? LIKE ?", [tableName, columnName]);
        return columns.length > 0;
    } catch (error) {
        console.error(`检查表 ${tableName} 的字段 ${columnName} 是否存在时出错:`, error);
        return false;
    }
}

// 创建价格记录主表
async function createPriceRecordsTable() {
    try {
        const exists = await checkTableExists('price_records');
        if (!exists) {
            console.log('创建 price_records 表...');
            await db.query(`
                CREATE TABLE price_records (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    user_id INT NOT NULL,
                    total_amount DECIMAL(10,2) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
                )
            `);
            console.log('price_records 表创建成功');
        } else {
            console.log('price_records 表已存在，检查是否需要添加 user_id 字段...');
            const hasUserIdColumn = await checkColumnExists('price_records', 'user_id');
            if (!hasUserIdColumn) {
                console.log('为 price_records 表添加 user_id 字段...');
                await db.query(`
                    ALTER TABLE price_records
                    ADD COLUMN user_id INT NOT NULL DEFAULT 1 AFTER id
                `);
                console.log('user_id 字段添加成功');

                // 单独添加外键约束
                try {
                    await db.query(`
                        ALTER TABLE price_records
                        ADD CONSTRAINT fk_price_records_user_id
                        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
                    `);
                    console.log('price_records 外键约束添加成功');
                } catch (fkError) {
                    console.warn('price_records 外键约束添加失败:', fkError.message);
                }
            } else {
                console.log('price_records 表已包含 user_id 字段');

                // 检查外键约束是否存在
                try {
                    const [constraints] = await db.query(`
                        SELECT CONSTRAINT_NAME
                        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
                        WHERE TABLE_SCHEMA = DATABASE()
                        AND TABLE_NAME = 'price_records'
                        AND COLUMN_NAME = 'user_id'
                        AND REFERENCED_TABLE_NAME = 'users'
                    `);

                    if (constraints.length === 0) {
                        console.log('添加缺失的 price_records 外键约束...');
                        await db.query(`
                            ALTER TABLE price_records
                            ADD CONSTRAINT fk_price_records_user_id
                            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
                        `);
                        console.log('price_records 外键约束添加成功');
                    } else {
                        console.log('price_records 外键约束已存在');
                    }
                } catch (fkError) {
                    console.warn('检查或添加 price_records 外键约束失败:', fkError.message);
                }
            }
        }
        return true;
    } catch (error) {
        console.error('创建或更新 price_records 表失败:', error);
        return false;
    }
}

// 创建价格记录明细表
async function createPriceItemsTable() {
    try {
        const exists = await checkTableExists('price_items');
        if (!exists) {
            console.log('创建 price_items 表...');
            await db.query(`
                CREATE TABLE price_items (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    record_id INT NOT NULL,
                    price DECIMAL(10,2) NOT NULL,
                    operation_type VARCHAR(50) NOT NULL,
                    description TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (record_id) REFERENCES price_records(id) ON DELETE CASCADE
                )
            `);
            console.log('price_items 表创建成功');
        } else {
            console.log('price_items 表已存在');
        }
        return true;
    } catch (error) {
        console.error('创建 price_items 表失败:', error);
        return false;
    }
}

// 更新原始表格数据表
async function updateRawTableDataTable() {
    try {
        const exists = await checkTableExists('raw_table_data');
        if (!exists) {
            console.log('创建 raw_table_data 表...');
            await db.query(`
                CREATE TABLE raw_table_data (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    user_id INT NOT NULL,
                    total_amount INT NOT NULL,
                    raw_data TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
                )
            `);
            console.log('raw_table_data 表创建成功');
        } else {
            console.log('raw_table_data 表已存在，检查是否需要添加 user_id 字段...');
            const hasUserIdColumn = await checkColumnExists('raw_table_data', 'user_id');
            if (!hasUserIdColumn) {
                console.log('为 raw_table_data 表添加 user_id 字段...');
                await db.query(`
                    ALTER TABLE raw_table_data 
                    ADD COLUMN user_id INT NOT NULL DEFAULT 1 AFTER id,
                    ADD FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
                `);
                console.log('user_id 字段添加成功');
            } else {
                console.log('raw_table_data 表已包含 user_id 字段');
            }
        }
        return true;
    } catch (error) {
        console.error('创建或更新 raw_table_data 表失败:', error);
        return false;
    }
}

// 创建索引
async function createIndexes() {
    try {
        console.log('创建索引...');
        
        // 检查索引是否已存在的函数
        const checkIndexExists = async (tableName, indexName) => {
            try {
                const [indexes] = await db.query("SHOW INDEX FROM ?? WHERE Key_name = ?", [tableName, indexName]);
                return indexes.length > 0;
            } catch (error) {
                return false;
            }
        };

        // 为 price_records 表创建索引
        if (await checkTableExists('price_records')) {
            if (!(await checkIndexExists('price_records', 'idx_price_records_user_id'))) {
                await db.query('CREATE INDEX idx_price_records_user_id ON price_records(user_id)');
                console.log('创建 price_records.user_id 索引成功');
            }
            if (!(await checkIndexExists('price_records', 'idx_price_records_created_at'))) {
                await db.query('CREATE INDEX idx_price_records_created_at ON price_records(created_at)');
                console.log('创建 price_records.created_at 索引成功');
            }
        }

        // 为 price_items 表创建索引
        if (await checkTableExists('price_items')) {
            if (!(await checkIndexExists('price_items', 'idx_price_items_record_id'))) {
                await db.query('CREATE INDEX idx_price_items_record_id ON price_items(record_id)');
                console.log('创建 price_items.record_id 索引成功');
            }
        }

        // 为 raw_table_data 表创建索引
        if (await checkTableExists('raw_table_data')) {
            if (!(await checkIndexExists('raw_table_data', 'idx_raw_table_data_user_id'))) {
                await db.query('CREATE INDEX idx_raw_table_data_user_id ON raw_table_data(user_id)');
                console.log('创建 raw_table_data.user_id 索引成功');
            }
            if (!(await checkIndexExists('raw_table_data', 'idx_raw_table_data_created_at'))) {
                await db.query('CREATE INDEX idx_raw_table_data_created_at ON raw_table_data(created_at)');
                console.log('创建 raw_table_data.created_at 索引成功');
            }
        }

        return true;
    } catch (error) {
        console.error('创建索引失败:', error);
        return false;
    }
}

// 执行完整的价格系统迁移
async function migratePriceSchema() {
    console.log('开始价格系统数据库迁移...');
    
    try {
        // 1. 创建或更新 price_records 表
        const priceRecordsResult = await createPriceRecordsTable();
        if (!priceRecordsResult) {
            throw new Error('price_records 表迁移失败');
        }

        // 2. 创建 price_items 表
        const priceItemsResult = await createPriceItemsTable();
        if (!priceItemsResult) {
            throw new Error('price_items 表迁移失败');
        }

        // 3. 创建或更新 raw_table_data 表
        const rawTableDataResult = await updateRawTableDataTable();
        if (!rawTableDataResult) {
            throw new Error('raw_table_data 表迁移失败');
        }

        // 4. 创建索引
        const indexesResult = await createIndexes();
        if (!indexesResult) {
            console.warn('部分索引创建失败，但不影响主要功能');
        }

        console.log('价格系统数据库迁移完成！');
        return true;

    } catch (error) {
        console.error('价格系统数据库迁移失败:', error);
        return false;
    }
}

// 如果直接运行此文件，则执行迁移
if (require.main === module) {
    migratePriceSchema()
        .then((success) => {
            if (success) {
                console.log('迁移成功完成');
                process.exit(0);
            } else {
                console.log('迁移失败');
                process.exit(1);
            }
        })
        .catch(error => {
            console.error('迁移过程中发生错误:', error);
            process.exit(1);
        });
}

module.exports = {
    migratePriceSchema,
    createPriceRecordsTable,
    createPriceItemsTable,
    updateRawTableDataTable,
    createIndexes
};
