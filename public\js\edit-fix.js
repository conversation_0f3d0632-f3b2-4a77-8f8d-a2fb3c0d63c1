/**
 * 批量添加权限控制到配件页面
 * 这个脚本会自动为所有配件页面添加权限控制代码
 */

// 页面组件类型列表
const componentPages = [
  'case', 'cooler', 'cpu', 'fan', 'gpu', 'monitor', 'motherboard', 'psu', 'ram', 'storage'
];

// 为每个页面注入权限检查
async function setupPermissionForAllPages() {
  console.log('开始为所有组件页面添加权限控制...');
  
  // 检查每个组件页面HTML文件是否已包含permission-helper.js
  componentPages.forEach(component => {
    const htmlFile = `${component}-info.html`;
    fetch(htmlFile)
      .then(response => response.text())
      .then(html => {
        if (!html.includes('permission-helper.js')) {
          console.warn(`${htmlFile} 未包含权限控制脚本，请手动添加`);
        } else {
          console.log(`${htmlFile} 已包含权限控制脚本`);
        }
      })
      .catch(err => console.error(`无法检查 ${htmlFile}:`, err));
  });
  
  // 等待DOM加载完成
  document.addEventListener('DOMContentLoaded', async function() {
    // 尝试使用权限控制模块
    if (typeof setupPermissionBasedUI === 'function') {
      console.log('初始化权限控制...');
      await setupPermissionBasedUI();
      
      // 根据用户角色显示提示信息
      const isAdminUser = typeof isAdmin === 'function' ? await isAdmin() : false;
      
      // 显示当前权限状态提示
      if (isAdminUser) {
        console.log('当前以管理员身份登录，可以执行所有操作');
      } else {
        console.log('当前以普通用户身份登录，只能查看数据');
        
        // 添加普通用户提示
        addNormalUserNotice();
      }
    } else {
      console.warn('权限控制模块未加载，请确保已引入permission-helper.js');
    }
  });
}

// 为普通用户添加提示信息
function addNormalUserNotice() {
  // 检查是否已经添加了提示
  if (document.querySelector('.permission-notice')) return;
  
  const header = document.querySelector('h1, h2') || document.body;
  const permissionNote = document.createElement('div');
  permissionNote.className = 'permission-notice bg-blue-50 border-l-4 border-blue-500 text-blue-700 p-4 mb-4';
  permissionNote.innerHTML = `
    <p class="font-medium">权限提示</p>
    <p>您当前为普通用户身份，只能查看数据，无法执行添加、修改或删除操作。</p>
  `;
  
  // 添加到页面
  if (header.parentNode) {
    header.parentNode.insertBefore(permissionNote, header.nextSibling);
  }
}

// 初始化权限控制
setupPermissionForAllPages();

// 检测浏览器是否支持WebP
let supportWebP = false;
function checkWebPSupport() {
  return new Promise(resolve => {
    const webP = new Image();
    webP.onload = webP.onerror = function() {
      supportWebP = webP.height === 2;
      resolve(supportWebP);
    };
    webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
  });
}

// 页面加载时检测WebP支持
document.addEventListener('DOMContentLoaded', function() {
  checkWebPSupport();
});

// 编辑模式下配件添加修复
document.addEventListener('DOMContentLoaded', function() {
    // 捕获编辑模式下的添加配件按钮点击
    document.addEventListener('click', function(e) {
        // 精确匹配editAddPartBtn按钮及其子元素
        const targetButton = e.target.closest('#editAddPartBtn');
        if (targetButton || e.target.id === 'editAddPartBtn') {
            console.log('检测到编辑模式下添加配件按钮点击');
            
            // 延迟执行以确保DOM已更新
            setTimeout(() => {
                // 获取所有配件行
                const rows = document.querySelectorAll('#editPartsTableBody .part-row');
                console.log(`检测到${rows.length}行配件数据`);
                
                // 记录两次添加的状态
                let hasEmptySelectRow = false;
                let lastAddedRow = null;
                
                // 遍历检查所有行
                rows.forEach(row => {
                    const typeSelect = row.querySelector('select.part-type');
                    // 判断是否有正确的类型选择元素
                    if (!typeSelect || typeSelect.options.length <= 1) {
                        console.log('删除没有类型选择的配件行');
                        row.remove();
                        hasEmptySelectRow = true;
                    } else {
                        // 记录最后一个有效行
                        lastAddedRow = row;
                    }
                });
                
                // 如果检测到有空行被删除，并且有已添加的行，清空该行的值
                if (hasEmptySelectRow && lastAddedRow) {
                    // 确保这个行是新行 (清除数据不必要清空类型选择)
                    const modelInput = lastAddedRow.querySelector('.part-model');
                    const priceInput = lastAddedRow.querySelector('.part-price');
                    if (modelInput) modelInput.value = '';
                    if (priceInput) priceInput.value = '0';
                }
            }, 50); // 短暂延迟确保DOM已更新
        }
    });
});
