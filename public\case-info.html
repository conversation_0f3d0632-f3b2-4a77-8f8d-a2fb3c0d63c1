<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>机箱管理 - 配件管理系统</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/viewerjs/1.11.6/viewer.min.css">
    <link rel="stylesheet" href="/css/case-info.css"></link>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/viewerjs/1.11.6/viewer.min.js"></script>
    <script src="/js/js-pinyin.js"></script>
    <script src="/js/pinyin-wrapper.js"></script>
    <script src="/js/main.js"></script>
    <script src="/js/permission-helper.js"></script>
</head>

<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-2 sm:px-4 py-4 sm:py-8">
        <header class="mb-4 sm:mb-8">
            <div class="flex justify-between items-center">
                <h1 class="text-xl sm:text-3xl font-bold text-indigo-700 flex items-center">
                    <i class="fas fa-server mr-2 sm:mr-3"></i> 机箱管理
                </h1>
                <button id="theme-toggle" title="切换主题">
                    <i class="fas fa-sun"></i>
                </button>
            </div>
            <div class="flex justify-between items-center">
                <p class="text-gray-600 mt-1 sm:mt-2 text-sm sm:text-base">管理电脑机箱信息</p>
                <div class="mt-2 flex space-x-2">
                    <a href="/pc-components.html"
                        class="inline-block bg-indigo-600 text-white py-1 sm:py-2 px-3 sm:px-4 rounded-md hover:bg-indigo-700 text-sm sm:text-base">
                        <i class="fas fa-arrow-left mr-1"></i> 返回配件列表
                    </a>
                    <a href="/"
                        class="inline-block bg-indigo-600 text-white py-1 sm:py-2 px-3 sm:px-4 rounded-md hover:bg-indigo-700 text-sm sm:text-base">
                        <i class="fas fa-home mr-1"></i> 首页
                    </a>
                </div>
            </div>
        </header>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
            <!-- 左侧表单区域 -->
            <div class="lg:col-span-1 bg-white p-3 sm:p-6 rounded-lg shadow-md">
                <h2 class="text-lg sm:text-xl font-semibold text-gray-800 mb-3 sm:mb-4 flex items-center">
                    <i class="fas fa-plus-circle mr-1 sm:mr-2 text-indigo-500"></i> 添加机箱信息
                </h2>

                <form id="caseForm" class="space-y-3 sm:space-y-4">
                    <!-- 智能识别区域 -->
                    <div class="border border-indigo-200 rounded-md p-3 bg-indigo-50">
                        <h3 class="text-md font-medium text-indigo-700 mb-2 flex items-center">
                            <i class="fas fa-magic mr-1"></i> 智能识别
                        </h3>
                        <div class="space-y-2">
                            <textarea id="smartInput" rows="3"
                                class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base"
                                placeholder="粘贴机箱参数文本，如：品牌：联力、型号：PC-O11 Dynamic、颜色：白色、材质：钢化玻璃+铝、尺寸：450x275x285mm..."></textarea>
                            <button type="button" id="autoFillBtn"
                                class="w-full bg-indigo-600 hover:bg-indigo-700 text-white py-2 px-4 rounded-md text-sm">
                                <i class="fas fa-magic mr-2"></i>智能识别
                            </button>
                        </div>
                    </div>

                    <!-- 基本信息 -->
                    <div class="border border-gray-200 rounded-md p-3">
                        <h3 class="text-md font-medium text-gray-700 mb-2">基本信息</h3>
                        <div class="space-y-3">
                            <div>
                                <label for="brand" class="block text-sm font-medium text-gray-700 mb-1">品牌 <span class="text-red-500">*</span></label>
                                <input type="text" id="brand" name="brand" required class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base">
                            </div>
                            <div>
                                <label for="model" class="block text-sm font-medium text-gray-700 mb-1">型号 <span class="text-red-500">*</span></label>
                                <input type="text" id="model" name="model" required class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base">
                            </div>
                            <div>
                                <label for="formFactor" class="block text-sm font-medium text-gray-700 mb-1">支持主板类型 <span class="text-red-500">*</span></label>
                                <select id="formFactor" name="formFactor" required class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base">
                                    <option value="">请选择</option>
                                    <option value="ATX">ATX</option>
                                    <option value="Micro-ATX">Micro-ATX</option>
                                    <option value="Mini-ITX">Mini-ITX</option>
                                    <option value="E-ATX">E-ATX</option>
                                    <option value="XL-ATX">XL-ATX</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- 物理特性 -->
                    <div class="border border-gray-200 rounded-md p-3">
                        <h3 class="text-md font-medium text-gray-700 mb-2">物理特性</h3>
                        <div class="space-y-3">
                            <div>
                                <label for="dimensions" class="block text-sm font-medium text-gray-700 mb-1">尺寸（宽x高x深）</label>
                                <input type="text" id="dimensions" name="dimensions" class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base">
                            </div>
                            <div>
                                <label for="color" class="block text-sm font-medium text-gray-700 mb-1">颜色</label>
                                <input type="text" id="color" name="color" class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base">
                            </div>
                            <div>
                                <label for="material" class="block text-sm font-medium text-gray-700 mb-1">材质</label>
                                <input type="text" id="material" name="material" class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base">
                            </div>
                            <div>
                                <label for="sidePanel" class="block text-sm font-medium text-gray-700 mb-1">侧板类型</label>
                                <input type="text" id="sidePanel" name="sidePanel" placeholder="例如: 钢化玻璃"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base">
                            </div>
                            <div>
                                <label for="psuShroud" class="block text-sm font-medium text-gray-700 mb-1">有无电源仓</label>
                                <select id="psuShroud" name="psuShroud"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base">
                                    <option value="1">有</option>
                                    <option value="0">无</option>
                                    <option value="">未知</option>
                                </select>
                            </div>
                            <div>
                                <label for="weight" class="block text-sm font-medium text-gray-700 mb-1">重量 (kg)</label>
                                <input type="number" id="weight" name="weight" step="0.01" min="0" class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base">
                            </div>
                        </div>
                    </div>

                    <!-- 容量和兼容性 -->
                    <div class="border border-gray-200 rounded-md p-3">
                        <h3 class="text-md font-medium text-gray-700 mb-2">容量和兼容性</h3>
                        <div class="space-y-3">
                            <div>
                                <label for="driveCapacity" class="block text-sm font-medium text-gray-700 mb-1">硬盘容量</label>
                                <input type="text" id="driveCapacity" name="driveCapacity" class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base" placeholder="例如: 2x 3.5\", 2x 2.5\"">
                            </div>
                            <div>
                                <label for="expansionSlots" class="block text-sm font-medium text-gray-700 mb-1">扩展槽数量</label>
                                <input type="number" id="expansionSlots" name="expansionSlots" min="0" class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base">
                            </div>
                            <div>
                                <label for="fanSupport" class="block text-sm font-medium text-gray-700 mb-1">风扇支持</label>
                                <input type="text" id="fanSupport" name="fanSupport" class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base" placeholder="例如: 前: 3x 120mm, 顶: 2x 140mm">
                            </div>
                            <div>
                                <label for="radiatorSupport" class="block text-sm font-medium text-gray-700 mb-1">散热器支持</label>
                                <input type="text" id="radiatorSupport" name="radiatorSupport" class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base" placeholder="例如: 前: 240/280mm, 顶: 360mm">
                            </div>
                            <div>
                                <label for="maxGpuLength" class="block text-sm font-medium text-gray-700 mb-1">最大显卡长度 (mm)</label>
                                <input type="number" id="maxGpuLength" name="maxGpuLength" min="0" class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base">
                            </div>
                            <div>
                                <label for="maxCpuCoolerHeight" class="block text-sm font-medium text-gray-700 mb-1">CPU散热器最大高度 (mm)</label>
                                <input type="number" id="maxCpuCoolerHeight" name="maxCpuCoolerHeight" min="0" class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base">
                            </div>
                        </div>
                    </div>

                    <!-- 接口和其他 -->
                    <div class="border border-gray-200 rounded-md p-3">
                        <h3 class="text-md font-medium text-gray-700 mb-2">接口和其他</h3>
                        <div class="space-y-3">
                            <div>
                                <label for="frontIo" class="block text-sm font-medium text-gray-700 mb-1">前置接口</label>
                                <input type="text" id="frontIo" name="frontIo" class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base" placeholder="例如: 2x USB 3.0, 1x USB-C, 音频">
                            </div>
                            <div>
                                <label for="includedFans" class="block text-sm font-medium text-gray-700 mb-1">附带风扇</label>
                                <input type="text" id="includedFans" name="includedFans" class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base" placeholder="例如: 3x 120mm RGB风扇">
                            </div>
                            <div>
                                <label for="price" class="block text-sm font-medium text-gray-700 mb-1">价格 (¥)</label>
                                <input type="number" id="price" name="price" step="0.01" min="0" class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base">
                            </div>
                            <div>
                                <label for="image" class="block text-sm font-medium text-gray-700 mb-1">图片</label>
                                <input type="file" id="image" name="image" accept="image/*" class="w-full px-2 sm:px-3 py-1 sm:py-2 text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100">
                                <p class="text-xs text-slate-500 dark:text-slate-400 mt-1">PNG, JPG, GIF 格式，大小不超过5MB（将自动转换为WebP格式以优化加载速度）</p>

                                <!-- 上传进度条 -->
                                <div id="uploadProgressContainer" class="mt-2 hidden">
                                    <div class="bg-gray-200 dark:bg-gray-700 rounded-full h-3 mb-2 overflow-hidden">
                                        <div id="uploadProgressBar" class="bg-gradient-to-r from-indigo-500 to-indigo-600 dark:from-indigo-400 dark:to-indigo-500 h-3 rounded-full transition-all duration-500 ease-out relative" style="width: 0%">
                                            <!-- 进度条光泽效果 -->
                                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-20 animate-pulse"></div>
                                        </div>
                                    </div>
                                    <div class="flex justify-between items-center text-xs">
                                        <span id="uploadProgressText" class="text-gray-600 dark:text-gray-400 flex items-center">
                                            <i id="uploadProgressIcon" class="fas fa-upload mr-1"></i>
                                            准备上传...
                                        </span>
                                        <span id="uploadProgressPercent" class="text-indigo-600 dark:text-indigo-400 font-medium">0%</span>
                                    </div>
                                </div>

                                <div id="imagePreview" class="mt-2 hidden">
                                    <img id="previewImage" class="h-32 object-contain border rounded-md" alt="预览">
                                </div>
                            </div>
                            <div>
                                <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">备注</label>
                                <textarea id="notes" name="notes" rows="3" class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base"></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="form-footer-buttons mt-6">
                        <button type="button" onclick="resetForm()" class="px-3 py-1 sm:px-4 sm:py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            重置
                        </button>
                        <button type="submit" class="px-3 py-1 sm:px-4 sm:py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <i class="fas fa-save mr-1"></i> 保存机箱信息
                        </button>
                    </div>
                </form>
            </div>

            <!-- 右侧列表区域 -->
            <div class="lg:col-span-2 bg-white p-3 sm:p-6 rounded-lg shadow-md">
                <h2 class="text-xl font-semibold text-indigo-800 mb-3 flex items-center">
                    <i class="fas fa-server mr-2 sm:mr-3"></i> 机箱列表
                </h2>
                <div class="flex flex-wrap items-center gap-2 sm:gap-4">
                    <div class="w-full sm:w-auto sm:flex-1 mb-2 sm:mb-0">
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-search text-gray-400"></i>
                            </div>
                            <input type="text" id="searchInput" placeholder="搜索型号、品牌等..." 
                                class="w-full py-2 pl-10 pr-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        </div>
                    </div>
                    <div class="flex flex-wrap gap-2 w-full sm:w-auto">
                        <select id="brandFilter" 
                            class="flex-1 sm:flex-none border border-gray-300 rounded-md shadow-sm py-2 pl-3 pr-8 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm">
                            <option value="">所有品牌</option>
                        </select>
                        <select id="formFactorFilter" 
                            class="flex-1 sm:flex-none border border-gray-300 rounded-md shadow-sm py-2 pl-3 pr-8 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm">
                            <option value="">所有规格</option>
                            <option value="ATX">ATX</option>
                            <option value="Micro-ATX">Micro-ATX</option>
                            <option value="Mini-ITX">Mini-ITX</option>
                            <option value="E-ATX">E-ATX</option>
                        </select>
                        <button id="resetFilterBtn" 
                            class="flex-none bg-gray-200 hover:bg-gray-300 text-gray-700 py-2 px-4 rounded-md">
                            <i class="fas fa-sync-alt mr-1"></i> 重置
                        </button>
                    </div>
                </div>
                <div class="overflow-x-auto mt-3">
                    <div class="table-wrapper">
                        <table class="min-w-full divide-y divide-gray-200 table-compact sm:table-auto">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell">图片</th>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider model-column">型号</th>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider brand-column">品牌</th>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell">规格</th>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell">颜色</th>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell">价格</th>
                                    <th class="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider action-column">操作</th>
                                </tr>
                            </thead>
                            <tbody id="caseTableBody" class="bg-white divide-y divide-gray-200">
                                <!-- 数据将在这里动态加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="flex items-center justify-between mt-4 pagination-mobile">
                    <div class="text-sm text-gray-700 mb-2 sm:mb-0" id="totalCount">
                        共 <span id="totalRecords">0</span> 条记录
                    </div>
                    <div class="flex items-center space-x-1 sm:space-x-2 page-controls-mobile">
                        <button id="firstPage" title="首页"
                            class="px-2 py-1 border border-gray-300 rounded-md text-sm bg-white text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                            <i class="fas fa-angle-double-left"></i>
                        </button>
                        <button id="prevBtn" title="上一页"
                            class="px-2 py-1 border border-gray-300 rounded-md text-sm bg-white text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        
                        <div id="pageNumbers" class="hidden sm:flex space-x-1">
                            <!-- 页码将通过JavaScript填充 -->
                        </div>
                        
                        <span id="pageInfo" class="px-2 py-1 text-sm whitespace-nowrap">第 <span id="currentPageDisplay">1</span> / <span id="totalPagesDisplay">1</span> 页</span>
                        
                        <button id="nextBtn" title="下一页"
                            class="px-2 py-1 border border-gray-300 rounded-md text-sm bg-white text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                        <button id="lastPage" title="尾页"
                            class="px-2 py-1 border border-gray-300 rounded-md text-sm bg-white text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                            <i class="fas fa-angle-double-right"></i>
                        </button>
                        
                        <div class="hidden sm:flex items-center ml-2">
                            <span class="text-sm">跳转到</span>
                            <input type="number" id="pageJump" min="1" class="w-12 ml-1 px-2 py-1 border border-gray-300 rounded-md text-sm">
                            <button id="goToPage" class="ml-1 px-2 py-1 bg-indigo-600 text-white text-sm rounded-md hover:bg-indigo-700">
                                确定
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 详情模态框 -->
    <div id="detailsModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden z-50">
        <div class="rounded-xl max-w-4xl w-full max-h-[90vh] shadow-2xl bg-white dark:bg-gray-900">
            <div class="px-6 py-4 bg-white dark:bg-gray-900 flex justify-between items-center border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-xl font-semibold text-indigo-800 dark:text-indigo-300">
                    <i class="fas fa-server mr-2"></i>机箱详情
                </h3>
                <button id="closeDetailsModal" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-6 overflow-y-auto custom-scrollbar modal-content bg-white dark:bg-gray-900">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div id="detailsImage" class="flex items-center justify-center">
                        <!-- 图片将在这里动态加载 -->
                    </div>
                    <div id="detailsInfo" class="space-y-4">
                        <!-- 详情将在这里动态加载 -->
                    </div>
                </div>
            </div>
            <div class="px-6 py-4 bg-white dark:bg-gray-900 flex justify-end space-x-3 border-t border-gray-200 dark:border-gray-700">
                <button id="detailsEditBtn" class="px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 dark:bg-indigo-700 dark:hover:bg-indigo-600 transition-colors">
                    <i class="fas fa-edit mr-1"></i> 编辑
                </button>
                <button id="detailsDeleteBtn" class="px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-600 transition-colors">
                    <i class="fas fa-trash mr-1"></i> 删除
                </button>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div id="deleteConfirmModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden z-50">
        <div class="rounded-xl p-6 max-w-md w-full shadow-2xl bg-white dark:bg-gray-900">
            <div class="text-center mb-6">
                <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 dark:bg-red-900 mb-4">
                    <i class="fas fa-exclamation-triangle text-2xl text-red-600 dark:text-red-400"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">确认删除</h3>
                <p class="text-gray-500 dark:text-gray-400">您确定要删除这条机箱信息吗？此操作无法撤销。</p>
            </div>
            <div class="flex justify-center space-x-3">
                <button id="cancelDeleteBtn" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700">
                    取消
                </button>
                <button id="confirmDeleteBtn" class="px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-600">
                    确认删除
                </button>
            </div>
        </div>
    </div>

    <!-- Toast消息 -->
    <div id="toast" class="fixed bottom-4 right-4 px-4 py-2 rounded-lg text-white bg-green-500 hidden"></div>

    <script src="js/upload-progress.js"></script>
    <script src="/js/case-info.js"></script>
</body>

</html>