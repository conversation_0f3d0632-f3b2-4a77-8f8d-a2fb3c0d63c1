const express = require('express');
const sharp = require('sharp');
const router = express.Router();
const db = require('../config/db');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// 配置multer
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        const dir = 'public/uploads/feedback';
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }
        cb(null, dir);
    },
    filename: function (req, file, cb) {
        cb(null, 'feedback-' + Date.now() + path.extname(file.originalname));
    }
});

const upload = multer({ 
    storage: storage,
    limits: { fileSize: 10 * 1024 * 1024 }, // 10MB限制
    fileFilter: function (req, file, cb) {
        const filetypes = /jpeg|jpg|png|gif/;
        const mimetype = filetypes.test(file.mimetype);
        const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
        
        if (mimetype && extname) {
            return cb(null, true);
        }
        cb(new Error('只允许上传JPG、PNG和GIF格式的图片'));
    }
});

// 创建店铺反馈表（如果不存在）
async function createFeedbackTable() {
    try {
        await db.query(`
            CREATE TABLE IF NOT EXISTS store_feedback (
                id INT PRIMARY KEY AUTO_INCREMENT,
                store_name VARCHAR(255) NOT NULL,
                store_location VARCHAR(255) NOT NULL,
                feedback_content TEXT NOT NULL,
                feedback_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                notes TEXT,
                image_url VARCHAR(255),
                status VARCHAR(20) DEFAULT 'pending',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        `);
        console.log('创建store_feedback表成功');
    } catch (err) {
        console.error('创建store_feedback表失败:', err);
    }
}

// 初始化表
createFeedbackTable();

// 获取所有反馈
router.get('/', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const offset = (page - 1) * limit;
        const search = req.query.search || '';
        const status = req.query.status || '';
        
        // 构建查询条件
        let whereClause = '';
        const queryParams = [];
        
        if (search) {
            whereClause += ' WHERE (store_name LIKE ? OR feedback_content LIKE ?)';
            queryParams.push(`%${search}%`, `%${search}%`);
        }
        
        if (status && status !== 'all') {
            whereClause += whereClause ? ' AND status = ?' : ' WHERE status = ?';
            queryParams.push(status);
        }
        
        // 查询总记录数
        const [countResult] = await db.query(
            `SELECT COUNT(*) as total FROM store_feedback${whereClause}`, 
            queryParams
        );
        
        const total = countResult[0].total;
        
        // 查询分页数据
        const [feedbacks] = await db.query(
            `SELECT * FROM store_feedback${whereClause} ORDER BY feedback_time DESC LIMIT ? OFFSET ?`,
            [...queryParams, limit, offset]
        );
        
        // 获取分析数据
        const analytics = await getAnalyticsData();
        
        res.json({
            feedbacks,
            total,
            page,
            limit,
            analytics
        });
    } catch (error) {
        console.error('获取反馈列表失败:', error);
        res.status(500).json({ error: error.message });
    }
});

// 获取分析数据
router.get('/analytics', async (req, res) => {
    try {
        const data = await getAnalyticsData();
        res.json(data);
    } catch (error) {
        console.error('获取分析数据失败:', error);
        res.status(500).json({ error: error.message });
    }
});

// 获取反馈详情
router.get('/:id', async (req, res) => {
    try {
        const [feedbacks] = await db.query(
            'SELECT * FROM store_feedback WHERE id = ?',
            [req.params.id]
        );
        
        if (feedbacks.length === 0) {
            return res.status(404).json({ error: '反馈记录不存在' });
        }
        
        res.json(feedbacks[0]);
    } catch (error) {
        console.error('获取反馈详情失败:', error);
        res.status(500).json({ error: error.message });
    }
});

// 添加新反馈
router.post('/', upload.single('image'), async (req, res) => {
  // 处理WebP转换
  if (req.file) {
    try {
      const originalPath = req.file.path;
      const webpPath = originalPath.replace(/\.[^\.]+$/, '.webp');
      
      // 转换为WebP格式
      await sharp(originalPath)
        .webp({ quality: 75 })
        .toFile(webpPath);
      
      // 删除原始文件
      await fs.promises.unlink(originalPath);
      
      // 更新req.file以指向新的WebP文件
      req.file.filename = req.file.filename.replace(/\.[^\.]+$/, '.webp');
      req.file.path = webpPath;
      req.file.mimetype = 'image/webp';
    } catch (err) {
      console.error('WebP转换失败:', err);
      // 继续处理，使用原始图片
    }
  }
    try {
        console.log('接收到反馈提交请求:', req.body);
        const { store_id, store_name, store_location, feedback_content, notes } = req.body;
        const image_url = req.file ? `/uploads/feedback/${req.file.filename}` : null;
        
        // 验证必填字段
        if ((!store_id && !store_name) || !store_location || !feedback_content) {
            console.error('提交表单验证失败:', { 
                store_id: store_id || '未提供', 
                store_name: store_name || '未提供',
                store_location: store_location || '未提供',
                feedback_content: feedback_content ? '已提供' : '未提供'
            });
            return res.status(400).json({ error: '商铺名称、商铺地点和反馈内容为必填项' });
        }
        
        // 确保store_id是整数且获取商铺名称
        let finalStoreId = store_id;
        let finalStoreName = store_name;
        
        if (store_id) {
            try {
                finalStoreId = parseInt(store_id);
                if (isNaN(finalStoreId)) {
                    throw new Error('无效的商铺ID');
                }
                console.log('使用商铺ID:', finalStoreId);
                
                // 查询店铺名称
                const [storeResult] = await db.query('SELECT name FROM stores WHERE id = ?', [finalStoreId]);
                if (storeResult.length > 0) {
                    finalStoreName = storeResult[0].name;
                    console.log('获取到商铺名称:', finalStoreName);
                } else {
                    console.log('未找到商铺名称，使用默认值');
                    finalStoreName = `商铺${finalStoreId}`;
                }
            } catch (err) {
                console.error('商铺ID格式无效:', store_id);
                return res.status(400).json({ error: '商铺ID格式无效' });
            }
        } else if (store_name) {
            // 如果没有提供store_id，先检查是否存在该名称的商铺
            console.log('尝试使用商铺名称:', store_name);
            const [existingStores] = await db.query('SELECT id FROM stores WHERE name = ?', [store_name]);
            
            if (existingStores.length > 0) {
                finalStoreId = existingStores[0].id;
                finalStoreName = store_name;
                console.log('找到匹配商铺名称的ID:', finalStoreId);
            } else {
                // 如果不存在，创建新商铺
                console.log('创建新商铺:', store_name);
                const [result] = await db.query('INSERT INTO stores (name) VALUES (?)', [store_name]);
                finalStoreId = result.insertId;
                finalStoreName = store_name;
                console.log('新创建商铺ID:', finalStoreId);
            }
        }
        
        // 获取地点名称
        let locationName = '';
        try {
            const [locationResult] = await db.query('SELECT address FROM addresses WHERE id = ?', [store_location]);
            if (locationResult.length > 0) {
                locationName = locationResult[0].address;
                console.log('获取到地点名称:', locationName);
            } else {
                console.log('未找到地点名称，使用ID作为名称');
                locationName = `地点${store_location}`;
            }
        } catch (err) {
            console.error('获取地点名称失败:', err);
            locationName = `地点${store_location}`;
        }
        
        console.log('准备插入反馈记录:', {
            store_id: finalStoreId,
            store_name: finalStoreName,
            store_location,
            location_name: locationName,
            feedback_content,
            notes: notes || null,
            image_url
        });
        
        const [result] = await db.query(
            'INSERT INTO store_feedback (store_name, store_location, feedback_content, notes, image_url) VALUES (?, ?, ?, ?, ?)',
            [finalStoreName, locationName, feedback_content, notes || null, image_url]
        );
        
        console.log('反馈记录已创建, ID:', result.insertId);
        
        res.status(201).json({ 
            id: result.insertId,
            store_id: finalStoreId,
            store_name: finalStoreName,
            store_location,
            location_name: locationName,
            feedback_content,
            notes,
            image_url
        });
    } catch (error) {
        console.error('添加反馈失败:', error);
        res.status(500).json({ error: error.message });
    }
});

// 更新反馈状态
router.put('/:id/status', async (req, res) => {
    try {
        const { id } = req.params;
        const { status } = req.body;
        
        // 验证状态值
        const validStatuses = ['pending', 'in-progress', 'resolved'];
        if (!validStatuses.includes(status)) {
            return res.status(400).json({ error: '无效的状态值' });
        }
        
        const [result] = await db.query(
            'UPDATE store_feedback SET status = ? WHERE id = ?',
            [status, id]
        );
        
        if (result.affectedRows === 0) {
            return res.status(404).json({ error: '反馈记录不存在' });
        }
        
        res.json({ id, status });
    } catch (error) {
        console.error('更新反馈状态失败:', error);
        res.status(500).json({ error: error.message });
    }
});

// 删除反馈
router.delete('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        
        // 先获取反馈记录以找到图片URL
        const [feedbacks] = await db.query(
            'SELECT image_url FROM store_feedback WHERE id = ?',
            [id]
        );
        
        if (feedbacks.length === 0) {
            return res.status(404).json({ error: '反馈记录不存在' });
        }
        
        // 删除图片文件
        const feedback = feedbacks[0];
        if (feedback.image_url) {
            const imagePath = path.join(__dirname, '..', 'public', feedback.image_url);
            if (fs.existsSync(imagePath)) {
                fs.unlinkSync(imagePath);
            }
        }
        
        // 删除记录
        const [result] = await db.query(
            'DELETE FROM store_feedback WHERE id = ?',
            [id]
        );
        
        res.json({ success: true, id });
    } catch (error) {
        console.error('删除反馈失败:', error);
        res.status(500).json({ error: error.message });
    }
});

// 辅助函数：获取分析数据
async function getAnalyticsData() {
    try {
        // 获取所有反馈内容
        const [feedbacks] = await db.query('SELECT * FROM store_feedback');
        
        // 词云数据
        const wordCloud = generateWordCloud(feedbacks);
        
        // 时间分布数据
        const timeDistribution = generateTimeDistribution(feedbacks);
        
        // 状态分布数据
        const statusDistribution = generateStatusDistribution(feedbacks);
        
        // 商铺地点分布
        const locationDistribution = generateLocationDistribution(feedbacks);
        
        // 热门商铺排名
        const storeRanking = generateStoreRanking(feedbacks);
        
        // 问题分类统计
        const categoryDistribution = generateCategoryDistribution(feedbacks);
        
        // 平均解决时间统计
        const resolutionTimeAvg = calculateResolutionTime(feedbacks);
        
        return {
            wordCloud,
            timeDistribution,
            statusDistribution,
            locationDistribution,
            storeRanking,
            categoryDistribution,
            resolutionTimeAvg
        };
    } catch (error) {
        console.error('生成分析数据失败:', error);
        throw error;
    }
}

// 生成词云数据
function generateWordCloud(feedbacks) {
    // 如果没有反馈，返回空数组
    if (!feedbacks || feedbacks.length === 0) {
        return [];
    }
    
    // 合并所有反馈内容
    const allContent = feedbacks.map(f => f.feedback_content).join(' ');
    
    // 简单的词频统计，不使用jieba
    const words = simpleWordExtraction(allContent);
    
    // 返回词云所需格式：[[词, 权重], ...]
    return words.slice(0, 50);
}

// 简单的词频统计
function simpleWordExtraction(text) {
    // 常见的停用词
    const stopWords = ['的', '了', '是', '在', '和', '有', '我', '你', '他', '她', '它', '这', '那', '个', '啊', '吧', '呢',
        '啦', '嗯', '哦', '哎', '就', '也', '都', '要', '不', '没', '很', '太', '可以', '能', '会', '被', '把', '给', '向',
        '从', '为', '比', '于', '但', '所', '如', '或', '而', '之', '与', '一', '以', '让', '来', '去', '到', '等'];
    
    // 将标点符号替换为空格
    const cleanText = text.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, ' ');
    
    // 按空格分词
    const words = cleanText.split(/\s+/);
    
    // 统计词频
    const wordCount = {};
    
    words.forEach(word => {
        if (word && word.length > 1 && !stopWords.includes(word)) {
            if (!wordCount[word]) {
                wordCount[word] = 0;
            }
            wordCount[word]++;
        }
    });
    
    // 转换为数组并排序
    return Object.keys(wordCount)
        .map(word => [word, wordCount[word]])
        .sort((a, b) => b[1] - a[1]);
}

// 生成时间分布数据
function generateTimeDistribution(feedbacks) {
    // 如果没有反馈，返回空对象
    if (!feedbacks || feedbacks.length === 0) {
        return {};
    }
    
    // 按日期分组统计
    const distribution = {};
    
    feedbacks.forEach(feedback => {
        const date = new Date(feedback.feedback_time);
        const dateStr = `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(date.getDate())}`;
        
        if (!distribution[dateStr]) {
            distribution[dateStr] = 0;
        }
        
        distribution[dateStr]++;
    });
    
    // 保留最近7天的数据
    const sortedDates = Object.keys(distribution).sort();
    const recentDates = sortedDates.slice(-7);
    
    const result = {};
    recentDates.forEach(date => {
        result[date] = distribution[date];
    });
    
    return result;
}

// 生成状态分布数据
function generateStatusDistribution(feedbacks) {
    // 如果没有反馈，返回空对象
    if (!feedbacks || feedbacks.length === 0) {
        return {};
    }
    
    // 统计各状态的数量
    const distribution = {
        'pending': 0,
        'in-progress': 0,
        'resolved': 0
    };
    
    feedbacks.forEach(feedback => {
        const status = feedback.status || 'pending';
        if (distribution[status] !== undefined) {
            distribution[status]++;
        }
    });
    
    return distribution;
}

// 生成地点分布数据
function generateLocationDistribution(feedbacks) {
    // 如果没有反馈，返回空对象
    if (!feedbacks || feedbacks.length === 0) {
        return {};
    }
    
    // 按地点分组统计
    const distribution = {};
    
    feedbacks.forEach(feedback => {
        const location = feedback.store_location;
        if (!distribution[location]) {
            distribution[location] = 0;
        }
        
        distribution[location]++;
    });
    
    // 按数量排序
    const sortedLocations = Object.keys(distribution).sort((a, b) => distribution[b] - distribution[a]);
    
    // 取前5个地点
    const topLocations = sortedLocations.slice(0, 5);
    
    const result = {};
    topLocations.forEach(location => {
        result[location] = distribution[location];
    });
    
    return result;
}

// 生成商铺排名数据
function generateStoreRanking(feedbacks) {
    // 如果没有反馈，返回空数组
    if (!feedbacks || feedbacks.length === 0) {
        return [];
    }
    
    // 按商铺名称分组统计
    const storeCount = {};
    
    feedbacks.forEach(feedback => {
        const storeName = feedback.store_name;
        if (!storeCount[storeName]) {
            storeCount[storeName] = 0;
        }
        
        storeCount[storeName]++;
    });
    
    // 转换为数组并排序
    const storeRanking = Object.keys(storeCount).map(store => ({
        name: store,
        count: storeCount[store]
    })).sort((a, b) => b.count - a.count);
    
    // 返回前10个商铺
    return storeRanking.slice(0, 10);
}

// 生成问题分类统计
function generateCategoryDistribution(feedbacks) {
    // 如果没有反馈，返回空对象
    if (!feedbacks || feedbacks.length === 0) {
        return {};
    }
    
    // 问题关键词分类
    const categories = {
        '商品质量': ['质量', '损坏', '破损', '有问题', '不良', '坏了', '不好', '差'],
        '配送问题': ['配送', '送货', '物流', '快递', '延迟', '迟到', '没到', '送错'],
        '服务态度': ['服务', '态度', '服务员', '店员', '不满', '差评', '不好', '差'],
        '价格问题': ['价格', '贵', '便宜', '高价', '降价', '涨价', '优惠', '折扣'],
        '其他问题': []
    };
    
    // 统计各分类的数量
    const distribution = {
        '商品质量': 0,
        '配送问题': 0,
        '服务态度': 0,
        '价格问题': 0,
        '其他问题': 0
    };
    
    feedbacks.forEach(feedback => {
        const content = feedback.feedback_content || '';
        let categorized = false;
        
        // 检查内容是否包含各分类的关键词
        for (const category in categories) {
            if (category === '其他问题') continue;
            
            const keywords = categories[category];
            if (keywords.some(keyword => content.includes(keyword))) {
                distribution[category]++;
                categorized = true;
                break;
            }
        }
        
        // 如果未分类，归为"其他问题"
        if (!categorized) {
            distribution['其他问题']++;
        }
    });
    
    return distribution;
}

// 计算平均解决时间
function calculateResolutionTime(feedbacks) {
    // 过滤出已解决的反馈
    const resolvedFeedbacks = feedbacks.filter(f => f.status === 'resolved');
    
    if (resolvedFeedbacks.length === 0) {
        return { avg: 0, unit: '天' };
    }
    
    let totalDays = 0;
    
    resolvedFeedbacks.forEach(feedback => {
        const createTime = new Date(feedback.created_at);
        const updateTime = new Date(feedback.updated_at);
        
        // 计算时间差（毫秒）
        const timeDiff = updateTime - createTime;
        
        // 转换为天
        const days = timeDiff / (1000 * 60 * 60 * 24);
        totalDays += days;
    });
    
    // 计算平均天数
    const avgDays = totalDays / resolvedFeedbacks.length;
    
    // 如果平均天数小于1，转换为小时
    if (avgDays < 1) {
        const avgHours = avgDays * 24;
        return { avg: avgHours.toFixed(1), unit: '小时' };
    }
    
    return { avg: avgDays.toFixed(1), unit: '天' };
}

// 工具函数：补零
function padZero(num) {
    return num < 10 ? `0${num}` : num;
}

module.exports = router; 