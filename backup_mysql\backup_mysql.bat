@echo off
setlocal enabledelayedexpansion
chcp 65001 >nul

:: ===== Database Configuration =====
set DB_USER=root
set DB_PASSWORD=root
set DB_NAME=delivery_system
set BACKUP_DIR=F:\backup\delivery_system
set MYSQL_DUMP="E:\phpstudy_pro\Extensions\MySQL8.0.12\bin\mysqldump"

:: ===== Gitee Configuration =====
set GITEE_USER=jiang-jinhn
set GIT_REPO_NAME=delivery_system
set GIT_REPO_URL=https://gitee.com/jiang-jinhn/delivery_system.git
set GIT_REPO_DIR=F:\gitee_repos\%GIT_REPO_NAME%
set GIT_BRANCH=main
set BACKUP_SUBDIR=backup_mysql

:: Set Git global config
git config --global user.name "楠枏"
git config --global user.email "<EMAIL>"

:: Create backup directory
if not exist "%BACKUP_DIR%" mkdir "%BACKUP_DIR%"

:: Initialize or update Git repository
if not exist "%GIT_REPO_DIR%" (
    echo Cloning Git repository...
    git clone %GIT_REPO_URL% "%GIT_REPO_DIR%"
    if %errorlevel% neq 0 (
        echo ERROR: Git repository clone failed
        pause
        exit /b 1
    )
)

:: Get current date/time for backup file
for /f "usebackq delims=" %%i in (`powershell -Command "Get-Date -Format 'yyyyMMdd_HHmmss'"`) do set FILE_DATE=%%i

:: Get current date/time for commit message
for /f "usebackq delims=" %%i in (`powershell -Command "Get-Date -Format 'yyyy年MM月dd日 HH:mm:ss'"`) do set COMMIT_DATE=%%i

:: Create backup filename
set BACKUP_FILE=%BACKUP_DIR%\delivery_system_backup_%FILE_DATE%.sql

:: ===== 修复：安全执行备份 =====
:: 方法1：使用配置文件（推荐）
echo [client] > %BACKUP_DIR%\mysql_temp.cnf
echo user = %DB_USER% >> %BACKUP_DIR%\mysql_temp.cnf
echo password = %DB_PASSWORD% >> %BACKUP_DIR%\mysql_temp.cnf

%MYSQL_DUMP% --defaults-file="%BACKUP_DIR%\mysql_temp.cnf" %DB_NAME% > %BACKUP_FILE% 2> %BACKUP_DIR%\backup_error.log

:: 方法2：使用环境变量（备选）
:: set MYSQL_PWD=%DB_PASSWORD%
:: %MYSQL_DUMP% -u%DB_USER% %DB_NAME% > %BACKUP_FILE% 2> %BACKUP_DIR%\backup_error.log

:: 删除临时配置文件
del /f /q "%BACKUP_DIR%\mysql_temp.cnf" 2>nul

:: Check if backup succeeded
if %errorlevel% neq 0 (
    echo Backup FAILED. Check error log: %BACKUP_DIR%\backup_error.log
    type "%BACKUP_DIR%\backup_error.log"
    pause
    exit /b 1
)

:: 检查SQL文件是否有效
findstr /i /c:"mysqldump: [Warning]" "%BACKUP_FILE%" >nul
if %errorlevel% equ 0 (
    echo ERROR: SQL file is corrupted by warning messages
    del "%BACKUP_FILE%"
    pause
    exit /b 1
)

echo Backup SUCCESS: %BACKUP_FILE%

:: ===== Sync to Gitee =====
:: Create backup subdirectory in Git repo
if not exist "%GIT_REPO_DIR%\%BACKUP_SUBDIR%" (
    mkdir "%GIT_REPO_DIR%\%BACKUP_SUBDIR%"
)

:: Copy backup to Git repo subdirectory
copy "%BACKUP_FILE%" "%GIT_REPO_DIR%\%BACKUP_SUBDIR%\" >nul

:: Enter Git repository directory
cd /d "%GIT_REPO_DIR%"

:: Ensure we're on the correct branch
git checkout %GIT_BRANCH% 2>nul
if %errorlevel% neq 0 (
    echo Creating branch %GIT_BRANCH%...
    git checkout -b %GIT_BRANCH% 2>&1
)

:: Pull latest changes
git pull origin %GIT_BRANCH% 2>&1

:: Add and commit new backup with descriptive message
git add "%BACKUP_SUBDIR%\delivery_system_backup_%FILE_DATE%.sql"
git commit -m "数据库备份: %COMMIT_DATE%" 2>&1

:: Push to Gitee
git push origin %GIT_BRANCH% 2>&1

:: Clean up old backups (keep last 7 days)
forfiles /p "%GIT_REPO_DIR%\%BACKUP_SUBDIR%" /m *.sql /d -7 /c "cmd /c del @path" 2>nul

:: Commit cleanup only if there are changes
cd /d "%GIT_REPO_DIR%\%BACKUP_SUBDIR%"
git add . 2>nul
git diff --quiet && git diff --staged --quiet || (
    git commit -m "清理7天前旧备份" 2>&1
    git push origin %GIT_BRANCH% 2>&1
)

echo Backup uploaded to Gitee: %GIT_REPO_URL%/%BACKUP_SUBDIR%
echo Completion time: %date% %time%
endlocal