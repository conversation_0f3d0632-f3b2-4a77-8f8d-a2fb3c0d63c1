/**
 * {{RESOURCE_NAME}}管理系统 - 现代化JavaScript模块
 * 支持响应式设计、主题切换、动态字段处理
 */

class {{RESOURCE_CLASS_NAME}} {
    constructor() {
        // 配置常量
        this.config = {
            apiEndpoint: '/api/{{TABLE_NAME}}',
            pageSize: 10,
            maxPageButtons: 5,
            uploadEndpoint: '/api/upload',
            supportedImageTypes: ['image/jpeg', 'image/png', 'image/webp'],
            maxImageSize: 5 * 1024 * 1024, // 5MB
            // 认证配置
            auth: {
                username: 'admin',
                password: 'admin'
            }
        };
        
        // 状态管理
        this.state = {
            currentPage: 1,
            totalRecords: 0,
            selectedItems: new Set(),
            isLoading: false,
            currentEditId: null,
            searchQuery: '',
            brandFilter: 'all',
            sortField: null,
            sortDirection: 'asc'
        };
        
        // DOM元素缓存
        this.elements = {};
        
        // 数据缓存
        this.dataCache = {
            items: [],
            brands: new Set()
        };
        
        // 初始化
        this.init();
    }

    /**
     * 获取认证头部
     */
    getAuthHeaders() {
        // 优先使用JWT token
        const token = localStorage.getItem('authToken') || localStorage.getItem('token');
        if (token && token !== 'null' && token !== 'undefined') {
            return {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            };
        }

        // 如果没有有效token，尝试自动登录获取token
        this.autoLogin();

        // 临时使用Basic Auth（在获取token之前）
        const credentials = btoa(`${this.config.auth.username}:${this.config.auth.password}`);
        return {
            'Authorization': `Basic ${credentials}`,
            'Content-Type': 'application/json'
        };
    }

    /**
     * 自动登录获取token
     */
    async autoLogin() {
        try {
            const response = await fetch('/api/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: this.config.auth.username,
                    password: this.config.auth.password
                })
            });

            if (response.ok) {
                const result = await response.json();
                if (result.token) {
                    localStorage.setItem('authToken', result.token);
                    localStorage.setItem('token', result.token);
                    console.log('自动登录成功');
                }
            }
        } catch (error) {
            console.warn('自动登录失败:', error);
        }
    }

    /**
     * 初始化应用
     */
    async init() {
        try {
            console.log('初始化{{RESOURCE_NAME}}管理系统...');
            
            // 等待DOM加载完成
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.initializeApp());
            } else {
                this.initializeApp();
            }
        } catch (error) {
            console.error('初始化失败:', error);
            this.showToast('系统初始化失败', 'error');
        }
    }
    
    /**
     * 初始化应用程序
     */
    async initializeApp() {
        // 缓存DOM元素
        this.cacheElements();

        // 初始化主题系统
        this.initTheme();

        // 初始化图片上传相关变量
        this.currentImageFile = null;
        this.currentImageUrl = null;

        // 设置事件监听器
        this.setupEventListeners();

        // 初始化表单验证
        this.initFormValidation();

        // 加载初始数据
        await this.loadData();

        // 检查用户权限
        await this.checkUserPermissions();

        console.log('{{RESOURCE_NAME}}管理系统初始化完成');
    }

    /**
     * 检查用户权限
     */
    async checkUserPermissions() {
        try {
            // 检查是否有有效的认证token
            const token = localStorage.getItem('authToken') || localStorage.getItem('token');

            if (!token || token === 'null' || token === 'undefined') {
                console.warn('未找到有效的认证token，尝试自动登录...');
                await this.autoLogin();
                return;
            }

            // 验证token有效性
            const response = await fetch('/api/validate-token', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                }
            });

            if (!response.ok) {
                console.warn('Token验证失败，尝试重新登录...');
                localStorage.removeItem('authToken');
                localStorage.removeItem('token');
                await this.autoLogin();
            } else {
                const result = await response.json();
                console.log('用户权限验证成功:', result.user);
            }

        } catch (error) {
            console.warn('权限检查失败:', error);
            // 权限检查失败不应该阻止页面加载
        }
    }

    /**
     * 缓存DOM元素
     */
    cacheElements() {
        // 缓存主要元素
        this.elements.{{RESOURCE_JS_PROPERTY}}Form = document.getElementById('{{RESOURCE_JS_PROPERTY}}Form');
        this.elements.{{RESOURCE_JS_PROPERTY}}TableBody = document.getElementById('{{RESOURCE_JS_PROPERTY}}TableBody');
        this.elements.{{RESOURCE_JS_PROPERTY}}Search = document.getElementById('{{RESOURCE_JS_PROPERTY}}Search');
        this.elements.{{RESOURCE_JS_PROPERTY}}Image = document.getElementById('{{RESOURCE_JS_PROPERTY}}Image');

        // 缓存通用元素
        const commonElementIds = [
            'brandFilter',
            'smartInput',
            'autoFillBtn',
            'editBtn',
            'deleteBtn',
            'selectAll',
            'totalCount',
            'loadingState',
            'emptyState',
            'prevPage',
            'nextPage',
            'pageNumbers',
            'themeToggle',
            'imagePreviewContainer',
            'imagePreview'
        ];

        commonElementIds.forEach(id => {
            this.elements[id] = document.getElementById(id);
        });

        // 动态字段元素
        {{DYNAMIC_FIELD_ELEMENTS}}

        // 添加价格和备注字段
        this.elements.price = document.getElementById('price');
        this.elements.notes = document.getElementById('notes');
    }
    
    /**
     * 初始化主题系统
     */
    initTheme() {
        // 从localStorage获取保存的主题
        const savedTheme = localStorage.getItem('theme') || 'light';
        this.setTheme(savedTheme);
        
        // 监听系统主题变化
        if (window.matchMedia) {
            window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
                if (!localStorage.getItem('theme')) {
                    this.setTheme(e.matches ? 'dark' : 'light');
                }
            });
        }
    }
    
    /**
     * 设置主题
     */
    setTheme(theme) {
        const html = document.documentElement;
        const themeToggle = this.elements.themeToggle;
        
        if (theme === 'dark') {
            html.classList.add('dark');
            if (themeToggle) {
                themeToggle.innerHTML = '<i class="fas fa-sun text-yellow-400"></i>';
            }
        } else {
            html.classList.remove('dark');
            if (themeToggle) {
                themeToggle.innerHTML = '<i class="fas fa-moon text-gray-600"></i>';
            }
        }
        
        localStorage.setItem('theme', theme);
        
        // 触发主题切换动画
        document.body.style.transition = 'all 0.3s ease';
        setTimeout(() => {
            document.body.style.transition = '';
        }, 300);
    }
    
    /**
     * 切换主题
     */
    toggleTheme() {
        const currentTheme = localStorage.getItem('theme') || 'light';
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        this.setTheme(newTheme);
        
        // 显示切换提示
        this.showToast(`已切换到${newTheme === 'dark' ? '深色' : '浅色'}主题`, 'success');
    }
    
    /**
     * 初始化表单验证
     */
    initFormValidation() {
        if (!this.elements.{{RESOURCE_JS_PROPERTY}}Form) return;

        // 添加表单验证样式
        const form = this.elements.{{RESOURCE_JS_PROPERTY}}Form;
        const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');

        inputs.forEach(input => {
            input.addEventListener('blur', () => this.validateField(input));
            input.addEventListener('input', () => this.clearFieldError(input));
        });
    }

    /**
     * 验证单个字段
     */
    validateField(field) {
        const isValid = field.checkValidity();
        const errorElement = field.parentElement.querySelector('.error-message');

        if (!isValid) {
            field.classList.add('border-red-500');
            field.classList.remove('border-gray-300');

            if (!errorElement) {
                const error = document.createElement('div');
                error.className = 'error-message text-red-500 text-sm mt-1';
                error.textContent = field.validationMessage;
                field.parentElement.appendChild(error);
            }
        } else {
            this.clearFieldError(field);
        }

        return isValid;
    }

    /**
     * 清除字段错误状态
     */
    clearFieldError(field) {
        field.classList.remove('border-red-500');
        field.classList.add('border-gray-300');

        const errorElement = field.parentElement.querySelector('.error-message');
        if (errorElement) {
            errorElement.remove();
        }
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 主题切换
        if (this.elements.themeToggle) {
            this.elements.themeToggle.addEventListener('click', () => this.toggleTheme());
        }
        
        // 表单提交
        if (this.elements.{{RESOURCE_JS_PROPERTY}}Form) {
            this.elements.{{RESOURCE_JS_PROPERTY}}Form.addEventListener('submit', (e) => this.handleFormSubmit(e));
        }

        // 搜索功能
        if (this.elements.{{RESOURCE_JS_PROPERTY}}Search) {
            this.elements.{{RESOURCE_JS_PROPERTY}}Search.addEventListener('input',
                this.debounce((e) => this.handleSearch(e.target.value), 300));
        }
        
        // 品牌筛选
        if (this.elements.brandFilter) {
            this.elements.brandFilter.addEventListener('change', (e) => this.handleBrandFilter(e.target.value));
        }
        
        // 智能识别
        if (this.elements.autoFillBtn) {
            this.elements.autoFillBtn.addEventListener('click', () => this.handleSmartFill());
        }
        
        // 图片上传
        const imageUpload = document.getElementById('image-upload');
        if (imageUpload) {
            imageUpload.addEventListener('change', (e) => this.handleImageUpload(e));
        }

        // 移除图片
        const removeImageBtn = document.getElementById('removeImageBtn');
        if (removeImageBtn) {
            removeImageBtn.addEventListener('click', () => this.removeImagePreview());
        }
        
        // 批量操作
        if (this.elements.selectAll) {
            this.elements.selectAll.addEventListener('change', (e) => this.handleSelectAll(e.target.checked));
        }
        
        // 编辑和删除按钮
        if (this.elements.editBtn) {
            this.elements.editBtn.addEventListener('click', () => this.handleEdit());
        }
        
        if (this.elements.deleteBtn) {
            this.elements.deleteBtn.addEventListener('click', () => this.handleDelete());
        }
        
        // 分页按钮
        if (this.elements.prevPage) {
            this.elements.prevPage.addEventListener('click', () => this.goToPage(this.state.currentPage - 1));
        }
        
        if (this.elements.nextPage) {
            this.elements.nextPage.addEventListener('click', () => this.goToPage(this.state.currentPage + 1));
        }
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));
        
        // 移动端触摸事件
        this.setupTouchEvents();
    }
    
    /**
     * 设置移动端触摸事件
     */
    setupTouchEvents() {
        let touchStartX = 0;
        let touchStartY = 0;
        
        document.addEventListener('touchstart', (e) => {
            touchStartX = e.touches[0].clientX;
            touchStartY = e.touches[0].clientY;
        });
        
        document.addEventListener('touchend', (e) => {
            const touchEndX = e.changedTouches[0].clientX;
            const touchEndY = e.changedTouches[0].clientY;
            
            const deltaX = touchEndX - touchStartX;
            const deltaY = touchEndY - touchStartY;
            
            // 水平滑动切换页面
            if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
                if (deltaX > 0 && this.state.currentPage > 1) {
                    this.goToPage(this.state.currentPage - 1);
                } else if (deltaX < 0) {
                    this.goToPage(this.state.currentPage + 1);
                }
            }
        });
    }
    
    /**
     * 处理键盘快捷键
     */
    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + S: 保存表单
        if ((e.ctrlKey || e.metaKey) && e.key === 's') {
            e.preventDefault();
            if (this.elements.{{RESOURCE_JS_PROPERTY}}Form) {
                this.elements.{{RESOURCE_JS_PROPERTY}}Form.dispatchEvent(new Event('submit'));
            }
        }

        // Ctrl/Cmd + F: 聚焦搜索框
        if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
            e.preventDefault();
            if (this.elements.{{RESOURCE_JS_PROPERTY}}Search) {
                this.elements.{{RESOURCE_JS_PROPERTY}}Search.focus();
            }
        }
        
        // Escape: 取消编辑
        if (e.key === 'Escape') {
            this.cancelEdit();
        }
    }
    
    /**
     * 防抖函数
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    /**
     * 显示Toast通知
     */
    showToast(message, type = 'info', duration = 3000) {
        const toastContainer = document.getElementById('toastContainer') || this.createToastContainer();
        
        const toast = document.createElement('div');
        toast.className = `
            px-4 py-3 rounded-lg shadow-lg transform transition-all duration-300 translate-x-full
            ${type === 'success' ? 'bg-green-500 text-white' : ''}
            ${type === 'error' ? 'bg-red-500 text-white' : ''}
            ${type === 'warning' ? 'bg-yellow-500 text-white' : ''}
            ${type === 'info' ? 'bg-blue-500 text-white' : ''}
        `;
        
        const icon = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        }[type];
        
        toast.innerHTML = `
            <div class="flex items-center space-x-2">
                <i class="${icon}"></i>
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" class="ml-2 hover:opacity-75">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        toastContainer.appendChild(toast);
        
        // 动画显示
        setTimeout(() => {
            toast.classList.remove('translate-x-full');
        }, 100);
        
        // 自动隐藏
        setTimeout(() => {
            toast.classList.add('translate-x-full');
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.parentElement.removeChild(toast);
                }
            }, 300);
        }, duration);
    }
    
    /**
     * 创建Toast容器
     */
    createToastContainer() {
        const container = document.createElement('div');
        container.id = 'toastContainer';
        container.className = 'fixed top-4 right-4 z-50 space-y-2';
        document.body.appendChild(container);
        return container;
    }
    
    /**
     * 显示加载状态
     */
    showLoading(show = true) {
        this.state.isLoading = show;
        
        if (this.elements.loadingState) {
            this.elements.loadingState.classList.toggle('hidden', !show);
        }
        
        if (this.elements.emptyState) {
            this.elements.emptyState.classList.add('hidden');
        }
        
        // 禁用表单提交按钮
        const submitBtn = this.elements.{{RESOURCE_JS_PROPERTY}}Form?.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = show;
            if (show) {
                submitBtn.innerHTML = '<div class="loading-spinner mr-2"></div>处理中...';
            } else {
                submitBtn.innerHTML = '<i class="fas fa-save mr-2"></i>保存{{RESOURCE_NAME}}信息';
            }
        }
    }
    
    /**
     * API请求封装
     */
    async apiRequest(url, options = {}) {
        const defaultOptions = {
            headers: this.getAuthHeaders()
        };

        // 合并选项，确保认证头部不被覆盖
        const config = {
            ...defaultOptions,
            ...options,
            headers: {
                ...defaultOptions.headers,
                ...(options.headers || {})
            }
        };

        try {
            const response = await fetch(url, config);

            if (!response.ok) {
                if (response.status === 401) {
                    console.warn('认证失败，尝试重新登录...');
                    await this.autoLogin();

                    // 重新获取认证头部并重试一次
                    const newConfig = {
                        ...config,
                        headers: {
                            ...this.getAuthHeaders(),
                            ...(config.headers || {})
                        }
                    };

                    const retryResponse = await fetch(url, newConfig);
                    if (!retryResponse.ok) {
                        this.showToast('认证失败，请检查登录状态', 'error');
                        throw new Error(`HTTP ${retryResponse.status}: ${retryResponse.statusText}`);
                    }

                    // 检查响应是否有内容
                    const contentType = retryResponse.headers.get('content-type');
                    if (contentType && contentType.includes('application/json')) {
                        const text = await retryResponse.text();
                        return text ? JSON.parse(text) : {};
                    }
                    return {};
                }
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            // 检查响应是否有内容
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                const text = await response.text();
                return text ? JSON.parse(text) : {};
            }
            return {};
        } catch (error) {
            console.error('API请求失败:', error);
            throw error;
        }
    }
    
    /**
     * 加载数据
     */
    async loadData() {
        try {
            this.showLoading(true);
            
            const params = new URLSearchParams({
                page: this.state.currentPage,
                limit: this.config.pageSize,
                search: this.state.searchQuery,
                brand: this.state.brandFilter !== 'all' ? this.state.brandFilter : '',
                sort: this.state.sortField || '',
                direction: this.state.sortDirection
            });
            
            const data = await this.apiRequest(`${this.config.apiEndpoint}?${params}`);

            this.dataCache.items = data.items || [];
            this.state.totalRecords = data.total || 0;
            
            // 更新品牌筛选选项
            this.updateBrandFilter();
            
            // 渲染表格
            this.renderTable();
            
            // 更新分页
            this.updatePagination();
            
            // 更新统计信息
            this.updateStats();
            
        } catch (error) {
            console.error('加载数据失败:', error);
            this.showToast('加载数据失败: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * 更新统计信息
     */
    updateStats() {
        if (!this.dataCache.items) return;

        const total = this.state.totalRecords || 0;
        const current = this.dataCache.items.length;
        const page = this.state.currentPage;
        const pageSize = this.config.pageSize;

        // 更新统计显示
        const statsElement = document.querySelector('.stats-info');
        if (statsElement) {
            const startIndex = (page - 1) * pageSize + 1;
            const endIndex = Math.min(page * pageSize, total);
            statsElement.textContent = `显示第 ${startIndex} 到 ${endIndex} 条，共 ${total} 条记录`;
        }

        // 更新页面标题中的计数
        const titleCount = document.querySelector('.title-count');
        if (titleCount) {
            titleCount.textContent = `(${total})`;
        }
    }

    /**
     * 更新品牌筛选器
     */
    updateBrandFilter() {
        if (!this.elements.brandFilter || !this.dataCache.items) return;

        // 获取所有唯一品牌
        const brands = [...new Set(this.dataCache.items.map(item => item.brand).filter(Boolean))];

        // 清空现有选项（保留"全部"选项）
        const currentValue = this.elements.brandFilter.value;
        this.elements.brandFilter.innerHTML = '<option value="">全部品牌</option>';

        // 添加品牌选项
        brands.forEach(brand => {
            const option = document.createElement('option');
            option.value = brand;
            option.textContent = brand;
            if (brand === currentValue) {
                option.selected = true;
            }
            this.elements.brandFilter.appendChild(option);
        });
    }
    
    /**
     * 渲染表格
     */
    renderTable() {
        const tbody = this.elements.{{RESOURCE_JS_PROPERTY}}TableBody;
        if (!tbody) return;

        tbody.innerHTML = '';

        if (this.dataCache.items.length === 0) {
            this.elements.emptyState?.classList.remove('hidden');
            return;
        }

        this.elements.emptyState?.classList.add('hidden');

        this.dataCache.items.forEach(item => {
            const row = this.createTableRow(item);
            tbody.appendChild(row);
        });
    }

    /**
     * 创建表格行
     */
    createTableRow(item) {
        const row = document.createElement('tr');
        row.className = 'table-row-hover cursor-pointer';
        row.dataset.id = item.id;

        // 选择框
        const selectCell = document.createElement('td');
        selectCell.className = 'px-4 py-3';
        selectCell.innerHTML = `<input type="checkbox" class="item-checkbox rounded border-gray-300 dark:border-gray-600" value="${item.id}">`;
        row.appendChild(selectCell);

        // 动态字段列
        {{DYNAMIC_TABLE_CELLS}}

        // 操作列
        const actionCell = document.createElement('td');
        actionCell.className = 'px-4 py-3';
        actionCell.innerHTML = `
            <div class="flex space-x-2">
                <button onclick="{{RESOURCE_JS_PROPERTY}}Manager.editItem(${item.id})"
                        class="p-1 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors">
                    <i class="fas fa-edit"></i>
                </button>
                <button onclick="{{RESOURCE_JS_PROPERTY}}Manager.deleteItem(${item.id})"
                        class="p-1 text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 transition-colors">
                    <i class="fas fa-trash"></i>
                </button>
                <button onclick="{{RESOURCE_JS_PROPERTY}}Manager.viewItem(${item.id})"
                        class="p-1 text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300 transition-colors">
                    <i class="fas fa-eye"></i>
                </button>
            </div>
        `;
        row.appendChild(actionCell);

        // 行点击事件
        row.addEventListener('click', (e) => {
            if (!e.target.closest('button') && !e.target.closest('input')) {
                this.selectRow(row);
            }
        });

        return row;
    }

    /**
     * 选择表格行
     */
    selectRow(row) {
        // 移除其他行的选中状态
        document.querySelectorAll('tr.selected').forEach(r => r.classList.remove('selected'));

        // 添加选中状态
        row.classList.add('selected', 'bg-blue-50', 'dark:bg-blue-900/20');

        // 启用编辑和删除按钮
        if (this.elements.editBtn) this.elements.editBtn.disabled = false;
        if (this.elements.deleteBtn) this.elements.deleteBtn.disabled = false;

        // 保存选中的ID
        this.state.selectedId = row.dataset.id;
    }

    /**
     * 更新分页
     */
    updatePagination() {
        const totalPages = Math.ceil(this.state.totalRecords / this.config.pageSize);
        const pageNumbers = this.elements.pageNumbers;

        if (!pageNumbers) return;

        pageNumbers.innerHTML = '';

        // 计算显示的页码范围
        const startPage = Math.max(1, this.state.currentPage - Math.floor(this.config.maxPageButtons / 2));
        const endPage = Math.min(totalPages, startPage + this.config.maxPageButtons - 1);

        // 生成页码按钮
        for (let i = startPage; i <= endPage; i++) {
            const button = document.createElement('button');
            button.className = `px-3 py-2 border rounded-lg transition-colors touch-target ${
                i === this.state.currentPage
                    ? 'bg-blue-600 text-white border-blue-600'
                    : 'border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700'
            }`;
            button.textContent = i;
            button.addEventListener('click', () => this.goToPage(i));
            pageNumbers.appendChild(button);
        }

        // 更新上一页/下一页按钮状态
        if (this.elements.prevPage) {
            this.elements.prevPage.disabled = this.state.currentPage <= 1;
        }

        if (this.elements.nextPage) {
            this.elements.nextPage.disabled = this.state.currentPage >= totalPages;
        }
    }

    /**
     * 跳转到指定页面
     */
    async goToPage(page) {
        const totalPages = Math.ceil(this.state.totalRecords / this.config.pageSize);

        if (page < 1 || page > totalPages) return;

        this.state.currentPage = page;
        await this.loadData();
    }

    /**
     * 处理图片上传
     */
    handleImageUpload(e) {
        console.log('上传{{RESOURCE_NAME}}图片');

        const file = e.target.files[0];
        if (!file) return;

        // 验证文件类型
        const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!validTypes.includes(file.type)) {
            this.showToast('请选择有效的图片文件（JPG, PNG, GIF, WEBP）', 'error');
            e.target.value = ''; // 清除文件选择
            return;
        }

        // 验证文件大小（最大2MB）
        if (file.size > 2 * 1024 * 1024) {
            this.showToast('图片文件大小不能超过2MB', 'error');
            e.target.value = ''; // 清除文件选择
            return;
        }

        // 存储文件到实例变量
        this.currentImageFile = file;

        // 显示图片预览
        const reader = new FileReader();
        reader.onload = (event) => {
            const imagePreviewContainer = document.getElementById('imagePreviewContainer');
            const imagePreview = document.getElementById('imagePreview');

            if (imagePreviewContainer && imagePreview) {
                imagePreview.src = event.target.result;
                imagePreviewContainer.classList.remove('hidden');

                // 添加点击预览功能
                imagePreview.classList.add('cursor-pointer');
                imagePreview.onclick = () => {
                    this.openImageFullscreen(event.target.result);
                };
            }

            // 显示上传提示
            this.showUploadNotification(file);
        };
        reader.readAsDataURL(file);
    }

    /**
     * 显示上传通知
     */
    showUploadNotification(file) {
        // 查找或创建上传通知元素
        let uploadNotification = document.querySelector('.upload-notification');
        if (!uploadNotification) {
            const imageInput = document.getElementById('image-upload');
            if (imageInput && imageInput.parentNode) {
                uploadNotification = document.createElement('div');
                uploadNotification.className = 'upload-notification mt-2 text-xs text-indigo-600 dark:text-indigo-400 flex items-center';
                imageInput.parentNode.appendChild(uploadNotification);
            }
        }

        if (uploadNotification) {
            uploadNotification.innerHTML = `<i class="fas fa-sync fa-spin mr-1"></i>图片将自动转换为WebP格式以优化加载速度 (文件大小: ${(file.size / 1024).toFixed(1)}KB)`;
        }
    }

    /**
     * 移除图片预览
     */
    removeImagePreview() {
        const imagePreviewContainer = document.getElementById('imagePreviewContainer');
        const imagePreview = document.getElementById('imagePreview');
        const imageInput = document.getElementById('image-upload');

        if (imagePreviewContainer) {
            imagePreviewContainer.classList.add('hidden');
        }

        if (imagePreview) {
            imagePreview.src = '#';
            imagePreview.onclick = null;
        }

        if (imageInput) {
            imageInput.value = '';
        }

        // 清除存储的文件
        this.currentImageFile = null;
        this.currentImageUrl = null;

        // 移除上传通知
        const uploadNotification = document.querySelector('.upload-notification');
        if (uploadNotification) {
            uploadNotification.remove();
        }
    }

    /**
     * 上传图片到服务器
     */
    async uploadImage(file) {
        const formData = new FormData();
        formData.append('image', file);
        formData.append('folder', '{{RESOURCE_NAME_LOWER}}'); // 使用资源名称作为文件夹名

        try {
            // 获取认证头部，但移除Content-Type让浏览器自动设置（FormData需要）
            const authHeaders = this.getAuthHeaders();
            delete authHeaders['Content-Type'];

            const response = await fetch(this.config.uploadEndpoint, {
                method: 'POST',
                headers: authHeaders,
                body: formData
            });

            if (!response.ok) {
                throw new Error(`上传失败: ${response.status} ${response.statusText}`);
            }

            const result = await response.json();

            if (result.success) {
                // 保存图片URL到状态中
                this.currentImageUrl = result.imageUrl;

                // 更新隐藏的image_url字段（如果存在）
                const imageUrlElement = document.getElementById('image_url');
                if (imageUrlElement) {
                    imageUrlElement.value = result.imageUrl;
                }

                // 显示上传成功消息
                this.showToast('图片上传成功', 'success');

                console.log('图片上传成功，URL:', result.imageUrl);
            } else {
                throw new Error(result.message || '上传失败');
            }

        } catch (error) {
            console.error('图片上传错误:', error);
            throw error;
        }
    }

    /**
     * 处理表单提交
     */
    async handleFormSubmit(e) {
        e.preventDefault();

        try {
            this.showLoading(true);

            const formData = new FormData();

            // 收集表单数据
            {{DYNAMIC_FORM_DATA_COLLECTION}}

            // 处理图片上传 - 直接添加文件到FormData
            if (this.currentImageFile) {
                formData.append('image', this.currentImageFile);
                console.log('添加图片到表单:', this.currentImageFile.name, '类型:', this.currentImageFile.type, '大小:', (this.currentImageFile.size / 1024).toFixed(1) + 'KB');

                // 更新上传提示
                const uploadNotification = document.querySelector('.upload-notification');
                if (uploadNotification) {
                    uploadNotification.innerHTML = '<i class="fas fa-cloud-upload-alt mr-1"></i>图片正在上传并转换为WebP格式...';
                }
            }

            const url = this.state.currentEditId
                ? `${this.config.apiEndpoint}/${this.state.currentEditId}`
                : this.config.apiEndpoint;

            const method = this.state.currentEditId ? 'PUT' : 'POST';

            // 获取认证头部，但移除Content-Type让浏览器自动设置（FormData需要）
            const authHeaders = this.getAuthHeaders();
            delete authHeaders['Content-Type'];

            const response = await fetch(url, {
                method,
                body: formData,
                headers: authHeaders
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();

            this.showToast(
                this.state.currentEditId ? '更新成功' : '添加成功',
                'success'
            );

            // 重置表单
            this.resetForm();

            // 重新加载数据
            await this.loadData();

        } catch (error) {
            console.error('提交失败:', error);
            this.showToast('操作失败: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * 重置表单
     */
    resetForm() {
        if (this.elements.{{RESOURCE_JS_PROPERTY}}Form) {
            this.elements.{{RESOURCE_JS_PROPERTY}}Form.reset();
        }

        this.state.currentEditId = null;

        // 清理图片状态
        this.currentImageFile = null;
        this.currentImageUrl = null;

        // 移除图片预览
        this.removeImagePreview();

        // 重置按钮文本
        const submitBtn = this.elements.{{RESOURCE_JS_PROPERTY}}Form?.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.innerHTML = '<i class="fas fa-save mr-2"></i>保存{{RESOURCE_NAME}}信息';
        }
    }

    /**
     * 编辑项目
     */
    async editItem(id) {
        try {
            this.showLoading(true);

            const response = await this.apiRequest(`${this.config.apiEndpoint}/${id}`);

            // 填充表单
            {{DYNAMIC_FORM_FILL}}

            this.state.currentEditId = id;

            // 处理图片显示
            if (response.image_url) {
                // 清除当前上传的图片状态（编辑时使用数据库中的图片）
                this.currentImageUrl = null;

                // 显示现有图片
                if (this.elements.imagePreview) {
                    this.elements.imagePreview.src = response.image_url;
                }
                if (this.elements.imagePreviewContainer) {
                    this.elements.imagePreviewContainer.classList.remove('hidden');
                }
            }

            // 更新按钮文本
            const submitBtn = this.elements.{{RESOURCE_JS_PROPERTY}}Form?.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-save mr-2"></i>更新{{RESOURCE_NAME}}信息';
            }

            // 滚动到表单
            this.elements.{{RESOURCE_JS_PROPERTY}}Form?.scrollIntoView({ behavior: 'smooth' });

        } catch (error) {
            console.error('加载编辑数据失败:', error);
            this.showToast('加载数据失败: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * 删除项目
     */
    async deleteItem(id) {
        if (!confirm('确定要删除这个{{RESOURCE_NAME}}吗？此操作不可撤销。')) {
            return;
        }

        try {
            this.showLoading(true);

            const response = await this.apiRequest(`${this.config.apiEndpoint}/${id}`, {
                method: 'DELETE'
            });

            // 删除成功（204状态码或其他成功响应）
            this.showToast('删除成功', 'success');

            // 重新加载数据
            await this.loadData();

        } catch (error) {
            console.error('删除失败:', error);

            // 提供更友好的错误信息
            let errorMessage = '删除失败';
            if (error.message.includes('404')) {
                errorMessage = '要删除的记录不存在';
            } else if (error.message.includes('403')) {
                errorMessage = '没有删除权限';
            } else if (error.message.includes('401')) {
                errorMessage = '认证失败，请重新登录';
            } else if (error.message) {
                errorMessage = `删除失败: ${error.message}`;
            }

            this.showToast(errorMessage, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * 查看项目详情
     */
    viewItem(id) {
        const item = this.dataCache.items.find(item => item.id == id);
        if (!item) return;

        // 创建详情模态框
        this.showItemModal(item);
    }

    /**
     * 显示项目详情模态框
     */
    showItemModal(item) {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4';
        modal.innerHTML = `
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white">{{RESOURCE_NAME}}详情</h3>
                        <button onclick="this.closest('.fixed').remove()"
                                class="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
                            <i class="fas fa-times text-gray-500"></i>
                        </button>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-4">
                            {{DYNAMIC_DETAIL_FIELDS}}
                        </div>

                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">产品图片</label>
                                <img src="${item.image_url || '/images/default-{{RESOURCE_NAME_LOWER}}.png'}"
                                     alt="${item.name || '{{RESOURCE_NAME}}'}"
                                     class="w-full h-48 object-cover rounded-lg border border-gray-200 dark:border-gray-700 cursor-pointer"
                                     onclick="{{RESOURCE_JS_PROPERTY}}Manager.openImageFullscreen('${item.image_url || '/images/default-{{RESOURCE_NAME_LOWER}}.png'}')">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">价格</label>
                                <p class="text-lg font-semibold text-green-600 dark:text-green-400">
                                    ${item.price ? '¥' + parseFloat(item.price).toFixed(2) : '未设置'}
                                </p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">备注</label>
                                <p class="text-gray-600 dark:text-gray-400">${item.notes || '无'}</p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">创建时间</label>
                                <p class="text-gray-600 dark:text-gray-400">${new Date(item.created_at).toLocaleString()}</p>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 flex justify-end space-x-3">
                        <button onclick="{{RESOURCE_JS_PROPERTY}}Manager.editItem(${item.id}); this.closest('.fixed').remove();"
                                class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                            <i class="fas fa-edit mr-2"></i>编辑
                        </button>
                        <button onclick="this.closest('.fixed').remove()"
                                class="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors">
                            关闭
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // 点击背景关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });

        // ESC键关闭
        const handleEsc = (e) => {
            if (e.key === 'Escape') {
                modal.remove();
                document.removeEventListener('keydown', handleEsc);
            }
        };
        document.addEventListener('keydown', handleEsc);
    }

    /**
     * 全屏显示图片
     */
    openImageFullscreen(imageUrl) {
        // 创建全屏图片模态框
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50';
        modal.innerHTML = `
            <div class="relative max-w-full max-h-full p-4">
                <img src="${imageUrl}"
                     alt="全屏图片"
                     class="max-w-full max-h-full object-contain">
                <button onclick="this.closest('.fixed').remove()"
                        class="absolute top-4 right-4 text-white text-2xl hover:text-gray-300 transition-colors">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        // 点击背景关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });

        // ESC键关闭
        const handleEsc = (e) => {
            if (e.key === 'Escape') {
                modal.remove();
                document.removeEventListener('keydown', handleEsc);
            }
        };
        document.addEventListener('keydown', handleEsc);

        document.body.appendChild(modal);
    }
}

// 初始化应用
const {{RESOURCE_INSTANCE_NAME}} = new {{RESOURCE_CLASS_NAME}}();
