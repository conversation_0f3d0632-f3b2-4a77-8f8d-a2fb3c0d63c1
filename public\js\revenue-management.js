/**
 * 收入管理系统 - 前端JavaScript
 */

console.log('收入管理系统JavaScript开始加载...');

// 定义一个变量追踪当前是否有活跃的预览
let activeViewer = null;

// 定义openImageFullscreen函数在全局作用域
function openImageFullscreen(src) {
    if (!src) return;
    console.log('[DEBUG] Opening image in fullscreen:', src);

    // 如果已经有活跃的预览，先关闭它
    if (activeViewer) {
        try {
            activeViewer.destroy();
            activeViewer = null;
            // 清理可能残留的容器
            const existingContainers = document.querySelectorAll('.viewer-container');
            existingContainers.forEach(container => {
                if (container.parentNode) {
                    container.parentNode.removeChild(container);
                }
            });
        } catch (e) {
            console.error('Error destroying existing viewer:', e);
        }
    }

    // 创建一个临时的图片容器
    const container = document.createElement('div');
    container.className = 'viewer-container';
    container.style.display = 'none';
    document.body.appendChild(container);

    // 创建图片元素
    const img = document.createElement('img');
    img.src = src;
    container.appendChild(img);

    // 确保Viewer是可用的
    if (typeof Viewer === 'undefined') {
        console.error('Viewer.js未加载，无法预览图片');
        alert('图片预览功能不可用，请刷新页面后重试');
        // 清理容器
        if (container && container.parentNode) {
            container.parentNode.removeChild(container);
        }
        return;
    }

    // 初始化 Viewer
    const viewer = new Viewer(img, {
        backdrop: true,          // 启用背景遮罩
        button: true,           // 显示关闭按钮
        navbar: false,          // 隐藏底部导航栏（只有一张图片时不需要）
        title: false,           // 不显示标题
        zIndex: 99999,          // 设置z-index确保在模态框之上
        toolbar: {              // 自定义工具栏
            zoomIn: true,       // 放大按钮
            zoomOut: true,      // 缩小按钮
            oneToOne: true,     // 1:1 尺寸按钮
            reset: true,        // 重置按钮
            prev: false,        // 上一张（隐藏，因为只有一张图片）
            play: false,        // 播放按钮（隐藏）
            next: false,        // 下一张（隐藏）
            rotateLeft: true,   // 向左旋转
            rotateRight: true,  // 向右旋转
            flipHorizontal: true, // 水平翻转
            flipVertical: true,  // 垂直翻转
        },
        viewed() {
            // 图片加载完成后自动打开查看器
            if (window.innerWidth < 640) {
                viewer.zoomTo(0.8);  // 移动设备使用较小的初始缩放比例
            } else {
                viewer.zoomTo(1);    // 桌面设备使用正常的缩放比例
            }

            // 临时降低所有模态框的z-index
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                modal.style.zIndex = '1000';
            });
        },
        show() {
            // 显示时也降低模态框z-index
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                modal.style.zIndex = '1000';
            });
        },
        hide() {
            // 查看器关闭时清理资源
            try {
                // 恢复模态框的z-index
                const modals = document.querySelectorAll('.modal');
                modals.forEach(modal => {
                    modal.style.zIndex = '9999';
                });

                viewer.destroy();
                if (container && container.parentNode) {
                    container.parentNode.removeChild(container);
                }
                activeViewer = null;
            } catch (e) {
                console.error('Error cleaning up viewer:', e);
            }
        },
        hidden() {
            // 查看器完全隐藏后也恢复模态框z-index
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                modal.style.zIndex = '9999';
            });
        }
    });

    // 保存当前活跃的查看器引用
    activeViewer = viewer;

    // 显示查看器
    viewer.show();
}

// 立即将函数暴露到全局作用域
window.openImageFullscreen = openImageFullscreen;

class RevenueManagement {
    constructor() {
        this.currentPage = 1;
        this.pageSize = 10;
        this.totalPages = 1; // 添加总页数属性
        this.currentUser = null;
        this.isAdmin = false;
        this.searchQuery = '';
        this.searchTimeout = null;
        this.filters = {
            shop_name: '',
            start_date: '',
            end_date: ''
        };
        this.allShops = []; // 存储所有店铺数据

        this.init();
    }

    async init() {
        try {
            // 检查身份验证
            await this.checkAuthentication();

            // 初始化事件监听器
            this.initEventListeners();
            this.initFormEventListeners();

            // 加载初始数据
            await this.loadStatistics();
            await this.loadRecords();
            await this.loadShops();

        } catch (error) {
            console.error('初始化失败:', error);
            this.showError('系统初始化失败，请刷新页面重试');
        }
    }

    async checkAuthentication() {
        const token = getToken();
        if (!token) {
            window.location.href = '/login.html?redirect=' + encodeURIComponent(window.location.pathname);
            return;
        }

        try {
            const response = await fetch('/api/validate-token', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (!response.ok) {
                throw new Error('Token验证失败');
            }

            const data = await response.json();
            if (!data.valid) {
                throw new Error('Token无效');
            }

            this.currentUser = data.user;
            this.isAdmin = this.currentUser.role === 'admin';

            // 显示用户信息
            this.displayUserInfo();

        } catch (error) {
            console.error('身份验证失败:', error);
            localStorage.removeItem('token');
            window.location.href = '/login.html?redirect=' + encodeURIComponent(window.location.pathname);
        }
    }

    displayUserInfo() {
        const userInfo = document.getElementById('userInfo');
        if (userInfo) {
            userInfo.textContent = `${this.currentUser.username} (${this.isAdmin ? '管理员' : '用户'})`;
        }
    }

    initEventListeners() {
        try {
            // 添加记录按钮
            const addRecordBtn = document.getElementById('addRecordBtn');
            if (addRecordBtn) {
                addRecordBtn.addEventListener('click', () => {
                    this.showRecordModal();
                });
            }

            // 导出Excel按钮
            const exportExcelBtn = document.getElementById('exportExcelBtn');
            if (exportExcelBtn) {
                exportExcelBtn.addEventListener('click', () => {
                    this.exportExcel();
                });
            }

            // 数据分析按钮
            const showChartsBtn = document.getElementById('showChartsBtn');
            if (showChartsBtn) {
                showChartsBtn.addEventListener('click', () => {
                    this.showChartsModal();
                });
            }

            // 筛选按钮
            const filterBtn = document.getElementById('filterBtn');
            if (filterBtn) {
                filterBtn.addEventListener('click', () => {
                    this.applyFilters();
                });
            }

            // 重置筛选按钮
            const resetFilterBtn = document.getElementById('resetFilterBtn');
            if (resetFilterBtn) {
                resetFilterBtn.addEventListener('click', () => {
                    this.resetFilters();
                });
            }

            // 刷新按钮
            const refreshBtn = document.getElementById('refreshBtn');
            if (refreshBtn) {
                refreshBtn.addEventListener('click', () => {
                    this.refreshData();
                });
            }

            // 搜索输入框（带防抖）
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.addEventListener('input', (e) => {
                    const query = e.target.value.trim();

                    // 清除之前的定时器
                    if (this.searchTimeout) {
                        clearTimeout(this.searchTimeout);
                    }

                    // 立即更新搜索状态显示
                    this.searchQuery = query;
                    this.updateSearchStatus();

                    // 设置新的定时器，300ms后执行搜索
                    this.searchTimeout = setTimeout(() => {
                        this.currentPage = 1; // 重置到第一页
                        this.loadRecords();
                    }, 300);
                });
            }

            // 清除搜索按钮
            const clearSearchBtn = document.getElementById('clearSearchBtn');
            if (clearSearchBtn) {
                clearSearchBtn.addEventListener('click', () => {
                    this.clearSearch();
                });
            }

            // 图片上传
            const imageUpload = document.getElementById('imageUpload');
            if (imageUpload) {
                imageUpload.addEventListener('change', (e) => {
                    this.handleImageUpload(e);
                });
            }

            // 移除图片
            const removeImageBtn = document.getElementById('removeImageBtn');
            if (removeImageBtn) {
                removeImageBtn.addEventListener('click', () => {
                    this.removeImage();
                });
            }

            // 模态框关闭按钮
            const closeModal = document.getElementById('closeModal');
            if (closeModal) {
                closeModal.addEventListener('click', () => {
                    this.hideRecordModal();
                });
            }

            const closeChartsModal = document.getElementById('closeChartsModal');
            if (closeChartsModal) {
                closeChartsModal.addEventListener('click', () => {
                    this.hideChartsModal();
                });
            }

            // 详情模态框关闭按钮
            const closeDetailModal = document.getElementById('closeDetailModal');
            if (closeDetailModal) {
                closeDetailModal.addEventListener('click', () => {
                    this.hideDetailModal();
                });
            }

            // 详情模态框第二个关闭按钮
            const closeDetailModal2 = document.getElementById('closeDetailModal2');
            if (closeDetailModal2) {
                closeDetailModal2.addEventListener('click', () => {
                    this.hideDetailModal();
                });
            }
            // 取消按钮
            const cancelBtn = document.getElementById('cancelBtn');
            if (cancelBtn) {
                cancelBtn.addEventListener('click', () => {
                    this.hideRecordModal();
                });
            }

            // 初始化店铺搜索功能
            this.initShopSearch();

            // 移动端分页按钮
            const prevPageMobile = document.getElementById('prevPageMobile');
            if (prevPageMobile) {
                prevPageMobile.addEventListener('click', () => {
                    if (this.currentPage > 1) {
                        this.currentPage--;
                        this.loadRecords();
                    }
                });
            }

            const nextPageMobile = document.getElementById('nextPageMobile');
            if (nextPageMobile) {
                nextPageMobile.addEventListener('click', () => {
                    // 这里需要从分页信息中获取总页数，我们在renderPagination中会设置这个值
                    if (this.currentPage < this.totalPages) {
                        this.currentPage++;
                        this.loadRecords();
                    }
                });
            }

        } catch (error) {
            console.error('初始化事件监听器时出错:', error);
            throw error;
        }






    }

    initFormEventListeners() {
        try {
            // 表单提交
            const recordForm = document.getElementById('recordForm');
            if (recordForm) {
                recordForm.addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.saveRecord();
                });
            }

            // 产品信息自动计算创收金额
            const productInfo = document.getElementById('productInfo');
            if (productInfo) {
                productInfo.addEventListener('input', (e) => {
                    this.autoCalculateRevenue(e.target.value);
                });
            }

            // 模态框点击外部关闭
            const chartsModal = document.getElementById('chartsModal');
            if (chartsModal) {
                chartsModal.addEventListener('click', (e) => {
                    if (e.target.id === 'chartsModal') {
                        this.hideChartsModal();
                    }
                });
            }

            const detailModal = document.getElementById('detailModal');
            if (detailModal) {
                detailModal.addEventListener('click', (e) => {
                    if (e.target.id === 'detailModal') {
                        this.hideDetailModal();
                    }
                });
            }
        } catch (error) {
            console.error('初始化表单事件监听器时出错:', error);
        }
    }

    async loadStatistics() {
        try {
            const response = await sendAuthenticatedRequest('/api/revenue-records/statistics');
            const stats = response.data;

            const totalRevenueEl = document.getElementById('totalRevenue');
            const totalOrdersEl = document.getElementById('totalOrders');
            const totalShopsEl = document.getElementById('totalShops');
            const avgOrderValueEl = document.getElementById('avgOrderValue');

            if (totalRevenueEl) totalRevenueEl.textContent = `¥${parseFloat(stats.totalRevenue).toFixed(2)}`;
            if (totalOrdersEl) totalOrdersEl.textContent = stats.totalOrders;
            if (totalShopsEl) totalShopsEl.textContent = stats.totalShops;
            if (avgOrderValueEl) avgOrderValueEl.textContent = `¥${parseFloat(stats.avgOrderValue).toFixed(2)}`;

        } catch (error) {
            console.error('加载统计数据失败:', error);
            this.showError('加载统计数据失败');
        }
    }

    showLoading(show = true) {
        const tbody = document.getElementById('revenueTableBody');
        if (!tbody) {
            console.warn('表格主体元素不存在，无法显示加载状态');
            return;
        }
        if (show) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="10" class="px-6 py-8 text-center">
                        <div class="flex items-center justify-center">
                            <div class="loading mr-2"></div>
                            <span class="text-gray-500">加载中...</span>
                        </div>
                    </td>
                </tr>
            `;
        }
    }

    async loadRecords() {
        try {
            this.showLoading(true);

            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.pageSize,
                search: this.searchQuery,
                ...this.filters
            });

            const response = await sendAuthenticatedRequest(`/api/revenue-records?${params}`);
            const { data: records, pagination } = response;

            this.renderRecordsTable(records);
            this.renderPagination(pagination);

        } catch (error) {
            console.error('加载记录失败:', error);
            this.showError('加载记录失败');
            this.renderRecordsTable([]); // 显示空表格
        }
    }

    async loadShops() {
        try {
            // 加载收入记录专用店铺数据（用于搜索功能）
            const revenueShopsResponse = await sendAuthenticatedRequest('/api/revenue-records/revenue-shops');
            this.allShops = revenueShopsResponse || [];

            // 为店铺数据添加拼音索引
            this.prepareShopsPinyinIndex();

            // 加载收入记录中的店铺名称（用于筛选器）
            const response = await sendAuthenticatedRequest('/api/revenue-records/shops');
            const shops = response.data;

            const shopFilter = document.getElementById('shopFilter');
            if (shopFilter) {
                shopFilter.innerHTML = '<option value="">所有店铺</option>';

                shops.forEach(shop => {
                    const option = document.createElement('option');
                    option.value = shop;
                    option.textContent = shop;
                    shopFilter.appendChild(option);
                });
            }

        } catch (error) {
            console.error('加载店铺列表失败:', error);
            this.allShops = [];
        }
    }

    renderRecordsTable(records) {
        // 渲染桌面端表格
        this.renderDesktopTable(records);
        // 渲染移动端卡片
        this.renderMobileCards(records);
    }

    renderDesktopTable(records) {
        const tbody = document.getElementById('revenueTableBody');
        if (!tbody) {
            console.warn('表格主体元素不存在');
            return;
        }
        tbody.innerHTML = '';

        if (records.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="9" class="px-6 py-4 text-center text-gray-500">
                        暂无数据
                    </td>
                </tr>
            `;
            return;
        }

        records.forEach(record => {
            const row = document.createElement('tr');
            row.className = 'hover:bg-gray-50';

            const statusClass = record.status === '已做单'
                ? 'bg-green-100 text-green-800'
                : record.status === '已关闭'
                ? 'bg-gray-100 text-gray-800'
                : 'bg-yellow-100 text-yellow-800';

            row.innerHTML = `
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${record.shop_name}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${record.order_number}</td>
                <td class="px-6 py-4 text-sm text-gray-900 max-w-xs truncate" title="${record.product_info}">${record.product_info}</td>
                <td class="px-6 py-4 whitespace-nowrap">
                    ${record.image_url ? `<img src="${record.image_url}" alt="产品图片" class="w-12 h-12 object-cover rounded cursor-pointer hover:opacity-90 transition-opacity" onclick="openImageFullscreen('${record.image_url}')">` : '<span class="text-gray-400">无图片</span>'}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">¥${parseFloat(record.total_revenue).toFixed(2)}</td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <select class="status-select px-2 py-1 rounded-full text-xs font-medium border-0 focus:ring-2 focus:ring-blue-500 ${statusClass}"
                            data-record-id="${record.id}"
                            onchange="revenueManager.updateStatus(${record.id}, this.value)">
                        <option value="未做单" ${record.status === '未做单' || !record.status ? 'selected' : ''}>未做单</option>
                        <option value="已做单" ${record.status === '已做单' ? 'selected' : ''}>已做单</option>
                        <option value="已关闭" ${record.status === '已关闭' ? 'selected' : ''}>已关闭</option>
                    </select>
                </td>
                <td class="px-6 py-4 text-sm text-gray-900 max-w-xs truncate" title="${record.revenue_notes || ''}">${record.revenue_notes || '-'}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${new Date(record.created_at).toLocaleString()}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button onclick="revenueManager.showDetail(${record.id})" class="text-green-600 hover:text-green-900 mr-3" title="查看详情">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button onclick="revenueManager.editRecord(${record.id})" class="text-blue-600 hover:text-blue-900 mr-3" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button onclick="revenueManager.deleteRecord(${record.id})" class="text-red-600 hover:text-red-900" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;

            tbody.appendChild(row);
        });
    }

    renderMobileCards(records) {
        const container = document.getElementById('revenueCardsContainer');
        if (!container) {
            console.warn('移动端卡片容器不存在');
            return;
        }
        container.innerHTML = '';

        if (records.length === 0) {
            container.innerHTML = `
                <div class="p-8 text-center text-gray-500">
                    <i class="fas fa-inbox text-4xl mb-4 text-gray-300"></i>
                    <p>暂无数据</p>
                </div>
            `;
            return;
        }

        records.forEach(record => {
            const statusClass = record.status === '已做单'
                ? 'bg-green-100 text-green-800'
                : record.status === '已关闭'
                ? 'bg-gray-100 text-gray-800'
                : 'bg-yellow-100 text-yellow-800';

            const card = document.createElement('div');
            card.className = 'revenue-card';
            card.innerHTML = `
                <div class="card-header">
                    <div class="flex-1">
                        <div class="card-title">${record.shop_name}</div>
                        <div class="text-sm text-gray-500 font-mono">${record.order_number}</div>
                    </div>
                    <div class="card-amount">¥${parseFloat(record.total_revenue).toFixed(2)}</div>
                </div>

                <div class="card-meta">
                    <div class="card-meta-item">
                        <span class="card-meta-label">状态</span>
                        <select class="status-select px-2 py-1 rounded-full text-xs font-medium border-0 focus:ring-2 focus:ring-blue-500 ${statusClass}"
                                data-record-id="${record.id}"
                                onchange="revenueManager.updateStatus(${record.id}, this.value)">
                            <option value="未做单" ${record.status === '未做单' || !record.status ? 'selected' : ''}>未做单</option>
                            <option value="已做单" ${record.status === '已做单' ? 'selected' : ''}>已做单</option>
                            <option value="已关闭" ${record.status === '已关闭' ? 'selected' : ''}>已关闭</option>
                        </select>
                    </div>
                    <div class="card-meta-item">
                        <span class="card-meta-label">创建时间</span>
                        <span class="card-meta-value">${new Date(record.created_at).toLocaleDateString()}</span>
                    </div>
                </div>

                <div class="card-content">
                    <div class="card-meta-label mb-1">产品信息</div>
                    <div class="card-product-info">${record.product_info}</div>
                </div>

                ${record.image_url ? `
                    <div class="mb-3">
                        <img src="${record.image_url}" alt="产品图片" class="card-image cursor-pointer hover:opacity-90 transition-opacity" onclick="openImageFullscreen('${record.image_url}')">
                    </div>
                ` : ''}

                ${record.revenue_notes ? `
                    <div class="mb-3">
                        <div class="card-meta-label mb-1">备注</div>
                        <div class="text-sm text-gray-600 bg-gray-50 p-2 rounded">${record.revenue_notes}</div>
                    </div>
                ` : ''}

                <div class="card-footer">
                    <div class="card-time">${new Date(record.created_at).toLocaleString()}</div>
                    <div class="card-actions">
                        <button onclick="revenueManager.showDetail(${record.id})" class="card-action-btn card-action-detail" title="查看详情">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button onclick="revenueManager.editRecord(${record.id})" class="card-action-btn card-action-edit" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button onclick="revenueManager.deleteRecord(${record.id})" class="card-action-btn card-action-delete" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;

            container.appendChild(card);
        });
    }

    renderPagination(pagination) {
        const { page, totalPages, total } = pagination;

        // 保存总页数供移动端分页按钮使用
        this.totalPages = totalPages;

        // 更新记录信息
        const startRecord = (page - 1) * this.pageSize + 1;
        const endRecord = Math.min(page * this.pageSize, total);

        const startRecordEl = document.getElementById('startRecord');
        const endRecordEl = document.getElementById('endRecord');
        const totalRecordsEl = document.getElementById('totalRecords');

        if (startRecordEl) startRecordEl.textContent = startRecord;
        if (endRecordEl) endRecordEl.textContent = endRecord;
        if (totalRecordsEl) totalRecordsEl.textContent = total;

        // 更新移动端分页按钮状态
        this.updateMobilePaginationButtons(page, totalPages);

        // 生成分页按钮
        const paginationContainer = document.getElementById('pagination');
        if (!paginationContainer) {
            console.warn('分页容器不存在');
            return;
        }
        paginationContainer.innerHTML = '';

        // 上一页按钮
        const prevBtn = this.createPaginationButton('上一页', page - 1, page <= 1);
        paginationContainer.appendChild(prevBtn);

        // 页码按钮
        const startPage = Math.max(1, page - 2);
        const endPage = Math.min(totalPages, page + 2);

        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = this.createPaginationButton(i, i, false, i === page);
            paginationContainer.appendChild(pageBtn);
        }

        // 下一页按钮
        const nextBtn = this.createPaginationButton('下一页', page + 1, page >= totalPages);
        paginationContainer.appendChild(nextBtn);
    }

    createPaginationButton(text, pageNum, disabled = false, active = false) {
        const button = document.createElement('button');
        button.textContent = text;
        button.className = `relative inline-flex items-center px-4 py-2 border text-sm font-medium ${active
                ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                : disabled
                    ? 'bg-gray-100 border-gray-300 text-gray-400 cursor-not-allowed'
                    : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
            }`;

        if (!disabled && !active) {
            button.addEventListener('click', () => {
                this.currentPage = pageNum;
                this.loadRecords();
            });
        }

        return button;
    }

    updateMobilePaginationButtons(currentPage, totalPages) {
        const prevPageMobile = document.getElementById('prevPageMobile');
        const nextPageMobile = document.getElementById('nextPageMobile');

        if (prevPageMobile) {
            if (currentPage <= 1) {
                prevPageMobile.disabled = true;
                prevPageMobile.classList.add('opacity-50', 'cursor-not-allowed');
                prevPageMobile.classList.remove('hover:bg-gray-50');
            } else {
                prevPageMobile.disabled = false;
                prevPageMobile.classList.remove('opacity-50', 'cursor-not-allowed');
                prevPageMobile.classList.add('hover:bg-gray-50');
            }
        }

        if (nextPageMobile) {
            if (currentPage >= totalPages) {
                nextPageMobile.disabled = true;
                nextPageMobile.classList.add('opacity-50', 'cursor-not-allowed');
                nextPageMobile.classList.remove('hover:bg-gray-50');
            } else {
                nextPageMobile.disabled = false;
                nextPageMobile.classList.remove('opacity-50', 'cursor-not-allowed');
                nextPageMobile.classList.add('hover:bg-gray-50');
            }
        }
    }

    showRecordModal(record = null) {
        const modal = document.getElementById('recordModal');
        const form = document.getElementById('recordForm');
        const title = document.getElementById('modalTitle');

        // 重置表单
        form.reset();
        document.getElementById('recordId').value = '';

        // 重置店铺搜索框
        const shopSearch = document.getElementById('shopSearch');
        if (shopSearch) shopSearch.value = '';

        // 隐藏搜索结果和新增店铺表单
        this.hideShopSearchResults();
        this.hideNewShopForm();

        // 重置图片预览
        const imagePreviewContainer = document.getElementById('imagePreviewContainer');
        if (imagePreviewContainer) {
            imagePreviewContainer.classList.add('hidden');
        }

        // 清空文件输入
        const imageUpload = document.getElementById('imageUpload');
        if (imageUpload) {
            imageUpload.value = '';
        }

        // 重置removeImage标记
        const removeImageInput = document.getElementById('removeImageInput');
        if (removeImageInput) {
            removeImageInput.remove();
        }

        if (record) {
            // 编辑模式
            title.textContent = '编辑收入记录';
            document.getElementById('recordId').value = record.id;

            // 设置店铺名称（同时设置搜索框和隐藏字段）
            const shopSearch = document.getElementById('shopSearch');
            const shopName = document.getElementById('shopName');
            if (shopSearch) shopSearch.value = record.shop_name;
            if (shopName) shopName.value = record.shop_name;

            document.getElementById('orderNumber').value = record.order_number;
            document.getElementById('productInfo').value = record.product_info;
            document.getElementById('totalRevenueInput').value = record.total_revenue;
            document.getElementById('revenueNotes').value = record.revenue_notes || '';

            const statusElement = document.getElementById('orderStatus');
            if (statusElement) {
                statusElement.value = record.status || '未做单';
            }

            // 处理图片预览
            if (record.image_url) {
                const imagePreviewContainer = document.getElementById('imagePreviewContainer');
                const imagePreview = document.getElementById('imagePreview');

                if (imagePreviewContainer && imagePreview) {
                    imagePreview.src = record.image_url;
                    imagePreviewContainer.classList.remove('hidden');

                    // 添加点击预览功能
                    imagePreview.onclick = () => {
                        openImageFullscreen(record.image_url);
                    };
                }
            }
        } else {
            // 添加模式
            title.textContent = '添加收入记录';
            const statusElement = document.getElementById('orderStatus');
            if (statusElement) {
                statusElement.value = '未做单'; // 默认状态
            }
        }

        modal.classList.add('show');
    }

    hideRecordModal() {
        const modal = document.getElementById('recordModal');
        modal.classList.remove('show');
    }

    validateForm() {
        const shopName = document.getElementById('shopName').value.trim();
        const orderNumber = document.getElementById('orderNumber').value.trim();
        const productInfo = document.getElementById('productInfo').value.trim();
        const totalRevenue = document.getElementById('totalRevenueInput').value;

        if (!shopName) {
            this.showError('请输入店铺名称');
            return false;
        }

        if (!orderNumber) {
            this.showError('请输入订单号');
            return false;
        }

        if (!productInfo) {
            this.showError('请输入产品信息');
            return false;
        }

        if (totalRevenue === '' || isNaN(parseFloat(totalRevenue)) || parseFloat(totalRevenue) < 0) {
            this.showError('请输入有效的创收金额（可以为0）');
            return false;
        }

        return true;
    }

    async saveRecord() {
        console.log('开始保存记录...');

        // 验证表单
        if (!this.validateForm()) {
            console.log('表单验证失败');
            return;
        }

        const form = document.getElementById('recordForm');
        const formData = new FormData(form);
        const recordId = document.getElementById('recordId').value;

        // 检查status字段的值
        const statusElement = document.getElementById('orderStatus');
        const statusValue = statusElement ? statusElement.value : 'NOT_FOUND';
        console.log('Status元素:', statusElement);
        console.log('Status值:', statusValue);
        console.log('FormData中的status:', formData.get('status'));

        // 打印所有FormData内容
        console.log('=== FormData 完整内容 ===');
        for (let [key, value] of formData.entries()) {
            console.log(`${key}:`, value);
        }
        console.log('=== FormData 结束 ===');

        console.log('表单数据:', {
            recordId,
            shop_name: formData.get('shop_name'),
            order_number: formData.get('order_number'),
            product_info: formData.get('product_info'),
            total_revenue: formData.get('total_revenue'),
            status: formData.get('status'),
            revenue_notes: formData.get('revenue_notes'),
            image: formData.get('image') ? 'file selected' : 'no file'
        });

        const saveBtn = document.getElementById('saveBtn');
        const saveBtnText = document.getElementById('saveBtnText');
        const loading = saveBtn.querySelector('.loading');

        // 显示上传进度条
        if (typeof showUploadProgress === 'function') {
            showUploadProgress();
        }

        // 显示加载状态
        loading.classList.remove('hidden');
        saveBtnText.textContent = '保存中...';
        saveBtn.disabled = true;

        try {
            const url = recordId ? `/api/revenue-records/${recordId}` : '/api/revenue-records';
            const method = recordId ? 'PUT' : 'POST';
            const token = getToken();

            console.log('发送请求:', { url, method, token: token ? 'exists' : 'missing' });

            // 使用带进度的上传函数
            const result = await uploadWithProgress(url, method, formData);
            console.log('响应结果:', result);

            if (result.success) {
                this.showSuccess(result.message);
                this.hideRecordModal();
                await this.loadRecords();
                await this.loadStatistics();
                await this.loadShops();
            } else {
                this.showError(result.message);
            }

        } catch (error) {
            console.error('保存记录失败:', error);
            this.showError('保存记录失败');
        } finally {
            // 隐藏上传进度条
            if (typeof hideUploadProgress === 'function') {
                hideUploadProgress();
            }

            // 恢复按钮状态
            loading.classList.add('hidden');
            saveBtnText.textContent = '保存';
            saveBtn.disabled = false;
        }
    }

    async showDetail(id) {
        try {
            const response = await sendAuthenticatedRequest(`/api/revenue-records?page=1&limit=1000`);
            const record = response.data.find(r => r.id === id);

            if (record) {
                this.showDetailModal(record);
            } else {
                this.showError('记录不存在');
            }

        } catch (error) {
            console.error('获取记录失败:', error);
            this.showError('获取记录失败');
        }
    }

    showDetailModal(record) {
        // 填充详情数据
        document.getElementById('detailShopName').textContent = record.shop_name;
        document.getElementById('detailOrderNumber').textContent = record.order_number;
        document.getElementById('detailTotalRevenue').textContent = `¥${parseFloat(record.total_revenue).toFixed(2)}`;
        document.getElementById('detailProductInfo').textContent = record.product_info;
        document.getElementById('detailRevenueNotes').textContent = record.revenue_notes || '暂无备注';
        document.getElementById('detailCreatedAt').textContent = new Date(record.created_at).toLocaleString();
        document.getElementById('detailUpdatedAt').textContent = new Date(record.updated_at).toLocaleString();

        // 处理状态显示
        const statusElement = document.getElementById('detailStatus');
        const status = record.status || '未做单';
        statusElement.textContent = status;
        if (status === '已做单') {
            statusElement.className = 'px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800';
        } else {
            statusElement.className = 'px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800';
        }

        // 处理图片显示
        const detailImage = document.getElementById('detailImage');
        const detailNoImage = document.getElementById('detailNoImage');

        if (record.image_url) {
            detailImage.src = record.image_url;
            detailImage.classList.remove('hidden');
            detailNoImage.classList.add('hidden');

            // 添加点击预览功能
            detailImage.onclick = () => {
                openImageFullscreen(record.image_url);
            };
        } else {
            detailImage.classList.add('hidden');
            detailNoImage.classList.remove('hidden');
        }

        // 绑定编辑按钮事件
        document.getElementById('detailEditBtn').onclick = () => {
            this.hideDetailModal();
            this.editRecord(record.id);
        };

        // 显示模态框
        document.getElementById('detailModal').classList.add('show');
    }

    hideDetailModal() {
        document.getElementById('detailModal').classList.remove('show');
    }

    async editRecord(id) {
        try {
            const response = await sendAuthenticatedRequest(`/api/revenue-records?page=1&limit=1000`);
            const record = response.data.find(r => r.id === id);

            if (record) {
                this.showRecordModal(record);
            } else {
                this.showError('记录不存在');
            }

        } catch (error) {
            console.error('获取记录失败:', error);
            this.showError('获取记录失败');
        }
    }

    async deleteRecord(id) {
        if (!confirm('确定要删除这条记录吗？此操作不可恢复。')) {
            return;
        }

        try {
            const response = await sendAuthenticatedRequest(`/api/revenue-records/${id}`, 'DELETE');

            if (response.success) {
                this.showSuccess(response.message);
                await this.loadRecords();
                await this.loadStatistics();
                await this.loadShops();
            } else {
                this.showError(response.message);
            }

        } catch (error) {
            console.error('删除记录失败:', error);
            this.showError('删除记录失败');
        }
    }

    async updateStatus(recordId, newStatus) {
        try {
            console.log('开始更新状态:', { recordId, newStatus });

            // 显示加载状态
            const selectElement = document.querySelector(`select[data-record-id="${recordId}"]`);
            if (selectElement) {
                selectElement.disabled = true;
                selectElement.style.opacity = '0.6';
            }

            const url = `/api/revenue-records/${recordId}/status`;
            console.log('请求URL:', url);
            console.log('请求数据:', { status: newStatus });

            const result = await sendAuthenticatedRequest(
                url,
                'PUT',
                { status: newStatus }
            );

            console.log('服务器响应:', result);

            if (result.success) {
                // 更新选择框样式
                if (selectElement) {
                    const statusClass = newStatus === '已做单'
                        ? 'bg-green-100 text-green-800'
                        : newStatus === '已关闭'
                        ? 'bg-gray-100 text-gray-800'
                        : 'bg-yellow-100 text-yellow-800';

                    selectElement.className = `status-select px-2 py-1 rounded-full text-xs font-medium border-0 focus:ring-2 focus:ring-blue-500 ${statusClass}`;
                    selectElement.disabled = false;
                    selectElement.style.opacity = '1';
                }

                this.showSuccess('订单状态更新成功');
                // 重新加载统计数据以反映状态变化
                await this.loadStatistics();
            } else {
                this.showError(result.message || '状态更新失败');
                // 恢复原始状态
                if (selectElement) {
                    selectElement.disabled = false;
                    selectElement.style.opacity = '1';
                    // 重新加载记录以恢复原始状态
                    await this.loadRecords();
                }
            }

        } catch (error) {
            console.error('更新状态失败:', error);
            this.showError('更新状态失败');

            // 恢复原始状态
            const selectElement = document.querySelector(`select[data-record-id="${recordId}"]`);
            if (selectElement) {
                selectElement.disabled = false;
                selectElement.style.opacity = '1';
                // 重新加载记录以恢复原始状态
                await this.loadRecords();
            }
        }
    }

    previewImage(file) {
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            document.getElementById('imagePreview').classList.remove('hidden');
            document.getElementById('previewImg').src = e.target.result;
        };
        reader.readAsDataURL(file);
    }

    applyFilters() {
        const shopFilter = document.getElementById('shopFilter');
        const startDate = document.getElementById('startDate');
        const endDate = document.getElementById('endDate');

        this.filters.shop_name = shopFilter ? shopFilter.value : '';
        this.filters.start_date = startDate ? startDate.value : '';
        this.filters.end_date = endDate ? endDate.value : '';

        this.currentPage = 1;
        this.loadRecords();
    }

    resetFilters() {
        const searchInput = document.getElementById('searchInput');
        const shopFilter = document.getElementById('shopFilter');
        const startDate = document.getElementById('startDate');
        const endDate = document.getElementById('endDate');

        if (searchInput) searchInput.value = '';
        if (shopFilter) shopFilter.value = '';
        if (startDate) startDate.value = '';
        if (endDate) endDate.value = '';

        this.searchQuery = '';
        this.filters = {
            shop_name: '',
            start_date: '',
            end_date: ''
        };

        this.currentPage = 1;
        this.updateSearchStatus();
        this.loadRecords();
    }

    updateSearchStatus() {
        const searchResultsInfo = document.getElementById('searchResultsInfo');
        const searchResultsText = document.getElementById('searchResultsText');

        if (this.searchQuery) {
            if (searchResultsText) {
                searchResultsText.innerHTML = `<i class="fas fa-search mr-1"></i>搜索 "${this.searchQuery}" 的结果`;
            }
            if (searchResultsInfo) {
                searchResultsInfo.classList.remove('hidden');
            }
        } else {
            if (searchResultsInfo) {
                searchResultsInfo.classList.add('hidden');
            }
        }
    }

    clearSearch() {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.value = '';
        }
        this.searchQuery = '';
        this.currentPage = 1;
        this.updateSearchStatus();
        this.loadRecords();
    }

    async refreshData() {
        try {
            await Promise.all([
                this.loadStatistics(),
                this.loadRecords(),
                this.loadShops()
            ]);
            this.showSuccess('数据刷新成功');
        } catch (error) {
            console.error('刷新数据失败:', error);
            this.showError('刷新数据失败');
        }
    }

    async exportExcel() {
        try {
            const params = new URLSearchParams(this.filters);
            const url = `/api/revenue-records/export?${params}`;

            const response = await fetch(url, {
                headers: {
                    'Authorization': `Bearer ${getToken()}`
                }
            });

            if (response.ok) {
                const blob = await response.blob();
                const downloadUrl = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = downloadUrl;
                a.download = `收入记录_${new Date().toISOString().split('T')[0]}.xlsx`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(downloadUrl);

                this.showSuccess('Excel文件导出成功');
            } else {
                this.showError('导出失败');
            }

        } catch (error) {
            console.error('导出Excel失败:', error);
            this.showError('导出Excel失败');
        }
    }



    async showChartsModal() {
        const modal = document.getElementById('chartsModal');
        modal.classList.add('show');

        try {
            const response = await sendAuthenticatedRequest('/api/revenue-records/charts');
            const { trend, shops, monthly, categories, orderTrend, avgOrder } = response.data;

            this.renderRevenueChart(trend);
            this.renderShopChart(shops);
            this.renderMonthlyChart(monthly || trend);
            this.renderCategoryChart(categories || shops);
            this.renderOrderTrendChart(orderTrend || trend);
            this.renderAvgOrderChart(avgOrder || trend);

        } catch (error) {
            console.error('加载图表数据失败:', error);
            this.showError('加载图表数据失败');
        }
    }

    hideChartsModal() {
        const modal = document.getElementById('chartsModal');
        modal.classList.remove('show');
    }

    updateChartsTheme() {
        // 更新图表主题，当主题切换时调用
        if (this.revenueChart) {
            const isDarkTheme = document.documentElement.classList.contains('dark');
            const currentTheme = {
                textColor: isDarkTheme ? '#f9fafb' : '#1f2937',
                gridColor: isDarkTheme ? '#374151' : '#e5e7eb',
                tooltipBg: isDarkTheme ? '#374151' : '#ffffff',
                tooltipBorder: isDarkTheme ? '#4b5563' : '#e5e7eb'
            };

            // 更新创收趋势图主题
            this.revenueChart.options.plugins.legend.labels.color = currentTheme.textColor;
            this.revenueChart.options.plugins.tooltip.backgroundColor = currentTheme.tooltipBg;
            this.revenueChart.options.plugins.tooltip.titleColor = currentTheme.textColor;
            this.revenueChart.options.plugins.tooltip.bodyColor = currentTheme.textColor;
            this.revenueChart.options.plugins.tooltip.borderColor = currentTheme.tooltipBorder;

            this.revenueChart.options.scales.x.title.color = currentTheme.textColor;
            this.revenueChart.options.scales.x.ticks.color = currentTheme.textColor;
            this.revenueChart.options.scales.x.grid.color = currentTheme.gridColor;

            this.revenueChart.options.scales.y.title.color = currentTheme.textColor;
            this.revenueChart.options.scales.y.ticks.color = currentTheme.textColor;
            this.revenueChart.options.scales.y.grid.color = currentTheme.gridColor;

            this.revenueChart.options.scales.y1.title.color = currentTheme.textColor;
            this.revenueChart.options.scales.y1.ticks.color = currentTheme.textColor;

            this.revenueChart.update();
        }

        if (this.shopChart) {
            const isDarkTheme = document.documentElement.classList.contains('dark');
            const currentTheme = {
                textColor: isDarkTheme ? '#f9fafb' : '#1f2937',
                borderColor: isDarkTheme ? '#374151' : '#ffffff',
                tooltipBg: isDarkTheme ? '#374151' : '#ffffff',
                tooltipBorder: isDarkTheme ? '#4b5563' : '#e5e7eb'
            };

            // 更新店铺分布图主题
            this.shopChart.options.plugins.legend.labels.color = currentTheme.textColor;
            this.shopChart.options.plugins.tooltip.backgroundColor = currentTheme.tooltipBg;
            this.shopChart.options.plugins.tooltip.titleColor = currentTheme.textColor;
            this.shopChart.options.plugins.tooltip.bodyColor = currentTheme.textColor;
            this.shopChart.options.plugins.tooltip.borderColor = currentTheme.tooltipBorder;

            // 更新边框颜色
            this.shopChart.data.datasets[0].borderColor = currentTheme.borderColor;

            this.shopChart.update();
        }
    }

    renderRevenueChart(trendData) {
        const ctx = document.getElementById('revenueChart').getContext('2d');

        // 销毁现有图表
        if (this.revenueChart) {
            this.revenueChart.destroy();
        }

        // 获取当前主题
        const isDarkTheme = document.documentElement.classList.contains('dark');
        const currentTheme = {
            textColor: isDarkTheme ? '#f9fafb' : '#1f2937',
            gridColor: isDarkTheme ? '#374151' : '#e5e7eb',
            tooltipBg: isDarkTheme ? '#374151' : '#ffffff',
            tooltipBorder: isDarkTheme ? '#4b5563' : '#e5e7eb'
        };

        // 处理数据，如果有日期字段则使用时间轴，否则使用标签
        let datasets, scales;

        if (trendData.length > 0 && trendData[0].date) {
            // 有日期数据，使用时间轴
            const processedData = trendData.map(item => ({
                x: item.date,
                revenue: parseFloat(item.revenue || 0),
                orders: parseInt(item.orders || 0)
            }));

            const revenues = processedData.map(item => ({ x: item.x, y: item.revenue }));
            const orders = processedData.map(item => ({ x: item.x, y: item.orders }));

            datasets = [{
                label: '收入金额 (¥)',
                data: revenues,
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                tension: 0.4,
                fill: true,
                yAxisID: 'y'
            }, {
                label: '订单数量',
                data: orders,
                borderColor: '#10b981',
                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                tension: 0.4,
                fill: true,
                yAxisID: 'y1'
            }];

            scales = {
                x: {
                    type: 'time',
                    time: {
                        unit: 'day',
                        displayFormats: {
                            day: 'MM-dd',
                            hour: 'HH:mm',
                            minute: 'HH:mm'
                        },
                        tooltipFormat: 'yyyy-MM-dd HH:mm'
                    },
                    grid: {
                        color: currentTheme.gridColor
                    },
                    ticks: {
                        color: currentTheme.textColor,
                        maxTicksLimit: 8,
                        callback: function(value, index, values) {
                            const date = new Date(value);
                            return date.toLocaleDateString('zh-CN', {
                                month: '2-digit',
                                day: '2-digit'
                            });
                        }
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    grid: {
                        color: currentTheme.gridColor
                    },
                    ticks: {
                        color: currentTheme.textColor,
                        callback: function(value) {
                            return '¥' + value.toFixed(2);
                        }
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    grid: {
                        drawOnChartArea: false,
                    },
                    ticks: {
                        color: currentTheme.textColor
                    }
                }
            };
        } else {
            // 没有日期数据，使用简单的标签
            const labels = trendData.map((item, index) => `数据${index + 1}`);
            const revenues = trendData.map(item => parseFloat(item.revenue || item.value || 0));

            datasets = [{
                label: '收入金额 (¥)',
                data: revenues,
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                tension: 0.4,
                fill: true
            }];

            scales = {
                x: {
                    grid: {
                        color: currentTheme.gridColor
                    },
                    ticks: {
                        color: currentTheme.textColor
                    }
                },
                y: {
                    grid: {
                        color: currentTheme.gridColor
                    },
                    ticks: {
                        color: currentTheme.textColor,
                        callback: function(value) {
                            return '¥' + value.toFixed(2);
                        }
                    }
                }
            };
        }

        this.revenueChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: !trendData[0]?.date ? trendData.map((item, index) => `数据${index + 1}`) : undefined,
                datasets: datasets
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    duration: 1500,
                    easing: 'easeInOutQuart'
                },
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                plugins: {
                    legend: {
                        labels: {
                            color: currentTheme.textColor,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        backgroundColor: currentTheme.tooltipBg,
                        titleColor: currentTheme.textColor,
                        bodyColor: currentTheme.textColor,
                        borderColor: currentTheme.tooltipBorder,
                        borderWidth: 1,
                        callbacks: {
                            title: function (context) {
                                if (trendData[0]?.date) {
                                    const date = new Date(context[0].parsed.x);
                                    return date.toLocaleDateString('zh-CN', {
                                        month: '2-digit',
                                        day: '2-digit'
                                    }) + ' ' + date.toLocaleTimeString('zh-CN', {
                                        hour: '2-digit',
                                        minute: '2-digit'
                                    });
                                }
                                return context[0].label;
                            },
                            label: function(context) {
                                const label = context.dataset.label || '';
                                if (label.includes('金额')) {
                                    return label + ': ¥' + context.parsed.y.toFixed(2);
                                }
                                return label + ': ' + context.parsed.y;
                            }
                        }
                    }
                },
                scales: scales

            }
        });
    }

    renderShopChart(shopData) {
        const ctx = document.getElementById('shopChart').getContext('2d');

        // 销毁现有图表
        if (this.shopChart) {
            this.shopChart.destroy();
        }

        // 获取当前主题
        const isDarkTheme = document.documentElement.classList.contains('dark');
        const currentTheme = {
            textColor: isDarkTheme ? '#f9fafb' : '#1f2937',
            borderColor: isDarkTheme ? '#374151' : '#ffffff',
            tooltipBg: isDarkTheme ? '#374151' : '#ffffff',
            tooltipBorder: isDarkTheme ? '#4b5563' : '#e5e7eb'
        };

        const labels = shopData.map(item => item.shop_name);
        const revenues = shopData.map(item => parseFloat(item.revenue));

        // 生成颜色
        const colors = [
            'rgba(59, 130, 246, 0.8)',
            'rgba(16, 185, 129, 0.8)',
            'rgba(245, 158, 11, 0.8)',
            'rgba(239, 68, 68, 0.8)',
            'rgba(139, 92, 246, 0.8)',
            'rgba(236, 72, 153, 0.8)',
            'rgba(20, 184, 166, 0.8)',
            'rgba(251, 146, 60, 0.8)',
            'rgba(34, 197, 94, 0.8)',
            'rgba(168, 85, 247, 0.8)'
        ];

        this.shopChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: revenues,
                    backgroundColor: colors.slice(0, labels.length),
                    borderWidth: 2,
                    borderColor: currentTheme.borderColor
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    duration: 1500,
                    easing: 'easeInOutQuart'
                },
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: currentTheme.textColor,
                            usePointStyle: true,
                            padding: 20
                        }
                    },
                    tooltip: {
                        backgroundColor: currentTheme.tooltipBg,
                        titleColor: currentTheme.textColor,
                        bodyColor: currentTheme.textColor,
                        borderColor: currentTheme.tooltipBorder,
                        borderWidth: 1,
                        callbacks: {
                            label: function (context) {
                                const label = context.label || '';
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return `${label}: ¥${value.toFixed(2)} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }

    // 月度创收分析图表
    renderMonthlyChart(monthlyData) {
        const ctx = document.getElementById('monthlyChart').getContext('2d');

        // 销毁现有图表
        if (this.monthlyChart) {
            this.monthlyChart.destroy();
        }

        // 获取当前主题
        const isDarkTheme = document.documentElement.classList.contains('dark');
        const currentTheme = {
            textColor: isDarkTheme ? '#f9fafb' : '#1f2937',
            gridColor: isDarkTheme ? '#374151' : '#e5e7eb',
            tooltipBg: isDarkTheme ? '#374151' : '#ffffff',
            tooltipBorder: isDarkTheme ? '#4b5563' : '#e5e7eb'
        };

        // 处理月度数据
        const processedData = monthlyData.map((item, index) => {
            let displayLabel;
            if (item.date) {
                // 如果有日期，格式化为简洁的日期格式
                const date = new Date(item.date);
                displayLabel = date.toLocaleDateString('zh-CN', {
                    month: '2-digit',
                    day: '2-digit'
                });
            } else {
                displayLabel = item.month || `月份${index + 1}`;
            }
            return {
                month: displayLabel,
                revenue: parseFloat(item.revenue || item.value || 0)
            };
        });

        const labels = processedData.map(item => item.month);
        const revenues = processedData.map(item => item.revenue);

        this.monthlyChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: '月度创收 (¥)',
                    data: revenues,
                    backgroundColor: 'rgba(139, 92, 246, 0.8)',
                    borderColor: '#8b5cf6',
                    borderWidth: 2,
                    borderRadius: 8,
                    borderSkipped: false,
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    duration: 1500,
                    easing: 'easeInOutQuart'
                },
                plugins: {
                    legend: {
                        labels: {
                            color: currentTheme.textColor,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        backgroundColor: currentTheme.tooltipBg,
                        titleColor: currentTheme.textColor,
                        bodyColor: currentTheme.textColor,
                        borderColor: currentTheme.tooltipBorder,
                        borderWidth: 1,
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': ¥' + context.parsed.y.toFixed(2);
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            color: currentTheme.gridColor
                        },
                        ticks: {
                            color: currentTheme.textColor
                        }
                    },
                    y: {
                        grid: {
                            color: currentTheme.gridColor
                        },
                        ticks: {
                            color: currentTheme.textColor,
                            callback: function(value) {
                                return '¥' + value.toFixed(0);
                            }
                        }
                    }
                }
            }
        });
    }

    // 产品类别分析图表
    renderCategoryChart(categoryData) {
        const ctx = document.getElementById('categoryChart').getContext('2d');

        // 销毁现有图表
        if (this.categoryChart) {
            this.categoryChart.destroy();
        }

        // 获取当前主题
        const isDarkTheme = document.documentElement.classList.contains('dark');
        const currentTheme = {
            textColor: isDarkTheme ? '#f9fafb' : '#1f2937',
            borderColor: isDarkTheme ? '#374151' : '#ffffff',
            tooltipBg: isDarkTheme ? '#374151' : '#ffffff',
            tooltipBorder: isDarkTheme ? '#4b5563' : '#e5e7eb'
        };

        // 处理类别数据
        const processedData = categoryData.map((item, index) => ({
            category: item.category || item.shop_name || `类别${index + 1}`,
            revenue: parseFloat(item.revenue || item.value || 0)
        }));

        const labels = processedData.map(item => item.category);
        const revenues = processedData.map(item => item.revenue);

        // 生成颜色
        const colors = [
            'rgba(236, 72, 153, 0.8)',
            'rgba(59, 130, 246, 0.8)',
            'rgba(16, 185, 129, 0.8)',
            'rgba(245, 158, 11, 0.8)',
            'rgba(239, 68, 68, 0.8)',
            'rgba(139, 92, 246, 0.8)',
            'rgba(20, 184, 166, 0.8)',
            'rgba(251, 146, 60, 0.8)'
        ];

        this.categoryChart = new Chart(ctx, {
            type: 'polarArea',
            data: {
                labels: labels,
                datasets: [{
                    data: revenues,
                    backgroundColor: colors.slice(0, labels.length),
                    borderWidth: 2,
                    borderColor: currentTheme.borderColor
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    duration: 1500,
                    easing: 'easeInOutQuart'
                },
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: currentTheme.textColor,
                            usePointStyle: true,
                            padding: 15
                        }
                    },
                    tooltip: {
                        backgroundColor: currentTheme.tooltipBg,
                        titleColor: currentTheme.textColor,
                        bodyColor: currentTheme.textColor,
                        borderColor: currentTheme.tooltipBorder,
                        borderWidth: 1,
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return `${label}: ¥${value.toFixed(2)} (${percentage}%)`;
                            }
                        }
                    }
                },
                scales: {
                    r: {
                        grid: {
                            color: currentTheme.gridColor
                        },
                        ticks: {
                            color: currentTheme.textColor,
                            callback: function(value) {
                                return '¥' + value.toFixed(0);
                            }
                        }
                    }
                }
            }
        });
    }

    // 订单量趋势图表
    renderOrderTrendChart(orderData) {
        const ctx = document.getElementById('orderTrendChart').getContext('2d');

        // 销毁现有图表
        if (this.orderTrendChart) {
            this.orderTrendChart.destroy();
        }

        // 获取当前主题
        const isDarkTheme = document.documentElement.classList.contains('dark');
        const currentTheme = {
            textColor: isDarkTheme ? '#f9fafb' : '#1f2937',
            gridColor: isDarkTheme ? '#374151' : '#e5e7eb',
            tooltipBg: isDarkTheme ? '#374151' : '#ffffff',
            tooltipBorder: isDarkTheme ? '#4b5563' : '#e5e7eb'
        };

        // 处理订单数据
        const processedData = orderData.map((item, index) => {
            let displayLabel;
            if (item.date) {
                // 如果有日期，格式化为简洁的日期格式
                const date = new Date(item.date);
                displayLabel = date.toLocaleDateString('zh-CN', {
                    month: '2-digit',
                    day: '2-digit'
                });
            } else {
                displayLabel = `数据${index + 1}`;
            }
            return {
                date: displayLabel,
                orders: parseInt(item.orders || item.value || 0)
            };
        });

        const labels = processedData.map(item => item.date);
        const orders = processedData.map(item => item.orders);

        this.orderTrendChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: '订单数量',
                    data: orders,
                    borderColor: '#10b981',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    tension: 0.4,
                    fill: true,
                    pointBackgroundColor: '#10b981',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 5
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    duration: 1500,
                    easing: 'easeInOutQuart'
                },
                plugins: {
                    legend: {
                        labels: {
                            color: currentTheme.textColor,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        backgroundColor: currentTheme.tooltipBg,
                        titleColor: currentTheme.textColor,
                        bodyColor: currentTheme.textColor,
                        borderColor: currentTheme.tooltipBorder,
                        borderWidth: 1
                    }
                },
                scales: {
                    x: {
                        grid: {
                            color: currentTheme.gridColor
                        },
                        ticks: {
                            color: currentTheme.textColor
                        }
                    },
                    y: {
                        grid: {
                            color: currentTheme.gridColor
                        },
                        ticks: {
                            color: currentTheme.textColor,
                            stepSize: 1
                        }
                    }
                }
            }
        });
    }

    // 平均订单价值图表
    renderAvgOrderChart(avgData) {
        const ctx = document.getElementById('avgOrderChart').getContext('2d');

        // 销毁现有图表
        if (this.avgOrderChart) {
            this.avgOrderChart.destroy();
        }

        // 获取当前主题
        const isDarkTheme = document.documentElement.classList.contains('dark');
        const currentTheme = {
            textColor: isDarkTheme ? '#f9fafb' : '#1f2937',
            gridColor: isDarkTheme ? '#374151' : '#e5e7eb',
            tooltipBg: isDarkTheme ? '#374151' : '#ffffff',
            tooltipBorder: isDarkTheme ? '#4b5563' : '#e5e7eb'
        };

        // 处理平均订单价值数据
        const processedData = avgData.map((item, index) => {
            const revenue = parseFloat(item.revenue || 0);
            const orders = parseInt(item.orders || 1);
            let displayLabel;
            if (item.date) {
                // 如果有日期，格式化为简洁的日期格式
                const date = new Date(item.date);
                displayLabel = date.toLocaleDateString('zh-CN', {
                    month: '2-digit',
                    day: '2-digit'
                });
            } else {
                displayLabel = `数据${index + 1}`;
            }
            return {
                date: displayLabel,
                avgValue: orders > 0 ? revenue / orders : 0
            };
        });

        const labels = processedData.map(item => item.date);
        const avgValues = processedData.map(item => item.avgValue);

        this.avgOrderChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: '平均订单价值 (¥)',
                    data: avgValues,
                    backgroundColor: 'rgba(245, 158, 11, 0.8)',
                    borderColor: '#f59e0b',
                    borderWidth: 2,
                    borderRadius: 6,
                    borderSkipped: false,
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    duration: 1500,
                    easing: 'easeInOutQuart'
                },
                plugins: {
                    legend: {
                        labels: {
                            color: currentTheme.textColor,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        backgroundColor: currentTheme.tooltipBg,
                        titleColor: currentTheme.textColor,
                        bodyColor: currentTheme.textColor,
                        borderColor: currentTheme.tooltipBorder,
                        borderWidth: 1,
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': ¥' + context.parsed.y.toFixed(2);
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            color: currentTheme.gridColor
                        },
                        ticks: {
                            color: currentTheme.textColor
                        }
                    },
                    y: {
                        grid: {
                            color: currentTheme.gridColor
                        },
                        ticks: {
                            color: currentTheme.textColor,
                            callback: function(value) {
                                return '¥' + value.toFixed(2);
                            }
                        }
                    }
                }
            }
        });
    }

    showError(message) {
        this.showNotification(message, 'error');
    }

    showSuccess(message) {
        this.showNotification(message, 'success');
    }

    showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transform transition-all duration-300 translate-x-full`;

        // 根据类型设置样式
        switch (type) {
            case 'success':
                notification.className += ' bg-green-500 text-white';
                break;
            case 'error':
                notification.className += ' bg-red-500 text-white';
                break;
            case 'warning':
                notification.className += ' bg-yellow-500 text-white';
                break;
            default:
                notification.className += ' bg-blue-500 text-white';
        }

        notification.innerHTML = `
            <div class="flex items-center">
                <div class="flex-1">${message}</div>
                <button onclick="this.parentElement.parentElement.remove()" class="ml-2 text-white hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        document.body.appendChild(notification);

        // 动画显示
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        // 自动消失
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }, 5000);
    }

    // 自动计算创收金额
    autoCalculateRevenue(productInfo) {
        if (!productInfo) {
            return;
        }

        // 正则表达式匹配各种金额格式
        const patterns = [
            // 匹配 +数字 格式 (如: +108, +20, +30)
            /\+(\d+(?:\.\d+)?)/g,
            // 匹配 数字元 格式 (如: 108元, 20元)
            /(\d+(?:\.\d+)?)元/g,
            // 匹配 ¥数字 格式 (如: ¥108, ¥20)
            /¥(\d+(?:\.\d+)?)/g,
            // 匹配 数字.数字 格式 (如: 108.50, 20.00)
            /(\d+\.\d+)/g
        ];

        let totalRevenue = 0;
        let foundAmounts = [];

        // 尝试每个正则表达式模式
        for (const pattern of patterns) {
            const matches = productInfo.match(pattern);
            if (matches) {
                for (const match of matches) {
                    // 提取数字部分
                    const numberMatch = match.match(/(\d+(?:\.\d+)?)/);
                    if (numberMatch) {
                        const amount = parseFloat(numberMatch[1]);
                        if (!isNaN(amount) && amount >= 0) {
                            foundAmounts.push(amount);
                            totalRevenue += amount;
                        }
                    }
                }
                // 如果找到了匹配项，就不再尝试其他模式
                if (foundAmounts.length > 0) {
                    break;
                }
            }
        }

        // 更新创收金额字段
        const totalRevenueInput = document.getElementById('totalRevenueInput');
        if (totalRevenueInput && totalRevenue >= 0) {
            totalRevenueInput.value = totalRevenue.toFixed(2);
        }
    }

    // 处理图片上传
    handleImageUpload(e) {
        console.log('上传产品图片');

        const file = e.target.files[0];
        if (!file) return;

        // 验证文件类型
        const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!validTypes.includes(file.type)) {
            this.showError('请选择有效的图片文件（JPG, PNG, GIF, WEBP）');
            e.target.value = ''; // 清除文件选择
            return;
        }

        // 验证文件大小（最大2MB）
        if (file.size > 2 * 1024 * 1024) {
            this.showError('图片文件大小不能超过2MB');
            e.target.value = ''; // 清除文件选择
            return;
        }

        // 显示图片预览
        const reader = new FileReader();
        reader.onload = (event) => {
            const imagePreviewContainer = document.getElementById('imagePreviewContainer');
            const imagePreview = document.getElementById('imagePreview');

            if (imagePreviewContainer && imagePreview) {
                imagePreview.src = event.target.result;
                imagePreviewContainer.classList.remove('hidden');

                // 添加点击预览功能
                imagePreview.onclick = () => {
                    openImageFullscreen(event.target.result);
                };
            }
        };
        reader.readAsDataURL(file);
    }

    // 移除图片
    removeImage() {
        console.log('移除产品图片');

        // 清空文件输入
        const imageUpload = document.getElementById('imageUpload');
        if (imageUpload) {
            imageUpload.value = '';
        }

        // 隐藏预览
        const imagePreviewContainer = document.getElementById('imagePreviewContainer');
        if (imagePreviewContainer) {
            imagePreviewContainer.classList.add('hidden');
        }

        // 清除图片URL（如果是编辑模式）
        const recordId = document.getElementById('recordId');
        if (recordId && recordId.value) {
            // 在编辑模式下，添加隐藏字段标记图片已被移除
            let removeImageInput = document.getElementById('removeImageInput');
            if (!removeImageInput) {
                removeImageInput = document.createElement('input');
                removeImageInput.type = 'hidden';
                removeImageInput.id = 'removeImageInput';
                removeImageInput.name = 'removeImage';
                document.getElementById('recordForm').appendChild(removeImageInput);
            }
            removeImageInput.value = 'true';
        }
    }

    // 店铺搜索相关方法
    prepareShopsPinyinIndex() {
        if (!this.allShops || this.allShops.length === 0) return;

        console.log('开始生成拼音索引，店铺数量:', this.allShops.length);
        console.log('拼音库是否可用:', typeof pinyin !== 'undefined');

        this.allShops.forEach(shop => {
            if (shop.name) {
                try {
                    if (typeof pinyin !== 'undefined') {
                        // 使用pinyin库
                        const fullPinyinArray = pinyin(shop.name, {
                            style: pinyin.STYLE_NORMAL
                        });
                        shop.pinyin_full = fullPinyinArray.join('').toLowerCase();

                        const firstLetterArray = pinyin(shop.name, {
                            style: pinyin.STYLE_FIRST_LETTER
                        });
                        shop.pinyin_first = firstLetterArray.join('').toLowerCase();

                        console.log(`店铺: ${shop.name}, 全拼: ${shop.pinyin_full}, 首字母: ${shop.pinyin_first}`);
                    } else {
                        // 如果拼音库不可用，使用简单的处理方式
                        shop.pinyin_full = this.generateSimplePinyin(shop.name);
                        shop.pinyin_first = this.generateFirstLetters(shop.name);
                        console.log(`店铺: ${shop.name}, 简单拼音: ${shop.pinyin_full}, 首字母: ${shop.pinyin_first}`);
                    }
                } catch (e) {
                    console.warn('生成拼音索引失败:', e);
                    shop.pinyin_full = '';
                    shop.pinyin_first = '';
                }
            } else {
                shop.pinyin_full = '';
                shop.pinyin_first = '';
            }
        });
    }

    // 简单的拼音处理（备用方案）
    generateSimplePinyin(text) {
        // 这是一个简化的拼音映射，实际项目中应该使用完整的拼音库
        const pinyinMap = {
            '京': 'jing', '东': 'dong', '微': 'wei', '星': 'xing', '组': 'zu', '装': 'zhuang',
            '淘': 'tao', '宝': 'bao', '天': 'tian', '猫': 'mao', '拼': 'pin', '多': 'duo',
            '苏': 'su', '宁': 'ning', '唯': 'wei', '品': 'pin', '会': 'hui', '小': 'xiao',
            '米': 'mi', '华': 'hua', '为': 'wei', '联': 'lian', '想': 'xiang', '戴': 'dai',
            '尔': 'er', '惠': 'hui', '普': 'pu', '门': 'men', '市': 'shi', '专': 'zhuan',
            '卖': 'mai', '店': 'dian', '旗': 'qi', '舰': 'jian', '官': 'guan', '方': 'fang'
        };

        return text.split('').map(char => pinyinMap[char] || char).join('').toLowerCase();
    }

    generateFirstLetters(text) {
        // 生成首字母缩写
        const firstLetterMap = {
            '京': 'j', '东': 'd', '微': 'w', '星': 'x', '组': 'z', '装': 'z',
            '淘': 't', '宝': 'b', '天': 't', '猫': 'm', '拼': 'p', '多': 'd',
            '苏': 's', '宁': 'n', '唯': 'w', '品': 'p', '会': 'h', '小': 'x',
            '米': 'm', '华': 'h', '为': 'w', '联': 'l', '想': 'x', '戴': 'd',
            '尔': 'e', '惠': 'h', '普': 'p', '门': 'm', '市': 's', '专': 'z',
            '卖': 'm', '店': 'd', '旗': 'q', '舰': 'j', '官': 'g', '方': 'f'
        };

        return text.split('').map(char => firstLetterMap[char] || char).join('').toLowerCase();
    }

    initShopSearch() {
        const shopSearch = document.getElementById('shopSearch');
        const shopSearchResults = document.getElementById('shopSearchResults');
        const shopName = document.getElementById('shopName');
        const addShopBtn = document.getElementById('addShopBtn');
        const newShopContainer = document.getElementById('newShopContainer');
        const cancelAddShop = document.getElementById('cancelAddShop');
        const newShopName = document.getElementById('newShopName');
        const saveNewShop = document.getElementById('saveNewShop');

        if (!shopSearch) return;

        // 搜索输入事件
        shopSearch.addEventListener('input', (e) => {
            const query = e.target.value.trim();
            if (query.length === 0) {
                this.hideShopSearchResults();
                return;
            }
            this.searchShops(query);
        });

        // 点击外部隐藏搜索结果
        document.addEventListener('click', (e) => {
            if (!shopSearch.contains(e.target) && !shopSearchResults.contains(e.target)) {
                this.hideShopSearchResults();
            }
        });

        // 添加新店铺按钮
        if (addShopBtn) {
            addShopBtn.addEventListener('click', () => {
                this.showNewShopForm();
            });
        }

        // 取消添加新店铺
        if (cancelAddShop) {
            cancelAddShop.addEventListener('click', () => {
                this.hideNewShopForm();
            });
        }

        // 保存新店铺
        if (saveNewShop) {
            saveNewShop.addEventListener('click', () => {
                this.saveNewShop();
            });
        }

        // 新店铺名称输入框回车保存
        if (newShopName) {
            newShopName.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.saveNewShop();
                }
            });
        }
    }

    searchShops(query) {
        if (!this.allShops || this.allShops.length === 0) {
            console.log('没有店铺数据可搜索');
            this.hideShopSearchResults();
            return;
        }

        const lowerQuery = query.toLowerCase();
        console.log('搜索关键词:', lowerQuery);

        const results = this.allShops.filter(shop => {
            const nameMatch = shop.name.toLowerCase().includes(lowerQuery);
            const fullPinyinMatch = shop.pinyin_full && shop.pinyin_full.includes(lowerQuery);
            const firstLetterMatch = shop.pinyin_first && shop.pinyin_first.includes(lowerQuery);

            const isMatch = nameMatch || fullPinyinMatch || firstLetterMatch;

            if (isMatch) {
                console.log(`匹配店铺: ${shop.name}, 名称匹配: ${nameMatch}, 全拼匹配: ${fullPinyinMatch}, 首字母匹配: ${firstLetterMatch}`);
            }

            return isMatch;
        }).slice(0, 10); // 限制显示10个结果

        console.log('搜索结果数量:', results.length);
        this.showShopSearchResults(results, query);
    }

    showShopSearchResults(results, query) {
        const shopSearchResults = document.getElementById('shopSearchResults');
        if (!shopSearchResults) return;

        if (results.length === 0) {
            shopSearchResults.innerHTML = `
                <div class="p-3 text-gray-500 dark:text-gray-400 text-sm">
                    未找到匹配的店铺
                    <div class="mt-2">
                        <button type="button" onclick="revenueManager.showNewShopForm('${query}')"
                                class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-xs">
                            <i class="fas fa-plus mr-1"></i>添加 "${query}" 为新店铺
                        </button>
                    </div>
                </div>
            `;
        } else {
            // 检查是否有完全匹配的店铺
            const exactMatch = results.find(shop => shop.name.toLowerCase() === query.toLowerCase());

            let resultsHtml = results.map(shop => `
                <div class="p-2 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer border-b border-gray-100 dark:border-gray-600 last:border-b-0"
                     onclick="revenueManager.selectShop('${shop.name}')">
                    <div class="font-medium text-sm text-gray-900 dark:text-white">${shop.name}</div>
                    ${shop.address ? `<div class="text-xs text-gray-500 dark:text-gray-400">${shop.address}</div>` : ''}
                </div>
            `).join('');

            // 如果没有完全匹配，提供添加新店铺的选项
            if (!exactMatch && query.trim().length > 0) {
                resultsHtml += `
                    <div class="p-2 border-t border-gray-200 dark:border-gray-600 bg-blue-50 dark:bg-gray-600">
                        <button type="button" onclick="revenueManager.showNewShopForm('${query}')"
                                class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-xs w-full text-left">
                            <i class="fas fa-plus mr-1"></i>添加 "${query}" 为新店铺
                        </button>
                    </div>
                `;
            }

            shopSearchResults.innerHTML = resultsHtml;
        }

        shopSearchResults.classList.remove('hidden');
    }

    hideShopSearchResults() {
        const shopSearchResults = document.getElementById('shopSearchResults');
        if (shopSearchResults) {
            shopSearchResults.classList.add('hidden');
        }
    }

    selectShop(shopName) {
        const shopSearch = document.getElementById('shopSearch');
        const shopNameHidden = document.getElementById('shopName');

        if (shopSearch) shopSearch.value = shopName;
        if (shopNameHidden) shopNameHidden.value = shopName;

        this.hideShopSearchResults();
    }

    showNewShopForm(defaultName = '') {
        const newShopContainer = document.getElementById('newShopContainer');
        const newShopName = document.getElementById('newShopName');

        if (newShopContainer) {
            newShopContainer.classList.remove('hidden');
        }

        if (newShopName) {
            newShopName.value = defaultName;
            newShopName.focus();
        }

        this.hideShopSearchResults();
    }

    hideNewShopForm() {
        const newShopContainer = document.getElementById('newShopContainer');
        const newShopName = document.getElementById('newShopName');

        if (newShopContainer) {
            newShopContainer.classList.add('hidden');
        }

        if (newShopName) {
            newShopName.value = '';
        }
    }

    async saveNewShop() {
        const newShopName = document.getElementById('newShopName');
        const shopName = newShopName?.value.trim();

        if (!shopName) {
            this.showError('请输入店铺名称');
            return;
        }

        try {
            console.log('尝试添加店铺:', shopName);

            const response = await sendAuthenticatedRequest('/api/revenue-records/revenue-shops', 'POST', {
                name: shopName,
                address: '',
                contact: '',
                notes: ''
            });

            console.log('服务器响应:', response);

            // 新API返回 { success: true, message: '...', data: {...} }
            if (response && response.success) {
                this.showSuccess(response.message || '店铺添加成功');

                // 添加到本地店铺列表
                const newShop = response.data;
                this.allShops.push(newShop);
                this.prepareShopsPinyinIndex();

                // 选择新添加的店铺
                this.selectShop(newShop.name);
                this.hideNewShopForm();

                // 重新加载店铺列表（更新筛选器）
                await this.loadShops();

            } else {
                this.showError(response.message || '添加店铺失败：响应格式错误');
            }

        } catch (error) {
            console.error('添加店铺失败:', error);

            // 处理不同的错误格式
            let errorMessage = '添加店铺失败';
            if (error.message) {
                errorMessage = error.message;
            } else if (typeof error === 'string') {
                errorMessage = error;
            }

            this.showError(errorMessage);
        }
    }

}

// 全局实例
let revenueManager;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    revenueManager = new RevenueManagement();
});

// 全局函数
function gohome() {
    localStorage.removeItem('token');
    window.location.href = '/index.html';
}

document.addEventListener('DOMContentLoaded', function () {
    console.log('页面加载完成，初始化主题切换功能');

    const themeToggle = document.getElementById('themeToggle');
    const html = document.documentElement;

    if (!themeToggle) {
        console.error('未找到主题切换按钮');
        return;
    }

    console.log('找到主题切换按钮，设置事件监听器');

    // 主题切换函数
    function toggleTheme() {
        const isDark = html.classList.contains('dark');
        console.log('当前主题:', isDark ? '深色' : '浅色');

        if (isDark) {
            // 切换到浅色主题
            html.classList.remove('dark');
            themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
            themeToggle.title = '切换到深色主题';
            localStorage.setItem('theme', 'light');
            console.log('已切换到浅色主题');
        } else {
            // 切换到深色主题
            html.classList.add('dark');
            themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
            themeToggle.title = '切换到浅色主题';
            localStorage.setItem('theme', 'dark');
            console.log('已切换到深色主题');
        }
    }

    // 绑定点击事件
    themeToggle.addEventListener('click', function (e) {
        e.preventDefault();
        console.log('主题切换按钮被点击');
        toggleTheme();
    });

    // 初始化主题
    const savedTheme = localStorage.getItem('theme');
    console.log('保存的主题偏好:', savedTheme);

    if (savedTheme === 'dark') {
        html.classList.add('dark');
        themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
        themeToggle.title = '切换到浅色主题';
        console.log('初始化为深色主题');
    } else {
        html.classList.remove('dark');
        themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
        themeToggle.title = '切换到深色主题';
        console.log('初始化为浅色主题');
    }
});