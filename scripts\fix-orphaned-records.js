#!/usr/bin/env node

/**
 * 修复孤立的数据记录
 * 为没有user_id的记录设置默认用户ID
 */

const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
    host: 'localhost',
    user: 'root',
    password: 'root',
    database: 'delivery_system'
};

async function fixOrphanedRecords() {
    let connection;
    
    try {
        console.log('🔧 开始修复孤立的数据记录...\n');
        
        // 连接数据库
        connection = await mysql.createConnection(dbConfig);
        console.log('✅ 数据库连接成功');
        
        // 1. 检查当前孤立记录数量
        console.log('\n📊 检查当前孤立记录数量...');
        
        const [orphanedPriceRecords] = await connection.query(`
            SELECT COUNT(*) as count 
            FROM price_records pr 
            LEFT JOIN users u ON pr.user_id = u.id 
            WHERE u.id IS NULL
        `);
        
        const [orphanedRawTableData] = await connection.query(`
            SELECT COUNT(*) as count 
            FROM raw_table_data rtd 
            LEFT JOIN users u ON rtd.user_id = u.id 
            WHERE u.id IS NULL
        `);
        
        console.log(`孤立的price_records记录: ${orphanedPriceRecords[0].count}`);
        console.log(`孤立的raw_table_data记录: ${orphanedRawTableData[0].count}`);
        
        // 2. 获取默认用户ID（通常是admin用户）
        console.log('\n👤 查找默认用户...');
        
        const [adminUsers] = await connection.query(`
            SELECT id, username FROM users WHERE role = 'admin' ORDER BY id LIMIT 1
        `);
        
        let defaultUserId = 1; // 默认值
        if (adminUsers.length > 0) {
            defaultUserId = adminUsers[0].id;
            console.log(`找到管理员用户: ${adminUsers[0].username} (ID: ${defaultUserId})`);
        } else {
            // 如果没有管理员，使用第一个用户
            const [firstUser] = await connection.query(`
                SELECT id, username FROM users ORDER BY id LIMIT 1
            `);
            if (firstUser.length > 0) {
                defaultUserId = firstUser[0].id;
                console.log(`使用第一个用户: ${firstUser[0].username} (ID: ${defaultUserId})`);
            } else {
                console.log('⚠️  没有找到任何用户，使用默认ID: 1');
            }
        }
        
        // 3. 修复price_records表中的孤立记录
        if (orphanedPriceRecords[0].count > 0) {
            console.log('\n🔧 修复price_records表中的孤立记录...');
            
            // 首先检查这些记录是否有user_id字段但值为NULL或无效
            const [nullUserIdRecords] = await connection.query(`
                SELECT COUNT(*) as count 
                FROM price_records 
                WHERE user_id IS NULL OR user_id = 0
            `);
            
            if (nullUserIdRecords[0].count > 0) {
                console.log(`发现 ${nullUserIdRecords[0].count} 条user_id为NULL或0的记录`);
                
                const result = await connection.query(`
                    UPDATE price_records 
                    SET user_id = ? 
                    WHERE user_id IS NULL OR user_id = 0
                `, [defaultUserId]);
                
                console.log(`✅ 已更新 ${result[0].affectedRows} 条price_records记录`);
            }
            
            // 检查是否还有引用不存在用户的记录
            const [invalidUserIdRecords] = await connection.query(`
                SELECT pr.id, pr.user_id 
                FROM price_records pr 
                LEFT JOIN users u ON pr.user_id = u.id 
                WHERE u.id IS NULL 
                LIMIT 10
            `);
            
            if (invalidUserIdRecords.length > 0) {
                console.log(`发现 ${invalidUserIdRecords.length} 条引用无效用户ID的记录:`);
                invalidUserIdRecords.forEach(record => {
                    console.log(`  - 记录ID: ${record.id}, 无效用户ID: ${record.user_id}`);
                });
                
                const updateResult = await connection.query(`
                    UPDATE price_records pr
                    LEFT JOIN users u ON pr.user_id = u.id 
                    SET pr.user_id = ? 
                    WHERE u.id IS NULL
                `, [defaultUserId]);
                
                console.log(`✅ 已修复 ${updateResult[0].affectedRows} 条无效引用的记录`);
            }
        }
        
        // 4. 修复raw_table_data表中的孤立记录
        if (orphanedRawTableData[0].count > 0) {
            console.log('\n🔧 修复raw_table_data表中的孤立记录...');
            
            const [nullUserIdRawData] = await connection.query(`
                SELECT COUNT(*) as count 
                FROM raw_table_data 
                WHERE user_id IS NULL OR user_id = 0
            `);
            
            if (nullUserIdRawData[0].count > 0) {
                const result = await connection.query(`
                    UPDATE raw_table_data 
                    SET user_id = ? 
                    WHERE user_id IS NULL OR user_id = 0
                `, [defaultUserId]);
                
                console.log(`✅ 已更新 ${result[0].affectedRows} 条raw_table_data记录`);
            }
            
            // 检查无效用户ID引用
            const [invalidRawDataRecords] = await connection.query(`
                UPDATE raw_table_data rtd
                LEFT JOIN users u ON rtd.user_id = u.id 
                SET rtd.user_id = ? 
                WHERE u.id IS NULL
            `, [defaultUserId]);
            
            if (invalidRawDataRecords[0].affectedRows > 0) {
                console.log(`✅ 已修复 ${invalidRawDataRecords[0].affectedRows} 条raw_table_data无效引用`);
            }
        }
        
        // 5. 验证修复结果
        console.log('\n✅ 验证修复结果...');
        
        const [finalOrphanedPriceRecords] = await connection.query(`
            SELECT COUNT(*) as count 
            FROM price_records pr 
            LEFT JOIN users u ON pr.user_id = u.id 
            WHERE u.id IS NULL
        `);
        
        const [finalOrphanedRawTableData] = await connection.query(`
            SELECT COUNT(*) as count 
            FROM raw_table_data rtd 
            LEFT JOIN users u ON rtd.user_id = u.id 
            WHERE u.id IS NULL
        `);
        
        console.log(`修复后孤立的price_records记录: ${finalOrphanedPriceRecords[0].count}`);
        console.log(`修复后孤立的raw_table_data记录: ${finalOrphanedRawTableData[0].count}`);
        
        // 6. 总结
        console.log('\n📋 修复总结:');
        
        if (finalOrphanedPriceRecords[0].count === 0 && finalOrphanedRawTableData[0].count === 0) {
            console.log('🎉 所有孤立记录已成功修复！');
            console.log('✅ 所有数据现在都正确关联到用户');
        } else {
            console.log('⚠️  仍有部分记录未修复:');
            if (finalOrphanedPriceRecords[0].count > 0) {
                console.log(`❌ price_records: ${finalOrphanedPriceRecords[0].count} 条`);
            }
            if (finalOrphanedRawTableData[0].count > 0) {
                console.log(`❌ raw_table_data: ${finalOrphanedRawTableData[0].count} 条`);
            }
        }
        
    } catch (error) {
        console.error('❌ 修复过程中发生错误:', error);
        throw error;
    } finally {
        if (connection) {
            await connection.end();
            console.log('\n🔌 数据库连接已关闭');
        }
    }
}

// 运行修复
if (require.main === module) {
    fixOrphanedRecords()
        .then(() => {
            console.log('\n✅ 修复完成');
            process.exit(0);
        })
        .catch(error => {
            console.error('\n💥 修复失败:', error);
            process.exit(1);
        });
}

module.exports = { fixOrphanedRecords };
