 #loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: background-color 0.3s ease;
        }

        .dark #loading-overlay {
            background-color: rgba(17, 24, 39, 0.8);
        }

        .spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            width: 36px;
            height: 36px;
            border-radius: 50%;
            border-left-color: #3f51b5;
            animation: spin 1s linear infinite;
            transition: border-color 0.3s ease;
        }

        .dark .spinner {
            border: 4px solid rgba(255, 255, 255, 0.1);
            border-left-color: #818cf8;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .fade-in {
            animation: fadeIn 0.3s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .image-preview {
            transition: all 0.3s ease;
        }

        .image-preview:hover {
            transform: scale(1.05);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        .dark .image-preview:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
        }

        /* 主题切换按钮样式 */
        #themeToggle {
            transition: all 0.2s ease;
        }

        #themeToggle:hover {
            transform: scale(1.05);
        }

        /* 暗色主题下的表格行悬停效果 */
        .dark tbody tr:hover {
            background-color: rgba(75, 85, 99, 0.5);
        }

        /* 暗色主题下的输入框焦点效果 */
        .dark input:focus,
        .dark textarea:focus,
        .dark select:focus {
            box-shadow: 0 0 0 3px rgba(129, 140, 248, 0.1);
        }
        
        /* 移动端优化 */
        @media (max-width: 640px) {
            .mobile-stack {
                flex-direction: column;
            }
            
            .mobile-full-width {
                width: 100%;
            }
            
            .mobile-p-2 {
                padding: 0.5rem;
            }
            
            .mobile-text-sm {
                font-size: 0.875rem;
                line-height: 1.25rem;
            }
            
            .mobile-hidden {
                display: none;
            }
            
            .mobile-flex-col {
                flex-direction: column;
            }
            
            .mobile-w-full {
                width: 100%;
            }
            
            .mobile-mt-2 {
                margin-top: 0.5rem;
            }
        }

        #wordCloudContainer {
            width: 100%;
            height: 300px;
        }

        #feedbackTimeChart {
            width: 100%;
            height: 300px;
        }

        #feedbackTypeChart {
            width: 100%;
            height: 300px;
        }