const express = require('express');
const router = express.Router();
const db = require('../config/db');
const { checkAdminRole } = require('../middleware/auth');

/**
 * 获取操作日志列表 (仅管理员)
 * GET /api/action-logs
 */
router.get('/', checkAdminRole, async (req, res) => {
    try {
        const {
            page = 1,
            limit = 20,
            username,
            actionType,
            targetTable,
            startDate,
            endDate,
            search
        } = req.query;

        const offset = (parseInt(page) - 1) * parseInt(limit);

        // 构建查询条件
        let whereConditions = [];
        let queryParams = [];

        // 用户名筛选
        if (username && username.trim()) {
            whereConditions.push('username LIKE ?');
            queryParams.push(`%${username.trim()}%`);
        }

        // 操作类型筛选
        if (actionType && actionType !== 'all') {
            whereConditions.push('action_type = ?');
            queryParams.push(actionType);
        }

        // 目标表筛选
        if (targetTable && targetTable !== 'all') {
            whereConditions.push('target_table = ?');
            queryParams.push(targetTable);
        }

        // 日期范围筛选
        if (startDate) {
            whereConditions.push('DATE(created_at) >= ?');
            queryParams.push(startDate);
        }

        if (endDate) {
            whereConditions.push('DATE(created_at) <= ?');
            queryParams.push(endDate);
        }

        // 全局搜索
        if (search && search.trim()) {
            whereConditions.push('(username LIKE ? OR target_table LIKE ? OR details LIKE ?)');
            const searchTerm = `%${search.trim()}%`;
            queryParams.push(searchTerm, searchTerm, searchTerm);
        }

        const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';

        // 获取总数
        const countQuery = `SELECT COUNT(*) as total FROM action_logs ${whereClause}`;
        const [countResult] = await db.query(countQuery, queryParams);
        const total = countResult[0].total;

        // 获取分页数据
        const dataQuery = `
            SELECT 
                id,
                user_id,
                username,
                action_type,
                target_table,
                target_id,
                details,
                ip_address,
                created_at
            FROM action_logs 
            ${whereClause}
            ORDER BY created_at DESC 
            LIMIT ? OFFSET ?
        `;

        const finalParams = [...queryParams, parseInt(limit), offset];
        const [logs] = await db.query(dataQuery, finalParams);

        // 处理details字段
        const processedLogs = logs.map(log => {
            if (log.details) {
                try {
                    log.details = JSON.parse(log.details);
                } catch (e) {
                    // 如果解析失败，保持原始字符串
                }
            }
            return log;
        });

        res.json({
            success: true,
            data: processedLogs,
            pagination: {
                total,
                page: parseInt(page),
                limit: parseInt(limit),
                pages: Math.ceil(total / parseInt(limit))
            }
        });

    } catch (error) {
        console.error('获取操作日志失败:', error);
        res.status(500).json({
            success: false,
            message: '服务器错误',
            error: error.message
        });
    }
});

/**
 * 获取操作统计信息 (仅管理员)
 * GET /api/action-logs/stats
 */
router.get('/stats', checkAdminRole, async (req, res) => {
    try {
        // 获取总体统计
        const [totalStats] = await db.query(`
            SELECT 
                COUNT(*) as total_actions,
                COUNT(DISTINCT username) as total_users,
                COUNT(DISTINCT target_table) as total_tables,
                COUNT(DISTINCT DATE(created_at)) as active_days
            FROM action_logs
        `);

        // 获取操作类型统计
        const [actionTypeStats] = await db.query(`
            SELECT 
                action_type,
                COUNT(*) as count
            FROM action_logs
            GROUP BY action_type
            ORDER BY count DESC
        `);

        // 获取目标表统计
        const [tableStats] = await db.query(`
            SELECT 
                target_table,
                COUNT(*) as count
            FROM action_logs
            WHERE target_table IS NOT NULL
            GROUP BY target_table
            ORDER BY count DESC
            LIMIT 10
        `);

        // 获取最活跃用户
        const [userStats] = await db.query(`
            SELECT 
                username,
                COUNT(*) as count
            FROM action_logs
            GROUP BY username
            ORDER BY count DESC
            LIMIT 10
        `);

        // 获取最近7天的活动统计
        const [dailyStats] = await db.query(`
            SELECT 
                DATE(created_at) as date,
                COUNT(*) as count
            FROM action_logs
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
            GROUP BY DATE(created_at)
            ORDER BY date DESC
        `);

        res.json({
            success: true,
            data: {
                total: totalStats[0],
                actionTypes: actionTypeStats,
                tables: tableStats,
                users: userStats,
                daily: dailyStats
            }
        });

    } catch (error) {
        console.error('获取操作统计失败:', error);
        res.status(500).json({
            success: false,
            message: '服务器错误',
            error: error.message
        });
    }
});

/**
 * 清理旧日志 (仅管理员)
 * DELETE /api/action-logs/cleanup
 */
router.delete('/cleanup', checkAdminRole, async (req, res) => {
    try {
        const { days = 30 } = req.body;
        
        const [result] = await db.query(`
            DELETE FROM action_logs 
            WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)
        `, [parseInt(days)]);

        res.json({
            success: true,
            message: `成功清理 ${result.affectedRows} 条 ${days} 天前的日志记录`
        });

    } catch (error) {
        console.error('清理日志失败:', error);
        res.status(500).json({
            success: false,
            message: '服务器错误',
            error: error.message
        });
    }
});

module.exports = router;
