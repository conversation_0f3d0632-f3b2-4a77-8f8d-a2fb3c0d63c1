<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电商客服常见问题与解决方案 - 管理系统</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {}
            }
        }
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- 主题管理器 - 优先加载 -->
    <script src="/js/theme-manager.js"></script>
    <script src="/js/main.js"></script>
    <!-- 加载状态和样式 -->
    <link rel="stylesheet" href="/css/customer-service-qa.css">
    <!-- 加载依赖库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- 加载脚本 -->
    <script src="/js/customer-service-qa.js"></script>
</head>
<body class="bg-gray-50 dark:bg-gray-900 min-h-screen transition-colors duration-200">
<!-- 加载状态指示器 -->
<div id="loading-overlay" style="display: none;">
    <div class="spinner mb-4"></div>
    <p id="loading-message" class="text-gray-700 dark:text-gray-300">正在加载数据，请稍候...</p>
</div>

<div class="container mx-auto px-2 sm:px-4 py-4 sm:py-8">
    <header class="mb-4 sm:mb-8">
        <div class="flex justify-between items-start">
            <div class="flex-1">
                <h1 class="text-xl sm:text-3xl font-bold text-indigo-700 dark:text-indigo-400 flex items-center">
                    <i class="fas fa-headset mr-2 sm:mr-3"></i> 电商客服常见问题与解决方案
                </h1>
                <p class="text-gray-600 dark:text-gray-300 mt-1 sm:mt-2 text-sm sm:text-base">记录和管理电商产品常见问题及解决方案</p>
            </div>

            <!-- 主题切换按钮 -->
            <button data-theme-toggle
                    class="ml-4 p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200 theme-toggle-btn"
                    title="切换主题">
                <i class="fas fa-moon"></i>
            </button>
        </div>

        <div class="mt-2">
            <a href="/"
               class="inline-block bg-indigo-600 dark:bg-indigo-500 text-white py-1 sm:py-2 px-3 sm:px-4 rounded-md hover:bg-indigo-700 dark:hover:bg-indigo-600 text-sm sm:text-base transition-colors duration-200">
                <i class="fas fa-home mr-1"></i> 返回首页
            </a>
        </div>
    </header>

    <div class="grid grid-cols-1 lg:grid-cols-4 gap-4 sm:gap-6">
        <!-- 左侧表单区域 -->
        <div class="lg:col-span-1 bg-white dark:bg-gray-800 p-3 sm:p-6 rounded-lg shadow-md transition-colors duration-200">
            <h2 class="text-lg sm:text-xl font-semibold text-gray-800 dark:text-gray-200 mb-3 sm:mb-4 flex items-center">
                <i class="fas fa-plus-circle mr-1 sm:mr-2 text-indigo-500 dark:text-indigo-400"></i> 添加问题解答
            </h2>

            <form id="qaForm" class="space-y-3 sm:space-y-4">
                <div>
                    <label for="categorySelect" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">问题分类</label>
                    <select id="categorySelect"
                            class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base transition-colors duration-200">
                        <option value="">选择分类</option>
                        <option value="hardware">硬件问题</option>
                        <option value="software">软件问题</option>
                        <option value="network">网络问题</option>
                        <option value="display">显示问题</option>
                        <option value="power">电源问题</option>
                        <option value="storage">存储问题</option>
                        <option value="cooling">散热问题</option>
                        <option value="compatibility">兼容性问题</option>
                        <option value="assembly">组装问题</option>
                        <option value="other">其它问题</option>
                    </select>
                </div>

                <div>
                    <label for="questionTitle" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">问题标题</label>
                    <input type="text" id="questionTitle"
                           class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base transition-colors duration-200"
                           placeholder="请输入问题标题...">
                </div>

                <div>
                    <label for="questionContent" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">问题描述</label>
                    <textarea id="questionContent" rows="3"
                              class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base transition-colors duration-200"
                              placeholder="请输入问题的详细描述..."></textarea>
                </div>

                <div>
                    <label for="questionImage" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">问题图片</label>
                    <div class="mt-1 flex justify-center px-4 sm:px-6 pt-3 sm:pt-5 pb-4 sm:pb-6 border-2 border-gray-300 dark:border-gray-600 border-dashed rounded-md bg-gray-50 dark:bg-gray-700 transition-colors duration-200">
                        <div class="space-y-1 text-center">
                            <div class="flex text-sm text-gray-600 dark:text-gray-400 items-center justify-center flex-wrap">
                                <label for="questionImage"
                                       class="relative cursor-pointer bg-white dark:bg-gray-800 rounded-md font-medium text-indigo-600 dark:text-indigo-400 hover:text-indigo-500 dark:hover:text-indigo-300 focus-within:outline-none transition-colors duration-200">
                                    <span>上传问题图片</span>
                                    <input id="questionImage" name="questionImage" type="file" accept="image/*"
                                           class="sr-only">
                                </label>
                                <p class="pl-1 mobile-hidden">或将图片拖拽到此处</p>
                            </div>
                            <p class="text-xs text-gray-500 dark:text-gray-400">PNG, JPG, GIF 格式</p>
                        </div>
                    </div>
                    <div id="questionImagePreview" class="mt-2 hidden">
                        <div class="relative inline-block">
                            <img id="questionImagePreviewImg" src="#" alt="问题图片预览"
                                 class="h-24 sm:h-32 rounded-md shadow image-preview">
                            <button type="button" id="removeQuestionImageBtn"
                                    class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 sm:w-6 sm:h-6 flex items-center justify-center hover:bg-red-600">
                                <i class="fas fa-times text-xs"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div>
                    <label for="solutionContent" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">解决方案</label>
                    <textarea id="solutionContent" rows="4"
                              class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base transition-colors duration-200"
                              placeholder="请输入问题的解决方案..."></textarea>
                </div>

                <div>
                    <label for="solutionImage" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">解决方案图片</label>
                    <div class="mt-1 flex justify-center px-4 sm:px-6 pt-3 sm:pt-5 pb-4 sm:pb-6 border-2 border-gray-300 dark:border-gray-600 border-dashed rounded-md bg-gray-50 dark:bg-gray-700 transition-colors duration-200">
                        <div class="space-y-1 text-center">
                            <div class="flex text-sm text-gray-600 dark:text-gray-400 items-center justify-center flex-wrap">
                                <label for="solutionImage"
                                       class="relative cursor-pointer bg-white dark:bg-gray-800 rounded-md font-medium text-indigo-600 dark:text-indigo-400 hover:text-indigo-500 dark:hover:text-indigo-300 focus-within:outline-none transition-colors duration-200">
                                    <span>上传解决方案图片</span>
                                    <input id="solutionImage" name="solutionImage" type="file" accept="image/*"
                                           class="sr-only">
                                </label>
                                <p class="pl-1 mobile-hidden">或将图片拖拽到此处</p>
                            </div>
                            <p class="text-xs text-gray-500 dark:text-gray-400">PNG, JPG, GIF 格式</p>
                        </div>
                    </div>
                    <div id="solutionImagePreview" class="mt-2 hidden">
                        <div class="relative inline-block">
                            <img id="solutionImagePreviewImg" src="#" alt="解决方案图片预览"
                                 class="h-24 sm:h-32 rounded-md shadow image-preview">
                            <button type="button" id="removeSolutionImageBtn"
                                    class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 sm:w-6 sm:h-6 flex items-center justify-center hover:bg-red-600">
                                <i class="fas fa-times text-xs"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div>
                    <label for="tags" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">标签（逗号分隔）</label>
                    <input type="text" id="tags"
                           class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base transition-colors duration-200"
                           placeholder="例如：显卡,驱动,安装">
                </div>

                <div class="flex space-x-2 sm:space-x-3 mobile-stack">
                    <button type="submit"
                            class="flex-1 bg-indigo-600 dark:bg-indigo-500 text-white py-1 sm:py-2 px-2 sm:px-4 rounded-md hover:bg-indigo-700 dark:hover:bg-indigo-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 text-sm sm:text-base transition-colors duration-200">
                        <i class="fas fa-save mr-1 sm:mr-2"></i> 保存问题
                    </button>
                    <button type="reset"
                            class="flex-1 bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-gray-200 py-1 sm:py-2 px-2 sm:px-4 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 text-sm sm:text-base transition-colors duration-200">
                        <i class="fas fa-undo mr-1 sm:mr-2"></i> 重置
                    </button>
                </div>
            </form>
        </div>

        <!-- 右侧列表和分析区域 -->
        <div class="lg:col-span-3 space-y-4 sm:space-y-6">
            <!-- 筛选和搜索区域 -->
            <div class="bg-white dark:bg-gray-800 p-3 sm:p-6 rounded-lg shadow-md transition-colors duration-200">
                <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 sm:gap-4">
                    <h2 class="text-lg sm:text-xl font-semibold text-gray-800 dark:text-gray-200 flex items-center">
                        <i class="fas fa-search mr-1 sm:mr-2 text-indigo-500 dark:text-indigo-400"></i> 问题筛选
                    </h2>

                    <div class="flex flex-wrap gap-2 w-full sm:w-auto">
                        <input id="searchInput" type="text" placeholder="搜索问题..."
                               class="px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-md text-sm flex-grow sm:flex-grow-0 transition-colors duration-200">

                        <select id="categoryFilter"
                                class="px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-md text-sm transition-colors duration-200">
                            <option value="all">全部分类</option>
                            <option value="hardware">硬件问题</option>
                            <option value="software">软件问题</option>
                            <option value="network">网络问题</option>
                            <option value="display">显示问题</option>
                            <option value="power">电源问题</option>
                            <option value="storage">存储问题</option>
                            <option value="cooling">散热问题</option>
                            <option value="compatibility">兼容性问题</option>
                            <option value="assembly">组装问题</option>
                            <option value="other">其它问题</option>
                        </select>

                        <button id="searchBtn"
                                class="px-2 sm:px-3 py-1 sm:py-2 bg-indigo-600 dark:bg-indigo-500 text-white rounded-md hover:bg-indigo-700 dark:hover:bg-indigo-600 text-sm transition-colors duration-200">
                            <i class="fas fa-search mr-1"></i> 搜索
                        </button>
                    </div>
                </div>

                <div class="mt-3 flex flex-wrap gap-2" id="tagFilters">
                    <!-- 标签过滤器将通过JS动态生成 -->
                </div>
            </div>

            <!-- 问题列表 -->
            <div class="bg-white dark:bg-gray-800 p-3 sm:p-6 rounded-lg shadow-md transition-colors duration-200">
                <h2 class="text-lg sm:text-xl font-semibold text-gray-800 dark:text-gray-200 mb-3 sm:mb-4 flex items-center justify-between">
                    <div>
                        <i class="fas fa-list mr-1 sm:mr-2 text-indigo-500 dark:text-indigo-400"></i> 问题列表
                    </div>
                    <div class="text-sm font-normal text-gray-500 dark:text-gray-400">
                        共 <span id="totalQuestions">0</span> 个问题
                    </div>
                </h2>

                <div id="questionsList" class="space-y-4">
                    <!-- 问题卡片将通过JavaScript动态填充 -->
                </div>

                <div class="mt-4 flex justify-center items-center">
                    <div class="flex space-x-1">
                        <button id="prevPageBtn"
                                class="px-2 py-1 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-md text-sm disabled:opacity-50 transition-colors duration-200">上一页
                        </button>
                        <span id="pageInfo" class="px-2 py-1 text-sm text-gray-700 dark:text-gray-300">第 <span id="currentPage">1</span> / <span
                                id="totalPages">1</span> 页</span>
                        <button id="nextPageBtn"
                                class="px-2 py-1 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-md text-sm disabled:opacity-50 transition-colors duration-200">下一页
                        </button>
                    </div>
                </div>
            </div>

            <!-- 数据分析区域 -->
            <div class="bg-white dark:bg-gray-800 p-3 sm:p-6 rounded-lg shadow-md transition-colors duration-200">
                <h2 class="text-lg sm:text-xl font-semibold text-gray-800 dark:text-gray-200 mb-3 sm:mb-4 flex items-center">
                    <i class="fas fa-chart-pie mr-1 sm:mr-2 text-indigo-500 dark:text-indigo-400"></i> 数据统计
                </h2>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- 分类统计 -->
                    <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg transition-colors duration-200">
                        <h3 class="text-md font-medium text-gray-700 dark:text-gray-300 mb-2">问题分类统计</h3>
                        <div class="h-64">
                            <canvas id="categoryChart"></canvas>
                        </div>
                    </div>

                    <!-- 时间分布 -->
                    <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg transition-colors duration-200">
                        <h3 class="text-md font-medium text-gray-700 dark:text-gray-300 mb-2">问题添加时间分布</h3>
                        <div class="h-64">
                            <canvas id="questionTimeChart"></canvas>
                        </div>
                    </div>

                    <!-- 热门标签 -->
                    <div class="col-span-1 md:col-span-2 bg-gray-50 dark:bg-gray-700 p-3 rounded-lg transition-colors duration-200">
                        <h3 class="text-md font-medium text-gray-700 dark:text-gray-300 mb-2">热门标签</h3>
                        <div class="flex flex-wrap gap-2 mt-3" id="popularTags">
                            <!-- 热门标签将通过JavaScript动态填充 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 查看问题详情模态框 -->
    <div id="qaModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-4 sm:p-6 max-w-4xl w-full max-h-[90vh] overflow-y-auto transition-colors duration-200">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg sm:text-xl font-semibold text-gray-800 dark:text-gray-200" id="modalTitle">问题详情</h3>
                <button id="closeQaModal" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors duration-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div id="qaDetails" class="space-y-4">
                <!-- 详情将动态填充 -->
            </div>

            <div class="mt-6 flex justify-end space-x-2">
                <button id="editQaBtn"
                        class="px-3 py-1 sm:px-4 sm:py-2 bg-indigo-600 dark:bg-indigo-500 text-white rounded-md hover:bg-indigo-700 dark:hover:bg-indigo-600 text-sm sm:text-base transition-colors duration-200">
                    编辑问题
                </button>
                <button id="closeQaModalBtn"
                        class="px-3 py-1 sm:px-4 sm:py-2 bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 text-sm sm:text-base transition-colors duration-200">
                    关闭
                </button>
            </div>
        </div>
    </div>

    <!-- 图片查看模态框 -->
    <div id="imageModal" class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 hidden">
        <div class="relative">
            <button id="closeImageModal"
                    class="absolute top-2 right-2 text-white bg-gray-800 dark:bg-gray-700 rounded-full w-8 h-8 flex items-center justify-center hover:bg-gray-700 dark:hover:bg-gray-600 transition-colors duration-200">
                <i class="fas fa-times"></i>
            </button>
            <img id="modalImage" src="#" alt="图片预览" class="max-w-[90vw] max-h-[90vh] rounded-lg">
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div id="deleteConfirmModal"
         class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md p-6 transition-colors duration-200">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">确认删除</h3>
            <p class="text-gray-700 dark:text-gray-300 mb-6">您确定要删除这个问题吗？此操作无法撤销。</p>
            <div class="flex justify-end space-x-3">
                <button id="cancelDeleteBtn" class="px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors duration-200">
                    取消
                </button>
                <button id="confirmDeleteBtn" class="px-4 py-2 bg-red-600 dark:bg-red-500 text-white rounded-md hover:bg-red-700 dark:hover:bg-red-600 transition-colors duration-200">
                    确认删除
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// 简单的主题切换功能
document.addEventListener('DOMContentLoaded', function() {
    console.log('初始化简单主题切换功能');

    // 获取主题切换按钮
    const themeToggleBtn = document.querySelector('[data-theme-toggle]');
    const html = document.documentElement;

    // 从localStorage加载保存的主题
    const savedTheme = localStorage.getItem('theme') || 'light';
    console.log('加载保存的主题:', savedTheme);

    // 应用主题
    function applyTheme(theme) {
        console.log('应用主题:', theme);

        if (theme === 'dark') {
            html.classList.add('dark');
            if (themeToggleBtn) {
                themeToggleBtn.innerHTML = '<i class="fas fa-sun"></i>';
                themeToggleBtn.title = '切换到浅色主题';
            }
        } else {
            html.classList.remove('dark');
            if (themeToggleBtn) {
                themeToggleBtn.innerHTML = '<i class="fas fa-moon"></i>';
                themeToggleBtn.title = '切换到深色主题';
            }
        }

        localStorage.setItem('theme', theme);
    }

    // 初始化主题
    applyTheme(savedTheme);

    // 主题切换事件
    if (themeToggleBtn) {
        console.log('找到主题切换按钮，绑定事件');
        themeToggleBtn.addEventListener('click', function(e) {
            e.preventDefault();
            const currentTheme = html.classList.contains('dark') ? 'dark' : 'light';
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            console.log('切换主题:', currentTheme, '->', newTheme);
            applyTheme(newTheme);
        });
    } else {
        console.error('未找到主题切换按钮');
    }
});
</script>

</body>
</html>