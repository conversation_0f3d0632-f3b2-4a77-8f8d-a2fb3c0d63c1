<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>客户常用话术管理</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="/js/main.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/viewerjs/1.11.6/viewer.min.css">
    <link rel="stylesheet" href="/css/style.css">
    <link rel="stylesheet" href="/css/common-phrases.css">

    <!-- 添加Viewer.js加载检查脚本 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('检查Viewer.js是否加载:', typeof Viewer !== 'undefined' ? '已加载' : '未加载');

            // 尝试修复可能的Viewer加载问题
            if (typeof Viewer === 'undefined') {
                console.warn('Viewer未定义，尝试重新加载Viewer.js...');
                const script = document.createElement('script');
                script.src = 'https://cdnjs.cloudflare.com/ajax/libs/viewerjs/1.11.6/viewer.min.js';
                script.onload = function() {
                    console.log('Viewer.js重新加载成功');
                    // 初始化全局变量以确保可用性
                    window.Viewer = Viewer;
                };
                script.onerror = function() {
                    console.error('Viewer.js重新加载失败');
                    alert('图片预览功能加载失败，部分功能可能无法正常使用');
                };
                document.head.appendChild(script);
            }
        });
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/viewerjs/1.11.6/viewer.min.js"></script>
</head>
<body class="bg-gray-50 min-h-screen transition-colors duration-300">
    <div class="container mx-auto px-2 sm:px-4 py-4 sm:py-8">
        <header class="mb-4 sm:mb-8">
            <div class="flex justify-between items-center">
                <h1 class="text-xl sm:text-3xl font-bold text-indigo-700 flex items-center">
                    <i class="fas fa-comments mr-2 sm:mr-3"></i> 客户常用话术管理
                </h1>
                <div class="flex items-center space-x-2 sm:space-x-4">
                    <button id="themeToggle" class="p-2 rounded-full bg-gray-100 dark:bg-gray-700 focus:outline-none">
                        <i id="themeIcon" class="fas fa-sun text-yellow-500"></i>
                    </button>
                    <a href="/" class="inline-block bg-indigo-600 text-white py-1 sm:py-2 px-3 sm:px-4 rounded-md hover:bg-indigo-700 text-sm sm:text-base">
                        <i class="fas fa-home mr-1"></i> 返回首页
                    </a>
                </div>
            </div>
            <p class="text-gray-600 mt-1 sm:mt-2 text-sm sm:text-base">管理和组织您的客户常用话术</p>
        </header>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-4 sm:gap-6">
            <!-- 左侧表单区域 -->
            <div class="lg:col-span-1 bg-white dark:bg-gray-800 p-3 sm:p-6 rounded-lg shadow-md">
                <h2 class="text-lg sm:text-xl font-semibold text-gray-800 dark:text-gray-200 mb-3 sm:mb-4 flex items-center">
                    <i class="fas fa-plus-circle mr-1 sm:mr-2 text-indigo-500"></i> 添加话术
                </h2>
                
                <form id="phraseForm" class="space-y-3 sm:space-y-4">
                    <div>
                        <label for="content" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">话术内容</label>
                        <textarea id="content" name="content" rows="4" class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base" placeholder="输入话术内容..."></textarea>
                    </div>
                    
                    <div>
                        <label for="type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">话术类型</label>
                        <select id="type" name="type" class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base">
                            <option value="text">文本</option>
                            <option value="link">链接</option>
                            <option value="image">图片</option>
                            <option value="file">文件</option>
                        </select>
                    </div>
                    
                    <div>
                        <label for="category" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">分类</label>
                        <div class="relative">
                            <input type="text" id="category" name="category" list="categoryList" class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base" placeholder="选择或创建新分类...">
                            <datalist id="categoryList"></datalist>
                        </div>
                    </div>
                    
                    <div>
                        <label for="tags" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">标签 (用逗号分隔)</label>
                        <input type="text" id="tags" name="tags" class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base" placeholder="例如: 退款,物流,价格...">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">添加文件</label>
                        <div class="grid grid-cols-2 gap-2 mb-3">
                            <button type="button" id="addImageBtn" class="flex items-center justify-center py-2 px-3 bg-blue-500 hover:bg-blue-600 text-white rounded-md">
                                <i class="fas fa-image mr-2"></i> 添加图片
                            </button>
                            <button type="button" id="addFileBtn" class="flex items-center justify-center py-2 px-3 bg-green-500 hover:bg-green-600 text-white rounded-md">
                                <i class="fas fa-file mr-2"></i> 添加文件
                            </button>
                        </div>
                        
                        <div id="fileUploadContainer" class="mt-2">
                            <div class="mt-1 flex justify-center px-4 sm:px-6 pt-3 sm:pt-5 pb-4 sm:pb-6 border-2 border-gray-300 dark:border-gray-600 border-dashed rounded-md">
                                <div class="space-y-1 text-center">
                                    <div class="flex text-sm text-gray-600 dark:text-gray-400 items-center justify-center flex-wrap">
                                        <label for="file" class="relative cursor-pointer bg-white dark:bg-gray-700 rounded-md font-medium text-indigo-600 dark:text-indigo-400 hover:text-indigo-500 focus-within:outline-none px-3 py-2">
                                            <span id="fileButtonText">选择文件</span>
                                            <input id="file" name="file" type="file" class="sr-only" accept="*/*">
                                        </label>
                                    </div>
                                    <div id="selectedFileName" class="text-sm text-indigo-600 dark:text-indigo-400 mt-2 hidden">
                                        <i class="fas fa-check-circle mr-1"></i> <span></span>
                                    </div>
                                    <div id="imagePreviewContainer" class="mt-3 hidden">
                                        <img id="imagePreview" class="max-w-full max-h-40 mx-auto rounded border border-gray-300 dark:border-gray-600" src="" alt="图片预览">
                                    </div>
                                    <div class="text-xs text-gray-500 dark:text-gray-400 mt-2">
                                        <p id="fileInfo">系统会自动识别文件类型并处理</p>
                                        <p class="mt-1"><i class="fas fa-image text-blue-500 mr-1"></i> 图片将自动转换为WebP格式</p>
                                        <p class="mt-1"><i class="fas fa-file text-green-500 mr-1"></i> 支持任何类型文件，最大10MB</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex space-x-2">
                        <button type="submit" class="flex-1 px-2 sm:px-3 py-1 sm:py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 text-sm sm:text-base min-h-[44px]">
                            <i class="fas fa-save mr-1"></i> 保存话术
                        </button>
                        <button type="button" id="resetBtn" class="px-2 sm:px-3 py-1 sm:py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 text-sm sm:text-base min-h-[44px] min-w-[44px]">
                            <i class="fas fa-redo-alt"></i>
                        </button>
                    </div>
                </form>
                
                <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                    <h3 class="text-md font-medium text-gray-800 dark:text-gray-200 mb-2">使用说明</h3>
                    <div class="text-sm text-gray-600 dark:text-gray-400">
                        <p>1. 在左侧添加您的常用话术</p>
                        <p>2. 系统会自动识别话术类型</p>
                        <p>3. 可上传图片(自动转换为webp)或文件</p>
                        <p>4. 使用上方搜索筛选功能快速查找</p>
                    </div>
                </div>
            </div>
            
            <!-- 右侧列表区域 -->
            <div class="lg:col-span-3">
                <div class="bg-white dark:bg-gray-800 p-3 sm:p-6 rounded-lg shadow-md">
                    <div class="flex justify-between items-center mb-3 sm:mb-4">
                        <h2 class="text-lg sm:text-xl font-semibold text-gray-800 dark:text-gray-200 flex items-center">
                            <i class="fas fa-list mr-1 sm:mr-2 text-indigo-500"></i> 话术列表
                            <div class="text-sm text-gray-500 dark:text-gray-400 ml-2" id="phraseCount">0个话术</div>
                        </h2>
                    </div>

                    <!-- 搜索和筛选区域 -->
                    <div class="mb-4 grid grid-cols-1 sm:grid-cols-4 gap-2">
                        <div class="sm:col-span-2">
                            <div class="relative">
                                <input type="text" id="searchTerm" class="w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 min-h-[44px]" placeholder="全字段搜索：内容、分类、标签...">
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                    <i class="fas fa-search text-gray-400"></i>
                                </div>
                            </div>
                        </div>
                        <div>
                            <select id="filterCategory" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 min-h-[44px]">
                                <option value="">所有分类</option>
                            </select>
                        </div>
                        <div>
                            <select id="filterType" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 min-h-[44px]">
                                <option value="">所有类型</option>
                                <option value="text">文本</option>
                                <option value="link">链接</option>
                                <option value="image">图片</option>
                                <option value="file">文件</option>
                            </select>
                        </div>
                    </div>

                    <!-- 重置按钮区域 -->
                    <div class="mb-4 flex justify-end">
                        <button id="resetFiltersBtn" class="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-md transition-colors duration-200 flex items-center min-h-[44px]">
                            <i class="fas fa-undo mr-2"></i> 重置筛选
                        </button>
                    </div>
                    
                    <div id="phraseList" class="space-y-3">
                        <!-- 话术列表将动态填充 -->
                        <div class="text-center text-gray-500 dark:text-gray-400 py-8">
                            <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                            <p>正在加载话术...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 编辑话术弹窗 -->
    <div id="editModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white dark:bg-gray-800 rounded-lg p-4 sm:p-6 max-w-lg w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200">编辑话术</h3>
                <button id="closeEditModal" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 p-2 rounded-full">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form id="editForm" class="space-y-3 sm:space-y-4">
                <input type="hidden" id="editId">
                
                <div>
                    <label for="editContent" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">话术内容</label>
                    <textarea id="editContent" name="editContent" rows="4" class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base"></textarea>
                </div>
                
                <div>
                    <label for="editType" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">话术类型</label>
                    <select id="editType" name="editType" class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base">
                        <option value="text">文本</option>
                        <option value="link">链接</option>
                        <option value="image">图片</option>
                        <option value="file">文件</option>
                    </select>
                </div>
                
                <div>
                    <label for="editCategory" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">分类</label>
                    <input type="text" id="editCategory" name="editCategory" list="editCategoryList" class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base">
                    <datalist id="editCategoryList"></datalist>
                </div>
                
                <div>
                    <label for="editTags" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">标签 (用逗号分隔)</label>
                    <input type="text" id="editTags" name="editTags" class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">当前文件</label>
                    <div id="currentFileDisplay" class="px-3 py-2 border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 rounded-md text-sm">
                        <span class="text-gray-500 dark:text-gray-400">无文件</span>
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">更换文件</label>
                    <div class="grid grid-cols-2 gap-2 mb-3">
                        <button type="button" id="editAddImageBtn" class="flex items-center justify-center py-2 px-3 bg-blue-500 hover:bg-blue-600 text-white rounded-md min-h-[44px]">
                            <i class="fas fa-image mr-2"></i> 添加图片
                        </button>
                        <button type="button" id="editAddFileBtn" class="flex items-center justify-center py-2 px-3 bg-green-500 hover:bg-green-600 text-white rounded-md min-h-[44px]">
                            <i class="fas fa-file mr-2"></i> 添加文件
                        </button>
                    </div>
                    
                    <div class="mt-1 flex justify-center px-4 sm:px-6 pt-3 sm:pt-5 pb-4 sm:pb-6 border-2 border-gray-300 dark:border-gray-600 border-dashed rounded-md">
                        <div class="space-y-1 text-center">
                            <div class="flex text-sm text-gray-600 dark:text-gray-400 items-center justify-center flex-wrap">
                                <label for="editFile" class="relative cursor-pointer bg-white dark:bg-gray-700 rounded-md font-medium text-indigo-600 dark:text-indigo-400 hover:text-indigo-500 focus-within:outline-none px-3 py-2">
                                    <span id="editFileButtonText">选择文件</span>
                                    <input id="editFile" name="editFile" type="file" class="sr-only" accept="*/*">
                                </label>
                            </div>
                            <div id="editSelectedFileName" class="text-sm text-indigo-600 dark:text-indigo-400 mt-2 hidden">
                                <i class="fas fa-check-circle mr-1"></i> <span></span>
                            </div>
                            <div id="editImagePreviewContainer" class="mt-3 hidden">
                                <img id="editImagePreview" class="max-w-full max-h-40 mx-auto rounded border border-gray-300 dark:border-gray-600" src="" alt="图片预览">
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="flex space-x-2 mt-6">
                    <button type="submit" class="flex-1 px-2 sm:px-3 py-1 sm:py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 text-sm sm:text-base min-h-[44px]">
                        <i class="fas fa-save mr-1"></i> 保存修改
                    </button>
                    <button type="button" id="deleteBtn" class="px-2 sm:px-3 py-1 sm:py-2 bg-red-600 text-white rounded-md hover:bg-red-700 text-sm sm:text-base min-h-[44px]">
                        <i class="fas fa-trash-alt mr-1"></i> 删除
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- 预览弹窗 -->
    <div id="previewModal" class="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 hidden">
        <div class="bg-white dark:bg-gray-800 rounded-lg p-4 sm:p-6 max-w-xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200">预览</h3>
                <button id="closePreviewModal" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 p-2 rounded-full">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div id="previewContent" class="my-4">
                <!-- 预览内容将动态填充 -->
            </div>
        </div>
    </div>
    
    <!-- Toast 通知 -->
    <div id="toast" class="fixed bottom-4 right-4 px-4 py-2 rounded-md text-white transform translate-y-20 opacity-0 transition-all duration-300 z-50 bg-green-600">
        <span class="flex items-center">
            <i id="toastIcon" class="fas fa-check-circle mr-2"></i>
            <span id="toastMessage">操作成功</span>
        </span>
    </div>
    
    <script src="/js/common-phrases.js"></script>
</body>
</html> 