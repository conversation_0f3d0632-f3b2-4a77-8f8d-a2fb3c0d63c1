/**
 * 拼音搜索工具包
 * 提供拼音搜索相关的实用函数
 */

// 确保pinyin实例存在
const pinyinUtil = {
    /**
     * 获取字符串的完整拼音
     * @param {string} str 中文字符串
     * @return {string} 拼音字符串
     */
    getFullPinyin: function(str) {
        if (typeof Pinyin !== 'undefined' && Pinyin.prototype.getFullChars) {
            const pinyinInstance = new Pinyin();
            return pinyinInstance.getFullChars(str);
        } else if (typeof PinyinHelper !== 'undefined' && PinyinHelper.getFullPinyin) {
            return PinyinHelper.getFullPinyin(str);
        } else {
            console.warn('No pinyin library available for getFullPinyin');
            return str;
        }
    },
    
    /**
     * 获取字符串的拼音首字母
     * @param {string} str 中文字符串
     * @return {string} 拼音首字母
     */
    getFirstLetters: function(str) {
        if (typeof Pinyin !== 'undefined' && Pinyin.prototype.getCamelChars) {
            const pinyinInstance = new Pinyin();
            return pinyinInstance.getCamelChars(str);
        } else if (typeof PinyinHelper !== 'undefined' && PinyinHelper.getPinyinInitials) {
            return PinyinHelper.getPinyinInitials(str);
        } else {
            console.warn('No pinyin library available for getFirstLetters');
            return str;
        }
    },
    
    /**
     * 检查文本是否包含拼音搜索匹配
     * @param {string} text 要检查的文本
     * @param {string} query 搜索查询
     * @return {boolean} 是否匹配
     */
    matchPinyin: function(text, query) {
        if (!text || !query) return false;
        
        const fullText = text.toLowerCase();
        const fullQuery = query.toLowerCase();
        
        // 直接文本匹配
        if (fullText.includes(fullQuery)) return true;
        
        try {
            // 全拼匹配
            const textPinyin = this.getFullPinyin(text).toLowerCase();
            if (textPinyin.includes(fullQuery)) return true;
            
            // 首字母匹配
            const textInitials = this.getFirstLetters(text).toLowerCase();
            if (textInitials.includes(fullQuery)) return true;
        } catch (e) {
            console.error('Error in pinyin matching:', e);
        }
        
        return false;
    },
    
    /**
     * 高亮显示匹配的文本
     * @param {string} text 原始文本
     * @param {string} query 查询字符串
     * @return {string} 高亮后的HTML
     */
    highlightMatch: function(text, query) {
        if (!query || !text) return text;
        
        // 简单替换，实际应用中可能需要更复杂的逻辑
        const regex = new RegExp(query, 'gi');
        return text.replace(regex, match => `<span class="highlighted">${match}</span>`);
    }
};

// 添加到全局对象
window.pinyinUtil = pinyinUtil; 