/**
 * 图片上传路由
 */

const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

const router = express.Router();

// 配置multer存储
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        // 获取文件夹名称，默认为'uploads'
        const folder = req.body.folder || 'uploads';
        const uploadPath = path.join(__dirname, '..', 'public', 'images', folder);
        
        // 确保目录存在
        if (!fs.existsSync(uploadPath)) {
            fs.mkdirSync(uploadPath, { recursive: true });
            console.log(`创建图片目录: ${uploadPath}`);
        }
        
        cb(null, uploadPath);
    },
    filename: function (req, file, cb) {
        // 生成唯一文件名
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const ext = path.extname(file.originalname);
        cb(null, file.fieldname + '-' + uniqueSuffix + ext);
    }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
    // 检查文件类型
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (allowedTypes.includes(file.mimetype)) {
        cb(null, true);
    } else {
        cb(new Error('不支持的文件类型。请上传 JPG、PNG 或 WebP 格式的图片。'), false);
    }
};

// 配置multer
const upload = multer({
    storage: storage,
    limits: {
        fileSize: 5 * 1024 * 1024 // 5MB限制
    },
    fileFilter: fileFilter
});

/**
 * 上传图片
 */
router.post('/', upload.single('image'), (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({
                success: false,
                message: '没有上传文件'
            });
        }
        
        // 构建图片URL
        const folder = req.body.folder || 'uploads';
        const imageUrl = `/images/${folder}/${req.file.filename}`;
        
        console.log('图片上传成功:', {
            originalName: req.file.originalname,
            filename: req.file.filename,
            size: req.file.size,
            folder: folder,
            imageUrl: imageUrl
        });
        
        res.json({
            success: true,
            message: '图片上传成功',
            imageUrl: imageUrl,
            filename: req.file.filename,
            originalName: req.file.originalname,
            size: req.file.size
        });
        
    } catch (error) {
        console.error('图片上传失败:', error);
        res.status(500).json({
            success: false,
            message: '图片上传失败: ' + error.message
        });
    }
});

/**
 * 删除图片
 */
router.delete('/:folder/:filename', (req, res) => {
    try {
        const { folder, filename } = req.params;
        const filePath = path.join(__dirname, '..', 'public', 'images', folder, filename);
        
        if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
            console.log('图片删除成功:', filePath);
            
            res.json({
                success: true,
                message: '图片删除成功'
            });
        } else {
            res.status(404).json({
                success: false,
                message: '图片文件不存在'
            });
        }
        
    } catch (error) {
        console.error('图片删除失败:', error);
        res.status(500).json({
            success: false,
            message: '图片删除失败: ' + error.message
        });
    }
});

/**
 * 获取图片列表
 */
router.get('/:folder', (req, res) => {
    try {
        const { folder } = req.params;
        const folderPath = path.join(__dirname, '..', 'public', 'images', folder);
        
        if (!fs.existsSync(folderPath)) {
            return res.json({
                success: true,
                images: []
            });
        }
        
        const files = fs.readdirSync(folderPath);
        const images = files
            .filter(file => {
                const ext = path.extname(file).toLowerCase();
                return ['.jpg', '.jpeg', '.png', '.webp'].includes(ext);
            })
            .map(file => {
                const filePath = path.join(folderPath, file);
                const stats = fs.statSync(filePath);
                return {
                    filename: file,
                    url: `/images/${folder}/${file}`,
                    size: stats.size,
                    created: stats.birthtime
                };
            });
        
        res.json({
            success: true,
            images: images
        });
        
    } catch (error) {
        console.error('获取图片列表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取图片列表失败: ' + error.message
        });
    }
});

module.exports = router;
