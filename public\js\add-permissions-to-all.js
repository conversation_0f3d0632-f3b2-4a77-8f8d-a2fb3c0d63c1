/**
 * 批量添加权限控制脚本
 * 该脚本需要在控制台中运行，用于为所有配件页面添加权限控制
 */

// 配件页面列表
const componentPages = [
  'case', 'cooler', 'cpu', 'fan', 'gpu', 'monitor', 'motherboard', 'psu', 'ram', 'storage'
];

// 需要在每个页面的JS文件中添加的权限初始化代码
const permissionCodeToAdd = `
// 初始化权限控制
function initPermissions() {
    console.log('初始化权限控制...');
    
    // 如果没有权限控制模块，发出警告
    if (typeof setupPermissionBasedUI !== 'function') {
        console.warn('警告：权限控制模块未加载！请确保已引入permission-helper.js');
        return;
    }
    
    // 执行权限控制初始化
    setupPermissionBasedUI().then(() => {
        // 检查编辑按钮
        const editButtons = document.querySelectorAll('.btn-edit, [data-action="edit"]');
        const deleteButtons = document.querySelectorAll('.btn-delete, [data-action="delete"]');
        
        // 根据用户角色处理按钮
        isAdmin().then(isAdminUser => {
            if (!isAdminUser) {
                // 禁用表单
                const form = document.querySelector('form');
                if (form) {
                    const inputs = form.querySelectorAll('input, select, textarea');
                    inputs.forEach(input => {
                        input.setAttribute('disabled', 'disabled');
                    });
                    
                    const submitBtn = form.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        submitBtn.setAttribute('disabled', 'disabled');
                        submitBtn.title = '只有管理员可以添加或修改数据';
                    }
                }
                
                // 隐藏或禁用编辑、删除按钮
                editButtons.forEach(btn => {
                    btn.style.display = 'none';
                });
                
                deleteButtons.forEach(btn => {
                    btn.style.display = 'none';
                });
                
                // 添加权限提示
                const header = document.querySelector('h1, h2') || document.body;
                const permissionNote = document.createElement('div');
                permissionNote.className = 'bg-blue-50 border-l-4 border-blue-500 text-blue-700 p-4 mb-4';
                permissionNote.innerHTML = \`
                    <p class="font-medium">权限提示</p>
                    <p>您当前为普通用户身份，只能查看数据，无法执行添加、修改或删除操作。</p>
                \`;
                
                if (header.parentNode) {
                    header.parentNode.insertBefore(permissionNote, header.nextSibling);
                }
            } else {
                // 添加管理员标识
                const header = document.querySelector('h1, h2');
                if (header) {
                    const adminBadge = document.createElement('span');
                    adminBadge.className = 'bg-green-500 text-white text-xs px-2 py-1 rounded ml-2';
                    adminBadge.innerText = '管理员';
                    header.appendChild(adminBadge);
                }
            }
        });
    });
}

// 页面加载完成后执行权限初始化
document.addEventListener('DOMContentLoaded', initPermissions);
`;

// 批量更新HTML文件的函数 - 这部分在实际使用时需要在服务器端执行
console.log('权限控制代码模板已生成，按照以下步骤为各个配件页面添加权限控制：');

// 添加HTML文件修改说明
console.log('\n===== HTML文件修改 =====');
console.log('请在每个配件页面的<head>部分添加以下代码：');
console.log('<script src="/js/permission-helper.js"></script>');

// 添加JS文件修改说明
console.log('\n===== JS文件修改 =====');
console.log('请在每个配件的JS文件末尾添加以下代码：');
console.log(permissionCodeToAdd);

console.log('\n===== 需要修改的页面 =====');
componentPages.forEach(component => {
  console.log(`- ${component}-info.html 和 /js/${component}-info.js`);
});

console.log('\n提示：您也可以使用自动脚本批量修改这些文件。'); 