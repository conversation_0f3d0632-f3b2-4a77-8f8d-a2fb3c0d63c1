/* 基本样式 */
:root {
  --primary-color: #4a6cf7;
  --primary-hover: #3151e8;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
}

body {
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #333;
}

/* 主要容器样式 */
.container {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
}

/* 卡片默认样式 */
.card {
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  background-color: #fff;
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

/* 表单元素样式 */
input[type="text"],
input[type="number"],
input[type="email"],
input[type="password"],
select,
textarea {
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

input[type="text"]:focus,
input[type="number"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
select:focus,
textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(74, 108, 247, 0.25);
}

/* 按钮样式 */
.btn {
  display: inline-block;
  font-weight: 500;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  transition: all 0.15s ease-in-out;
}

.btn-primary {
  background-color: var(--primary-color);
  border: 1px solid var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-hover);
  border-color: var(--primary-hover);
}

.btn-secondary {
  background-color: var(--secondary-color);
  border: 1px solid var(--secondary-color);
  color: white;
}

.btn-success {
  background-color: var(--success-color);
  border: 1px solid var(--success-color);
  color: white;
}

.btn-danger {
  background-color: var(--danger-color);
  border: 1px solid var(--danger-color);
  color: white;
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 表格样式 */
.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

/* 详情页图片样式 */
.image-preview {
  max-width: 100%;
  height: auto;
  border-radius: 0.375rem;
  cursor: pointer;
}

.image-preview:hover {
  opacity: 0.9;
}

.fullscreen-image {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.fullscreen-image img {
  max-width: 90%;
  max-height: 90%;
  object-fit: contain;
}

/* Toast消息样式 */
.toast {
  position: fixed;
  bottom: 1rem;
  right: 1rem;
  padding: 0.75rem 1.25rem;
  border-radius: 0.25rem;
  color: white;
  z-index: 1000;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.toast.show {
  opacity: 1;
}

.toast-success {
  background-color: var(--success-color);
}

.toast-error {
  background-color: var(--danger-color);
}

.toast-warning {
  background-color: var(--warning-color);
  color: #212529;
}

.toast-info {
  background-color: var(--info-color);
}

/* 响应式工具 */
@media (max-width: 640px) {
  .mobile-hidden {
    display: none;
  }
}

/* 导航样式 */
.nav-link {
  color: var(--dark-color);
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
}

.nav-link:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.nav-link.active {
  color: var(--primary-color);
  font-weight: 500;
}

/* 图标样式 */
.icon {
  vertical-align: middle;
}

/* 文件上传按钮样式 */
.file-upload {
  position: relative;
  overflow: hidden;
  display: inline-block;
}

.file-upload-input {
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.file-upload-label {
  display: inline-block;
  padding: 0.5rem 1rem;
  background-color: var(--light-color);
  color: var(--dark-color);
  border-radius: 0.25rem;
  cursor: pointer;
}

.file-upload-label:hover {
  background-color: #e2e6ea;
}

/* 实用工具类 */
.cursor-pointer {
  cursor: pointer;
}

.transition-all {
  transition: all 0.3s ease;
}

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
  
  body {
    font-size: 12pt;
  }
  
  .container {
    max-width: 100%;
    padding: 0;
  }
} 