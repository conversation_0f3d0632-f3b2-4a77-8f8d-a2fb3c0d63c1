<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电脑配件管理 - 电脑配件综合管理系统</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {}
            }
        }
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="/css/pc-components.css">
    <script src="/js/permission-helper.js"></script>
    <script src="/js/main.js"></script>
</head>

<body class="bg-gray-50 dark:bg-gray-900 min-h-screen transition-colors duration-300">
    <div class="container mx-auto px-4 py-8">
        <header class="mb-8 bg-white dark:bg-gray-800 shadow-md p-4 rounded-lg">
            <div class="flex justify-between items-start mb-4">
                <h1 class="text-3xl font-bold text-indigo-700 dark:text-indigo-400 flex items-center">
                    <i class="fas fa-microchip fa-lg mr-3"></i> 电脑配件管理
                </h1>
                <!-- 主题切换按钮 -->
                <button id="themeToggle" class="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200" title="切换主题">
                    <i id="themeIcon" class="fas fa-moon text-sm text-gray-600 dark:text-gray-300"></i>
                </button>
            </div>
            <div class="flex justify-between items-center">
                <p class="text-gray-600 dark:text-gray-300 text-sm">管理电脑配件信息和规格</p>
                <div class="flex flex-col md:flex-row">
                    <a href="/modern-code-generator.html" id="codeGeneratorLink"
                        class="inline-block bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 dark:from-purple-500 dark:to-blue-500 dark:hover:from-purple-600 dark:hover:to-blue-600 text-white py-2 px-3 rounded-md mb-2 md:mb-0 md:mr-2 transition duration-300 ease-in-out shadow-lg"
                        style="display: none;">
                        <i class="fas fa-magic mr-1"></i> 代码生成
                    </a>
                    <a href="/price.html"
                        class="inline-block bg-red-600 hover:bg-red-700 dark:bg-red-500 dark:hover:bg-red-600 text-white py-2 px-3 rounded-md mb-2 md:mb-0 md:mr-2 transition duration-300 ease-in-out">
                        <i class="fas fa-calculator mr-1"></i> 价格
                    </a>
                    <a href="/notes.html"
                        class="inline-block bg-yellow-600 hover:bg-yellow-700 dark:bg-yellow-500 dark:hover:bg-yellow-600 text-white py-2 px-3 rounded-md mb-2 md:mb-0 md:mr-2 transition duration-300 ease-in-out">
                        <i class="fas fa-sticky-note mr-1"></i> 笔记
                    </a>
                    <a href="/" id="homeLink"
                        class="inline-block bg-green-600 hover:bg-green-700 dark:bg-green-500 dark:hover:bg-green-600 text-white py-2 px-3 rounded-md transition duration-300 ease-in-out">
                        <i class="fas fa-home mr-1"></i> 首页
                    </a>
                </div>
            </div>
        </header>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- CPU 卡片 -->
            <div class="component-card bg-white dark:bg-gray-800 rounded-lg shadow-md dark:shadow-gray-700/50 overflow-hidden">
                <div class="bg-blue-500 dark:bg-blue-600 text-white p-4">
                    <h2 class="text-xl font-semibold flex items-center">
                        <i class="fas fa-microchip mr-2"></i> CPU
                    </h2>
                </div>
                <div class="p-5">
                    <p class="text-gray-600 dark:text-gray-300 mb-4">管理CPU型号、核心数、频率等信息</p>
                    <a href="/cpu-info.html"
                        class="block w-full bg-blue-500 hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700 text-white text-center py-2 rounded-md transition-colors">
                        <i class="fas fa-arrow-right mr-1"></i> 管理CPU
                    </a>
                </div>
            </div>

            <!-- 内存卡片 -->
            <div class="component-card bg-white dark:bg-gray-800 rounded-lg shadow-md dark:shadow-gray-700/50 overflow-hidden">
                <div class="bg-green-500 dark:bg-green-600 text-white p-4">
                    <h2 class="text-xl font-semibold flex items-center">
                        <i class="fas fa-memory mr-2"></i> 内存
                    </h2>
                </div>
                <div class="p-5">
                    <p class="text-gray-600 dark:text-gray-300 mb-4">管理内存规格、频率、时序等信息</p>
                    <a href="/ram-info.html"
                        class="block w-full bg-green-500 hover:bg-green-600 dark:bg-green-600 dark:hover:bg-green-700 text-white text-center py-2 rounded-md transition-colors">
                        <i class="fas fa-arrow-right mr-1"></i> 管理内存
                    </a>
                </div>
            </div>

            <!-- 硬盘卡片 -->
            <div class="component-card bg-white dark:bg-gray-800 rounded-lg shadow-md dark:shadow-gray-700/50 overflow-hidden">
                <div class="bg-purple-500 dark:bg-purple-600 text-white p-4">
                    <h2 class="text-xl font-semibold flex items-center">
                        <i class="fas fa-hdd mr-2"></i> 硬盘
                    </h2>
                </div>
                <div class="p-5">
                    <p class="text-gray-600 dark:text-gray-300 mb-4">管理硬盘类型、容量、读写速度等信息</p>
                    <a href="/storage-info.html"
                        class="block w-full bg-purple-500 hover:bg-purple-600 dark:bg-purple-600 dark:hover:bg-purple-700 text-white text-center py-2 rounded-md transition-colors">
                        <i class="fas fa-arrow-right mr-1"></i> 管理硬盘
                    </a>
                </div>
            </div>
        
            <!-- 显卡卡片 -->
            <div class="component-card bg-white dark:bg-gray-800 rounded-lg shadow-md dark:shadow-gray-700/50 overflow-hidden">
                <div class="bg-red-500 dark:bg-red-600 text-white p-4">
                    <h2 class="text-xl font-semibold flex items-center">
                        <i class="fas fa-tv mr-2"></i> 显卡
                    </h2>
                </div>
                <div class="p-5">
                    <p class="text-gray-600 dark:text-gray-300 mb-4">管理显卡型号、显存、频率等信息</p>
                    <a href="/gpu-info.html"
                        class="block w-full bg-red-500 hover:bg-red-600 dark:bg-red-600 dark:hover:bg-red-700 text-white text-center py-2 rounded-md transition-colors">
                        <i class="fas fa-arrow-right mr-1"></i> 管理显卡
                    </a>
                </div>
            </div>

            <!-- 散热器卡片 -->
            <div class="component-card bg-white dark:bg-gray-800 rounded-lg shadow-md dark:shadow-gray-700/50 overflow-hidden">
                <div class="bg-cyan-500 dark:bg-cyan-600 text-white p-4">
                    <h2 class="text-xl font-semibold flex items-center">
                        <i class="fas fa-wind mr-2"></i> 散热器
                    </h2>
                </div>
                <div class="p-5">
                    <p class="text-gray-600 dark:text-gray-300 mb-4">管理散热器类型、兼容性、散热性能等信息</p>
                    <a href="/cooler-info.html"
                        class="block w-full bg-cyan-500 hover:bg-cyan-600 dark:bg-cyan-600 dark:hover:bg-cyan-700 text-white text-center py-2 rounded-md transition-colors">
                        <i class="fas fa-arrow-right mr-1"></i> 管理散热器
                    </a>
                </div>
            </div>

            <!-- 机箱风扇卡片 -->
            <div class="component-card bg-white dark:bg-gray-800 rounded-lg shadow-md dark:shadow-gray-700/50 overflow-hidden">
                <div class="bg-yellow-500 dark:bg-yellow-600 text-white p-4">
                    <h2 class="text-xl font-semibold flex items-center">
                        <i class="fas fa-fan mr-2"></i> 机箱风扇
                    </h2>
                </div>
                <div class="p-5">
                    <p class="text-gray-600 dark:text-gray-300 mb-4">管理风扇尺寸、转速、风量等信息</p>
                    <a href="/fan-info.html"
                        class="block w-full bg-yellow-500 hover:bg-yellow-600 dark:bg-yellow-600 dark:hover:bg-yellow-700 text-white text-center py-2 rounded-md transition-colors">
                        <i class="fas fa-arrow-right mr-1"></i> 管理机箱风扇
                    </a>
                </div>
            </div>
        
            <!-- 电源卡片 -->
            <div class="component-card bg-white dark:bg-gray-800 rounded-lg shadow-md dark:shadow-gray-700/50 overflow-hidden">
                <div class="bg-gray-700 dark:bg-gray-600 text-white p-4">
                    <h2 class="text-xl font-semibold flex items-center">
                        <i class="fas fa-plug mr-2"></i> 电源
                    </h2>
                </div>
                <div class="p-5">
                    <p class="text-gray-600 dark:text-gray-300 mb-4">管理电源功率、认证、接口等信息</p>
                    <a href="/psu-info.html"
                        class="block w-full bg-gray-700 hover:bg-gray-800 dark:bg-gray-600 dark:hover:bg-gray-700 text-white text-center py-2 rounded-md transition-colors">
                        <i class="fas fa-arrow-right mr-1"></i> 管理电源
                    </a>
                </div>
            </div>

            <!-- 机箱卡片 -->
            <div class="component-card bg-white dark:bg-gray-800 rounded-lg shadow-md dark:shadow-gray-700/50 overflow-hidden">
                <div class="bg-indigo-500 dark:bg-indigo-600 text-white p-4">
                    <h2 class="text-xl font-semibold flex items-center">
                        <i class="fas fa-desktop mr-2"></i> 机箱
                    </h2>
                </div>
                <div class="p-5">
                    <p class="text-gray-600 dark:text-gray-300 mb-4">管理机箱规格、兼容性、接口等信息</p>
                    <a href="/case-info.html"
                        class="block w-full bg-indigo-500 hover:bg-indigo-600 dark:bg-indigo-600 dark:hover:bg-indigo-700 text-white text-center py-2 rounded-md transition-colors">
                        <i class="fas fa-arrow-right mr-1"></i> 管理机箱
                    </a>
                </div>
            </div>

            <!-- 主板卡片 -->
            <div class="component-card bg-white dark:bg-gray-800 rounded-lg shadow-md dark:shadow-gray-700/50 overflow-hidden">
                <div class="bg-orange-500 dark:bg-orange-600 text-white p-4">
                    <h2 class="text-xl font-semibold flex items-center">
                        <i class="fas fa-microchip mr-2"></i> 主板
                    </h2>
                </div>
                <div class="p-5">
                    <p class="text-gray-600 dark:text-gray-300 mb-4">管理主板芯片组、接口、扩展槽等信息</p>
                    <a href="/motherboard-info.html"
                        class="block w-full bg-orange-500 hover:bg-orange-600 dark:bg-orange-600 dark:hover:bg-orange-700 text-white text-center py-2 rounded-md transition-colors">
                        <i class="fas fa-arrow-right mr-1"></i> 管理主板
                    </a>
                </div>
            </div>

            <!-- 显示器卡片 -->
            <div class="component-card bg-white dark:bg-gray-800 rounded-lg shadow-md dark:shadow-gray-700/50 overflow-hidden">
                <div class="bg-pink-500 dark:bg-pink-600 text-white p-4">
                    <h2 class="text-xl font-semibold flex items-center">
                        <i class="fas fa-desktop mr-2"></i> 显示器
                    </h2>
                </div>
                <div class="p-5">
                    <p class="text-gray-600 dark:text-gray-300 mb-4">管理显示器品牌、尺寸、分辨率等信息</p>
                    <a href="/monitor-info.html"
                        class="block w-full bg-pink-500 hover:bg-pink-600 dark:bg-pink-600 dark:hover:bg-pink-700 text-white text-center py-2 rounded-md transition-colors">
                        <i class="fas fa-arrow-right mr-1"></i> 管理显示器
                    </a>
                </div>
            </div>

        </div>

        <div class="mt-10 bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md dark:shadow-gray-700/50">
            <h2 class="text-2xl font-semibold text-gray-800 dark:text-gray-100 mb-4">配件数据库统计</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4" id="stats-container">
                <div class="bg-blue-100 dark:bg-blue-900/30 p-4 rounded-md hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-colors cursor-pointer" onclick="window.location.href='/cpu-info.html'">
                    <h3 class="text-lg font-medium text-blue-800 dark:text-blue-300">CPU</h3>
                    <p class="text-3xl font-bold text-blue-900 dark:text-blue-200" id="cpu-count">-</p>
                </div>
                <div class="bg-green-100 dark:bg-green-900/30 p-4 rounded-md hover:bg-green-200 dark:hover:bg-green-900/50 transition-colors cursor-pointer" onclick="window.location.href='/ram-info.html'">
                    <h3 class="text-lg font-medium text-green-800 dark:text-green-300">内存</h3>
                    <p class="text-3xl font-bold text-green-900 dark:text-green-200" id="ram-count">-</p>
                </div>
                <div class="bg-purple-100 dark:bg-purple-900/30 p-4 rounded-md hover:bg-purple-200 dark:hover:bg-purple-900/50 transition-colors cursor-pointer" onclick="window.location.href='/storage-info.html'">
                    <h3 class="text-lg font-medium text-purple-800 dark:text-purple-300">硬盘</h3>
                    <p class="text-3xl font-bold text-purple-900 dark:text-purple-200" id="storage-count">-</p>
                </div>
                <div class="bg-red-100 dark:bg-red-900/30 p-4 rounded-md hover:bg-red-200 dark:hover:bg-red-900/50 transition-colors cursor-pointer" onclick="window.location.href='/gpu-info.html'">
                    <h3 class="text-lg font-medium text-red-800 dark:text-red-300">显卡</h3>
                    <p class="text-3xl font-bold text-red-900 dark:text-red-200" id="gpu-count">-</p>
                </div>
                <div class="bg-cyan-100 dark:bg-cyan-900/30 p-4 rounded-md hover:bg-cyan-200 dark:hover:bg-cyan-900/50 transition-colors cursor-pointer" onclick="window.location.href='/cooler-info.html'">
                    <h3 class="text-lg font-medium text-cyan-800 dark:text-cyan-300">散热器</h3>
                    <p class="text-3xl font-bold text-cyan-900 dark:text-cyan-200" id="cooler-count">-</p>
                </div>
                <div class="bg-yellow-100 dark:bg-yellow-900/30 p-4 rounded-md hover:bg-yellow-200 dark:hover:bg-yellow-900/50 transition-colors cursor-pointer" onclick="window.location.href='/fan-info.html'">
                    <h3 class="text-lg font-medium text-yellow-800 dark:text-yellow-300">风扇</h3>
                    <p class="text-3xl font-bold text-yellow-900 dark:text-yellow-200" id="fan-count">-</p>
                </div>
                <div class="bg-gray-200 dark:bg-gray-700 p-4 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors cursor-pointer" onclick="window.location.href='/psu-info.html'">
                    <h3 class="text-lg font-medium text-gray-800 dark:text-gray-300">电源</h3>
                    <p class="text-3xl font-bold text-gray-900 dark:text-gray-200" id="psu-count">-</p>
                </div>
                <div class="bg-indigo-100 dark:bg-indigo-900/30 p-4 rounded-md hover:bg-indigo-200 dark:hover:bg-indigo-900/50 transition-colors cursor-pointer" onclick="window.location.href='/case-info.html'">
                    <h3 class="text-lg font-medium text-indigo-800 dark:text-indigo-300">机箱</h3>
                    <p class="text-3xl font-bold text-indigo-900 dark:text-indigo-200" id="case-count">-</p>
                </div>
                <div class="bg-orange-100 dark:bg-orange-900/30 p-4 rounded-md hover:bg-orange-200 dark:hover:bg-orange-900/50 transition-colors cursor-pointer" onclick="window.location.href='/motherboard-info.html'">
                    <h3 class="text-lg font-medium text-orange-800 dark:text-orange-300">主板</h3>
                    <p class="text-3xl font-bold text-orange-900 dark:text-orange-200" id="motherboard-count">-</p>
                </div>
                <div class="bg-pink-100 dark:bg-pink-900/30 p-4 rounded-md hover:bg-pink-200 dark:hover:bg-pink-900/50 transition-colors cursor-pointer" onclick="window.location.href='/monitor-info.html'">
                    <h3 class="text-lg font-medium text-pink-800 dark:text-pink-300">显示器</h3>
                    <p class="text-3xl font-bold text-pink-900 dark:text-pink-200" id="monitor-count">-</p>
                </div>
            </div>
        </div>
    </div>
    <script src="/js/pc-components.js"></script>
</body>
</html> 