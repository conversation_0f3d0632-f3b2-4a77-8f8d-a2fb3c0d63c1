.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 表格行动画 */
.table-modern tbody tr {
    animation: fadeIn 0.3s ease-in-out;
    animation-fill-mode: both;
}

/* 错开动画效果 */
.table-modern tbody tr:nth-child(1) {
    animation-delay: 0.05s;
}

.table-modern tbody tr:nth-child(2) {
    animation-delay: 0.1s;
}

.table-modern tbody tr:nth-child(3) {
    animation-delay: 0.15s;
}

.table-modern tbody tr:nth-child(4) {
    animation-delay: 0.2s;
}

.table-modern tbody tr:nth-child(5) {
    animation-delay: 0.25s;
}

.table-modern tbody tr:nth-child(6) {
    animation-delay: 0.3s;
}

.table-modern tbody tr:nth-child(7) {
    animation-delay: 0.35s;
}

.table-modern tbody tr:nth-child(8) {
    animation-delay: 0.4s;
}

.table-modern tbody tr:nth-child(9) {
    animation-delay: 0.45s;
}

.table-modern tbody tr:nth-child(10) {
    animation-delay: 0.5s;
}

/* 表格样式优化 */
.table-modern tr {
    transition: background-color 0.15s ease-in-out;
}

.table-modern tr:hover {
    background-color: rgba(249, 250, 251, 0.8);
}

/* 暗色模式的表格行悬停 */
.dark .table-modern tr:hover {
    background-color: rgba(30, 41, 59, 0.8);
}

/* 图片效果 */
.image-column img,
.w-12 img {
    transition: transform 0.2s ease-out, opacity 0.2s ease;
}

.image-column img:hover,
.w-12 img:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* 图片查看覆盖层 */
.img-container {
    position: relative;
}

.img-overlay {
    position: absolute;
    inset: 0;
    background-color: rgba(0, 0, 0, 0);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.2s ease;
    border-radius: 0.375rem;
    color: white;
}

.w-12:hover .img-overlay {
    background-color: rgba(0, 0, 0, 0.3);
    opacity: 1;
}

/* 操作按钮样式 */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.action-buttons button {
    padding: 0.25rem;
    width: 1.75rem;
    height: 1.75rem;
    border-radius: 9999px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    background-color: transparent;
}

.btn-view {
    color: #3B82F6;
}

.dark .btn-view {
    color: #60a5fa;
}

.btn-view:hover {
    background-color: rgba(59, 130, 246, 0.1);
    transform: translateY(-2px);
}

.dark .btn-view:hover {
    background-color: rgba(59, 130, 246, 0.2);
}

.btn-edit {
    color: #10B981;
}

.dark .btn-edit {
    color: #34d399;
}

.btn-edit:hover {
    background-color: rgba(16, 185, 129, 0.1);
    transform: translateY(-2px);
}

.dark .btn-edit:hover {
    background-color: rgba(16, 185, 129, 0.2);
}

.btn-delete {
    color: #EF4444;
}

.dark .btn-delete {
    color: #f87171;
}

.btn-delete:hover {
    background-color: rgba(239, 68, 68, 0.1);
    transform: translateY(-2px);
}

.dark .btn-delete:hover {
    background-color: rgba(239, 68, 68, 0.2);
}

/* 标签样式增强 */
.brand-badge {
    transition: transform 0.2s ease;
    padding: 0.15rem 0.5rem;
    font-weight: 600;
    border-radius: 0.25rem;
    display: inline-block;
}

.brand-badge:hover {
    transform: scale(1.05);
}

/* 主板品牌专属样式 */
.brand-asus {
    background-color: rgba(0, 112, 201, 0.1);
    color: #0070c9;
    border: 1px solid rgba(0, 112, 201, 0.2);
}

.dark .brand-asus {
    background-color: rgba(0, 112, 201, 0.2);
    color: #4da6ff;
    border: 1px solid rgba(0, 112, 201, 0.3);
}

.brand-msi {
    background-color: rgba(234, 0, 41, 0.1);
    color: #ea0029;
    border: 1px solid rgba(234, 0, 41, 0.2);
}

.dark .brand-msi {
    background-color: rgba(234, 0, 41, 0.2);
    color: #ff5c7a;
    border: 1px solid rgba(234, 0, 41, 0.3);
}

.brand-gigabyte {
    background-color: rgba(255, 107, 0, 0.1);
    color: #ff6b00;
    border: 1px solid rgba(255, 107, 0, 0.2);
}

.dark .brand-gigabyte {
    background-color: rgba(255, 107, 0, 0.2);
    color: #ff8c33;
    border: 1px solid rgba(255, 107, 0, 0.3);
}

.brand-asrock {
    background-color: rgba(44, 62, 80, 0.1);
    color: #2c3e50;
    border: 1px solid rgba(44, 62, 80, 0.2);
}

.dark .brand-asrock {
    background-color: rgba(44, 62, 80, 0.2);
    color: #8d9fb0;
    border: 1px solid rgba(44, 62, 80, 0.3);
}

.brand-colorful {
    background-color: rgba(255, 65, 108, 0.1);
    color: #ff416c;
    border: 1px solid rgba(255, 65, 108, 0.2);
}

.dark .brand-colorful {
    background-color: rgba(255, 65, 108, 0.2);
    color: #ff7596;
    border: 1px solid rgba(255, 65, 108, 0.3);
}

.image-preview {
    transition: all 0.3s ease;
}

.image-preview:hover {
    transform: scale(1.05);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 全局表格修复 */
.table-wrapper {
    width: 100%;
    max-width: 100%;
    overflow-x: auto;
}

/* 确保内容不会被裁剪 */
* {
    box-sizing: border-box;
}

.container {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
}

/* 表格容器内部元素修复 */
.table-wrapper table {
    width: 100%;
}

/* 主题切换按钮样式 */
#theme-toggle {
    transition: all 0.3s ease;
    border-radius: 50%;
    padding: 8px;
    box-sizing: border-box;
    cursor: pointer;
    outline: none;
}

#theme-toggle:focus-visible {
    box-shadow: 0 0 0 2px rgba(249, 115, 22, 0.6);
}

.theme-toggle-icon {
    width: 20px;
    height: 20px;
    transition: transform 0.5s ease;
}

#theme-toggle:hover .theme-toggle-icon {
    transform: rotate(30deg);
}

.theme-toggle-sun {
    color: #ff8c00;
    display: none;
}

.theme-toggle-moon {
    color: #4b5563;
}

.dark .theme-toggle-sun {
    display: block;
}

.dark .theme-toggle-moon {
    display: none;
}

/* 移动端优化 */
@media (max-width: 640px) {
    /* ... 保留原来的移动端样式 ... */
}

/* PC端表格优化 - 新增 */
@media (min-width: 641px) {
    /* ... 保留原来的PC端样式 ... */

    /* 添加深色模式表格特定样式 */
    .dark .table-modern tbody tr {
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
        background-color: #1e293b;
    }

    .dark .table-modern tbody tr:hover {
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        background-color: #1e293b;
    }

    .dark thead th {
        background-color: #0f172a;
        border-bottom: 2px solid #1e293b;
    }

    .dark tbody tr:nth-child(even) {
        background-color: #1e293b;
    }

    .dark tbody tr:hover {
        background-color: #334155;
    }

    /* 优化PC端搜索区域布局 */
    .filter-container {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1.25rem;
    }

    .search-container {
        width: 40%;
        flex-shrink: 0;
    }

    .filter-group {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        flex-grow: 1;
        justify-content: flex-end;
    }

    .filter-group select {
        min-width: 120px;
        max-width: 180px;
        flex-grow: 0;
    }

    #resetFilterBtn {
        white-space: nowrap;
        flex-shrink: 0;
        margin-left: 0.5rem;
    }
}

/* ... 保留其他现有样式 ... */

/* 深色模式全局样式 */
.dark {
    color-scheme: dark;
}

.dark body {
    background-color: #0f172a;
    color: #e2e8f0;
}

.dark .bg-white,
.dark .bg-gray-50 {
    background-color: #1e293b;
}

.dark .bg-gray-100,
.dark .bg-gray-200 {
    background-color: #334155;
}

.dark .text-gray-500 {
    color: #94a3b8;
}

.dark .text-gray-600,
.dark .text-gray-700,
.dark .text-gray-800,
.dark .text-gray-900 {
    color: #e2e8f0;
}

.dark .border-gray-200,
.dark .border-gray-300 {
    border-color: #334155;
}

.dark .shadow-md,
.dark .shadow-sm,
.dark .shadow-lg,
.dark .shadow-xl {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.25);
}

.dark .bg-orange-50 {
    background-color: #422006;
}

.dark .border-orange-200 {
    border-color: #9a3412;
}

.dark .text-orange-500,
.dark .text-orange-600,
.dark .text-orange-700,
.dark .text-orange-800 {
    color: #fdba74;
}

.dark input,
.dark textarea,
.dark select {
    background-color: #1e293b;
    border-color: #475569;
    color: #e2e8f0;
}

.dark input:focus,
.dark textarea:focus,
.dark select:focus {
    border-color: #fb923c;
    box-shadow: 0 0 0 2px rgba(249, 115, 22, 0.25);
}

.dark option {
    background-color: #1e293b;
}

.dark .spec-tag.tdp {
    background-color: rgba(194, 65, 12, 0.2) !important;
    color: #fdba74 !important;
}

/* PC端深色模式下表格无边框 */
@media (min-width: 641px) {
    .dark .table-modern tbody tr {
        border: none !important;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    }

    .dark .table-modern tbody td {
        border: none !important;
    }

    .dark .table-modern thead th {
        border-bottom: none !important;
    }

    /* 优化深色模式下的表格悬停效果 */
    .dark .table-modern tbody tr:hover {
        background-color: rgba(51, 65, 85, 0.8) !important;
    }
}

/* 修复移动端重置按钮和搜索组件 */
@media (max-width: 640px) {
    .filter-container {
        display: flex;
        flex-direction: column;
        gap: 8px;
        width: 100%;
    }

    .search-container {
        margin-bottom: 10px;
        width: 100%;
    }

    .filter-group {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        width: 100%;
    }

    .filter-group select,
    .filter-group button {
        flex: 1;
        min-width: 0;
        white-space: nowrap;
    }

    /* 确保重置按钮不超出 */
    #resetFilterBtn {
        padding: 0.5rem 0.75rem;
        text-overflow: ellipsis;
        overflow: hidden;
        width: auto;
        flex-basis: auto;
        flex-grow: 0;
    }

    /* 优化移动端卡片布局 - 减小宽度和内边距避免需要左右滚动 */
    .motherboard-card-outer-container {
        width: 100% !important;
        margin-bottom: 16px !important;
        padding: 0 !important;
        /* 移除横向内边距 */
        box-sizing: border-box !important;
    }

    .table-modern.mobile-table tbody tr {
        display: block !important;
        width: 100% !important;
        margin-bottom: 20px !important;
        /* 增加卡片间距 */
        border-radius: 12px !important;
        overflow: hidden !important;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.12) !important;
        /* 增强阴影效果 */
        max-width: 100% !important;
        box-sizing: border-box !important;
        border: none !important;
        /* 移除边框线 */
    }

    .table-wrapper {
        overflow-x: hidden !important;
        padding: 0 !important;
        margin: 0 !important;
        width: 100% !important;
    }

    .dark .table-modern.mobile-table tbody tr {
        background-color: #1e293b !important;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3) !important;
        border: none !important;
        /* 确保深色模式也没有边框 */
    }

    .table-modern.mobile-table td {
        display: block !important;
        width: 100% !important;
        padding: 0 !important;
        /* 移除内边距 */
        box-sizing: border-box !important;
    }

    /* 减小标签组的边距 */
    .motherboard-tag-group {
        gap: 4px !important;
        margin-top: 6px !important;
    }

    /* 优化标签大小 */
    .motherboard-spec-tag {
        padding: 1px 5px !important;
        font-size: 0.65rem !important;
    }

    /* 调整操作按钮的大小和间距 */
    .motherboard-action-btn {
        padding: 3px 8px !important;
        font-size: 0.8rem !important;
    }

    .motherboard-card-footer {
        padding: 6px 10px !important;
    }

    /* 调整图片容器的尺寸 */
    .motherboard-card-body img,
    .motherboard-card-body>div:first-child {
        width: 40px !important;
        height: 40px !important;
    }

    /* 调整卡片主体部分的间距 */
    .motherboard-card-body {
        gap: 8px !important;
        padding: 10px !important;
    }

    /* 确保表格父容器不会溢出 */
    .table-container,
    .overflow-x-auto,
    .min-w-full,
    .inline-block {
        overflow-x: hidden !important;
        max-width: 100% !important;
        width: 100% !important;
        box-sizing: border-box !important;
    }

    /* 确保container容器有正确的内边距 */
    .container {
        padding-left: 8px !important;
        padding-right: 8px !important;
        max-width: 100% !important;
        box-sizing: border-box !important;
        overflow: hidden !important;
    }

    /* 确保列表在移动端正确显示 */
    .table-modern.mobile-table .model-column {
        border-bottom: 1px solid #e5e7eb !important;
    }

    .dark .table-modern.mobile-table .model-column {
        border-bottom: 1px solid #334155 !important;
    }

    /* 修复移动端分页控件 */
    .pagination-mobile {
        flex-wrap: nowrap !important;
    }

    /* 确保分页显示区域不被挤压 */
    #pageInfo {
        white-space: nowrap !important;
        width: auto !important;
        text-align: center !important;
        padding: 0 4px !important;
    }
}

/* 主板卡片样式优化 - 深色模式支持 */
.motherboard-card-new {
    width: 100% !important;
    border-radius: 12px !important;
    overflow: hidden !important;
    background-color: white !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.07) !important;
    display: flex !important;
    flex-direction: column !important;
    min-width: 0 !important;
    transition: transform 0.2s ease, box-shadow 0.2s ease !important;
    border: none !important;
    /* 移除所有边框 */
}

.dark .motherboard-card-new {
    background-color: #1e293b !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2) !important;
}

/* 修改卡片头部，完全移除上边框线 */
.motherboard-card-header {
    border: none !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;
    padding-top: 14px !important;
    /* 增加顶部内边距 */
    padding-bottom: 14px !important;
    display: flex !important;
    /* 恢复Flex布局 */
    justify-content: space-between !important;
    /* 恢复两端对齐 */
    align-items: center !important;
    /* 恢复垂直居中 */
    padding-left: 14px !important;
    padding-right: 14px !important;
}

.dark .motherboard-card-header {
    border: none !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05) !important;
}

/* 为移动端卡片设置更好的高度 */
@media (max-width: 640px) {

    /* 增加卡片内部空间 */
    .motherboard-card-body>div:nth-child(2) {
        padding: 4px 0 !important;
    }

    /* 图片容器尺寸调整 */
    .motherboard-card-body img,
    .motherboard-card-body>div:first-child {
        width: 50px !important;
        height: 50px !important;
    }
}

.motherboard-card-body {
    padding: 12px 14px !important;
    background-color: white !important;
    display: flex !important;
    gap: 12px !important;
}

.dark .motherboard-card-body {
    background-color: #1e293b !important;
}

.motherboard-card-footer {
    padding: 8px 14px !important;
    background-color: white !important;
    border-top: 1px solid #e5e7eb !important;
    display: flex !important;
    justify-content: space-around !important;
    align-items: center !important;
    gap: 8px !important;
}

.dark .motherboard-card-footer {
    background-color: #1e293b !important;
    border-top: 1px solid #334155 !important;
}

/* 移动端卡片样式调整 */
@media (max-width: 640px) {

    /* 增加卡片高度，减少紧凑感 */
    .motherboard-card-header {
        padding: 12px 14px !important;
        min-height: 48px !important;
    }

    .motherboard-card-body {
        padding: 14px !important;
        min-height: 80px !important;
        /* 增加主体内容高度 */
        gap: 12px !important;
        /* 增加图片与内容的间距 */
    }

    .motherboard-card-footer {
        padding: 10px 14px !important;
        min-height: 44px !important;
    }

    /* 增加标签间距和大小 */
    .motherboard-tag-group {
        gap: 6px !important;
        margin-top: 8px !important;
        margin-bottom: 4px !important;
    }

    .motherboard-spec-tag {
        padding: 2px 6px !important;
        font-size: 0.7rem !important;
    }

    /* 增加字体大小和行间距 */
    .motherboard-card-body {
        font-size: 0.8rem !important;
        line-height: 1.5 !important;
    }

    /* 调整文本间距 */
    .motherboard-card-body>div:nth-child(2)>div:not(:last-child) {
        margin-bottom: 5px !important;
    }

    /* 优化芯片组和接口文字间距 */
    .motherboard-card-body>div:nth-child(2)>div {
        margin-bottom: 6px !important;
    }
}

/* 修改卡片的阴影效果，去除边框线 */
.table-modern.mobile-table tbody tr {
    border: none !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1) !important;
    margin-bottom: 20px !important;
    /* 增加卡片之间的间距 */
}

.dark .table-modern.mobile-table tbody tr {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3) !important;
    border: none !important;
}

/* 移动端卡片布局组件样式 */
/* 标签样式 */
.motherboard-tag-group {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 6px !important;
    margin-top: 8px !important;
}

.motherboard-brand-badge {
    padding: 3px 8px !important;
    font-size: 0.75rem !important;
    font-weight: 600 !important;
    border-radius: 4px !important;
    flex-shrink: 0 !important;
    line-height: 1.2 !important;
}

.motherboard-spec-tag {
    padding: 2px 6px !important;
    border-radius: 4px !important;
    font-size: 0.7rem !important;
    font-weight: 500 !important;
    display: inline-flex !important;
    align-items: center !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: fit-content !important;
}

.motherboard-spec-tag i {
    margin-right: 4px !important;
}

.motherboard-action-btn {
    padding: 4px 10px !important;
    border-radius: 6px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 0.85rem !important;
    font-weight: 500 !important;
    transition: background-color 0.15s ease-out !important;
}

/* 标签颜色 */
.tag-socket {
    background-color: rgba(180, 83, 9, 0.08) !important;
    color: #b45309 !important;
    border: 1px solid rgba(180, 83, 9, 0.15) !important;
}

.dark .tag-socket {
    background-color: rgba(180, 83, 9, 0.15) !important;
    color: #fb923c !important;
    border: 1px solid rgba(180, 83, 9, 0.25) !important;
}

.tag-memory {
    background-color: rgba(76, 29, 149, 0.08) !important;
    color: #4c1d95 !important;
    border: 1px solid rgba(76, 29, 149, 0.15) !important;
}

.dark .tag-memory {
    background-color: rgba(76, 29, 149, 0.15) !important;
    color: #a78bfa !important;
    border: 1px solid rgba(76, 29, 149, 0.25) !important;
}

.tag-cpu {
    background-color: rgba(22, 101, 52, 0.08) !important;
    color: #166534 !important;
    border: 1px solid rgba(22, 101, 52, 0.15) !important;
}

.dark .tag-cpu {
    background-color: rgba(22, 101, 52, 0.15) !important;
    color: #4ade80 !important;
    border: 1px solid rgba(22, 101, 52, 0.25) !important;
}

.tag-chipset {
    background-color: rgba(6, 95, 70, 0.08) !important;
    color: #065f46 !important;
    border: 1px solid rgba(6, 95, 70, 0.15) !important;
}

.dark .tag-chipset {
    background-color: rgba(6, 95, 70, 0.15) !important;
    color: #34d399 !important;
    border: 1px solid rgba(6, 95, 70, 0.25) !important;
}

.tag-power {
    background-color: rgba(194, 65, 12, 0.08) !important;
    color: #c2410c !important;
    border: 1px solid rgba(194, 65, 12, 0.15) !important;
}

.dark .tag-power {
    background-color: rgba(194, 65, 12, 0.15) !important;
    color: #fb923c !important;
    border: 1px solid rgba(194, 65, 12, 0.25) !important;
}

/* 进一步优化移动端卡片布局 */
@media (max-width: 640px) {

    /* 更紧凑的标签布局，一行显示更多 */
    .motherboard-tag-group {
        gap: 3px !important;
        margin-top: 4px !important;
    }

    .motherboard-spec-tag {
        padding: 1px 3px !important;
        font-size: 0.8rem !important;
    }

    .motherboard-spec-tag i {
        margin-right: 2px !important;
        font-size: 0.6rem !important;
    }

    /* 减小文字大小，确保显示完整 */
    .motherboard-card-body {
        font-size: 0.75rem !important;
    }

    /* 减少不必要的边距和空白 */
    .motherboard-card-body {
        padding: 8px 10px !important;
        gap: 6px !important;
    }

    /* 优化图片尺寸 */
    .motherboard-card-body img,
    .motherboard-card-body>div:first-child {
        width: 40px !important;
        height: 40px !important;
    }

    /* 优化动作按钮 */
    .motherboard-action-btn {
        padding: 2px 6px !important;
        font-size: 0.7rem !important;
    }

    .motherboard-action-btn i {
        margin-right: 2px !important;
    }

    .motherboard-card-footer {
        padding: 5px 8px !important;
        gap: 5px !important;
    }

    /* 优化型号和品牌显示 */
    .motherboard-card-header {
        padding: 8px 10px !important;
    }

    /* 优化正文区域，确保所有文本能正确显示 */
    .motherboard-card-body div {
        overflow-wrap: break-word !important;
        word-wrap: break-word !important;
        hyphens: auto !important;
        line-height: 1.3 !important;
    }

    /* 标签紧凑排布 */
    .table-modern.mobile-table .motherboard-tag-group {
        display: flex !important;
        flex-wrap: wrap !important;
        justify-content: flex-start !important;
    }

    /* 增加信息区域宽度，减少图片区域宽度 */
    .motherboard-card-body>div:first-child {
        flex-shrink: 0 !important;
        flex-basis: 40px !important;
    }

    .motherboard-card-body>div:nth-child(2) {
        flex-grow: 1 !important;
        min-width: 0 !important;
        /* 确保可以被压缩 */
        padding-right: 0 !important;
        max-width: calc(100% - 50px) !important;
        /* 留给图片区域一些空间 */
    }
}

/* 更好的文本控制 */
.motherboard-card-header {
    padding: 8px 10px !important;
}

/* 文字换行控制，确保长型号名称正确显示 */
.motherboard-card-header>div:first-child {
    line-height: 1.2 !important;
    word-break: break-word !important;
    white-space: normal !important;
    font-size: 0.9rem !important;
}

/* 针对型号等长文本的特殊处理 */
.table-modern.mobile-table .model-text {
    word-break: break-word !important;
    overflow-wrap: break-word !important;
    white-space: normal !important;
    max-width: calc(100% - 5px) !important;
    display: inline-block !important;
}

/* 确保布局方式适合移动端 */
.motherboard-card-header {
    flex-wrap: wrap !important;
}

/* 主卡片容器溢出控制 */
.motherboard-card-new {
    max-width: 100% !important;
    overflow-x: hidden !important;
}