// 主题切换功能
class ThemeManager {
    constructor() {
        this.themeToggle = null;
        this.themeIcon = null;
        this.currentTheme = localStorage.getItem('theme') || 'light';
        this.init();
    }

    init() {
        // 应用保存的主题
        this.applyTheme(this.currentTheme);

        // 等待DOM加载完成后绑定事件
        document.addEventListener('DOMContentLoaded', () => {
            this.themeToggle = document.getElementById('themeToggle');
            this.themeIcon = document.getElementById('themeIcon');

            if (this.themeToggle) {
                this.themeToggle.addEventListener('click', () => this.toggleTheme());
            }

            // 更新图标
            this.updateIcon();
        });
    }

    applyTheme(theme) {
        if (theme === 'dark') {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark');
        }
        this.currentTheme = theme;
        localStorage.setItem('theme', theme);
    }

    toggleTheme() {
        const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.applyTheme(newTheme);
        this.updateIcon();
        this.updateTableRowStyles();

        // 添加切换动画效果
        document.body.style.transition = 'background-color 0.3s ease';
        setTimeout(() => {
            document.body.style.transition = '';
        }, 300);
    }

    updateIcon() {
        if (this.themeIcon) {
            if (this.currentTheme === 'dark') {
                this.themeIcon.className = 'fas fa-sun text-sm';
                this.themeToggle.title = '切换到明亮模式';
            } else {
                this.themeIcon.className = 'fas fa-moon text-sm';
                this.themeToggle.title = '切换到暗黑模式';
            }
        }
    }

    // 更新动态生成的表格行样式
    updateTableRowStyles() {
        // 由于我们已经在CSS中添加了全局的暗色主题样式，
        // 这里主要是确保动态生成的内容能正确应用主题
        const tableRows = document.querySelectorAll('#recordsTableBody tr');
        tableRows.forEach(row => {
            // 强制重新渲染以应用CSS类
            row.style.display = 'none';
            row.offsetHeight; // 触发重排
            row.style.display = '';
        });
    }
}

// 初始化主题管理器
const themeManager = new ThemeManager();

// 全局变量
let stores = [];
let addresses = [];
let currentPage = 1;
const recordsPerPage = 5;
let recordToDelete = null;
let recordToEdit = null;
let isLoggingOut = false; // 防止重复退出登录
let isPageUnloading = false; // 标记页面正在卸载

// DOM元素
const deliveryForm = document.getElementById('deliveryForm');

// 页面卸载事件监听
window.addEventListener('beforeunload', () => {
    isPageUnloading = true;
});

window.addEventListener('unload', () => {
    isPageUnloading = true;
});
const storeNameSelect = document.getElementById('storeName');
const deliveryAddressSelect = document.getElementById('deliveryAddress');
const deliveryImageInput = document.getElementById('deliveryImage');
const imagePreviewContainer = document.getElementById('imagePreviewContainer');
const imagePreview = document.getElementById('imagePreview');
const removeImageBtn = document.getElementById('removeImageBtn');
const recordsTableBody = document.getElementById('recordsTableBody');
const noRecordsMessage = document.getElementById('noRecordsMessage');
const recordCount = document.getElementById('recordCount');
const prevPageBtn = document.getElementById('prevPageBtn');
const nextPageBtn = document.getElementById('nextPageBtn');
const pageInfo = document.getElementById('pageInfo');
const searchInput = document.getElementById('searchInput');
const addStoreBtn = document.getElementById('addStoreBtn');
const newStoreContainer = document.getElementById('newStoreContainer');
const newStoreNameInput = document.getElementById('newStoreName');
const saveStoreBtn = document.getElementById('saveStoreBtn');
const addAddressBtn = document.getElementById('addAddressBtn');
const newAddressContainer = document.getElementById('newAddressContainer');
const newAddressInput = document.getElementById('newAddress');
const saveAddressBtn = document.getElementById('saveAddressBtn');
const editModal = document.getElementById('editModal');
const editForm = document.getElementById('editForm');
const editRecordIdInput = document.getElementById('editRecordId');
const editStoreNameSelect = document.getElementById('editStoreName');
const editDeliveryAddressSelect = document.getElementById('editDeliveryAddress');
const editImagePreview = document.getElementById('editImagePreview');
const editDeliveryImageInput = document.getElementById('editDeliveryImage');
const cancelEditBtn = document.getElementById('cancelEditBtn');
const saveEditBtn = document.getElementById('saveEditBtn');
const deleteModal = document.getElementById('deleteModal');
const cancelDeleteBtn = document.getElementById('cancelDeleteBtn');
const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
const storeSearch = document.getElementById('storeSearch');
const storeSearchResults = document.getElementById('storeSearchResults');

// 图表实例
let storeChart = null;
let addressChart = null;
let dailyChart = null;
let dailyDeliveriesChart = null;
let dailyAmountChart = null; // 新增每日送货总金额图表变量

// 加载数据
async function loadData() {
    try {
        // 获取 token
        const token = localStorage.getItem('token');
        if (!token) {
            window.location.href = '/login.html';
            return;
        }

        // 加载门店数据
        const storesResponse = await fetch('/api/stores', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!storesResponse.ok) {
            if (storesResponse.status === 401 || storesResponse.status === 403) {
                if (!isPageUnloading) {
                    logout();
                }
                return;
            }
            throw new Error('加载门店数据失败');
        }

        // 更新全局stores变量，用于店铺搜索
        const storesData = await storesResponse.json();
        console.log(`成功加载${storesData.length}个店铺数据`);

        // 确保全局stores变量被正确设置
        window.stores = storesData;
        stores = storesData;

        // 更新店铺下拉菜单和搜索功能
        updateStoreDropdowns(stores);

        // 加载地址数据
        const addressesResponse = await fetch('/api/addresses', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!addressesResponse.ok) {
            throw new Error('加载地址数据失败');
        }

        // 更新全局addresses变量
        const addressesData = await addressesResponse.json();
        console.log(`成功加载${addressesData.length}个地址数据`);

        window.addresses = addressesData;
        addresses = addressesData;
        updateAddressDropdowns(addresses);

        // 初始化店铺搜索
        initStoreSearch();

        // 加载记录数据
        loadRecords();
    } catch (error) {
        console.error('加载数据失败:', error);
        showToast('加载数据失败', 'error');
    }
}

// 加载统计数据
async function loadStatistics() {
    try {
        // 获取 token
        const token = localStorage.getItem('token');
        if (!token) {
            window.location.href = '/login.html';
            return;
        }

        const response = await fetch('/api/statistics', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            if (response.status === 401 || response.status === 403) {
                // Token 无效或过期
                localStorage.removeItem('token');
                localStorage.removeItem('username');
                window.location.href = '/login.html';
                return;
            }
            throw new Error('加载统计数据失败');
        }

        const data = await response.json();

        // 更新统计图表
        updateStoreChart(data.storeStats);
        updateAddressChart(data.addressStats);
        updateDailyChart(data.dailyStats);
    } catch (error) {
        console.error('加载统计数据失败:', error);
        showToast('加载统计数据失败', 'error');
    }
}

// 搜索记录
searchInput.addEventListener('input', async (e) => {
    const searchTerm = e.target.value.toLowerCase();

    if (searchTerm.trim() === '') {
        currentPage = 1;
        await loadRecords();
        return;
    }

    try {
        // 首先验证是否有令牌
        const token = localStorage.getItem('token');
        if (!token) {
            console.error('没有找到认证令牌');
            if (!isPageUnloading) {
                logout();
            }
            return;
        }

        const response = await fetch(`/api/records?page=${currentPage}&limit=${recordsPerPage}&search=${searchTerm}`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        const data = await response.json();

        if (data.records.length > 0) {
            noRecordsMessage.classList.add('hidden');
            renderRecords(data.records);
            renderPagination(data.total);
        } else {
            noRecordsMessage.classList.remove('hidden');
            recordsTableBody.innerHTML = `
                        <tr>
                            <td colspan="5" class="px-3 sm:px-6 py-3 text-center text-gray-500 text-sm sm:text-base">没有找到匹配的记录</td>
                        </tr>
                    `;
        }
    } catch (error) {
        console.error('搜索失败:', error);
        showToast('搜索失败', 'error');
    }
});

// 加载记录数据
async function loadRecords(page = 1, search = '') {
    try {
        // 获取 token
        const token = localStorage.getItem('token');
        if (!token) {
            console.error('没有找到认证令牌');
            logout();
            return;
        }

        console.log(`加载记录 (页码: ${page}, 搜索: ${search || '无'})`);

        currentPage = page;
        const searchParams = new URLSearchParams();
        searchParams.append('page', page);
        searchParams.append('limit', recordsPerPage);
        if (search) {
            searchParams.append('search', search);
        }

        // 明确打印请求URL和headers，用于调试
        const requestUrl = `/api/records?${searchParams.toString()}`;
        console.log(`API请求: ${requestUrl}`);
        console.log('使用令牌: Bearer ' + token.substring(0, 10) + '...');

        const response = await fetch(requestUrl, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            if (response.status === 401 || response.status === 403) {
                console.error(`认证失败 (${response.status}): ${response.statusText}`);
                if (!isPageUnloading) {
                    showToast('登录已过期，请重新登录', 'error');
                    setTimeout(() => {
                        if (!isPageUnloading) {
                            logout();
                        }
                    }, 1000);
                }
                return;
            }
            throw new Error(`加载记录失败: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log(`记录加载成功，获取到 ${data.records ? data.records.length : 0}/${data.total || 0} 条记录`);

        // Get all elements with null checks
        const noRecordsMessage = document.getElementById('noRecordsMessage');
        const recordsTableBody = document.getElementById('recordsTableBody');
        const paginationContainer = document.getElementById('paginationContainer');
        const recordCountSpan = document.getElementById('recordCount');

        // Handle missing elements
        if (!noRecordsMessage || !recordsTableBody) {
            console.error('必要的DOM元素不存在: noRecordsMessage或recordsTableBody');
            return; // Exit early if critical elements don't exist
        }

        if (data.records && data.records.length > 0) {
            console.log(`Loaded ${data.records.length} records.`);
            records = data.records;
            renderRecords(records);

            // 确保新渲染的记录应用正确的主题样式
            if (window.themeManager) {
                window.themeManager.updateTableRowStyles();
            }

            if (data.total > 0) {
                renderPagination(data.total);
            }

            // Safe access with null checks
            if (noRecordsMessage) noRecordsMessage.classList.add('hidden');
            if (recordsTableBody) recordsTableBody.classList.remove('hidden');
            if (paginationContainer) paginationContainer.classList.remove('hidden');
        } else {
            console.log('No records found.');
            records = [];
            renderRecords([]);

            // 确保空表格也应用正确的主题样式
            if (window.themeManager) {
                window.themeManager.updateTableRowStyles();
            }

            if (typeof data.total === 'number') {
                renderPagination(data.total);
            } else {
                renderPagination(0);
            }

            // Safe access with null checks
            if (noRecordsMessage) noRecordsMessage.classList.remove('hidden');
            if (recordsTableBody) recordsTableBody.classList.add('hidden');
            if (paginationContainer) paginationContainer.classList.add('hidden');
        }

        // Update record count with null check
        if (recordCountSpan) {
            recordCountSpan.textContent = `共${data.total || 0}条记录`;
        }

    } catch (error) {
        console.error('加载记录失败:', error);
        showToast(`加载记录失败: ${error.message}`, 'error');

        // Get elements with null checks
        const noRecordsMessage = document.getElementById('noRecordsMessage');
        const recordsTableBody = document.getElementById('recordsTableBody');
        const paginationContainer = document.getElementById('paginationContainer');
        const recordCountSpan = document.getElementById('recordCount');

        if (noRecordsMessage) noRecordsMessage.classList.remove('hidden');
        if (recordsTableBody) recordsTableBody.classList.add('hidden');
        if (paginationContainer) paginationContainer.classList.add('hidden');
        if (recordCountSpan) recordCountSpan.textContent = '共0条记录';
    }
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function (...args) {
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(this, args), wait);
    };
}

// 加载每日送货次数数据并显示图表
async function loadDailyDeliveries(storeId = '') {
    try {
        const token = localStorage.getItem('token');
        if (!token) return;

        // 构建API请求URL，添加查询参数
        let url = `/api/statistics/daily-deliveries`;
        const params = new URLSearchParams();
        if (storeId) {
            params.append('storeId', storeId);
        }

        if (params.toString()) {
            url += `?${params.toString()}`;
        }

        console.log('发送请求获取每日送货数据:', url);

        const response = await fetch(url, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            throw new Error('获取每日送货数据失败');
        }

        const data = await response.json();
        console.log('接收到的每日送货数据:', data);
        console.log('数据标签(日期):', data.labels);
        console.log('数据集合:', data.datasets);

        updateDailyDeliveriesChart(data);
    } catch (error) {
        console.error('加载每日送货次数失败:', error);
        showToast('加载每日送货次数统计失败', 'error');
    }
}

// 更新每日送货总金额图表
function updateDailyAmountChart(data) {
    console.log('开始更新每日金额图表，数据:', data);
    const ctx = document.getElementById('dailyAmountChart')?.getContext('2d');
    if (!ctx) {
        console.error('Canvas context for dailyAmountChart not found');
        return;
    }

    // 销毁旧图表（如果存在）
    if (dailyAmountChart) {
        dailyAmountChart.destroy();
    }

    // 确保数据有效 - 非空且非全为零
    let hasData = false;
    if (data && data.datasets && data.datasets[0] && data.datasets[0].data && data.datasets[0].data.length > 0) {
        // 检查是否至少有一个非零值
        hasData = data.datasets[0].data.some(value => parseFloat(value) > 0);
        console.log('图表数据检查: 共有', data.datasets[0].data.length, '个数据点, 有效数据:', hasData);
    } else {
        console.warn('图表数据无效或为空');
    }

    // 如果没有有效数据，显示空状态
    if (!hasData) {
        console.warn('每日金额图表没有有效数据');
        ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.font = '14px Arial';
        ctx.fillStyle = '#6b7280'; // gray-500

        // 确保画布大小正确
        const parentWidth = ctx.canvas.parentElement.clientWidth;
        const parentHeight = ctx.canvas.parentElement.clientHeight;
        ctx.canvas.width = parentWidth;
        ctx.canvas.height = parentHeight;

        ctx.fillText('暂无金额数据', ctx.canvas.width / 2, ctx.canvas.height / 2 - 10);
        ctx.font = '12px Arial';
        ctx.fillText('请确保配件记录中含有有效的价格和数量数据', ctx.canvas.width / 2, ctx.canvas.height / 2 + 15);
        return;
    }

    console.log('创建图表，数据长度:', data.labels.length, '数值:', data.datasets[0].data);

    // 准备图表数据
    const chartData = {
        labels: data.labels,
        datasets: [{
            label: '每日送货总金额(元)',
            data: data.datasets[0].data,
            borderColor: '#16A34A', // green-600
            backgroundColor: 'rgba(22, 163, 74, 0.1)', // light green
            borderWidth: 2,
            fill: true,
            pointRadius: 4,
            pointHoverRadius: 6,
            tension: 0.4,
            spanGaps: false
        }]
    };

    // 移动端检测
    const isMobile = window.innerWidth < 640;

    // 创建图表
    dailyAmountChart = new Chart(ctx, {
        type: 'line',
        data: chartData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            layout: {
                padding: {
                    left: isMobile ? 10 : 20,
                    right: isMobile ? 20 : 40,
                    top: isMobile ? 10 : 20,
                    bottom: isMobile ? 5 : 10
                }
            },
            plugins: {
                legend: {
                    display: false // 隐藏图例，只有一个数据集
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    backgroundColor: 'rgba(0, 0, 0, 0.7)',
                    padding: 10,
                    callbacks: {
                        title: function (tooltipItems) {
                            return `日期: ${tooltipItems[0].label}`;
                        },
                        label: function (context) {
                            return `金额: ¥${parseFloat(context.raw).toFixed(2)}`;
                        }
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        maxRotation: isMobile ? 45 : 0,
                        font: {
                            size: isMobile ? 9 : 11
                        }
                    }
                },
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function (value) {
                            return '¥' + value;
                        }
                    },
                    title: {
                        display: true,
                        font: {
                            weight: 'bold'
                        }
                    }
                }
            },
            animation: {
                duration: 1000
            }
        }
    });

    console.log('金额图表创建完成');
}

// 生成图表颜色
function generateChartColors(count) {
    const baseColors = [
        '#4F46E5', // indigo-600
        '#0891B2', // cyan-600
        '#16A34A', // green-600
        '#D97706', // amber-600
        '#DC2626', // red-600
        '#7C3AED', // violet-600
        '#2563EB', // blue-600
        '#059669', // emerald-600
        '#EA580C', // orange-600
        '#DB2777', // pink-600
        '#4338CA', // indigo-700
        '#0E7490', // cyan-700
        '#15803D', // green-700
        '#B45309', // amber-700
        '#B91C1C', // red-700
        '#6D28D9', // violet-700
        '#1D4ED8', // blue-700
        '#047857', // emerald-700
        '#C2410C', // orange-700
        '#BE185D'  // pink-700
    ];

    // 如果基础颜色不够，生成更多随机颜色
    if (count > baseColors.length) {
        const additionalColors = [];
        for (let i = 0; i < count - baseColors.length; i++) {
            additionalColors.push(getRandomColor());
        }
        return [...baseColors, ...additionalColors];
    }

    return baseColors.slice(0, count);
}

// 生成随机HEX颜色
function getRandomColor() {
    const letters = '0123456789ABCDEF';
    let color = '#';
    for (let i = 0; i < 6; i++) {
        color += letters[Math.floor(Math.random() * 16)];
    }
    return color;
}

// 初始化页面
// 获取门市店铺ID
async function getStoreIdByName(name) {
    try {
        const token = localStorage.getItem('token');
        if (!token) return null;

        const response = await fetch('/api/stores', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            throw new Error('获取店铺数据失败');
        }

        const stores = await response.json();
        const store = stores.find(s => s.name === '门市');
        return store ? store.id : null;
    } catch (error) {
        console.error('获取门市ID失败:', error);
        return null;
    }
}

document.addEventListener('DOMContentLoaded', function () {
    const token = localStorage.getItem('token');
    const username = localStorage.getItem('username');
    const userInfo = document.getElementById('user-info');

    // 只有当user-info元素存在时才设置内容
    if (userInfo) {
        if (token && username) {
            console.log(`从localStorage恢复用户: ${username}`);
            userInfo.innerHTML = `
                        <span>欢迎，${username}</span>
                        <button onclick="logout()" class="text-red-600 hover:text-red-800" id="logoutBtn3">
                            <i class="fas fa-sign-out-alt mr-1"></i>退出
                        </button>
                    `;
        } else {
            userInfo.innerHTML = `
                        <a href="/login.html" class="text-blue-600 hover:text-blue-800">
                            <i class="fas fa-sign-in-alt mr-1"></i>请登录
                        </a>
                    `;
        }
    } else {
        console.log('user-info元素不存在，跳过设置');
    }

    // 为编辑模式的添加配件按钮添加事件监听 - 防止多次绑定
    const editAddPartBtn = document.getElementById('editAddPartBtn');
    if (editAddPartBtn && !editAddPartBtn._hasAddListener) {
        editAddPartBtn.addEventListener('click', function () {
            const tbody = document.getElementById('editPartsTableBody');
            const newRow = document.createElement('tr');
            newRow.className = 'part-row';
            newRow.innerHTML = `
                        <td class="px-3 py-2">
                            <select class="part-type w-full px-2 py-1 text-sm border border-gray-300 rounded-md">
                                <option value="">选择类型</option>
                                <option value="CPU">CPU</option>
                                <option value="主板">主板</option>
                                <option value="内存">内存</option>
                                <option value="硬盘">硬盘</option>
                                <option value="显卡">显卡</option>
                                <option value="电源">电源</option>
                                <option value="机箱">机箱</option>
                                <option value="散热器">散热器</option>
                                <option value="显示器">显示器</option>
                                <option value="其他">其他</option>
                            </select>
                        </td>
                        <td class="px-3 py-2">
                            <input type="text" class="part-model w-full px-2 py-1 text-sm border border-gray-300 rounded-md" value="">
                        </td>
                        <td class="px-3 py-2">
                            <input type="number" class="part-quantity w-full px-2 py-1 text-sm border border-gray-300 rounded-md" value="1" min="1">
                        </td>
                        <td class="px-3 py-2">
                            <input type="number" class="part-price w-full px-2 py-1 text-sm border border-gray-300 rounded-md" value="0" step="0.01" min="0">
                        </td>
                        <td class="px-3 py-2 text-sm part-amount text-gray-900 font-medium">¥0.00</td>
                        <td class="px-3 py-2">
                            <button type="button" class="remove-part-btn text-red-600 hover:text-red-900">
                                <i class="fas fa-times"></i>
                            </button>
                        </td>
                    `;
            tbody.appendChild(newRow);

            const priceInput = newRow.querySelector('.part-price');
            const quantityInput = newRow.querySelector('.part-quantity');

            // 更新总价的内联函数
            const inlineUpdateTotal = () => {
                let total = 0;
                document.querySelectorAll('#editPartsTableBody .part-row').forEach(row => {
                    const price = parseFloat(row.querySelector('.part-price')?.value) || 0;
                    const quantity = parseInt(row.querySelector('.part-quantity')?.value) || 1;
                    const lineAmount = price * quantity;

                    // 更新行项目金额
                    const amountElement = row.querySelector('.part-amount');
                    if (amountElement) {
                        amountElement.textContent = `¥${lineAmount.toFixed(2)}`;
                    }

                    total += lineAmount;
                });
                const totalElement = document.getElementById('editTotalAmount');
                if (totalElement) {
                    totalElement.textContent = `¥${total.toFixed(2)}`;
                }
            };

            priceInput.addEventListener('input', inlineUpdateTotal);
            quantityInput.addEventListener('input', inlineUpdateTotal);

            newRow.querySelector('.remove-part-btn').addEventListener('click', () => {
                newRow.remove();
                inlineUpdateTotal();
            });

            // 立即更新总价
            inlineUpdateTotal();
        });
        editAddPartBtn._hasAddListener = true;
    }

    // 初始化
    initPartsHandlers();

    // 加载数据和统计信息
    loadData();
    loadStatistics();

    // 默认加载门市数据
    getStoreIdByName('门市').then(storeId => {
        if (storeId) {
            // 如果找到门市ID，加载其数据
            console.log('默认加载门市数据, ID:', storeId);
            loadDailyDeliveries(storeId);

            // 同时选中店铺选择器中的门市
            setTimeout(() => {
                const storeFilterSelect = document.getElementById('storeFilterSelect');
                if (storeFilterSelect) {
                    // 找到店铺选项并选中
                    for (const option of storeFilterSelect.options) {
                        if (option.value == storeId) {
                            option.selected = true;
                            break;
                        }
                    }
                }
            }, 500); // 给一点时间让店铺选择器加载完成
        } else {
            // 如果找不到，则加载所有数据
            console.log('找不到门市，加载所有数据');
            loadDailyDeliveries();
        }
    });
    initStoreSearch();
});

// 智能图片压缩函数
function smartCompressImage(file) {
    return new Promise((resolve, reject) => {
        // 如果文件小于1MB，直接返回原文件
        if (file.size < 1024 * 1024) {
            resolve(file);
            return;
        }

        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = (event) => {
            const img = new Image();
            img.src = event.target.result;
            img.onload = () => {
                const canvas = document.createElement('canvas');
                let width = img.width;
                let height = img.height;

                // 根据原始尺寸和文件大小决定是否调整尺寸
                let shouldResize = false;
                let targetWidth = width;
                let targetHeight = height;

                // 如果图片尺寸超过2000px或文件大小超过2MB，则进行尺寸调整
                if ((width > 2000 || height > 2000) || file.size > 2 * 1024 * 1024) {
                    shouldResize = true;
                    const maxDimension = 2000;
                    if (width > height) {
                        targetWidth = maxDimension;
                        targetHeight = Math.round((height * maxDimension) / width);
                    } else {
                        targetHeight = maxDimension;
                        targetWidth = Math.round((width * maxDimension) / height);
                    }
                }

                // 根据文件大小动态调整压缩质量
                let quality = 0.9; // 默认高质量
                if (file.size > 5 * 1024 * 1024) {
                    quality = 0.8;
                } else if (file.size > 3 * 1024 * 1024) {
                    quality = 0.85;
                }

                // 如果不需要调整尺寸，直接进行质量压缩
                if (!shouldResize) {
                    canvas.width = width;
                    canvas.height = height;
                    const ctx = canvas.getContext('2d');
                    ctx.drawImage(img, 0, 0, width, height);
                } else {
                    // 需要调整尺寸
                    canvas.width = targetWidth;
                    canvas.height = targetHeight;
                    const ctx = canvas.getContext('2d');
                    // 使用高质量缩放
                    ctx.imageSmoothingEnabled = true;
                    ctx.imageSmoothingQuality = 'high';
                    ctx.drawImage(img, 0, 0, targetWidth, targetHeight);
                }

                // 转换为Blob对象
                canvas.toBlob((blob) => {
                    if (blob) {
                        // 如果压缩后的文件比原始文件还大，则返回原始文件
                        if (blob.size >= file.size) {
                            resolve(file);
                        } else {
                            resolve(blob);
                        }
                    } else {
                        reject(new Error('图片压缩失败'));
                    }
                }, 'image/jpeg', quality);
            };
            img.onerror = () => reject(new Error('图片加载失败'));
        };
        reader.onerror = () => reject(new Error('文件读取失败'));
    });
}

// 表单提交 - 新增记录
deliveryForm.addEventListener('submit', async (e) => {
    e.preventDefault();

    const token = localStorage.getItem('token');
    if (!token) {
        window.location.href = '/login.html';
        return;
    }

    const store_id = storeNameSelect.value;
    const address_id = deliveryAddressSelect.value;
    const deliveryImage = deliveryImageInput.files[0];
    const notes = document.getElementById('notes').value.trim();
    const parts = getPartsData();

    if (!store_id || !address_id) {
        showToast('请填写完整的店铺名称和送货地点', 'error');
        return;
    }

    console.log("准备提交的新增记录数据:", {
        store_id,
        address_id,
        notes,
        parts
    });

    const formData = new FormData();
    formData.append('store_id', store_id);
    formData.append('address_id', address_id);
    formData.append('notes', notes);
    formData.append('parts', JSON.stringify(parts));
    if (deliveryImage) {
        try {
            // 智能压缩图片
            const processedImage = await smartCompressImage(deliveryImage);
            formData.append('image', processedImage, deliveryImage.name);
        } catch (error) {
            console.error('图片处理失败:', error);
            showToast('图片处理失败，请重试', 'error');
            return;
        }
    }

    try {
        const response = await fetch('/api/records', {
            method: 'POST',
            body: formData,
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            // 检查是否授权失败
            if (response.status === 401 || response.status === 403) {
                localStorage.removeItem('token');
                localStorage.removeItem('username');
                window.location.href = '/login.html';
                return;
            }

            const error = await response.json();
            throw new Error(error.error || '保存记录失败');
        }

        // 重置表单
        deliveryForm.reset();
        imagePreviewContainer.classList.add('hidden');

        // 重新加载记录
        currentPage = 1;
        await loadRecords();

        showToast('记录已成功保存', 'success');
    } catch (error) {
        console.error('保存记录失败:', error);
        showToast(error.message || '保存记录失败', 'error');
    }
});

// 图片上传预览
deliveryImageInput.addEventListener('change', (e) => {
    const file = e.target.files[0];
    if (file) {
        // 检查文件大小 (10MB限制)
        if (file.size > 10 * 1024 * 1024) {
            showToast('图片大小不能超过10MB', 'error');
            deliveryImageInput.value = '';
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            imagePreview.src = e.target.result;
            imagePreviewContainer.classList.remove('hidden');
        };
        reader.readAsDataURL(file);
    }
});

// 移除图片
removeImageBtn.addEventListener('click', () => {
    deliveryImageInput.value = '';
    imagePreviewContainer.classList.add('hidden');
});

// 渲染记录表格
function renderRecords(records) {
    recordsTableBody.innerHTML = '';

    records.forEach(record => {
        const row = document.createElement('tr');
        row.className = 'hover:bg-gray-50 dark:hover:bg-gray-700';

        row.innerHTML = `
                    <td class="px-3 sm:px-6 py-3">
                        <div class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate max-w-[150px]" title="${record.store_name}">${record.store_name}</div>
                        <div class="text-xs text-gray-500 dark:text-gray-300 md:hidden truncate max-w-[150px]" title="${record.address}">${record.address}</div>
                        <div class="text-xs text-gray-500 dark:text-gray-300 md:hidden">${formatDate(record.created_at)}</div>
                    </td>
                    <td class="px-3 sm:px-6 py-3 mobile-hidden">
                        <div class="text-sm text-gray-500 dark:text-gray-300 truncate max-w-[200px]" title="${record.address}">${record.address}</div>
                    </td>
                    <td class="px-3 sm:px-6 py-3 whitespace-nowrap">
                        ${record.image_url ?
                `<img src="${record.image_url}" alt="送货图片" class="h-10 sm:h-12 rounded-md shadow cursor-pointer" onclick="showImageModal('${record.image_url}')">` :
                '<span class="text-sm text-gray-500 dark:text-gray-300">无</span>'}
                    </td>
                    <td class="px-3 sm:px-6 py-3 whitespace-nowrap mobile-hidden">
                        <div class="text-sm text-gray-500 dark:text-gray-300">${formatDate(record.created_at)}</div>
                    </td>
                    <td class="px-3 sm:px-6 py-3 whitespace-nowrap">
                        <div class="flex items-center space-x-2">
                            <button onclick="showRecordDetails(${record.id})" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                                <i class="fas fa-info-circle"></i>
                        </button>
                            <button onclick="editRecord(${record.id})" class="text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button onclick="confirmDeleteRecord(${record.id})" class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                `;

        recordsTableBody.appendChild(row);
    });
}

// 格式化日期
function formatDate(dateString) {
    const date = new Date(dateString);
    return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(date.getDate())} ${padZero(date.getHours())}:${padZero(date.getMinutes())}`;
}

function padZero(num) {
    return num < 10 ? `0${num}` : num;
}

// 分页控制
function renderPagination(total) {
    const totalPages = Math.ceil(total / recordsPerPage);

    recordCount.textContent = `共${total}条记录`;
    pageInfo.textContent = `${currentPage}/${totalPages || 1}`;

    prevPageBtn.disabled = currentPage === 1;
    nextPageBtn.disabled = currentPage === totalPages || totalPages === 0;
}

// 上一页
prevPageBtn.addEventListener('click', async () => {
    if (currentPage > 1) {
        currentPage--;
        const searchTerm = searchInput.value.trim();
        await loadRecords(currentPage, searchTerm); // Pass current page and search term
    }
});

// 下一页
nextPageBtn.addEventListener('click', async () => {
    const token = localStorage.getItem('token');
    if (!token) {
        console.error('没有找到认证令牌');
        logout();
        return;
    }

    const searchTerm = searchInput.value.trim();
    const url = searchTerm
        ? `/api/records?page=${currentPage + 1}&limit=${recordsPerPage}&search=${searchTerm}`
        : `/api/records?page=${currentPage + 1}&limit=${recordsPerPage}`;

    try {
        const response = await fetch(url, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            if (response.status === 401 || response.status === 403) {
                logout();
            } else {
                console.error('检查下一页数据失败', response.statusText);
            }
            return; // Don't proceed if the next page check fails
        }

        const data = await response.json();
        const totalPages = Math.ceil(data.total / recordsPerPage);

        if (currentPage < totalPages) {
            currentPage++;
            await loadRecords(currentPage, searchTerm); // Pass current page and search term
        }
    } catch (error) {
        console.error('获取下一页数据时出错:', error);
    }
});

// 搜索功能
searchInput.addEventListener('input', async (e) => {
    const searchTerm = e.target.value.toLowerCase();

    if (searchTerm.trim() === '') {
        currentPage = 1;
        await loadRecords();
        return;
    }

    try {
        // 首先验证是否有令牌
        const token = localStorage.getItem('token');
        if (!token) {
            console.error('没有找到认证令牌');
            logout();
            return;
        }

        const response = await fetch(`/api/records?page=${currentPage}&limit=${recordsPerPage}&search=${searchTerm}`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        const data = await response.json();

        if (data.records.length > 0) {
            noRecordsMessage.classList.add('hidden');
            renderRecords(data.records);
            renderPagination(data.total);
        } else {
            noRecordsMessage.classList.remove('hidden');
            recordsTableBody.innerHTML = `
                        <tr>
                            <td colspan="5" class="px-3 sm:px-6 py-3 text-center text-gray-500 text-sm sm:text-base">没有找到匹配的记录</td>
                        </tr>
                    `;
        }
    } catch (error) {
        console.error('搜索失败:', error);
        showToast('搜索失败', 'error');
    }
});

// 添加新店铺
addStoreBtn.addEventListener('click', () => {
    newStoreContainer.classList.remove('hidden');
    newStoreNameInput.focus();
});

// 保存新店铺
saveStoreBtn.addEventListener('click', async () => {
    const newStoreName = newStoreNameInput.value.trim();
    const newStoreAddressId = document.getElementById('newStoreAddress').value; // Get selected address ID

    if (!newStoreName) {
        showToast('请输入店铺名称', 'error');
        return;
    }

    try {
        const token = localStorage.getItem('token');
        if (!token) {
            window.location.href = '/login.html';
            return;
        }

        const payload = { name: newStoreName };
        if (newStoreAddressId) {
            payload.address_id = newStoreAddressId;
        }

        const response = await fetch('/api/stores', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(payload)
        });

        if (!response.ok) {
            throw new Error('添加店铺失败');
        }

        const data = await response.json(); // data should contain { id, name, default_address_id }

        // 重新加载数据 (包括更新后的店铺和地址列表)
        await loadData();

        // 重置并隐藏输入框
        newStoreNameInput.value = '';
        document.getElementById('newStoreAddress').value = '';
        newStoreContainer.classList.add('hidden');

        // 选中新添加的店铺，并尝试设置地址
        storeSearch.value = data.name;
        storeNameSelect.value = data.id;
        if (data.default_address_id) {
            deliveryAddressSelect.value = data.default_address_id;
        }

        showToast('店铺已成功添加', 'success');
    } catch (error) {
        console.error('添加店铺失败:', error);
        showToast(error.message || '添加店铺失败', 'error');
    }
});

// 添加新地址
addAddressBtn.addEventListener('click', () => {
    newAddressContainer.classList.remove('hidden');
    newAddressInput.focus();
});

// 保存新地址
saveAddressBtn.addEventListener('click', async () => {
    const newAddress = newAddressInput.value.trim();

    if (!newAddress) {
        showToast('请输入送货地址', 'error');
        return;
    }

    try {
        const token = localStorage.getItem('token');
        if (!token) {
            window.location.href = '/login.html';
            return;
        }

        const response = await fetch('/api/addresses', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({ address: newAddress })
        });

        if (!response.ok) {
            throw new Error('添加地址失败');
        }

        const data = await response.json();

        // 重新加载地址列表
        const addressesResponse = await fetch('/api/addresses', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        const addresses = await addressesResponse.json();
        updateAddressDropdowns(addresses);

        // 重置并隐藏输入框
        newAddressInput.value = '';
        newAddressContainer.classList.add('hidden');

        // 选中新添加的地址
        deliveryAddressSelect.value = data.id;

        showToast('地址已成功添加', 'success');
    } catch (error) {
        console.error('添加地址失败:', error);
        showToast(error.message || '添加地址失败', 'error');
    }
});

// 更新店铺下拉菜单
function updateStoreDropdowns(stores) {
    // 主表单的下拉菜单
    storeNameSelect.innerHTML = '<option value="">选择店铺</option>';
    stores.forEach(store => {
        storeNameSelect.innerHTML += `<option value="${store.id}">${store.name}</option>`;
    });

    // 编辑模态框的下拉菜单
    editStoreNameSelect.innerHTML = '<option value="">选择店铺</option>';
    stores.forEach(store => {
        editStoreNameSelect.innerHTML += `<option value="${store.id}">${store.name}</option>`;
    });

    // 更新图表的店铺筛选器
    populateStoreFilter(stores);
}

// 更新地址下拉菜单 (Also populate the new store address dropdown)
function updateAddressDropdowns(addressesData) {
    if (!addressesData || !Array.isArray(addressesData)) {
        console.error('更新地址下拉菜单时没有收到有效数据');
        return;
    }

    console.log(`更新地址下拉菜单，共${addressesData.length}个地址`);
    const addressOptions = addressesData.map(address =>
        `<option value="${address.id}">${address.address}</option>`
    ).join('');

    // 主表单的下拉菜单
    if (deliveryAddressSelect) {
        deliveryAddressSelect.innerHTML = '<option value="">选择地点</option>' + addressOptions;
    }

    // 编辑模态框的下拉菜单
    const editAddressSelect = document.getElementById('editDeliveryAddress');
    if (editAddressSelect) {
        editAddressSelect.innerHTML = '<option value="">选择地点</option>' + addressOptions;
    }

    // 新增店铺的地址下拉菜单
    const newStoreAddressSelect = document.getElementById('newStoreAddress');
    if (newStoreAddressSelect) {
        newStoreAddressSelect.innerHTML = '<option value="">选择地点</option>' + addressOptions;
    }
}

// 显示记录详情
async function showRecordDetails(id) {
    try {
        const token = localStorage.getItem('token');
        if (!token) {
            window.location.href = '/login.html';
            return;
        }

        const response = await fetch(`/api/records/${id}/details`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error || '获取记录详情失败');
        }

        const record = await response.json();
        console.log("获取到的记录详情数据:", record);

        // 填充基本信息
        document.getElementById('detailStoreName').textContent = record.store_name || '-';
        document.getElementById('detailAddress').textContent = record.address || '-';
        document.getElementById('detailTime').textContent = record.created_at ? formatDate(record.created_at) : '-';
        document.getElementById('detailNotes').textContent = record.notes || '-';

        // 处理图片
        const detailImage = document.getElementById('detailImage');
        const detailImageContainer = document.getElementById('detailImageContainer');
        if (record.image_url) {
            detailImage.src = record.image_url;
            detailImageContainer.classList.remove('hidden');
        } else {
            detailImageContainer.classList.add('hidden');
        }

        // 填充配件明细
        const partsTableBody = document.getElementById('detailPartsTableBody');
        partsTableBody.innerHTML = '';

        if (record.parts && record.parts.length > 0) {
            let totalAmount = 0;
            record.parts.forEach(part => {
                const quantity = parseInt(part.quantity) || 1;
                const price = parseFloat(part.price) || 0;
                const amount = price * quantity;
                totalAmount += amount;

                const row = document.createElement('tr');
                row.innerHTML = `
                            <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">${part.type || '-'}</td>
                            <td class="px-3 py-2 text-sm text-gray-900">
                                <div class="truncate max-w-[200px]" title="${part.model || '-'}">${part.model || '-'}</div>
                            </td>
                            <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">${quantity}</td>
                            <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">¥${price.toFixed(2)}</td>
                            <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">¥${amount.toFixed(2)}</td>
                        `;
                partsTableBody.appendChild(row);
            });

            document.getElementById('detailTotalAmount').textContent = `¥${totalAmount.toFixed(2)}`;
        } else {
            const row = document.createElement('tr');
            row.innerHTML = `
                        <td colspan="5" class="px-3 py-2 text-sm text-gray-500 text-center">暂无配件明细</td>
                    `;
            partsTableBody.appendChild(row);
            document.getElementById('detailTotalAmount').textContent = '¥0.00';
        }

        // 显示模态框
        document.getElementById('detailsModal').classList.remove('hidden');
    } catch (error) {
        console.error('获取记录详情失败:', error);
        showToast(error.message || '获取记录详情失败', 'error');
    }
}

// 关闭详情模态框
document.getElementById('closeDetailsBtn').addEventListener('click', () => {
    document.getElementById('detailsModal').classList.add('hidden');
});

// 编辑记录
async function editRecord(id) {
    try {
        console.log('编辑记录 ID:', id);

        // 获取详细记录
        const token = localStorage.getItem('token');
        if (!token) {
            window.location.href = '/login.html';
            return;
        }

        const response = await fetch(`/api/records/${id}/details`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            throw new Error(`获取记录详情失败: ${response.status} ${response.statusText}`);
        }

        const record = await response.json();
        console.log('编辑页获取的记录数据: ', record);

        // 填充表单
        document.getElementById('editRecordId').value = record.id;

        // 填充店铺下拉菜单
        const editStoreNameSelect = document.getElementById('editStoreName');
        editStoreNameSelect.value = record.store_id;

        // 填充地址下拉菜单
        const editDeliveryAddressSelect = document.getElementById('editDeliveryAddress');
        editDeliveryAddressSelect.value = record.address_id;

        // 备注
        document.getElementById('editNotes').value = record.notes || '';

        // 图片预览
        const editImagePreview = document.getElementById('editImagePreview');
        const editImagePreviewContainer = document.getElementById('editImagePreviewContainer');
        const editRemoveImageBtn = document.getElementById('editRemoveImageBtn');

        // 检查元素是否存在，防止null错误
        if (record.image_url && editImagePreview) {
            editImagePreview.src = record.image_url;

            // 安全地访问classList
            if (editImagePreviewContainer) {
                editImagePreviewContainer.classList.remove('hidden');
            }

            if (editRemoveImageBtn) {
                editRemoveImageBtn.classList.remove('hidden');
            }
        } else {
            // 安全地访问classList
            if (editImagePreviewContainer) {
                editImagePreviewContainer.classList.add('hidden');
            }

            if (editRemoveImageBtn) {
                editRemoveImageBtn.classList.add('hidden');
            }
        }

        // 配件列表
        const tbody = document.getElementById('editPartsTableBody');
        tbody.innerHTML = '';

        if (record.parts && record.parts.length > 0) {
            record.parts.forEach(part => {
                const row = document.createElement('tr');
                row.className = 'part-row';
                row.innerHTML = `
                            <td class="px-3 py-2">
                                <select class="part-type w-full px-2 py-1 text-sm border border-gray-300 rounded-md">
                                    <option value="" ${!part.type ? 'selected' : ''}>选择类型</option>
                                    <option value="CPU" ${part.type === 'CPU' ? 'selected' : ''}>CPU</option>
                                    <option value="主板" ${part.type === '主板' ? 'selected' : ''}>主板</option>
                                    <option value="内存" ${part.type === '内存' ? 'selected' : ''}>内存</option>
                                    <option value="硬盘" ${part.type === '硬盘' ? 'selected' : ''}>硬盘</option>
                                    <option value="显卡" ${part.type === '显卡' ? 'selected' : ''}>显卡</option>
                                    <option value="电源" ${part.type === '电源' ? 'selected' : ''}>电源</option>
                                    <option value="机箱" ${part.type === '机箱' ? 'selected' : ''}>机箱</option>
                                    <option value="散热器" ${part.type === '散热器' ? 'selected' : ''}>散热器</option>
                                    <option value="显示器" ${part.type === '显示器' ? 'selected' : ''}>显示器</option>
                                    <option value="其他" ${part.type === '其他' ? 'selected' : ''}>其他</option>
                                </select>
                            </td>
                            <td class="px-3 py-2">
                                <input type="text" class="part-model w-full px-2 py-1 text-sm border border-gray-300 rounded-md" value="${part.model || ''}">
                            </td>
                            <td class="px-3 py-2">
                                <input type="number" class="part-quantity w-full px-2 py-1 text-sm border border-gray-300 rounded-md" value="${part.quantity || 1}" min="1">
                            </td>
                            <td class="px-3 py-2">
                                <input type="number" class="part-price w-full px-2 py-1 text-sm border border-gray-300 rounded-md" value="${part.price || 0}" step="0.01" min="0">
                            </td>
                            <td class="px-3 py-2 text-sm part-amount text-gray-900 font-medium">¥${((part.price || 0) * (part.quantity || 1)).toFixed(2)}</td>
                            <td class="px-3 py-2">
                                <button type="button" class="remove-part-btn text-red-600 hover:text-red-900">
                                    <i class="fas fa-times"></i>
                                </button>
                            </td>
                        `;
                tbody.appendChild(row);

                const priceInput = row.querySelector('.part-price');
                const quantityInput = row.querySelector('.part-quantity');
                const amountElement = row.querySelector('.part-amount');

                // 内联更新函数 - 统一所有行项目的更新逻辑
                const inlineUpdateTotal = () => {
                    let total = 0;
                    document.querySelectorAll('#editPartsTableBody .part-row').forEach(row => {
                        const price = parseFloat(row.querySelector('.part-price')?.value) || 0;
                        const quantity = parseInt(row.querySelector('.part-quantity')?.value) || 1;
                        const lineAmount = price * quantity;

                        // 更新行项目金额
                        const rowAmountElement = row.querySelector('.part-amount');
                        if (rowAmountElement) {
                            rowAmountElement.textContent = `¥${lineAmount.toFixed(2)}`;
                        }

                        total += lineAmount;
                    });

                    const totalElement = document.getElementById('editTotalAmount');
                    if (totalElement) {
                        totalElement.textContent = `¥${total.toFixed(2)}`;
                    }
                };

                // 绑定事件监听器
                priceInput.addEventListener('input', inlineUpdateTotal);
                quantityInput.addEventListener('input', inlineUpdateTotal);

                row.querySelector('.remove-part-btn').addEventListener('click', () => {
                    row.remove();
                    inlineUpdateTotal();
                });
            });

            // 计算总金额
            // 使用函数引用而不是字符串名称
            if (typeof updateTotalAmount === 'function') {
                updateTotalAmount();
            } else {
                console.warn('updateTotalAmount function not available for initial calculation');
                // 计算总额的内联版本
                let total = 0;
                document.querySelectorAll('#editPartsTableBody .part-row').forEach(row => {
                    const price = parseFloat(row.querySelector('.part-price')?.value) || 0;
                    const quantity = parseInt(row.querySelector('.part-quantity')?.value) || 1;
                    total += price * quantity;
                });
                const totalElement = document.getElementById('editTotalAmount');
                if (totalElement) {
                    totalElement.textContent = `¥${total.toFixed(2)}`;
                }
            }
        }

        // 显示编辑模态框
        const editModal = document.getElementById('editModal');
        if (editModal) {
            editModal.classList.remove('hidden');
        } else {
            console.error('编辑模态框元素不存在');
            showToast('无法显示编辑界面，请刷新页面后重试', 'error');
        }
    } catch (error) {
        console.error('获取记录详情失败:', error);
        showToast(`获取记录详情失败: ${error.message}`, 'error');
    }
}

// 添加配件按钮事件 (Edit Modal)
document.getElementById('editAddPartBtn').addEventListener('click', () => {
    const tbody = document.getElementById('editPartsTableBody');
    const row = document.createElement('tr');
    row.className = 'part-row';
    row.innerHTML = `
                <td class="px-3 py-2">
                    <input type="text" class="part-type w-full px-2 py-1 text-sm border border-gray-300 rounded-md" value="">
                </td>
                <td class="px-3 py-2">
                    <input type="text" class="part-model w-full px-2 py-1 text-sm border border-gray-300 rounded-md" value="">
                </td>
                <td class="px-3 py-2">
                    <input type="number" class="part-quantity w-full px-2 py-1 text-sm border border-gray-300 rounded-md" value="1" min="1">
                </td>
                <td class="px-3 py-2">
                    <input type="number" class="part-price w-full px-2 py-1 text-sm border border-gray-300 rounded-md" value="0" step="0.01" min="0">
                </td>
                <td class="px-3 py-2 text-sm part-amount text-gray-900 font-medium">¥0.00</td>
                <td class="px-3 py-2">
                    <button type="button" class="remove-part-btn text-red-600 hover:text-red-900">
                        <i class="fas fa-times"></i>
                    </button>
                </td>
            `;
    tbody.appendChild(row);

    const priceInput = row.querySelector('.part-price');
    const quantityInput = row.querySelector('.part-quantity');

    // 使用全局定义的updateTotalAmount函数
    if (typeof window.updateTotalAmount === 'function') {
        priceInput.addEventListener('input', window.updateTotalAmount);
        quantityInput.addEventListener('input', window.updateTotalAmount);

        row.querySelector('.remove-part-btn').addEventListener('click', () => {
            row.remove();
            window.updateTotalAmount();
        });
    } else {
        console.warn('updateTotalAmount function not found in add part, using inline calculation');
        // 备用的内联更新函数，以防全局函数不可用
        const inlineUpdateTotal = () => {
            let total = 0;
            document.querySelectorAll('#editPartsTableBody .part-row').forEach(row => {
                const price = parseFloat(row.querySelector('.part-price')?.value) || 0;
                const quantity = parseInt(row.querySelector('.part-quantity')?.value) || 1;
                total += price * quantity;
            });
            const totalElement = document.getElementById('editTotalAmount');
            if (totalElement) {
                totalElement.textContent = `¥${total.toFixed(2)}`;
            }
        };

        priceInput.addEventListener('input', inlineUpdateTotal);
        quantityInput.addEventListener('input', inlineUpdateTotal);

        row.querySelector('.remove-part-btn').addEventListener('click', () => {
            row.remove();
            inlineUpdateTotal();
        });
    }
});

// 修改保存编辑按钮事件处理
saveEditBtn.addEventListener('click', async () => {
    const store_id = editStoreNameSelect.value;
    const address_id = editDeliveryAddressSelect.value;
    const deliveryImage = editDeliveryImageInput.files[0];
    const notes = document.getElementById('editNotes').value.trim();

    if (!store_id || !address_id) {
        showToast('请填写完整的店铺名称和送货地点', 'error');
        return;
    }

    // 获取token
    const token = localStorage.getItem('token');
    if (!token) {
        window.location.href = '/login.html';
        return;
    }

    // 获取配件数据
    const parts = [];
    document.querySelectorAll('#editPartsTableBody > .part-row').forEach(row => {
        const type = row.querySelector('.part-type').value.trim();
        const model = row.querySelector('.part-model').value.trim();
        const price = parseFloat(row.querySelector('.part-price').value) || 0;
        const quantity = parseInt(row.querySelector('.part-quantity').value) || 1;

        if (type || model) {  // 只要有类型或型号就添加
            parts.push({ type, model, price, quantity });
        }
    });

    console.log("准备提交的编辑数据:", {
        store_id,
        address_id,
        notes,
        parts
    });

    const formData = new FormData();
    formData.append('store_id', store_id);
    formData.append('address_id', address_id);
    formData.append('notes', notes);
    formData.append('parts', JSON.stringify(parts));

    if (deliveryImage) {
        try {
            const processedImage = await smartCompressImage(deliveryImage);
            formData.append('image', processedImage, deliveryImage.name);
        } catch (error) {
            console.error('图片处理失败:', error);
            showToast('图片处理失败，请重试', 'error');
            return;
        }
    }

    try {
        const response = await fetch(`/api/records/${editRecordIdInput.value}`, {
            method: 'PUT',
            body: formData,
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            if (response.status === 401 || response.status === 403) {
                // Token 无效或过期
                localStorage.removeItem('token');
                localStorage.removeItem('username');
                window.location.href = '/login.html';
                return;
            }

            const error = await response.json();
            throw new Error(error.error || '更新记录失败');
        }

        // 重新加载记录
        await loadRecords();

        editModal.classList.add('hidden');
        editDeliveryImageInput.value = '';
        showToast('记录已成功更新', 'success');
    } catch (error) {
        console.error('更新记录失败:', error);
        showToast(error.message || '更新记录失败', 'error');
    }
});

// 取消编辑
cancelEditBtn.addEventListener('click', () => {
    editModal.classList.add('hidden');
    editDeliveryImageInput.value = ''; // 清除文件输入
});

// 确认删除记录
window.confirmDeleteRecord = function (id) {
    recordToDelete = id;
    deleteModal.classList.remove('hidden');
};

// 执行删除
confirmDeleteBtn.addEventListener('click', async () => {
    if (recordToDelete) {
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                window.location.href = '/login.html';
                return;
            }

            const response = await fetch(`/api/records/${recordToDelete}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (!response.ok) {
                if (response.status === 401 || response.status === 403) {
                    // Token 无效或过期
                    localStorage.removeItem('token');
                    localStorage.removeItem('username');
                    window.location.href = '/login.html';
                    return;
                }
                throw new Error('删除记录失败');
            }

            // 重新加载记录
            await loadRecords();

            deleteModal.classList.add('hidden');
            showToast('记录已删除', 'success');
        } catch (error) {
            console.error('删除记录失败:', error);
            showToast(error.message || '删除记录失败', 'error');
        }
    }
});

// 取消删除
cancelDeleteBtn.addEventListener('click', () => {
    deleteModal.classList.add('hidden');
});

// 显示图片模态框
window.showImageModal = function (imageUrl) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-2';
    modal.innerHTML = `
                <div class="relative max-w-full max-h-screen">
                    <img src="${imageUrl}" alt="送货图片" class="max-w-full max-h-screen">
                    <button onclick="this.parentElement.parentElement.remove()" class="absolute top-2 right-2 bg-black bg-opacity-50 text-white rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-75">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
    document.body.appendChild(modal);
};

// 显示Toast通知
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    let bgColor = 'bg-blue-500';

    if (type === 'success') bgColor = 'bg-green-500';
    if (type === 'error') bgColor = 'bg-red-500';

    toast.className = `fixed bottom-4 right-4 ${bgColor} text-white px-3 sm:px-4 py-2 rounded-md shadow-lg flex items-center fade-in text-sm sm:text-base`;
    toast.innerHTML = `
                <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'} mr-2"></i>
                <span>${message}</span>
            `;

    document.body.appendChild(toast);

    setTimeout(() => {
        toast.classList.add('opacity-0');
        setTimeout(() => toast.remove(), 300);
    }, 3000);
}

// 导出数据
document.getElementById('exportBtn').addEventListener('click', async () => {
    try {
        const token = localStorage.getItem('token');
        if (!token) {
            window.location.href = '/login.html';
            return;
        }

        const response = await fetch('/api/export', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            if (response.status === 401 || response.status === 403) {
                localStorage.removeItem('token');
                localStorage.removeItem('username');
                window.location.href = '/login.html';
                return;
            }
            throw new Error('导出失败');
        }

        // 创建下载链接
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'delivery_records.zip';
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        a.remove();

        showToast('数据导出成功', 'success');
    } catch (error) {
        console.error('导出失败:', error);
        showToast(error.message || '导出失败', 'error');
    }
});

// 导入数据
document.getElementById('importFile').addEventListener('change', async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    if (!file.name.endsWith('.zip')) {
        showToast('请选择ZIP格式的文件', 'error');
        return;
    }

    const token = localStorage.getItem('token');
    if (!token) {
        window.location.href = '/login.html';
        return;
    }

    const formData = new FormData();
    formData.append('file', file);

    try {
        const response = await fetch('/api/import', {
            method: 'POST',
            body: formData,
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            if (response.status === 401 || response.status === 403) {
                localStorage.removeItem('token');
                localStorage.removeItem('username');
                window.location.href = '/login.html';
                return;
            }
            throw new Error('导入失败');
        }

        const data = await response.json();
        showToast(data.message || '数据导入成功', 'success');

        // 加载每日金额数据 - 默认加载7天
        await loadDailyAmounts(7);

        // 重新加载数据
        await loadData();
    } catch (error) {
        console.error('导入失败:', error);
        showToast(error.message || '导入失败', 'error');
    } finally {
        // 清除文件输入
        e.target.value = '';
    }
});

// 计算总价格函数
function updateTotalPrice() {
    const partsContainer = document.getElementById('partsContainer');
    let total = 0;

    // 遍历所有配件项
    partsContainer.querySelectorAll('.part-item').forEach(item => {
        const price = parseFloat(item.querySelector('.part-price').value) || 0;
        const quantity = parseInt(item.querySelector('.part-quantity').value) || 1;
        total += price * quantity;
    });

    // 更新总价显示
    document.getElementById('totalPrice').textContent = total.toFixed(2);
}

// 配件相关函数
function initPartsHandlers() {
    const addPartBtn = document.getElementById('addPartBtn');
    const partsContainer = document.getElementById('partsContainer');
    const partsSearchInput = document.getElementById('partsSearchInput');
    const clearPartsSearchBtn = document.getElementById('clearPartsSearchBtn');

    // 添加配件 - 防止多次绑定事件
    if (!addPartBtn._hasAddListener) {
        addPartBtn.addEventListener('click', () => {
            const template = partsContainer.children[0].cloneNode(true);
            template.querySelector('.part-type').value = '';
            template.querySelector('.part-model').value = '';
            template.querySelector('.part-price').value = '';
            template.querySelector('.part-quantity').value = '1';

            // 绑定删除按钮事件
            template.querySelector('.remove-part').addEventListener('click', function () {
                if (partsContainer.children.length > 1) {
                    this.closest('.part-item').remove();
                    updateTotalPrice();
                }
            });

            // 绑定价格变化事件
            template.querySelector('.part-price').addEventListener('input', updateTotalPrice);
            template.querySelector('.part-quantity').addEventListener('input', updateTotalPrice);

            partsContainer.appendChild(template);
        });
        addPartBtn._hasAddListener = true;
    }

    // 为第一个删除按钮绑定事件
    partsContainer.querySelector('.remove-part').addEventListener('click', function () {
        if (partsContainer.children.length > 1) {
            this.closest('.part-item').remove();
            updateTotalPrice();
        }
    });

    // 为所有价格输入框绑定事件
    document.querySelectorAll('.part-price').forEach(input => {
        input.addEventListener('input', updateTotalPrice);
    });

    // 为所有数量输入框绑定事件
    document.querySelectorAll('.part-quantity').forEach(input => {
        input.addEventListener('input', updateTotalPrice);
    });

    // 配件搜索功能
    if (partsSearchInput) {
        partsSearchInput.addEventListener('input', function () {
            const searchTerm = this.value.toLowerCase().trim();
            const partItems = partsContainer.querySelectorAll('.part-item');

            partItems.forEach(item => {
                const type = item.querySelector('.part-type').value.toLowerCase();
                const model = item.querySelector('.part-model').value.toLowerCase();

                if (searchTerm === '' || type.includes(searchTerm) || model.includes(searchTerm)) {
                    item.style.display = '';
                    item.classList.remove('hidden');
                } else {
                    item.style.display = 'none';
                    item.classList.add('hidden');
                }
            });
        });
    }

    // 清除搜索
    if (clearPartsSearchBtn) {
        clearPartsSearchBtn.addEventListener('click', function () {
            if (partsSearchInput) {
                partsSearchInput.value = '';
                // 显示所有配件项
                partsContainer.querySelectorAll('.part-item').forEach(item => {
                    item.style.display = '';
                    item.classList.remove('hidden');
                });
            }
        });
    }

    // 初始化总价
    updateTotalPrice();
}

// 获取配件数据
function getPartsData() {
    const parts = [];
    document.querySelectorAll('.part-item').forEach(item => {
        const type = item.querySelector('.part-type').value.trim();
        const model = item.querySelector('.part-model').value.trim();
        const price = parseFloat(item.querySelector('.part-price').value) || 0;
        const quantity = parseInt(item.querySelector('.part-quantity').value) || 1;

        if (type || model) { // 只要有类型或型号就添加
            parts.push({ type, model, price, quantity });
        }
    });
    console.log("获取的配件数据:", parts);
    return parts;
}

// 渲染配件信息
function renderParts(parts) {
    if (!parts || !parts.length) return '-';

    const totalPrice = parts.reduce((sum, part) => sum + (parseFloat(part.price) || 0) * (parseInt(part.quantity) || 1), 0);
    const partsHtml = parts.map(part =>
        `<div class="text-xs">
                    ${part.type || '未知类型'}: ${part.model || '未知型号'} × ${part.quantity || 1} (¥${(part.price || 0).toFixed(2)})
                </div>`
    ).join('');

    return `
                <div class="space-y-1">
                    ${partsHtml}
                    <div class="text-xs font-medium text-indigo-600">总价: ¥${totalPrice.toFixed(2)}</div>
                </div>
            `;
}

// 店铺搜索功能 - 使用js-pinyin库增强拼音搜索支持
function initStoreSearch() {
    if (!storeSearch || !storeSearchResults || !storeNameSelect) {
        console.error('店铺搜索组件未找到');
        return;
    }

    console.log('初始化店铺搜索功能');

    // 检查PinyinHelper是否加载
    if (typeof PinyinHelper === 'undefined') {
        console.error('PinyinHelper未加载，拼音搜索功能可能不可用');
    } else {
        console.log('PinyinHelper加载成功');
    }

    // 更新搜索框的提示文本
    storeSearch.placeholder = '搜索店铺(支持拼音首字母)';

    // 为每个店铺预先生成拼音索引
    function prepareStoresPinyinIndex() {
        if (!stores || !Array.isArray(stores)) return;

        stores.forEach(store => {
            if (store.name) {
                try {
                    // 使用js-pinyin获取完整拼音
                    store.fullPinyin = PinyinHelper.getFullPinyin(store.name);

                    // 获取拼音首字母
                    store.pinyinInitials = PinyinHelper.getPinyinInitials(store.name);

                    // 提取数字和字母作为额外的搜索条件
                    const numbersAndLetters = store.name.split('').filter(char =>
                        /[0-9a-zA-Z]/.test(char)
                    ).join('');

                    if (numbersAndLetters) {
                        store.numbersAndLetters = numbersAndLetters.toLowerCase();
                    }

                    console.log(`店铺: ${store.name}, 完整拼音: ${store.fullPinyin}, 拼音首字母: ${store.pinyinInitials}`);
                } catch (error) {
                    console.error(`处理店铺"${store.name}"的拼音时出错:`, error);
                    // 出错时使用原始字符作为备选
                    store.fullPinyin = store.name;
                    store.pinyinInitials = store.name;
                }
            }
        });
    }

    // 初始化时预处理拼音索引
    prepareStoresPinyinIndex();

    // 添加搜索事件处理
    storeSearch.addEventListener('input', function (e) {
        const searchTerm = e.target.value.toLowerCase().trim();

        if (!stores || !Array.isArray(stores) || stores.length === 0) {
            console.warn('初始化搜索时没有店铺数据');
            return;
        }

        console.log(`搜索店铺: "${searchTerm}", 有${stores.length}个店铺数据`);

        // 清空结果区
        storeSearchResults.innerHTML = '';

        // 如果搜索词为空，不显示结果
        if (!searchTerm) {
            storeSearchResults.classList.add('hidden');
            return;
        }

        // 搜索匹配的店铺，支持多种匹配方式
        const matches = stores.filter(store => {
            if (!store.name) return false;

            // 1. 直接匹配店铺名称
            const nameMatch = store.name.toLowerCase().includes(searchTerm);

            // 2. 匹配完整拼音
            const fullPinyinMatch = store.fullPinyin &&
                store.fullPinyin.toLowerCase().includes(searchTerm);

            // 3. 匹配拼音首字母
            const pinyinInitialsMatch = store.pinyinInitials &&
                store.pinyinInitials.toLowerCase().includes(searchTerm);

            // 4. 匹配数字和字母组合
            const numbersAndLettersMatch = store.numbersAndLetters &&
                store.numbersAndLetters.includes(searchTerm);

            return nameMatch || fullPinyinMatch || pinyinInitialsMatch || numbersAndLettersMatch;
        });

        // 显示搜索结果
        if (matches.length > 0) {
            matches.forEach(store => {
                const div = document.createElement('div');
                div.className = 'cursor-pointer px-3 py-2 hover:bg-gray-100';

                // 创建一个更信息化的结果显示
                const nameSpan = document.createElement('span');
                nameSpan.className = 'font-medium';
                nameSpan.textContent = store.name;

                const pinyinSpan = document.createElement('span');
                pinyinSpan.className = 'text-xs text-gray-500 ml-2';
                pinyinSpan.textContent = store.pinyinInitials ? `(${store.pinyinInitials})` : '';

                div.appendChild(nameSpan);
                div.appendChild(pinyinSpan);

                div.addEventListener('click', function () {
                    storeNameSelect.value = store.id;
                    storeSearch.value = store.name;

                    // *** Update Address Dropdown based on default_address_id ***
                    if (store.default_address_id) {
                        deliveryAddressSelect.value = store.default_address_id;
                    } else {
                        // Reset address if no default is set for this store
                        deliveryAddressSelect.value = '';
                    }

                    storeSearchResults.classList.add('hidden');
                    console.log(`已选择店铺: "${store.name}" (ID=${store.id}), 默认地址ID: ${store.default_address_id || '无'}`);
                });
                storeSearchResults.appendChild(div);
            });
            storeSearchResults.classList.remove('hidden');
        } else {
            // 无匹配结果
            const div = document.createElement('div');
            div.className = 'px-3 py-2 text-gray-500';
            div.textContent = '未找到匹配的店铺';
            storeSearchResults.appendChild(div);
            storeSearchResults.classList.remove('hidden');
        }
    });

    // 处理下拉菜单的点击事件（点击外部时隐藏）
    document.addEventListener('click', function (e) {
        if (!storeSearch.contains(e.target) && !storeSearchResults.contains(e.target)) {
            storeSearchResults.classList.add('hidden');
        }
    });

    // 修改表单提交逻辑，确保店铺ID正确
    if (deliveryForm) {
        deliveryForm.addEventListener('submit', function (e) {
            // 检查storeNameSelect是否有值
            if (!storeNameSelect.value) {
                e.preventDefault(); // 阻止表单提交
                showToast('请选择或添加店铺', 'error');
                return false;
            }
        });
    }
}

// 更新店铺下拉菜单
function updateStoreDropdowns(storesData) {
    // 检查是否有店铺数据
    if (!storesData || !Array.isArray(storesData)) {
        console.error('更新店铺下拉菜单时没有收到有效数据');
        return;
    }

    console.log(`更新店铺下拉菜单，共${storesData.length}个店铺`);

    // 主表单的下拉菜单和搜索框
    const storeNameSelect = document.getElementById('storeName');
    const editStoreNameSelect = document.getElementById('editStoreName');
    const storeSearchResults = document.getElementById('storeSearchResults');

    // 清除现有选项
    if (storeNameSelect) storeNameSelect.innerHTML = '<option value="">选择店铺</option>';
    if (editStoreNameSelect) editStoreNameSelect.innerHTML = '<option value="">选择店铺</option>';
    if (storeSearchResults) storeSearchResults.innerHTML = '';

    // 添加店铺选项
    storesData.forEach(store => {
        if (store && store.id && store.name) {
            // 添加到主表单下拉列表
            if (storeNameSelect) {
                const option = document.createElement('option');
                option.value = store.id;
                option.textContent = store.name;
                storeNameSelect.appendChild(option);
            }

            // 添加到编辑模态框下拉菜单
            if (editStoreNameSelect) {
                const option = document.createElement('option');
                option.value = store.id;
                option.textContent = store.name;
                editStoreNameSelect.appendChild(option);
            }

            // 添加到搜索结果列表
            if (storeSearchResults) {
                const div = document.createElement('div');
                div.className = 'p-2 hover:bg-gray-100 cursor-pointer';
                div.textContent = store.name;
                div.dataset.id = store.id;
                div.dataset.name = store.name;
                div.onclick = function () {
                    const storeSearch = document.getElementById('storeSearch');
                    if (storeSearch) storeSearch.value = this.dataset.name;
                    if (storeNameSelect) storeNameSelect.value = this.dataset.id;
                    if (storeSearchResults) storeSearchResults.classList.add('hidden');
                    // 检查店铺默认地址
                    loadStoreDefaultAddress(this.dataset.id);
                };
                storeSearchResults.appendChild(div);
            }
        }
    });

    // 填充图表店铺筛选器
    populateStoreFilter(storesData);
}

function getStoreIdByNameSync(storeName) {
    if (!stores || stores.length === 0) {
        console.log('店铺数据为空，无法获取ID');
        return null;
    }

    const store = stores.find(s => s.name === storeName);
    if (store) {
        console.log(`找到店铺 "${storeName}" 的ID: ${store.id}`);
        return store.id;
    } else {
        console.log(`未找到店铺 "${storeName}" 的ID`);
        return null;
    }
}

// 显示特定日期和店铺的送货记录
async function showDateRecords(date, storeId, storeName) {
    try {
        const token = localStorage.getItem('token');
        if (!token) {
            console.log('未登录状态，无法获取记录');
            return;
        }

        console.log('尝试获取日期记录 - 原始日期:', date, '店铺ID:', storeId, '店铺名称:', storeName);

        // 格式化日期为YYYY-MM-DD格式
        let formattedDate;
        if (date instanceof Date) {
            formattedDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
            console.log('日期对象格式化为:', formattedDate);
        } else if (typeof date === 'string') {
            // 如果已经是YYYY-MM-DD格式，直接使用
            if (date.match(/^\d{4}-\d{2}-\d{2}$/)) {
                formattedDate = date;
                console.log('使用原始日期格式:', formattedDate);
            } else {
                // 如果是M-D格式，添加当前年份
                const parts = date.split('-');
                const currentYear = new Date().getFullYear();
                // 确保月和日是两位数字
                const month = String(parseInt(parts[0])).padStart(2, '0');
                const day = parts.length > 1 ? String(parseInt(parts[1])).padStart(2, '0') : '01';
                formattedDate = `${currentYear}-${month}-${day}`;
                console.log('将M-D格式转换为YYYY-MM-DD:', date, '->', formattedDate);
            }
        } else {
            console.error('日期格式无效:', date);
            showToast('日期格式无效', 'error');
            return;
        }

        // 构建查询URL
        let url = `/api/records/by-date?date=${formattedDate}`;
        if (storeId) {
            url += `&storeId=${storeId}`;
        }

        console.log('发送请求到:', url);

        // 发送请求
        const response = await fetch(url, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        console.log('响应状态:', response.status, response.statusText);

        if (!response.ok) {
            const errorText = await response.text();
            console.error('响应错误:', errorText);
            throw new Error(`获取记录失败: ${response.status} ${response.statusText}`);
        }

        const records = await response.json();
        console.log(`获取到 ${records.length} 条记录`);

        // 创建模态框显示结果
        showDateRecordsModal(records, formattedDate, storeName);
    } catch (error) {
        console.error('获取日期记录失败:', error);
        showToast(`获取送货记录失败: ${error.message}`, 'error');
    }
}

// 显示日期记录模态框
function showDateRecordsModal(records, date, storeName) {
    // 如果没有记录，显示提示
    if (!records || records.length === 0) {
        showToast(`${date} ${storeName || ''} 没有送货记录`, 'info');
        return;
    }

    // 创建模态框（如果不存在）
    let dateRecordsModal = document.getElementById('dateRecordsModal');
    if (!dateRecordsModal) {
        dateRecordsModal = document.createElement('div');
        dateRecordsModal.id = 'dateRecordsModal';
        dateRecordsModal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden p-2 sm:p-4';
        dateRecordsModal.innerHTML = `
                    <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl fade-in overflow-hidden flex flex-col max-h-[90vh]">
                        <div class="px-4 sm:px-6 py-3 sm:py-4 border-b border-gray-200 flex justify-between items-center">
                            <h3 id="dateRecordsTitle" class="text-base sm:text-lg font-semibold text-gray-900"></h3>
                            <button id="closeDateRecordsBtn" class="text-gray-400 hover:text-gray-500">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="px-4 sm:px-6 py-3 sm:py-4 overflow-auto flex-grow">
                            <div id="dateRecordsContent" class="space-y-4"></div>
                        </div>
                    </div>
                `;
        document.body.appendChild(dateRecordsModal);

        // 添加关闭按钮事件
        document.getElementById('closeDateRecordsBtn').addEventListener('click', () => {
            dateRecordsModal.classList.add('hidden');
        });
    }

    // 设置标题
    const title = document.getElementById('dateRecordsTitle');
    title.textContent = `${date} ${storeName ? storeName + ' ' : ''}送货记录 (${records.length}条)`;

    // 渲染记录
    const content = document.getElementById('dateRecordsContent');
    content.innerHTML = '';

    records.forEach(record => {
        const recordDiv = document.createElement('div');
        recordDiv.className = 'bg-gray-50 p-3 rounded-lg shadow-sm';

        // 格式化时间
        const createdAt = new Date(record.created_at);
        const timeStr = `${String(createdAt.getHours()).padStart(2, '0')}:${String(createdAt.getMinutes()).padStart(2, '0')}`;

        // 构建记录HTML
        let recordHTML = `
                    <div class="flex justify-between items-start mb-2">
                        <div>
                            <span class="font-semibold">${record.store_name}</span> 送货到 <span class="font-semibold">${record.address}</span>
                            <div class="text-sm text-gray-500">${timeStr}</div>
                        </div>
                        <div class="flex space-x-2">
                            <button class="text-indigo-600 hover:text-indigo-800 view-details-btn" data-id="${record.id}">
                                <i class="fas fa-eye"></i> 详情
                            </button>
                            <button class="text-blue-600 hover:text-blue-800 edit-record-btn" data-id="${record.id}">
                                <i class="fas fa-edit"></i> 编辑
                            </button>
                        </div>
                    </div>
                `;

        // 添加备注（如果有）
        if (record.notes) {
            recordHTML += `<div class="text-sm text-gray-600 mb-2">备注: ${record.notes}</div>`;
        }

        // 添加图片（如果有）
        if (record.image_url) {
            recordHTML += `
                        <div class="mb-2">
                            <img src="${record.image_url}" alt="送货图片" class="h-20 rounded cursor-pointer hover:opacity-90 view-image" data-url="${record.image_url}">
                        </div>
                    `;
        }

        // 添加配件信息（如果有）
        if (record.parts && record.parts.length > 0) {
            recordHTML += `
                        <div class="mt-2">
                            <div class="text-sm font-medium">配件明细:</div>
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-1 text-sm">
                    `;

            record.parts.forEach(part => {
                recordHTML += `
                            <div class="flex justify-between bg-white p-1 rounded">
                                <div>${part.type} ${part.model}</div>
                                <div>${part.quantity} 件 x ￥${part.price}</div>
                            </div>
                        `;
            });

            recordHTML += `</div></div>`;
        }

        recordDiv.innerHTML = recordHTML;
        content.appendChild(recordDiv);
    });

    // 添加事件监听器
    content.querySelectorAll('.view-details-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            const id = btn.getAttribute('data-id');
            dateRecordsModal.classList.add('hidden'); // 隐藏当前模态框
            showRecordDetails(id); // 显示详情模态框
        });
    });

    content.querySelectorAll('.edit-record-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            const id = btn.getAttribute('data-id');
            dateRecordsModal.classList.add('hidden'); // 隐藏当前模态框
            editRecord(id); // 显示编辑模态框
        });
    });

    content.querySelectorAll('.view-image').forEach(img => {
        img.addEventListener('click', () => {
            const url = img.getAttribute('data-url');
            showImageModal(url);
        });
    });

    // 显示模态框
    dateRecordsModal.classList.remove('hidden');
}

// 选中门市店铺的全局变量
let defaultStoreId = null;

// 在页面加载时检查登录状态
document.addEventListener('DOMContentLoaded', () => {
    // 如果页面正在卸载，不执行任何操作
    if (isPageUnloading) {
        console.log('DOMContentLoaded: 页面正在卸载，跳过初始化');
        return;
    }

    const token = localStorage.getItem('token');
    const username = localStorage.getItem('username');

    // 初始化每日送货次数图表的控件
    initDailyDeliveriesControls();

    if (!token) {
        console.log('DOMContentLoaded: No token, redirecting via checkLogin.');
        if (!isPageUnloading) {
            checkLogin(); // This will handle the redirect
        }
    } else {
        console.log('DOMContentLoaded: Token found, adding user info and starting validation/load.');
        // Add user info and logout button immediately if token exists
        const setupUserInfo = async () => {
            const header = document.querySelector('header');
            // Remove existing user info if any to prevent duplicates on potential re-renders
            const existingUserInfo = header.querySelector('.user-info-container');
            if (existingUserInfo) {
                console.log('用户信息已存在，跳过创建');
                return;
            }

            // 输出详细的调试信息
            console.log(`用户信息: token=${token ? '已存在' : '不存在'}, username=${username || '未找到'}`);

            const userInfo = document.createElement('div');
            userInfo.className = 'flex items-center space-x-4 text-sm text-gray-600 user-info-container'; // Added a class
            userInfo.innerHTML = `
                        <span>欢迎，${username || '请重新登录'}</span>
                        <button onclick="checkAdminInfo()" class="text-blue-600 hover:text-blue-800 mr-2" style="display: none;">
                            <i class="fas fa-user-shield mr-1"></i>检查权限
                        </button>
                        <button onclick="logout()" class="text-red-600 hover:text-red-800" id="logoutBtn">
                            <i class="fas fa-sign-out-alt mr-1"></i>退出
                        </button>
                    `;

            // 检查是否为管理员，添加登录日志入口
            try {
                console.log('准备验证管理员身份...');
                const response = await fetch('/api/validate-token', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                console.log('API响应状态:', response.status, response.ok);

                if (response.ok) {
                    const data = await response.json();
                    console.log('验证结果:', data);
                    console.log('用户角色:', data.user?.role);

                    // 检查用户是否为管理员(admin)
                    if (data.valid && data.user && data.user.role === 'admin') {
                        console.log('用户是管理员，添加管理员链接');
                        const adminLinks = document.createElement('div');
                        adminLinks.className = 'admin-links ml-4 flex space-x-4';
                        adminLinks.innerHTML = `
                                    <a href="/login-logs.html" class="text-indigo-600 hover:text-indigo-800">
                                        <i class="fas fa-clipboard-list mr-1"></i>登录日志
                                    </a>
                                    <a href="/action-logs.html" class="text-indigo-600 hover:text-indigo-800">
                                        <i class="fas fa-history mr-1"></i>操作日志
                                    </a>
                                    <a href="/user-management.html" class="text-indigo-600 hover:text-indigo-800">
                                        <i class="fas fa-users-cog mr-1"></i>用户管理
                                    </a>
                                `;
                        userInfo.appendChild(adminLinks);
                    } else {
                        console.log('用户不是管理员或管理员信息不完整');
                    }
                } else {
                    console.error('验证请求失败，状态码:', response.status);
                }
            } catch (err) {
                console.error('验证管理员身份失败:', err);
            }

            header.appendChild(userInfo);
        };

        // 执行设置用户信息
        setupUserInfo();

        // Now proceed with validation and initial data load via checkLogin
        // checkLogin will call loadData/loadStatistics if token is valid
        checkLogin();

        // Initial setup calls that don't depend on async data can stay here
        initPartsHandlers(); // Re-add the call here
        initStoreSearch();
    }
});

// 更新统计图表
function updateStoreChart(storeStats) {
    const ctx = document.getElementById('storeChart')?.getContext('2d');
    if (!ctx) return;

    // 如果图表已存在，销毁它
    if (storeChart) {
        storeChart.destroy();
    }

    // 准备数据
    const labels = storeStats.map(stat => stat.store_name);
    const data = storeStats.map(stat => stat.count);

    storeChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: '送货次数',
                data: data,
                backgroundColor: 'rgba(79, 70, 229, 0.7)',
                borderColor: 'rgba(79, 70, 229, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function (context) {
                            return `送货次数: ${context.raw}`;
                        }
                    }
                }
            }
        }
    });
}

function updateAddressChart(addressStats) {
    const ctx = document.getElementById('addressChart')?.getContext('2d');
    if (!ctx) return;

    // 如果图表已存在，销毁它
    if (addressChart) {
        addressChart.destroy();
    }

    // 准备数据
    const labels = addressStats.map(stat => stat.address);
    const data = addressStats.map(stat => stat.count);

    // 生成随机颜色
    const backgroundColors = labels.map(() => {
        const r = Math.floor(Math.random() * 200);
        const g = Math.floor(Math.random() * 200);
        const b = Math.floor(Math.random() * 200);
        return `rgba(${r}, ${g}, ${b}, 0.7)`;
    });

    addressChart = new Chart(ctx, {
        type: 'pie',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: backgroundColors,
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right',
                    labels: {
                        boxWidth: 12,
                        padding: 10,
                        font: {
                            size: 12
                        }
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function (context) {
                            const label = context.label || '';
                            const value = context.raw || 0;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
                            return `${label}: ${value} (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });
}

function updateDailyChart(dailyStats) {
    const ctx = document.getElementById('dailyChart')?.getContext('2d');
    if (!ctx) return;

    // 准备数据
    const dates = dailyStats.map(stat => {
        const date = new Date(stat.date);
        return `${date.getMonth() + 1}/${date.getDate()}`;
    });
    const counts = dailyStats.map(stat => stat.count);

    // 如果图表已存在，销毁它
    if (dailyChart) {
        dailyChart.destroy();
    }

    // 创建新图表
    dailyChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: dates,
            datasets: [{
                label: '每日送货次数',
                data: counts,
                borderColor: 'rgb(79, 70, 229)',
                backgroundColor: 'rgba(79, 70, 229, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function (context) {
                            return `送货次数: ${context.raw}`;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });
}

// 添加店铺选项到筛选器
function populateStoreFilter(stores) {
    const storeFilterSelect = document.getElementById('storeFilterSelect');
    const chartStoreSearch = document.getElementById('chartStoreSearch');

    if (!storeFilterSelect) return;

    // 清除所有店铺选项，保留第一个"所有店铺"选项
    const allStoresOption = storeFilterSelect.options[0];
    storeFilterSelect.innerHTML = '';
    storeFilterSelect.appendChild(allStoresOption);

    // 添加店铺选项
    stores.forEach(store => {
        const option = document.createElement('option');
        option.value = store.id;
        option.textContent = store.name;
        storeFilterSelect.appendChild(option);
    });

    // 查找并默认选中 "门市"
    const doorStore = stores.find(store => store.name === '门市');
    if (doorStore) {
        storeFilterSelect.value = doorStore.id;
        if (chartStoreSearch) {
            chartStoreSearch.value = '门市';
        }
    } else {
        storeFilterSelect.value = '';
        if (chartStoreSearch) {
            chartStoreSearch.value = '';
        }
    }
}

// 处理每日送货次数图表的交互
function initDailyDeliveriesControls() {
    // 实现店铺搜索
    const chartStoreSearch = document.getElementById('chartStoreSearch');
    const chartStoreSearchResults = document.getElementById('chartStoreSearchResults');
    const storeFilterSelect = document.getElementById('storeFilterSelect');

    // 搜索框输入事件
    chartStoreSearch.addEventListener('input', function () {
        const searchTerm = this.value.toLowerCase().trim();

        // 清空搜索结果
        chartStoreSearchResults.innerHTML = '';

        if (searchTerm === '') {
            chartStoreSearchResults.classList.add('hidden');
            return;
        }

        // 在全局stores中搜索
        const matches = stores.filter(store =>
            store.name.toLowerCase().includes(searchTerm) ||
            (store.pinyinInitials && store.pinyinInitials.toLowerCase().includes(searchTerm))
        );

        if (matches.length > 0) {
            matches.forEach(store => {
                const div = document.createElement('div');
                div.className = 'cursor-pointer px-3 py-2 hover:bg-gray-100 text-sm';

                // 创建店铺名称元素
                const nameSpan = document.createElement('span');
                nameSpan.className = 'font-medium';
                nameSpan.textContent = store.name;

                // 添加拼音首字母（如果有）
                const pinyinSpan = document.createElement('span');
                pinyinSpan.className = 'text-xs text-gray-500 ml-2';
                pinyinSpan.textContent = store.pinyinInitials ? `(${store.pinyinInitials})` : '';

                div.appendChild(nameSpan);
                div.appendChild(pinyinSpan);

                // 点击事件：选择该店铺并加载数据
                div.addEventListener('click', function () {
                    storeFilterSelect.value = store.id;
                    chartStoreSearch.value = store.name;
                    chartStoreSearchResults.classList.add('hidden');

                    // 加载所选店铺的数据
                    loadDailyDeliveries(store.id);
                });

                chartStoreSearchResults.appendChild(div);
            });

            chartStoreSearchResults.classList.remove('hidden');
        } else {
            // 显示"未找到"消息
            const noResultsDiv = document.createElement('div');
            noResultsDiv.className = 'p-2 text-sm text-gray-500';
            noResultsDiv.textContent = '未找到匹配的店铺';
            chartStoreSearchResults.appendChild(noResultsDiv);
            chartStoreSearchResults.classList.remove('hidden');
        }
    });

    // 点击其他区域时隐藏搜索结果
    document.addEventListener('click', function (event) {
        if (!chartStoreSearch.contains(event.target) && !chartStoreSearchResults.contains(event.target)) {
            chartStoreSearchResults.classList.add('hidden');
        }
    });

    // 清除按钮功能: 双击搜索框清除内容并显示所有店铺数据
    chartStoreSearch.addEventListener('dblclick', function () {
        this.value = '';
        storeFilterSelect.value = '';
        loadDailyDeliveries('');
    });
}

// 编辑配件模态框
function showEditModal(id, storeId, addressId, desc, partId, price, quantity, status) {
    document.getElementById('editId').value = id;
    document.getElementById('editStoreId').value = storeId;
    document.getElementById('editAddressId').value = addressId;
    document.getElementById('editDesc').value = desc;
    document.getElementById('editPartId').value = partId;
    document.getElementById('editPrice').value = price;
    document.getElementById('editQuantity').value = quantity;
    document.getElementById('editStatus').value = status;

    function updateTotalAmount() {
        var price = parseFloat(document.getElementById('editPrice').value) || 0;
        var quantity = parseInt(document.getElementById('editQuantity').value) || 0;
        document.getElementById('editTotalAmount').textContent = (price * quantity).toFixed(2);
    }

    updateTotalAmount();

    document.getElementById('editPrice').addEventListener('input', updateTotalAmount);
    document.getElementById('editQuantity').addEventListener('input', updateTotalAmount);

    const editModal = document.getElementById('editModal');
    editModal.classList.remove('hidden');
}

// 退出登录
function logout() {
    // 防止重复点击
    if (isLoggingOut) {
        console.log('正在退出登录中，忽略重复请求');
        return;
    }
    isLoggingOut = true;
    isPageUnloading = true; // 标记页面正在卸载，阻止其他异步操作
    console.log('开始退出登录流程');

    try {
        // 立即清除localStorage中的token和用户信息
        localStorage.removeItem('token');
        localStorage.removeItem('username');

        // 清除可能存在的cookie
        document.cookie = 'token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';

        // 显示toast提示而不是alert
        showToast('您已成功退出！', 'success');

        // 立即跳转，不要延迟，避免其他异步操作干扰
        console.log('立即跳转到登录页面');

        // 使用setTimeout确保toast显示，但时间很短
        setTimeout(() => {
            window.location.replace('/login.html'); // 使用replace而不是href，避免历史记录问题
        }, 100);

    } catch (error) {
        console.error('退出登录时发生错误:', error);
        // 重置状态
        isLoggingOut = false;
        // 即使出错也要立即跳转到登录页面
        window.location.replace('/login.html');
    }
}

// 检查管理员信息
async function checkAdminInfo() {
    try {
        // 检查当前登录用户权限
        const token = localStorage.getItem('token');
        if (!token) {
            alert('您尚未登录!');
            return;
        }

        console.log('正在验证当前登录用户的权限...');
        const response = await fetch('/api/validate-token', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (response.ok) {
            const data = await response.json();
            alert(`当前用户信息:\n用户名: ${data.user.username}\n角色: ${data.user.role || '未设置'}\n用户ID: ${data.user.id}`);
            console.log('当前用户完整信息:', data);
        } else {
            alert('验证失败: ' + (await response.text()));
        }

        // 检查admin用户信息
        console.log('检查数据库中admin用户信息...');
        const adminResponse = await fetch('/api/check-admin');
        if (adminResponse.ok) {
            const adminInfo = await adminResponse.json();
            console.log('数据库中admin用户信息:', adminInfo);
        } else {
            console.error('获取admin信息失败:', await adminResponse.text());
        }
    } catch (err) {
        console.error('检查管理员信息失败:', err);
        alert('检查失败: ' + err.message);
    }
}

// 计算并更新总金额
function updateTotalAmount() {
    let total = 0;
    document.querySelectorAll('#editPartsTableBody .part-row').forEach(row => {
        const price = parseFloat(row.querySelector('.part-price')?.value) || 0;
        const quantity = parseInt(row.querySelector('.part-quantity')?.value) || 1;
        const amount = price * quantity;
        // Update individual row amount display
        const amountSpan = row.querySelector('.part-amount');
        if (amountSpan) {
            amountSpan.textContent = `¥${amount.toFixed(2)}`;
        }
        total += amount;
    });
    const totalAmountElement = document.getElementById('editTotalAmount');
    if (totalAmountElement) {
        totalAmountElement.textContent = `¥${total.toFixed(2)}`;
    }
}

// Calculate and update the total amount in the edit modal
async function checkLogin() {
    // 如果页面正在卸载，不执行检查
    if (isPageUnloading) {
        console.log('CheckLogin: 页面正在卸载，跳过检查');
        return;
    }

    const token = localStorage.getItem('token');
    if (!token) {
        console.log('CheckLogin: No token, redirecting to login.');
        if (!isPageUnloading) {
            window.location.href = '/login.html';
        }
        return;
    }

    try {
        console.log('CheckLogin: Validating token...');
        const response = await fetch('/api/validate-token', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            const errorData = await response.json();
            console.error('Token validation failed:', response.status, errorData.error);
            if (!isPageUnloading) {
                logout(); // Token invalid or expired, log out
            }
            return;
        }

        const data = await response.json();
        if (data.user && data.user.username) {
            console.log('Token valid for user:', data.user.username);
            localStorage.setItem('username', data.user.username);
        } else {
            console.log('Token valid, but username not in payload. Using existing from localStorage.');
        }


        // Add user info if it wasn't added on DOMContentLoaded (e.g., race condition)
        const header = document.querySelector('header');
        const username = localStorage.getItem('username');
        if (header && !header.querySelector('.user-info-container')) {
            // 确保用户名被正确存储和显示
            console.log(`Token验证成功，用户名: ${username || '未返回用户名'}`);

            const userInfo = document.createElement('div');
            userInfo.className = 'flex items-center space-x-4 text-sm text-gray-600 user-info-container';
            userInfo.innerHTML = `
                        <span>欢迎，${username || '请重新登录'}</span>
                        <button onclick="checkAdminInfo()" class="text-blue-600 hover:text-blue-800 mr-2">
                            <i class="fas fa-user-shield mr-1"></i>检查权限
                        </button>
                        <button onclick="logout()" class="text-red-600 hover:text-red-800" id="logoutBtn2">
                            <i class="fas fa-sign-out-alt mr-1"></i>退出
                        </button>
                    `;
            header.appendChild(userInfo);
        } else if (header.querySelector('.user-info-container')) {
            console.log('用户信息已存在，跳过重复创建');
        }

        // Load initial data after successful validation
        console.log('CheckLogin: Loading initial data...');
        await loadData(); // Load stores, addresses, records
        await loadStatistics(); // Load general stats charts

        // 默认加载"门市"的数据
        const doorStore = stores.find(store => store.name === '门市');
        const storeIdToLoad = doorStore ? doorStore.id : '';
        await loadDailyDeliveries(storeIdToLoad);

        // Load daily amounts chart data
        await loadDailyAmounts();

    } catch (error) {
        console.error('Error validating token or loading initial data:', error);
        // Use window.location directly instead of logout() to avoid potential circular dependency
        localStorage.removeItem('token');
        localStorage.removeItem('username');
        window.location.href = '/login.html';
    }
}

// --- BEGIN NEW CODE ---
// 更新每日送货总金额图表
function updateDailyAmountChart(data) {
    const ctx = document.getElementById('dailyAmountChart')?.getContext('2d');
    if (!ctx) {
        console.error('Canvas context for dailyAmountChart not found');
        return;
    }

    // 销毁旧图表（如果存在）
    if (dailyAmountChart) {
        dailyAmountChart.destroy();
    }

    // 检查数据有效性和是否所有值都是0
    let isAllZero = true;
    if (data && data.datasets && data.datasets[0] && data.datasets[0].data) {
        isAllZero = data.datasets[0].data.every(value => parseFloat(value) === 0);
    }

    const hasValidData = data && data.labels && data.datasets &&
        data.labels.length > 0 &&
        data.datasets[0]?.data &&
        data.datasets[0]?.data.length > 0 &&
        !isAllZero;

    // 如果没有有效数据，显示空状态消息
    if (!hasValidData) {
        console.warn('无有效的每日送货总金额数据或所有值都为0');
        // 清除画布并显示消息
        ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.font = '14px Arial';
        ctx.fillStyle = '#6b7280'; // gray-500

        // 确保画布大小正确
        const parentWidth = ctx.canvas.parentElement.clientWidth;
        const parentHeight = ctx.canvas.parentElement.clientHeight;
        ctx.canvas.width = parentWidth;
        ctx.canvas.height = parentHeight;

        // 绘制背景和消息
        ctx.fillText('暂无金额数据', ctx.canvas.width / 2, ctx.canvas.height / 2 - 10);
        ctx.font = '12px Arial';
        ctx.fillText('请确保配件记录中含有有效的价格和数量数据', ctx.canvas.width / 2, ctx.canvas.height / 2 + 15);
        return;
    }

    const chartData = JSON.parse(JSON.stringify(data));
    const isMobile = window.innerWidth < 640;

    // 配置数据集样式
    chartData.datasets.forEach(dataset => {
        // 使用更现代的渐变色
        dataset.borderColor = '#2563EB'; // blue-600
        dataset.backgroundColor = function (context) {
            const chart = context.chart;
            const { ctx, chartArea } = chart;
            if (!chartArea) {
                return 'rgba(37, 99, 235, 0.1)';
            }
            // 创建蓝色到浅蓝色的渐变
            let gradient = ctx.createLinearGradient(0, chartArea.bottom, 0, chartArea.top);
            gradient.addColorStop(0, 'rgba(37, 99, 235, 0.05)');
            gradient.addColorStop(1, 'rgba(37, 99, 235, 0.25)');
            return gradient;
        };
        dataset.borderWidth = isMobile ? 2.5 : 3.5;
        dataset.fill = true;
        dataset.pointRadius = isMobile ? 4 : 6;
        dataset.pointHoverRadius = isMobile ? 6 : 8;
        dataset.pointBackgroundColor = '#2563EB';
        dataset.pointBorderColor = '#FFFFFF';
        dataset.pointBorderWidth = 2;
        dataset.tension = 0.4;
        dataset.spanGaps = false;
    });

    dailyAmountChart = new Chart(ctx, {
        type: 'line',
        data: chartData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            layout: {
                padding: {
                    left: isMobile ? 10 : 20,
                    right: isMobile ? 20 : 40,
                    top: isMobile ? 10 : 20,
                    bottom: isMobile ? 5 : 10
                }
            },
            plugins: {
                legend: {
                    display: false // Hide legend as there's only one dataset
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    backgroundColor: 'rgba(0, 0, 0, 0.7)',
                    padding: isMobile ? 8 : 10,
                    callbacks: {
                        title: function (tooltipItems) {
                            return `日期: ${tooltipItems[0].label}`;
                        },
                        label: function (context) {
                            let label = context.dataset.label || '';
                            let value = context.parsed.y;
                            return `${label}: ¥${value.toFixed(2)}`;
                        }
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        maxRotation: isMobile ? 45 : 0,
                        font: {
                            size: isMobile ? 9 : 11
                        },
                        autoSkip: isMobile,
                        maxTicksLimit: isMobile ? 15 : 31
                    }
                },
                y: {
                    beginAtZero: true,
                    ticks: {
                        font: {
                            size: isMobile ? 9 : 11
                        },
                        // Format Y-axis labels as currency
                        callback: function (value, index, values) {
                            return '¥' + value.toFixed(0); // Show currency, no decimals for cleaner axis
                        }
                    },
                    title: {
                        display: true,
                        text: '送货金额 (元)',
                        font: {
                            size: isMobile ? 10 : 12,
                            weight: 'bold'
                        },
                        padding: { top: 0, bottom: isMobile ? 5 : 10 }
                    },
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    }
                }
            },
            animation: {
                duration: 800,
                easing: 'easeOutQuad'
            }
        }
    });
}

// 添加移除图片按钮的事件监听
document.getElementById('editRemoveImageBtn')?.addEventListener('click', function () {
    const editImagePreview = document.getElementById('editImagePreview');
    const editImagePreviewContainer = document.getElementById('editImagePreviewContainer');
    const editRemoveImageBtn = document.getElementById('editRemoveImageBtn');

    if (editImagePreview) {
        editImagePreview.src = ''; // 清空图片源
    }

    if (editImagePreviewContainer) {
        editImagePreviewContainer.classList.add('hidden');
    }

    if (editRemoveImageBtn) {
        editRemoveImageBtn.classList.add('hidden');
    }

    // 将图片输入字段标记为'remove'，告诉后端删除现有图片
    const imageRemoveField = document.getElementById('editImageRemove');
    if (!imageRemoveField) {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.id = 'editImageRemove';
        input.name = 'editImageRemove';
        input.value = 'true';
        document.getElementById('editForm')?.appendChild(input);
    } else {
        imageRemoveField.value = 'true';
    }
});

// 编辑界面添加图片预览功能
document.getElementById('editDeliveryImage')?.addEventListener('change', function (e) {
    const file = e.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = function (e) {
        const editImagePreview = document.getElementById('editImagePreview');
        const editImagePreviewContainer = document.getElementById('editImagePreviewContainer');
        const editRemoveImageBtn = document.getElementById('editRemoveImageBtn');

        if (editImagePreview) {
            editImagePreview.src = e.target.result;
        }

        if (editImagePreviewContainer) {
            editImagePreviewContainer.classList.remove('hidden');
        }

        if (editRemoveImageBtn) {
            editRemoveImageBtn.classList.remove('hidden');
        }

        // 如果之前标记了要删除图片，现在取消这个标记
        const imageRemoveField = document.getElementById('editImageRemove');
        if (imageRemoveField) {
            imageRemoveField.value = 'false';
        }
    };

    reader.readAsDataURL(file);
});

// 加载每日送货总金额数据并显示图表
async function loadDailyAmounts() {
    try {
        const token = localStorage.getItem('token');
        if (!token) return;

        // 显示加载中状态
        const chartElement = document.getElementById('dailyAmountChart');
        if (chartElement) {
            const ctx = chartElement.getContext('2d');
            if (ctx) {
                ctx.clearRect(0, 0, chartElement.width, chartElement.height);
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.font = '14px Arial';
                ctx.fillStyle = '#6b7280';
                ctx.fillText('加载中...', chartElement.width / 2, chartElement.height / 2);
            }
        }

        // 构建API请求URL，添加查询参数
        let url = `/api/statistics/daily-amounts`;
        console.log('发送请求获取每日送货总金额数据:', url);

        const response = await fetch(url, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            throw new Error(`获取每日送货总金额数据失败: ${response.status} ${response.statusText}`);
        }

        // 捕获并记录原始响应文本，便于调试
        const responseText = await response.text();
        console.log('接收到的原始响应:', responseText);

        let data;
        try {
            data = JSON.parse(responseText);
        } catch (e) {
            console.error('解析响应JSON失败:', e);
            throw new Error('响应格式错误，无法解析JSON');
        }

        console.log('解析后的金额数据:', data);

        // 检查数据格式
        if (!data || !data.datasets || !data.labels) {
            console.error('金额数据格式不正确:', data);
            throw new Error('响应数据格式不正确');
        }

        // 更新图表
        updateDailyAmountChart(data);
    } catch (error) {
        console.error('加载每日送货总金额失败:', error);
        showToast(`加载每日送货总金额统计失败: ${error.message}`, 'error');

        // 显示错误状态
        const chartElement = document.getElementById('dailyAmountChart');
        if (chartElement) {
            const ctx = chartElement.getContext('2d');
            if (ctx) {
                ctx.clearRect(0, 0, chartElement.width, chartElement.height);
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.font = '14px Arial';
                ctx.fillStyle = '#EF4444'; // Red color for error
                ctx.fillText('加载数据失败', chartElement.width / 2, chartElement.height / 2 - 10);
                ctx.font = '12px Arial';
                ctx.fillText(error.message, chartElement.width / 2, chartElement.height / 2 + 15);
            }
        }
    }
}

// 格式化日期为"月-日"格式
function formatDateForAxis(dateString) {
    // 采用直接处理字符串的方式，避免时区转换问题
    if (typeof dateString === 'string') {
        // 假设输入已经是 YYYY-MM-DD 格式
        const parts = dateString.split('-');
        if (parts.length >= 3) {
            // 只返回月-日
            return `${parseInt(parts[1])}-${parseInt(parts[2])}`;
        }
    }

    try {
        // 备用方案，使用 Date 对象处理
        const date = new Date(dateString);
        return `${date.getMonth() + 1}-${date.getDate()}`;
    } catch (e) {
        console.error('日期格式化错误:', dateString, e);
        return dateString; // 返回原始值
    }
}

// 生成图表颜色
function generateChartColors(count) {
    const baseColors = [
        '#4F46E5', // indigo-600
        '#06B6D4', // cyan-500
        '#0EA5E9', // sky-500
        '#8B5CF6', // violet-500
        '#EC4899', // pink-500
        '#F97316', // orange-500
        '#EAB308', // yellow-500
        '#84CC16', // lime-500
        '#10B981', // emerald-500
        '#14B8A6', // teal-500
    ];

    const colors = [];
    for (let i = 0; i < count; i++) {
        colors.push(baseColors[i % baseColors.length]);
    }
    return colors;
}

// 更新每日送货次数图表
function updateDailyDeliveriesChart(data) {
    const ctx = document.getElementById('dailyDeliveriesChart')?.getContext('2d');
    if (!ctx) {
        console.error('Cannot find dailyDeliveriesChart canvas');
        return;
    }

    // 销毁旧图表（如果存在）
    if (dailyDeliveriesChart) {
        dailyDeliveriesChart.destroy();
    }

    // 检查数据是否有效，或所有数据点是否都为0
    const hasData = data && data.labels && data.datasets && data.labels.length > 0 &&
        data.datasets.some(dataset => dataset.data && dataset.data.some(d => d > 0));

    // 如果没有有效数据，显示空状态
    if (!hasData) {
        console.warn('店铺每日送货次数图表没有有效数据');
        ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);

        const parentWidth = ctx.canvas.parentElement.clientWidth;
        const parentHeight = ctx.canvas.parentElement.clientHeight;
        ctx.canvas.width = parentWidth;
        ctx.canvas.height = parentHeight;

        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.font = '16px Arial';
        ctx.fillStyle = '#6b7280'; // text-gray-500
        ctx.fillText('暂无送货数据', ctx.canvas.width / 2, ctx.canvas.height / 2 - 10);

        ctx.font = '12px Arial';
        ctx.fillStyle = '#9ca3af'; // text-gray-400
        ctx.fillText('请尝试选择其他日期范围或店铺', ctx.canvas.width / 2, ctx.canvas.height / 2 + 15);
        return;
    }

    // 深拷贝数据防止修改原始数据
    const chartData = JSON.parse(JSON.stringify(data));

    // 生成颜色数组，确保每个店铺有固定颜色
    const colors = generateChartColors(chartData.datasets.length);

    // 格式化日期标签（只显示月-日）
    const formattedLabels = chartData.labels.map(dateStr => {
        const date = new Date(dateStr);
        return `${date.getMonth() + 1}-${date.getDate()}`;
    });

    // 检测是否为移动设备
    const isMobile = window.innerWidth < 640;

    // 配置数据集样式 - 移动端适配
    chartData.datasets.forEach((dataset, index) => {
        const color = colors[index % colors.length];
        dataset.borderColor = color;
        dataset.backgroundColor = color + '20'; // 添加透明度
        dataset.borderWidth = isMobile ? 2 : 3; // 移动端线条适当变细
        dataset.fill = true;
        dataset.pointRadius = isMobile ? 3 : 5; // 移动端点变小
        dataset.pointHoverRadius = isMobile ? 5 : 7;
        dataset.tension = 0.4; // 确保线条平滑
        // 确保显示所有数据点
        dataset.spanGaps = false;
    });

    // 创建图表
    dailyDeliveriesChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: formattedLabels,
            datasets: chartData.datasets
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            // 确保数据在图表区域内完全显示 - 移动端适配
            layout: {
                padding: {
                    left: isMobile ? 10 : 20,
                    right: isMobile ? 20 : 40,
                    top: isMobile ? 10 : 20,
                    bottom: isMobile ? 5 : 10
                }
            },
            // 添加点击交互
            onClick: function (event, elements) {
                if (elements && elements.length > 0) {
                    const element = elements[0];
                    const datasetIndex = element.datasetIndex;
                    const index = element.index;

                    // 获取点击的日期和店铺
                    const date = chartData.labels[index];
                    const storeName = chartData.datasets[datasetIndex].label;
                    const storeId = getStoreIdByNameSync(storeName);

                    console.log('图表点击 - 日期:', date, '店铺:', storeName, 'ID:', storeId);

                    // 显示该日期该店铺的送货记录
                    showDateRecords(date, storeId, storeName);
                }
            },
            // 防止数据点被裁剪 - 移动端适配
            plugins: {
                legend: {
                    position: 'top',
                    align: 'start',
                    labels: {
                        boxWidth: isMobile ? 10 : 15,
                        usePointStyle: true,
                        pointStyle: 'circle',
                        padding: isMobile ? 10 : 20,
                        font: {
                            size: isMobile ? 10 : 12
                        }
                    }
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    backgroundColor: 'rgba(0, 0, 0, 0.7)',
                    padding: isMobile ? 8 : 10,
                    callbacks: {
                        title: function (tooltipItems) {
                            // 显示完整日期格式
                            const index = tooltipItems[0].dataIndex;
                            const originalDate = chartData.labels[index];
                            const date = new Date(originalDate);
                            return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
                        },
                        label: function (context) {
                            // 格式化提示框的每一项数据
                            let label = context.dataset.label || '';
                            let value = context.parsed.y;
                            return `${label}: ${value} 次送货`;
                        }
                    }
                }
            },
            // 调整坐标轴 - 移动端适配
            scales: {
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        maxRotation: isMobile ? 45 : 0, // 移动端允许旋转标签以节省空间
                        font: {
                            size: isMobile ? 9 : 11
                        },
                        // 移动端自动跳过部分标签以避免拥挤
                        autoSkip: isMobile,
                        maxTicksLimit: isMobile ? 15 : 31, // 移动端减少标签数量
                        callback: function (value, index) {
                            // 仅返回月-日格式
                            return formattedLabels[index];
                        }
                    }
                },
                y: {
                    // 确保从0开始
                    beginAtZero: true,
                    // 添加一点空间在顶部
                    suggestedMax: function (context) {
                        const max = context.max || 0;
                        return max + 1; // 增加一点高度确保数据点不会触及图表顶部
                    },
                    ticks: {
                        precision: 0, // 只显示整数
                        stepSize: 1,
                        font: {
                            size: isMobile ? 9 : 11
                        }
                    },
                    title: {
                        display: true,
                        text: '送货次数',
                        font: {
                            size: isMobile ? 10 : 12,
                            weight: 'bold'
                        },
                        padding: { top: 0, bottom: isMobile ? 5 : 10 }
                    },
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    }
                }
            },
            animation: {
                duration: 800, // 动画持续时间
                easing: 'easeOutQuad' // 使用平滑的缓动函数
            }
        }
    });
}

// 加载每日送货次数图表数据
async function loadDailyDeliveries(storeId = '') {
    try {
        const token = localStorage.getItem('token');
        if (!token) return;

        // 构建API请求URL，添加查询参数
        let url = `/api/statistics/daily-deliveries`;
        const params = new URLSearchParams();
        if (storeId) {
            params.append('storeId', storeId);
        }

        if (params.toString()) {
            url += `?${params.toString()}`;
        }

        console.log('发送请求获取每日送货数据:', url);

        const response = await fetch(url, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            throw new Error(`获取每日送货次数数据失败: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log('接收到的每日送货次数数据:', data);

        // 更新图表
        updateDailyDeliveriesChart(data);
    } catch (error) {
        console.error('加载每日送货次数失败:', error);
        showToast('加载每日送货次数统计失败', 'error');
    }
}

// 根据店铺名称获取ID的同步方法
function getStoreIdByNameSync(name) {
    const store = stores.find(s => s.name === name);
    return store ? store.id : null;
}

// 添加到页面顶部，在其他脚本之前执行
document.addEventListener('DOMContentLoaded', function () {
    // 调试信息默认隐藏
    const debugInfo = document.getElementById('debugInfo');
    const debugContent = document.getElementById('debugContent');

    if (debugInfo && debugContent) {
        debugInfo.style.display = 'none'; // 默认隐藏调试信息

        // 收集localStorage信息
        const token = localStorage.getItem('token');
        const username = localStorage.getItem('username');

        let debugText = `
页面加载时间: ${new Date().toLocaleString()}
当前路径: ${window.location.pathname}
localStorage.token: ${token ? '存在 (长度:' + token.length + ')' : '不存在'}
localStorage.username: ${username || '不存在'}
`;

        // 如果有token，尝试解析
        if (token) {
            try {
                const tokenParts = token.split('.');
                if (tokenParts.length === 3) {
                    const payload = JSON.parse(atob(tokenParts[1]));
                    debugText += `\nToken解析:\n${JSON.stringify(payload, null, 2)}`;

                    // 检查过期时间
                    if (payload.exp) {
                        const expDate = new Date(payload.exp * 1000);
                        const now = new Date();
                        debugText += `\nToken过期时间: ${expDate.toLocaleString()}`;
                        debugText += `\nToken是否过期: ${expDate < now ? '是' : '否'}`;
                    }
                }
            } catch (e) {
                debugText += `\nToken解析失败: ${e.message}`;
            }
        }

        debugContent.textContent = debugText;

        // 监听localStorage变化
        const originalSetItem = localStorage.setItem;
        localStorage.setItem = function (key, value) {
            debugContent.textContent += `\n\n[localStorage更新] ${key}: ${value}`;
            originalSetItem.apply(this, arguments);
        };

        // 监听页面重定向
        const originalAssign = window.location.assign;
        window.location.assign = function (url) {
            debugContent.textContent += `\n\n[页面重定向] ${url}`;
            originalAssign.apply(this, arguments);
        };
    }
});