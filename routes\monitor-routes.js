const express = require('express');
const router = express.Router();
const multer = require('multer');
const fs = require('fs');
const path = require('path');
const sharp = require('sharp');
const db = require('../config/db');

// 显示器图片上传目录配置
const monitorStorage = multer.diskStorage({
    destination: function (req, file, cb) {
        const uploadDir = path.join(__dirname, '../public', 'uploads', 'monitors');
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const extension = path.extname(file.originalname);
        cb(null, `monitor-${uniqueSuffix}${extension}`);
    }
});

const monitorUpload = multer({
    storage: monitorStorage,
    limits: { fileSize: 10 * 1024 * 1024 }, // 10MB
    fileFilter: function (req, file, cb) {
        if (file.mimetype.startsWith('image/')) {
            cb(null, true);
        } else {
            cb(new Error('只允许上传图片文件!'));
        }
    }
});

// 创建monitors表（如果不存在）
db.query(`
    CREATE TABLE IF NOT EXISTS monitors (
        id INT AUTO_INCREMENT PRIMARY KEY,
        brand VARCHAR(100) NOT NULL,
        model VARCHAR(255) NOT NULL,
        screen_size DECIMAL(4,1),
        resolution VARCHAR(50),
        panel_type VARCHAR(50),
        refresh_rate INT,
        response_time DECIMAL(4,1),
        aspect_ratio VARCHAR(20),
        brightness INT,
        contrast_ratio VARCHAR(50),
        color_gamut VARCHAR(100),
        hdr_support VARCHAR(100),
        speakers BOOLEAN,
        vesa_mount VARCHAR(50),
        connectivity TEXT,
        adjustable_stand VARCHAR(255),
        price DECIMAL(10,2),
        notes TEXT,
        image_url VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )
`).then(() => {
    console.log('创建monitors表成功');
}).catch(err => {
    console.error('创建monitors表失败:', err);
});

// 获取显示器列表 (分页, 搜索, 筛选)
router.get('/', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const offset = (page - 1) * limit;
        const search = req.query.search || '';
        const brand = req.query.brand || '';

        let conditions = [];
        let params = [];

        if (search) {
            conditions.push(`(model LIKE ? OR brand LIKE ? OR resolution LIKE ? OR panel_type LIKE ?)`);
            const searchTerm = `%${search}%`;
            params.push(searchTerm, searchTerm, searchTerm, searchTerm);
        }

        if (brand && brand !== 'all') {
            conditions.push(`brand = ?`);
            params.push(brand);
        }

        const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

        const [countResult] = await db.query(`SELECT COUNT(*) as total FROM monitors ${whereClause}`, params);
        const total = countResult[0].total;

        const [monitors] = await db.query(`SELECT * FROM monitors ${whereClause} ORDER BY id DESC LIMIT ? OFFSET ?`, [...params, limit, offset]);

        res.json({
            items: monitors,
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit)
        });
    } catch (error) {
        console.error('获取显示器列表失败:', error);
        res.status(500).json({ error: '获取显示器列表失败' });
    }
});

// 获取所有显示器品牌
router.get('/brands', async (req, res) => {
    try {
        const [brands] = await db.query('SELECT DISTINCT brand FROM monitors ORDER BY brand ASC');
        res.json(brands.map(b => b.brand));
    } catch (error) {
        console.error('获取显示器品牌失败:', error);
        res.status(500).json({ error: '获取品牌列表失败' });
    }
});

// 获取单个显示器信息
router.get('/:id', async (req, res) => {
    try {
        const [monitor] = await db.query('SELECT * FROM monitors WHERE id = ?', [req.params.id]);
        if (monitor.length === 0) {
            return res.status(404).json({ error: '未找到该显示器' });
        }
        res.json(monitor[0]);
    } catch (error) {
        console.error('获取显示器详情失败:', error);
        res.status(500).json({ error: '获取显示器详情失败' });
    }
});

// 添加新显示器
router.post('/', monitorUpload.single('image'), async (req, res) => {
    const conn = await db.getConnection();
    try {
        await conn.beginTransaction();
        const data = req.body;
        
        let imageUrl = null;
        if (req.file) {
            imageUrl = `/uploads/monitors/${req.file.filename}`;
            
            // 如果是图片，尝试转换为WebP格式
            if (req.file.mimetype.startsWith('image/')) {
                try {
                    const originalPath = req.file.path;
                    const fileNameWithoutExt = path.basename(req.file.filename, path.extname(req.file.filename));
                    const webpFilename = `${fileNameWithoutExt}.webp`;
                    const webpPath = path.join(path.dirname(originalPath), webpFilename);
                    
                    await sharp(originalPath)
                        .webp({ quality: 100, lossless: true })
                        .toFile(webpPath);
                    
                    // 删除原始文件
                    if (fs.existsSync(originalPath)) {
                        fs.unlinkSync(originalPath);
                    }
                    
                    // 更新图片URL为WebP文件
                    imageUrl = `/uploads/monitors/${webpFilename}`;
                } catch (err) {
                    console.error('WebP转换失败:', err);
                    // 如果转换失败，使用原始文件
                }
            }
        }

        const fields = [
            'brand', 'model', 'screen_size', 'resolution', 'panel_type', 'refresh_rate',
            'response_time', 'aspect_ratio', 'brightness', 'contrast_ratio', 'color_gamut',
            'hdr_support', 'speakers', 'vesa_mount', 'connectivity', 'adjustable_stand',
            'price', 'notes'
        ];
        
        const monitorData = {};
        fields.forEach(field => {
            monitorData[field] = data[field] === '' || data[field] === undefined ? null : data[field];
        });
        monitorData.image_url = imageUrl;
        
        const columns = Object.keys(monitorData).join(', ');
        const placeholders = Object.keys(monitorData).map(() => '?').join(', ');
        const values = Object.values(monitorData);

        const [result] = await conn.query(`INSERT INTO monitors (${columns}) VALUES (${placeholders})`, values);
        const insertId = result.insertId;

        const [newMonitor] = await conn.query('SELECT * FROM monitors WHERE id = ?', [insertId]);
        
        await conn.commit();
        res.status(201).json(newMonitor[0]);
    } catch (error) {
        await conn.rollback();
        console.error('添加显示器失败:', error);
        res.status(500).json({ error: '添加显示器失败' });
    } finally {
        conn.release();
    }
});

// 更新显示器信息
router.put('/:id', monitorUpload.single('image'), async (req, res) => {
    const conn = await db.getConnection();
    const id = req.params.id;
    try {
        await conn.beginTransaction();
        const data = req.body;

        const [existing] = await conn.query('SELECT image_url FROM monitors WHERE id = ?', [id]);
        if (existing.length === 0) {
            await conn.rollback();
            return res.status(404).json({ error: '未找到该显示器' });
        }

        let imageUrl = existing[0].image_url;
        if (req.file) {
            // 删除旧图片
            if (imageUrl) {
                const oldPath = path.join(__dirname, '../public', imageUrl);
                if (fs.existsSync(oldPath)) fs.unlinkSync(oldPath);
            }

            imageUrl = `/uploads/monitors/${req.file.filename}`;
            
            // 如果是图片，尝试转换为WebP格式
            if (req.file.mimetype.startsWith('image/')) {
                try {
                    const originalPath = req.file.path;
                    const fileNameWithoutExt = path.basename(req.file.filename, path.extname(req.file.filename));
                    const webpFilename = `${fileNameWithoutExt}.webp`;
                    const webpPath = path.join(path.dirname(originalPath), webpFilename);
                    
                    await sharp(originalPath)
                        .webp({ quality: 100, lossless: true })
                        .toFile(webpPath);
                    
                    // 删除原始文件
                    if (fs.existsSync(originalPath)) {
                        fs.unlinkSync(originalPath);
                    }
                    
                    // 更新图片URL为WebP文件
                    imageUrl = `/uploads/monitors/${webpFilename}`;
                } catch (err) {
                    console.error('WebP转换失败:', err);
                    // 如果转换失败，使用原始文件
                }
            }
        }

        const fields = [
            'brand', 'model', 'screen_size', 'resolution', 'panel_type', 'refresh_rate',
            'response_time', 'aspect_ratio', 'brightness', 'contrast_ratio', 'color_gamut',
            'hdr_support', 'speakers', 'vesa_mount', 'connectivity', 'adjustable_stand',
            'price', 'notes'
        ];
        
        const monitorData = {};
        fields.forEach(field => {
            if (data[field] !== undefined) {
                monitorData[field] = data[field] === '' ? null : data[field];
            }
        });
        if (req.file) { // 只有上传了新图片才更新图片URL
            monitorData.image_url = imageUrl;
        }

        const setClause = Object.keys(monitorData).map(key => `${key} = ?`).join(', ');
        const values = [...Object.values(monitorData), id];

        await conn.query(`UPDATE monitors SET ${setClause} WHERE id = ?`, values);
        
        const [updatedMonitor] = await conn.query('SELECT * FROM monitors WHERE id = ?', [id]);

        await conn.commit();
        res.json(updatedMonitor[0]);
    } catch (error) {
        await conn.rollback();
        console.error(`更新显示器(ID: ${id})失败:`, error);
        res.status(500).json({ error: `更新显示器失败` });
    } finally {
        conn.release();
    }
});

// 删除显示器
router.delete('/:id', async (req, res) => {
    const conn = await db.getConnection();
    const id = req.params.id;
    try {
        await conn.beginTransaction();

        const [monitor] = await conn.query('SELECT image_url FROM monitors WHERE id = ?', [id]);
        if (monitor.length === 0) {
            await conn.rollback();
            return res.status(404).json({ error: '未找到该显示器' });
        }

        // 删除图片文件
        const imageUrl = monitor[0].image_url;
        if (imageUrl) {
            const imagePath = path.join(__dirname, '../public', imageUrl);
            if (fs.existsSync(imagePath)) {
                fs.unlinkSync(imagePath);
            }
        }

        await conn.query('DELETE FROM monitors WHERE id = ?', [id]);
        
        await conn.commit();
        res.status(204).send();
    } catch (error) {
        await conn.rollback();
        console.error(`删除显示器(ID: ${id})失败:`, error);
        res.status(500).json({ error: '删除显示器失败' });
    } finally {
        conn.release();
    }
});

module.exports = router;