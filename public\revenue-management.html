<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创收管理 - 电脑配件管理系统</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/viewerjs/1.11.6/viewer.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/viewerjs/1.11.6/viewer.min.js"></script>
    <!-- 拼音库 -->
    <script src="https://unpkg.com/pinyin@2.11.2/lib/pinyin-web.js"></script>
    <link rel="stylesheet" href="/css/revenue-management.css">

    <!-- 添加Viewer.js加载检查脚本 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('检查Viewer.js是否加载:', typeof Viewer !== 'undefined' ? '已加载' : '未加载');

            // 尝试修复可能的Viewer加载问题
            if (typeof Viewer === 'undefined') {
                console.warn('Viewer未定义，尝试重新加载Viewer.js...');
                const script = document.createElement('script');
                script.src = 'https://cdnjs.cloudflare.com/ajax/libs/viewerjs/1.11.6/viewer.min.js';
                script.onload = function() {
                    console.log('Viewer.js重新加载成功');
                    // 初始化全局变量以确保可用性
                    window.Viewer = Viewer;
                };
                script.onerror = function() {
                    console.error('Viewer.js重新加载失败');
                    alert('图片预览功能加载失败，部分功能可能无法正常使用');
                };
                document.head.appendChild(script);
            }
        });
    </script>
</head>
<body class="bg-gray-100 min-h-screen transition-colors duration-300">
    <!-- 导航栏 -->
    <nav class="bg-blue-600 text-white p-4 shadow-lg">
        <div class="container mx-auto flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <h1 class="text-xl font-bold">
                    <i class="fas fa-chart-line mr-2"></i>
                    创收管理系统
                </h1>
            </div>
            <div class="flex items-center space-x-4">
                <!-- 主题切换按钮 -->
                <button id="themeToggle"
                        class="p-2 rounded-lg hover:bg-blue-500 transition-colors"
                        title="切换主题">
                    <i class="fas fa-moon"></i>
                </button>

                <span id="userInfo" class="text-sm"></span>
                <button  class="bg-green-500 hover:bg-green-600 px-3 py-1 rounded text-sm">
                    <i class="fas fa-home mr-1"></i>
                    <a href="index.html">首页</a>
                </button>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container mx-auto p-6">
        <!-- 统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div class="bg-white p-6 rounded-lg shadow-md">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                        <i class="fas fa-dollar-sign text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-gray-600 text-sm">总创收</p>
                        <p id="totalRevenue" class="text-2xl font-bold text-gray-800">¥0.00</p>
                    </div>
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-md">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100 text-green-600">
                        <i class="fas fa-shopping-cart text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-gray-600 text-sm">订单数量</p>
                        <p id="totalOrders" class="text-2xl font-bold text-gray-800">0</p>
                    </div>
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-md">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                        <i class="fas fa-store text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-gray-600 text-sm">店铺数量</p>
                        <p id="totalShops" class="text-2xl font-bold text-gray-800">0</p>
                    </div>
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-md">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                        <i class="fas fa-chart-line text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-gray-600 text-sm">平均订单价值</p>
                        <p id="avgOrderValue" class="text-2xl font-bold text-gray-800">¥0.00</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作栏 -->
        <div class="bg-white p-4 rounded-lg shadow-md mb-6">
            <div class="flex flex-wrap items-center justify-between gap-4">
                <div class="flex flex-wrap items-center gap-4">
                    <button id="addRecordBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-plus mr-2"></i>
                        添加记录
                    </button>
                    <button id="exportExcelBtn" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-file-excel mr-2"></i>
                        导出Excel
                    </button>
                    <button id="showChartsBtn" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-chart-bar mr-2"></i>
                        数据分析
                    </button>
                    <button id="refreshBtn" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-sync-alt mr-2"></i>
                        刷新
                    </button>
                </div>

                <!-- 筛选器 -->
                <div class="flex flex-wrap items-center gap-4">
                     <!-- 搜索框 -->
                    <div class="flex items-center gap-2">
                        <input type="text" id="searchInput" placeholder="搜索订单号、店铺名称..."
                               class="border border-gray-300 rounded-lg px-3 py-2 w-64">
                    </div>
                    <select id="shopFilter" class="border border-gray-300 rounded-lg px-3 py-2">
                        <option value="">所有店铺</option>
                    </select>

                    <input type="date" id="startDate" class="border border-gray-300 rounded-lg px-3 py-2">
                    <span class="text-gray-500">至</span>
                    <input type="date" id="endDate" class="border border-gray-300 rounded-lg px-3 py-2">

                    <button id="filterBtn" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">
                        <i class="fas fa-filter mr-2"></i>
                        筛选
                    </button>
                    <button id="resetFilterBtn" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg">
                        <i class="fas fa-times mr-2"></i>
                        重置
                    </button>


                </div>
            </div>
        </div>

        <!-- 搜索结果状态 -->
        <div id="searchResultsInfo" class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4 hidden">
            <span id="searchResultsText" class="text-blue-700"></span>
        </div>

        <!-- 桌面端表格视图 -->
        <div class="hidden md:block bg-white rounded-lg shadow-md overflow-hidden">
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">店铺</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">订单号</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">产品信息</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">图片</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创收金额</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">订单状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">备注</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody id="revenueTableBody" class="bg-white divide-y divide-gray-200">
                        <!-- 数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 移动端卡片视图 -->
        <div class="block md:hidden bg-white rounded-lg shadow-md overflow-hidden p-4">
            <div id="revenueCardsContainer" class="space-y-4">
                <!-- 移动端卡片将通过JavaScript动态加载 -->
            </div>
        </div>

            <!-- 分页 -->
            <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <div class="flex-1 flex justify-between sm:hidden">
                    <button id="prevPageMobile" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        上一页
                    </button>
                    <button id="nextPageMobile" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        下一页
                    </button>
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            显示第 <span id="startRecord" class="font-medium">1</span> 到 <span id="endRecord" class="font-medium">10</span> 条，
                            共 <span id="totalRecords" class="font-medium">0</span> 条记录
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" id="pagination">
                            <!-- 分页按钮将通过JavaScript动态生成 -->
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加/编辑记录模态框 -->
    <div id="recordModal" class="modal">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-screen overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 id="modalTitle" class="text-lg font-semibold text-gray-900">添加创收记录</h3>
                    <button id="closeModal" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <form id="recordForm" class="space-y-4">
                    <input type="hidden" id="recordId">

                    <!-- 基本信息卡片 -->
                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-100">
                        <div class="flex items-center mb-3">
                            <div class="bg-blue-500 text-white p-2 rounded-lg mr-3">
                                <i class="fas fa-info-circle"></i>
                            </div>
                            <h4 class="font-semibold text-gray-800">基本信息</h4>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="shopName" class="block text-sm font-medium text-gray-700 mb-1">
                                    <i class="fas fa-store text-blue-500 mr-2"></i>店铺名称 *
                                </label>
                                <div class="relative">
                                    <input type="text" id="shopSearch"
                                           class="w-full border border-gray-300 rounded-lg px-3 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200"
                                           placeholder="搜索或输入店铺名称(支持拼音)...">
                                    <input type="hidden" id="shopName" name="shop_name" required>
                                    <button type="button" id="addShopBtn"
                                            class="absolute right-2 top-1/2 transform -translate-y-1/2 text-blue-500 hover:text-blue-700 focus:outline-none"
                                            title="添加新店铺">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                    <div id="shopSearchResults"
                                         class="absolute z-10 w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg mt-1 max-h-60 overflow-y-auto hidden">
                                    </div>
                                </div>

                                <!-- 新增店铺容器 -->
                                <div id="newShopContainer" class="mt-3 p-3 bg-blue-50 dark:bg-gray-700 border border-blue-200 dark:border-gray-600 rounded-lg hidden">
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="text-sm font-medium text-blue-800 dark:text-blue-300">添加新店铺</span>
                                        <button type="button" id="cancelAddShop" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                    <div class="flex gap-2">
                                        <input type="text" id="newShopName"
                                               class="flex-1 border border-blue-300 dark:border-gray-500 dark:bg-gray-600 dark:text-white rounded px-2 py-1 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 dark:focus:ring-blue-400"
                                               placeholder="输入新店铺名称...">
                                        <button type="button" id="saveNewShop"
                                                class="px-3 py-1 bg-blue-600 dark:bg-blue-700 text-white rounded text-sm hover:bg-blue-700 dark:hover:bg-blue-600 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:focus:ring-blue-400">
                                            保存
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div>
                                <label for="orderNumber" class="block text-sm font-medium text-gray-700 mb-1">
                                    <i class="fas fa-hashtag text-blue-500 mr-2"></i>订单号 *
                                </label>
                                <input type="text" id="orderNumber" name="order_number" required
                                       class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200">
                            </div>
                        </div>
                    </div>

                    <!-- 产品信息卡片 -->
                    <div class="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-4 border border-green-100">
                        <div class="flex items-center mb-3">
                            <div class="bg-green-500 text-white p-2 rounded-lg mr-3">
                                <i class="fas fa-box"></i>
                            </div>
                            <h4 class="font-semibold text-gray-800">产品信息</h4>
                        </div>
                        <div>
                            <label for="productInfo" class="block text-sm font-medium text-gray-700 mb-1">
                                <i class="fas fa-clipboard-list text-green-500 mr-2"></i>产品信息 *
                            </label>
                            <textarea id="productInfo" name="product_info" required rows="4"
                                      placeholder="格式：创收金额+具体型号"
                                      class="w-full border border-gray-300 rounded-lg px-3 py-3 focus:outline-none focus:ring-2 focus:ring-green-500 transition-all duration-200 resize-none"></textarea>
                        </div>
                    </div>

                    <!-- 订单详情卡片 -->
                    <div class="bg-gradient-to-r from-orange-50 to-amber-50 rounded-lg p-4 border border-orange-100">
                        <div class="flex items-center mb-3">
                            <div class="bg-orange-500 text-white p-2 rounded-lg mr-3">
                                <i class="fas fa-receipt"></i>
                            </div>
                            <h4 class="font-semibold text-gray-800">订单详情</h4>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="totalRevenue" class="block text-sm font-medium text-gray-700 mb-1">
                                    <i class="fas fa-dollar-sign text-orange-500 mr-2"></i>创收金额 *
                                </label>
                                <input type="number" id="totalRevenueInput" name="total_revenue" step="0.01" min="0" required
                                       class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500 transition-all duration-200">
                            </div>

                            <div>
                                <label for="orderStatus" class="block text-sm font-medium text-gray-700 mb-1">
                                    <i class="fas fa-clipboard-check text-orange-500 mr-2"></i>订单状态
                                </label>
                                <select id="orderStatus" name="status"
                                        class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500 transition-all duration-200">
                                    <option value="未做单">未做单</option>
                                    <option value="已做单">已做单</option>
                                    <option value="已关闭">已关闭</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- 图片上传卡片 -->
                    <div class="bg-gradient-to-r from-pink-50 to-rose-50 rounded-lg p-4 border border-pink-100">
                        <div class="flex items-center mb-3">
                            <div class="bg-pink-500 text-white p-2 rounded-lg mr-3">
                                <i class="fas fa-image"></i>
                            </div>
                            <h4 class="font-semibold text-gray-800">图片上传</h4>
                        </div>
                        <div class="flex justify-center px-3 py-4 border-2 border-pink-300 border-dashed rounded-lg bg-white bg-opacity-50">
                            <div class="text-center">
                                <label for="imageUpload"
                                    class="cursor-pointer bg-white rounded-lg font-medium text-pink-600 hover:text-pink-500 px-3 py-2 border border-pink-200 hover:border-pink-300 transition-all duration-200 inline-flex items-center">
                                    <i class="fas fa-upload mr-2"></i>
                                    <span>上传图片</span>
                                    <input id="imageUpload" name="image" type="file" accept="image/*" class="sr-only">
                                </label>
                                <p class="text-xs text-gray-500 mt-2">PNG, JPG, GIF ≤5MB</p>
                            </div>
                        </div>
                        <div id="imagePreviewContainer" class="mt-2 hidden">
                            <div class="relative inline-block">
                                <img id="imagePreview" src="#" alt="预览图"
                                    class="h-20 rounded-md shadow image-preview">
                                <button type="button" id="removeImageBtn"
                                    class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center hover:bg-red-600">
                                    <i class="fas fa-times text-xs"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 备注信息卡片 -->
                    <div class="bg-gradient-to-r from-purple-50 to-violet-50 rounded-lg p-4 border border-purple-100">
                        <div class="flex items-center mb-3">
                            <div class="bg-purple-500 text-white p-2 rounded-lg mr-3">
                                <i class="fas fa-sticky-note"></i>
                            </div>
                            <h4 class="font-semibold text-gray-800">创收备注</h4>
                        </div>
                        <div>
                            <label for="revenueNotes" class="block text-sm font-medium text-gray-700 mb-1">
                                <i class="fas fa-comment-alt text-purple-500 mr-2"></i>备注信息
                            </label>
                            <textarea id="revenueNotes" name="revenue_notes" rows="4"
                                      placeholder="记录额外说明信息"
                                      class="w-full border border-gray-300 rounded-lg px-3 py-3 focus:outline-none focus:ring-2 focus:ring-purple-500 transition-all duration-200 resize-none"></textarea>
                        </div>
                    </div>

                        <!-- 上传进度条 -->
                        <div id="uploadProgressContainer" class="mt-2 hidden">
                            <div class="bg-gray-200 dark:bg-gray-700 rounded-full h-3 mb-2 overflow-hidden">
                                <div id="uploadProgressBar" class="bg-gradient-to-r from-green-500 to-green-600 dark:from-green-400 dark:to-green-500 h-3 rounded-full transition-all duration-500 ease-out relative" style="width: 0%">
                                    <!-- 进度条光泽效果 -->
                                    <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-20 animate-pulse"></div>
                                </div>
                            </div>
                            <div class="flex justify-between items-center text-xs">
                                <span id="uploadProgressText" class="text-gray-600 dark:text-gray-400 flex items-center">
                                    <i id="uploadProgressIcon" class="fas fa-upload mr-1"></i>
                                    准备上传...
                                </span>
                                <span id="uploadProgressPercent" class="text-green-600 dark:text-green-400 font-medium">0%</span>
                            </div>
                        </div>

                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" id="cancelBtn" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                            取消
                        </button>
                        <button type="submit" id="saveBtn" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                            <span class="loading hidden"></span>
                            <span id="saveBtnText">保存</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 详情查看模态框 -->
    <div id="detailModal" class="modal">
        <div class="bg-white rounded-xl shadow-2xl max-w-4xl w-full mx-4 max-h-screen overflow-y-auto border border-gray-200">
            <!-- 模态框头部 -->
            <div class="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-6 rounded-t-xl">
                <div class="flex justify-between items-center">
                    <div class="flex items-center">
                        <div class="bg-white bg-opacity-20 p-2 rounded-lg mr-3 detail-icon-bounce">
                            <i class="fas fa-file-invoice-dollar text-xl"></i>
                        </div>
                        <h3 class="text-xl font-bold">创收记录详情</h3>
                    </div>
                    <button id="closeDetailModal" class="text-white hover:text-gray-200 transition-colors p-2 hover:bg-white hover:bg-opacity-20 rounded-lg">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>

            <div class="p-6">
                <div class="space-y-6">
                    <!-- 基本信息卡片 -->
                    <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100 detail-modal-card">
                        <div class="flex items-center mb-4">
                            <div class="bg-blue-500 text-white p-2 rounded-lg mr-3">
                                <i class="fas fa-info-circle"></i>
                            </div>
                            <h4 class="text-lg font-semibold text-gray-800">基本信息</h4>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="space-y-4">
                                <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
                                    <div class="flex items-center mb-2">
                                        <i class="fas fa-store text-blue-500 mr-2"></i>
                                        <label class="text-sm font-medium text-gray-700">店铺名称</label>
                                    </div>
                                    <div id="detailShopName" class="text-gray-900 font-medium"></div>
                                </div>

                                <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
                                    <div class="flex items-center mb-2">
                                        <i class="fas fa-hashtag text-purple-500 mr-2"></i>
                                        <label class="text-sm font-medium text-gray-700">订单号</label>
                                    </div>
                                    <div id="detailOrderNumber" class="text-gray-900 font-mono bg-gray-50 px-2 py-1 rounded text-sm"></div>
                                </div>

                                <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
                                    <div class="flex items-center mb-2">
                                        <i class="fas fa-tags text-orange-500 mr-2"></i>
                                        <label class="text-sm font-medium text-gray-700">状态</label>
                                    </div>
                                    <div id="detailStatus" class="inline-block"></div>
                                </div>
                            </div>

                            <div class="space-y-4">
                                <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
                                    <div class="flex items-center mb-2">
                                        <i class="fas fa-dollar-sign text-green-500 mr-2"></i>
                                        <label class="text-sm font-medium text-gray-700">创收金额</label>
                                    </div>
                                    <div id="detailTotalRevenue" class="text-green-600 font-bold text-2xl"></div>
                                </div>

                                <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
                                    <div class="flex items-center mb-2">
                                        <i class="fas fa-image text-pink-500 mr-2"></i>
                                        <label class="text-sm font-medium text-gray-700">产品图片</label>
                                    </div>
                                    <div class="mt-2">
                                        <img id="detailImage" src="" alt="产品图片"
                                             class="max-w-full h-40 object-cover rounded-lg cursor-pointer hover:opacity-80 transition-opacity shadow-sm border border-gray-200">
                                        <div id="detailNoImage" class="text-gray-400 text-center py-8 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                                            <i class="fas fa-image text-3xl mb-2"></i>
                                            <p class="text-sm">暂无图片</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 产品信息卡片 -->
                    <div class="bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-6 border border-green-100 detail-modal-card">
                        <div class="flex items-center mb-4">
                            <div class="bg-green-500 text-white p-2 rounded-lg mr-3">
                                <i class="fas fa-box"></i>
                            </div>
                            <h4 class="text-lg font-semibold text-gray-800">产品信息</h4>
                        </div>
                        <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
                            <div id="detailProductInfo" class="text-gray-900 whitespace-pre-wrap leading-relaxed"></div>
                        </div>
                    </div>

                    <!-- 备注信息卡片 -->
                    <div class="bg-gradient-to-br from-yellow-50 to-amber-50 rounded-xl p-6 border border-yellow-100 detail-modal-card">
                        <div class="flex items-center mb-4">
                            <div class="bg-yellow-500 text-white p-2 rounded-lg mr-3">
                                <i class="fas fa-sticky-note"></i>
                            </div>
                            <h4 class="text-lg font-semibold text-gray-800">创收备注</h4>
                        </div>
                        <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
                            <div id="detailRevenueNotes" class="text-gray-900 whitespace-pre-wrap leading-relaxed"></div>
                        </div>
                    </div>

                    <!-- 时间信息卡片 -->
                    <div class="bg-gradient-to-br from-purple-50 to-violet-50 rounded-xl p-6 border border-purple-100 detail-modal-card">
                        <div class="flex items-center mb-4">
                            <div class="bg-purple-500 text-white p-2 rounded-lg mr-3">
                                <i class="fas fa-clock"></i>
                            </div>
                            <h4 class="text-lg font-semibold text-gray-800">时间信息</h4>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
                                <div class="flex items-center mb-2">
                                    <i class="fas fa-plus-circle text-green-500 mr-2"></i>
                                    <label class="text-sm font-medium text-gray-700">创建时间</label>
                                </div>
                                <div id="detailCreatedAt" class="text-gray-600 font-mono text-sm"></div>
                            </div>
                            <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
                                <div class="flex items-center mb-2">
                                    <i class="fas fa-edit text-blue-500 mr-2"></i>
                                    <label class="text-sm font-medium text-gray-700">更新时间</label>
                                </div>
                                <div id="detailUpdatedAt" class="text-gray-600 font-mono text-sm"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                        <button id="detailEditBtn" class="px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 detail-btn-hover">
                            <i class="fas fa-edit mr-2"></i>
                            编辑记录
                        </button>
                        <button id="closeDetailModal2" class="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-all duration-200 shadow-sm hover:shadow-md">
                            <i class="fas fa-times mr-2"></i>
                            关闭
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 数据分析模态框 -->
    <div id="chartsModal" class="modal">
        <div class="bg-white rounded-lg shadow-xl max-w-6xl w-full mx-4 max-h-screen overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">数据分析</h3>
                    <button id="closeChartsModal" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <!-- 第一行图表 -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="text-md font-medium text-gray-800 mb-4">创收趋势图</h4>
                        <div class="chart-container">
                            <canvas id="revenueChart"></canvas>
                        </div>
                    </div>

                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="text-md font-medium text-gray-800 mb-4">店铺创收对比</h4>
                        <div class="chart-container">
                            <canvas id="shopChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- 第二行图表 -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="text-md font-medium text-gray-800 mb-4">月度创收分析</h4>
                        <div class="chart-container">
                            <canvas id="monthlyChart"></canvas>
                        </div>
                    </div>

                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="text-md font-medium text-gray-800 mb-4">产品类别分析</h4>
                        <div class="chart-container">
                            <canvas id="categoryChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- 第三行图表 -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="text-md font-medium text-gray-800 mb-4">订单量趋势</h4>
                        <div class="chart-container">
                            <canvas id="orderTrendChart"></canvas>
                        </div>
                    </div>

                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="text-md font-medium text-gray-800 mb-4">平均订单价值</h4>
                        <div class="chart-container">
                            <canvas id="avgOrderChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 脚本引用 -->
    <script src="/js/main.js"></script>
    <script src="/js/auth.js"></script>
    <script src="/js/upload-progress.js"></script>
    <script src="/js/revenue-management.js"></script>

</body>
</html>
