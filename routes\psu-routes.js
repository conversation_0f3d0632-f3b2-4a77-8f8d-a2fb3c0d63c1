const express = require('express');
const sharp = require('sharp');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// 数据库连接
const db = require('../db');

// 配置PSU图片上传
const psuUpload = multer({
    storage: multer.diskStorage({
        destination: function (req, file, cb) {
            const uploadDir = path.join(__dirname, '../public/uploads/psu');
            if (!fs.existsSync(uploadDir)) {
                fs.mkdirSync(uploadDir, { recursive: true });
            }
            cb(null, uploadDir);
        },
        filename: function (req, file, cb) {
            const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
            const extension = path.extname(file.originalname);
            cb(null, 'psu-' + uniqueSuffix + extension);
        }
    })
});

// 获取所有电源（带分页和筛选）
router.get('/', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const offset = (page - 1) * limit;
        const search = req.query.search || '';
        
        let query = 'SELECT * FROM psus WHERE 1=1';
        let countQuery = 'SELECT COUNT(*) as total FROM psus WHERE 1=1';
        const queryParams = [];
        
        if (search) {
            query += ' AND (brand LIKE ? OR model LIKE ? OR wattage LIKE ?)';
            countQuery += ' AND (brand LIKE ? OR model LIKE ? OR wattage LIKE ?)';
            for (let i = 0; i < 3; i++) {
                queryParams.push(`%${search}%`);
            }
        }
        
        query += ' ORDER BY id DESC LIMIT ? OFFSET ?';
        queryParams.push(limit, offset);
        
        const [psus] = await db.query(query, queryParams);
        const [countResult] = await db.query(countQuery, queryParams.slice(0, -2));
        const total = countResult[0].total;
        
        res.json({
            data: psus,
            pagination: {
                total,
                page,
                limit,
                totalPages: Math.ceil(total / limit)
            }
        });
    } catch (error) {
        console.error('获取电源列表失败:', error);
        res.status(500).json({ error: '获取电源列表失败' });
    }
});

// 获取单个电源详情
router.get('/:id', async (req, res) => {
    try {
        const [psus] = await db.query('SELECT * FROM psus WHERE id = ?', [req.params.id]);
        
        if (psus.length === 0) {
            return res.status(404).json({ error: '未找到电源' });
        }
        
        res.json(psus[0]);
    } catch (error) {
        console.error('获取电源详情失败:', error);
        res.status(500).json({ error: '获取电源详情失败' });
    }
});

// 添加新电源
router.post('/', psuUpload.single('image'), async (req, res) => {
    try {
        console.log('添加电源请求体:', req.body);
        console.log('上传文件:', req.file);
        
        const {
            brand,
            model,
            wattage,
            psu_length,
            efficiency,
            modular,
            pcie_connector,
            sata_count,
            cpu_connector,
            fan_size,
            physical_length,
            warranty,
            price,
            notes,
            use_default_image
        } = req.body;
        
        // 详细记录pcie_connector的值
        console.log('原始pcie_connector值:', pcie_connector);
        console.log('pcie_connector类型:', typeof pcie_connector);
        
        // 验证必填字段
        if (!brand || !model || !wattage) {
            return res.status(400).json({ error: '品牌、型号和功率为必填字段' });
        }
        
        // 安全解析数字字段
        const parsedWattage = wattage === '' ? null : parseInt(wattage) || null;
        
        // 确保pcie_connector被正确处理为字符串
        let parsedPcieConnector = null;
        if (pcie_connector !== undefined && pcie_connector !== null && pcie_connector !== '') {
            parsedPcieConnector = String(pcie_connector);  // 强制转换为字符串
        }
        console.log('处理后的parsedPcieConnector值:', parsedPcieConnector);
        
        // 处理sata_count，可能包含"个"字
        let parsedSataCount;
        if (sata_count === '') {
            parsedSataCount = null;
        } else if (typeof sata_count === 'string' && sata_count.includes('个')) {
            // 如果包含"个"字，提取数字部分
            const numericPart = sata_count.replace(/[^0-9]/g, '');
            parsedSataCount = numericPart ? parseInt(numericPart) : null;
            console.log('提取sata_count数字部分:', numericPart, '结果:', parsedSataCount);
        } else {
            // 尝试直接解析为整数
            parsedSataCount = parseInt(sata_count) || null;
        }
        const parsedFanSize = fan_size === '' ? null : parseInt(fan_size) || null;
        const parsedPhysicalLength = physical_length === '' ? null : parseInt(physical_length) || null;
        const parsedWarranty = warranty === '' ? null : parseInt(warranty) || null;
        const parsedPrice = price === '' ? null : parseFloat(price) || null;
        
        // 处理图片URL
        let image_url = null;
        if (req.file) {
            // 检查是否为图片类型
            if (req.file.mimetype.startsWith('image/')) {
                // 转换为WebP格式
                const originalPath = req.file.path;
                const fileNameWithoutExt = path.basename(req.file.filename, path.extname(req.file.filename));
                const webpFilename = `${fileNameWithoutExt}.webp`;
                const webpPath = path.join(path.dirname(originalPath), webpFilename);
                
                try {
                    // 使用sharp转换为WebP格式
                    await sharp(originalPath)
                        .webp({ quality: 100, lossless: true }) // 无损压缩，只转换格式
                        .toFile(webpPath);
                    
                    // 删除原始文件
                    fs.unlinkSync(originalPath);
                    
                    // 更新图片URL为WebP格式
                    image_url = `/uploads/psu/${webpFilename}`;
                    console.log(`图片已转换为WebP格式: ${image_url}`);
                } catch (err) {
                    console.error('WebP转换失败:', err);
                    // 如果转换失败，保留原始文件
                    image_url = `/uploads/psu/${req.file.filename}`;
                }
            } else {
                image_url = `/uploads/psu/${req.file.filename}`;
            }
        } else if (use_default_image === 'true') {
            image_url = '/images/default-psu.png';
        }
        
        // 插入数据
        const insertSql = `
            INSERT INTO psus (
                brand, model, wattage, psu_length, efficiency, modular,
                pcie_connector, sata_count, cpu_connector, fan_size,
                physical_length, warranty, price, notes, image_url
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;
        
        const values = [
            brand, model, parsedWattage, psu_length, efficiency, modular,
            parsedPcieConnector, parsedSataCount, cpu_connector, parsedFanSize,
            parsedPhysicalLength, parsedWarranty, parsedPrice, notes, image_url
        ];
        
        console.log('执行SQL:', insertSql);
        console.log('SQL参数:', values);
        
        const [result] = await db.query(insertSql, values);
        
        res.status(201).json({
            message: '电源添加成功',
            id: result.insertId
        });
    } catch (error) {
        console.error('添加电源失败:', error);
        res.status(500).json({ error: '添加电源失败: ' + error.message });
    }
});

// 更新电源
router.put('/:id', psuUpload.single('image'), async (req, res) => {
    try {
        const psuId = req.params.id;
        
        // 检查电源是否存在
        const [existingPsus] = await db.query('SELECT * FROM psus WHERE id = ?', [psuId]);
        
        if (existingPsus.length === 0) {
            return res.status(404).json({ error: '未找到电源' });
        }
        
        console.log('更新电源请求体:', req.body);
        console.log('上传文件:', req.file);
        
        const {
            brand,
            model,
            wattage,
            psu_length,
            efficiency,
            modular,
            pcie_connector,
            sata_count,
            cpu_connector,
            fan_size,
            physical_length,
            warranty,
            price,
            notes,
            use_default_image
        } = req.body;
        
        // 详细记录pcie_connector的值
        console.log('更新操作 - 原始pcie_connector值:', pcie_connector);
        console.log('更新操作 - pcie_connector类型:', typeof pcie_connector);
        
        // 安全解析数字字段
        const parsedWattage = wattage === '' ? null : parseInt(wattage) || null;
        
        // 确保pcie_connector被正确处理为字符串
        let parsedPcieConnector = null;
        if (pcie_connector !== undefined && pcie_connector !== null && pcie_connector !== '') {
            parsedPcieConnector = String(pcie_connector);  // 强制转换为字符串
        }
        console.log('更新操作 - 处理后的parsedPcieConnector值:', parsedPcieConnector);
        
        // 处理sata_count，可能包含"个"字
        let parsedSataCount;
        if (sata_count === '') {
            parsedSataCount = null;
        } else if (typeof sata_count === 'string' && sata_count.includes('个')) {
            // 如果包含"个"字，提取数字部分
            const numericPart = sata_count.replace(/[^0-9]/g, '');
            parsedSataCount = numericPart ? parseInt(numericPart) : null;
            console.log('提取sata_count数字部分:', numericPart, '结果:', parsedSataCount);
        } else {
            // 尝试直接解析为整数
            parsedSataCount = parseInt(sata_count) || null;
        }
        const parsedFanSize = fan_size === '' ? null : parseInt(fan_size) || null;
        const parsedPhysicalLength = physical_length === '' ? null : parseInt(physical_length) || null;
        const parsedWarranty = warranty === '' ? null : parseInt(warranty) || null;
        const parsedPrice = price === '' ? null : parseFloat(price) || null;
        
        // 处理图片URL
        let image_url = existingPsus[0].image_url;
        if (req.file) {
            // 删除旧图片
            if (existingPsus[0].image_url && existingPsus[0].image_url.startsWith('/uploads/')) {
                const oldImagePath = path.join(__dirname, '../public', existingPsus[0].image_url);
                if (fs.existsSync(oldImagePath)) {
                    fs.unlinkSync(oldImagePath);
                    console.log(`已删除旧图片: ${oldImagePath}`);
                }
            }
            
            // 检查是否为图片类型并转换为WebP
            if (req.file.mimetype.startsWith('image/')) {
                // 转换为WebP格式
                const originalPath = req.file.path;
                const fileNameWithoutExt = path.basename(req.file.filename, path.extname(req.file.filename));
                const webpFilename = `${fileNameWithoutExt}.webp`;
                const webpPath = path.join(path.dirname(originalPath), webpFilename);
                
                try {
                    // 使用sharp转换为WebP格式
                    await sharp(originalPath)
                        .webp({ quality: 100, lossless: true }) // 无损压缩，只转换格式
                        .toFile(webpPath);
                    
                    // 删除原始文件
                    fs.unlinkSync(originalPath);
                    
                    // 更新图片URL为WebP格式
                    image_url = `/uploads/psu/${webpFilename}`;
                    console.log(`图片已转换为WebP格式: ${image_url}`);
                } catch (err) {
                    console.error('WebP转换失败:', err);
                    // 如果转换失败，保留原始文件
                    image_url = `/uploads/psu/${req.file.filename}`;
                }
            } else {
                image_url = `/uploads/psu/${req.file.filename}`;
            }
        } else if (use_default_image === 'true') {
            image_url = '/images/default-psu.png';
        }
        
        // 更新数据
        const updateSql = `
            UPDATE psus SET
                brand = ?, model = ?, wattage = ?, psu_length = ?,
                efficiency = ?, modular = ?, pcie_connector = ?,
                sata_count = ?, cpu_connector = ?, fan_size = ?,
                physical_length = ?, warranty = ?, price = ?, notes = ?, image_url = ?
            WHERE id = ?
        `;
        
        const values = [
            brand, model, parsedWattage, psu_length,
            efficiency, modular, parsedPcieConnector,
            parsedSataCount, cpu_connector, parsedFanSize,
            parsedPhysicalLength, parsedWarranty, parsedPrice, notes, image_url,
            psuId
        ];
        
        console.log('执行SQL:', updateSql);
        console.log('SQL参数:', values);
        
        await db.query(updateSql, values);
        
        res.json({
            message: '电源更新成功',
            id: psuId
        });
    } catch (error) {
        console.error('更新电源失败:', error);
        res.status(500).json({ error: '更新电源失败: ' + error.message });
    }
});

// 删除电源
router.delete('/:id', async (req, res) => {
    try {
        const psuId = req.params.id;
        
        // 检查电源是否存在
        const [existingPsus] = await db.query('SELECT * FROM psus WHERE id = ?', [psuId]);
        
        if (existingPsus.length === 0) {
            return res.status(404).json({ error: '未找到电源' });
        }
        
        // 删除相关的图片文件
        const existingImageUrl = existingPsus[0].image_url;
        if (existingImageUrl && existingImageUrl.startsWith('/uploads/')) {
            const imagePath = path.join(__dirname, '../public', existingImageUrl);
            if (fs.existsSync(imagePath)) {
                fs.unlinkSync(imagePath);
                console.log(`已删除图片文件: ${imagePath}`);
            }
        }
        
        // 从数据库删除
        await db.query('DELETE FROM psus WHERE id = ?', [psuId]);
        
        res.json({ message: '电源删除成功' });
    } catch (error) {
        console.error('删除电源失败:', error);
        res.status(500).json({ error: '删除电源失败' });
    }
});

module.exports = router; 