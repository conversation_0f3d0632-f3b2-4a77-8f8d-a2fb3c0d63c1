const express = require('express');
const router = express.Router();
const db = require('../config/db');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const sharp = require('sharp');

// 配置multer用于图片上传
const caseStorage = multer.diskStorage({
    destination: function (req, file, cb) {
        const uploadDir = path.join(__dirname, '..', 'public', 'uploads', 'case');
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const fileExtension = path.extname(file.originalname);
        cb(null, 'case-' + uniqueSuffix + fileExtension);
    }
});

const caseUpload = multer({
    storage: caseStorage,
    limits: { fileSize: 5 * 1024 * 1024 }, // 5MB
    fileFilter: function (req, file, cb) {
        const filetypes = /jpeg|jpg|png|gif/;
        const mimetype = filetypes.test(file.mimetype);
        const extname = filetypes.test(path.extname(file.originalname).toLowerCase());

        if (mimetype && extname) {
            return cb(null, true);
        }
        cb(new Error('只允许上传JPG, PNG, GIF格式的图片'));
    }
});

// 获取所有机箱 (带分页和筛选)
router.get('/', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const offset = (page - 1) * limit;
        const search = req.query.search || '';
        const brand = req.query.brand || '';
        const formFactor = req.query.formFactor || '';

        // 构建基础查询
        let countQuery = 'SELECT COUNT(*) as total FROM cases WHERE 1=1';
        let dataQuery = 'SELECT * FROM cases WHERE 1=1';
        let queryParams = [];

        // 添加搜索条件（如果提供）
        if (search) {
            const searchCondition = `
                AND (
                    id LIKE ? OR
                    brand LIKE ? OR
                    model LIKE ? OR
                    form_factor LIKE ? OR
                    dimensions LIKE ? OR
                    weight LIKE ? OR
                    material LIKE ? OR
                    color LIKE ? OR
                    psu_shroud LIKE ? OR
                    side_panel LIKE ? OR
                    max_gpu_length LIKE ? OR
                    max_cpu_cooler_height LIKE ? OR
                    expansion_slots LIKE ? OR
                    fan_supports LIKE ? OR
                    drive_capacity LIKE ? OR
                    front_io LIKE ? OR
                    included_fans LIKE ? OR
                    price LIKE ? OR
                    notes LIKE ? OR
                    image_url LIKE ? OR
                    created_at LIKE ? OR
                    updated_at LIKE ? OR
                    radiator_support LIKE ?
                )`;
            countQuery += searchCondition;
            dataQuery += searchCondition;
            const searchParam = `%${search}%`;
            for (let i = 0; i < 23; i++) {
                queryParams.push(searchParam);
            }
        }

        // 添加品牌筛选（如果提供）
        if (brand) {
            countQuery += ' AND brand = ?';
            dataQuery += ' AND brand = ?';
            queryParams.push(brand);
        }

        // 添加主板规格筛选（如果提供）
        if (formFactor) {
            countQuery += ' AND form_factor = ?';
            dataQuery += ' AND form_factor = ?';
            queryParams.push(formFactor);
        }

        // 添加分页到数据查询
        dataQuery += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
        const dataQueryParams = [...queryParams, limit, offset];

        // 执行计数查询
        const [countResult] = await db.query(countQuery, queryParams);
        const totalCount = countResult[0].total;

        // 执行数据查询
        const [cases] = await db.query(dataQuery, dataQueryParams);

        res.json({
            total: totalCount,
            page,
            limit,
            data: cases
        });
    } catch (error) {
        console.error('Error fetching cases:', error);
        res.status(500).json({ error: 'Failed to fetch cases' });
    }
});

// 根据ID获取机箱
router.get('/:id', async (req, res) => {
    try {
        const [results] = await db.query('SELECT * FROM cases WHERE id = ?', [req.params.id]);
        
        if (results.length === 0) {
            return res.status(404).json({ error: 'Case not found' });
        }
        
        res.json(results[0]);
    } catch (error) {
        console.error('Error fetching case:', error);
        res.status(500).json({ error: 'Failed to fetch case' });
    }
});

// 创建机箱
router.post('/', caseUpload.single('image'), async (req, res) => {
    try {
        const {
            model,
            brand,
            form_factor,
            dimensions,
            color,
            material,
            weight,
            drive_capacity,
            expansion_slots,
            fan_supports,
            radiator_support,
            max_gpu_length,
            max_cpu_cooler_height,
            front_io,
            included_fans,
            price,
            notes,
            psu_shroud,
            side_panel
        } = req.body;

        // 处理空字段为null
        const parsedWeight = weight === '' ? null : parseFloat(weight) || null;
        const parsedExpansionSlots = expansion_slots === '' ? null : parseInt(expansion_slots) || null;
        const parsedMaxGpuLength = max_gpu_length === '' ? null : parseInt(max_gpu_length) || null;
        const parsedMaxCpuCoolerHeight = max_cpu_cooler_height === '' ? null : parseInt(max_cpu_cooler_height) || null;
        const parsedPrice = price === '' ? null : parseFloat(price) || null;
        const parsedPsuShroud = psu_shroud === 'true' ? 1 : (psu_shroud === 'false' ? 0 : null);
        const finalSidePanel = side_panel || null;
        const finalFanSupports = fan_supports || null;
        const finalRadiatorSupport = radiator_support || null;

        // 处理图片
        let image_url = null;
        if (req.file) {
            // 获取原始文件路径
            const filePath = req.file.path;
            const filename = path.basename(filePath);
            
            // 创建WebP转换后的文件名
            const webpFilename = path.parse(filename).name + '.webp';
            const webpPath = path.join(path.dirname(filePath), webpFilename);
            
            // 使用Sharp将图片转换为WebP格式
            await sharp(filePath)
                .webp({ quality: 100, lossless: true })
                .toFile(webpPath);
                
            // 删除原始文件
            fs.unlinkSync(filePath);
            
            // 设置图片URL
            image_url = `/uploads/case/${webpFilename}`;
        }

        const [result] = await db.query(
            `INSERT INTO cases (
                model, 
                brand, 
                form_factor, 
                dimensions, 
                color,
                material, 
                weight, 
                drive_capacity, 
                expansion_slots, 
                fan_supports, 
                radiator_support, 
                max_gpu_length, 
                max_cpu_cooler_height, 
                front_io,
                included_fans, 
                price, 
                notes, 
                image_url,
                psu_shroud,
                side_panel
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [
                model, 
                brand, 
                form_factor, 
                dimensions, 
                color,
                material, 
                parsedWeight, 
                drive_capacity, 
                parsedExpansionSlots, 
                finalFanSupports, 
                finalRadiatorSupport, 
                parsedMaxGpuLength, 
                parsedMaxCpuCoolerHeight, 
                front_io,
                included_fans, 
                parsedPrice, 
                notes, 
                image_url,
                parsedPsuShroud,
                finalSidePanel
            ]
        );

        res.status(201).json({
            message: '机箱添加成功',
            id: result.insertId
        });
    } catch (error) {
        console.error('Error adding case:', error);
        res.status(500).json({ error: 'Failed to add case: ' + error.message });
    }
});

// 更新机箱
router.put('/:id', caseUpload.single('image'), async (req, res) => {
    try {
        const caseId = req.params.id;
        
        // 检查机箱是否存在
        const [existingCase] = await db.query('SELECT * FROM cases WHERE id = ?', [caseId]);
        
        if (existingCase.length === 0) {
            return res.status(404).json({ error: 'Case not found' });
        }
        
        const {
            model,
            brand,
            form_factor,
            dimensions,
            color,
            material,
            weight,
            drive_capacity,
            expansion_slots,
            fan_supports,
            radiator_support,
            max_gpu_length,
            max_cpu_cooler_height,
            front_io,
            included_fans,
            price,
            notes,
            psu_shroud,
            side_panel
        } = req.body;

        // 处理空字段为null
        const parsedWeight = weight === '' ? null : parseFloat(weight) || null;
        const parsedExpansionSlots = expansion_slots === '' ? null : parseInt(expansion_slots) || null;
        const parsedMaxGpuLength = max_gpu_length === '' ? null : parseInt(max_gpu_length) || null;
        const parsedMaxCpuCoolerHeight = max_cpu_cooler_height === '' ? null : parseInt(max_cpu_cooler_height) || null;
        const parsedPrice = price === '' ? null : parseFloat(price) || null;
        const parsedPsuShroud = psu_shroud === 'true' ? 1 : (psu_shroud === 'false' ? 0 : null);
        const finalSidePanel = side_panel || null;
        const finalFanSupports = fan_supports || null;
        const finalRadiatorSupport = radiator_support || null;

        let image_url = existingCase[0].image_url;
        if (req.file) {
            // 获取原始文件路径
            const filePath = req.file.path;
            const filename = path.basename(filePath);
            
            // 创建WebP转换后的文件名
            const webpFilename = path.parse(filename).name + '.webp';
            const webpPath = path.join(path.dirname(filePath), webpFilename);
            
            // 使用Sharp将图片转换为WebP格式
            await sharp(filePath)
                .webp({ quality: 100, lossless: true })
                .toFile(webpPath);
                
            // 删除原始文件
            fs.unlinkSync(filePath);
            
            // 设置图片URL
            image_url = `/uploads/case/${webpFilename}`;

            // 删除旧图片
            if (existingCase[0].image_url) {
                const oldImagePath = path.join(__dirname, '..', 'public', existingCase[0].image_url);
                if (fs.existsSync(oldImagePath)) {
                    fs.unlinkSync(oldImagePath);
                }
            }
        }

        const [result] = await db.query(
            `UPDATE cases SET
                model = ?, 
                brand = ?, 
                form_factor = ?, 
                dimensions = ?, 
                color = ?,
                material = ?, 
                weight = ?, 
                drive_capacity = ?, 
                expansion_slots = ?, 
                fan_supports = ?, 
                radiator_support = ?, 
                max_gpu_length = ?, 
                max_cpu_cooler_height = ?, 
                front_io = ?, 
                included_fans = ?, 
                price = ?, 
                notes = ?, 
                image_url = ?,
                psu_shroud = ?,
                side_panel = ?
            WHERE id = ?`,
            [
                model, 
                brand, 
                form_factor, 
                dimensions, 
                color,
                material, 
                parsedWeight, 
                drive_capacity, 
                parsedExpansionSlots, 
                finalFanSupports, 
                finalRadiatorSupport, 
                parsedMaxGpuLength, 
                parsedMaxCpuCoolerHeight, 
                front_io, 
                included_fans, 
                parsedPrice, 
                notes, 
                image_url,
                parsedPsuShroud,
                finalSidePanel,
                caseId
            ]
        );

        res.json({
            message: '机箱更新成功',
            id: caseId
        });
    } catch (error) {
        console.error('Error updating case:', error);
        res.status(500).json({ error: 'Failed to update case' });
    }
});

// 删除机箱
router.delete('/:id', async (req, res) => {
    try {
        const caseId = req.params.id;
        
        // 检查机箱是否存在并获取图片路径
        const [existingCase] = await db.query('SELECT * FROM cases WHERE id = ?', [caseId]);
        
        if (existingCase.length === 0) {
            return res.status(404).json({ error: 'Case not found' });
        }
        
        // 删除机箱记录
        await db.query('DELETE FROM cases WHERE id = ?', [caseId]);
        
        // 如果存在图片则删除
        if (existingCase[0].image_url) {
            const imagePath = path.join(__dirname, '..', 'public', existingCase[0].image_url);
            if (fs.existsSync(imagePath)) {
                fs.unlinkSync(imagePath);
            }
        }
        
        res.json({ message: '机箱删除成功' });
    } catch (error) {
        console.error('Error deleting case:', error);
        res.status(500).json({ error: 'Failed to delete case' });
    }
});

module.exports = router;
