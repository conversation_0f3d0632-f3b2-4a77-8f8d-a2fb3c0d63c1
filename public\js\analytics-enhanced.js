// 内联JavaScript，避免模块冲突
(function () {
    'use strict';

    // 页面级变量
    let currentUser = null;
    let charts = {};
    let isAdmin = false;
    let isDarkTheme = false;

    // 主题配置
    const themes = {
        light: {
            backgroundColor: '#ffffff',
            textColor: '#1f2937',
            gridColor: '#e5e7eb',
            tooltipBg: '#ffffff',
            tooltipBorder: '#e5e7eb'
        },
        dark: {
            backgroundColor: '#1f2937',
            textColor: '#f9fafb',
            gridColor: '#374151',
            tooltipBg: '#374151',
            tooltipBorder: '#4b5563'
        }
    };

    // 获取存储的token
    function getToken() {
        return localStorage.getItem('token') || sessionStorage.getItem('token');
    }

    // 发送带认证的请求
    async function sendAuthenticatedRequest(url, method = 'GET', data = null) {
        const token = getToken();
        if (!token) {
            throw new Error('未找到认证token');
        }

        const options = {
            method: method,
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        };

        if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
            options.body = JSON.stringify(data);
        }

        const response = await fetch(url, options);

        if (response.status === 401) {
            localStorage.removeItem('token');
            sessionStorage.removeItem('token');
            window.location.href = '/login.html?redirect=' + encodeURIComponent(window.location.pathname);
            throw new Error('认证失败，请重新登录');
        }

        const result = await response.json();

        if (!response.ok) {
            throw new Error(result.message || `请求失败: ${response.status}`);
        }

        return result;
    }

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', async function () {
        try {
            // 初始化主题
            initializeTheme();

            // 检查用户身份验证
            await checkAuthentication();

            // 初始化页面
            await initializePage();

            // 绑定事件监听器
            bindEventListeners();

            // 加载初始数据
            await loadAnalyticsData();

        } catch (error) {
            console.error('页面初始化失败:', error);
            showError('页面初始化失败，请刷新重试');
        }
    });

    // 初始化主题
    function initializeTheme() {
        const savedTheme = localStorage.getItem('theme');
        isDarkTheme = savedTheme === 'dark' || (!savedTheme && window.matchMedia('(prefers-color-scheme: dark)').matches);

        updateTheme();
    }

    // 更新主题
    function updateTheme() {
        const body = document.body;
        const themeIcon = document.getElementById('themeIcon');

        if (isDarkTheme) {
            body.setAttribute('data-theme', 'dark');
            themeIcon.className = 'fas fa-sun';
        } else {
            body.removeAttribute('data-theme');
            themeIcon.className = 'fas fa-moon';
        }

        localStorage.setItem('theme', isDarkTheme ? 'dark' : 'light');

        // 更新所有图表的主题
        updateChartsTheme();
    }

    // 切换主题
    function toggleTheme() {
        isDarkTheme = !isDarkTheme;
        updateTheme();
    }

    // 更新图表主题
    function updateChartsTheme() {
        const currentTheme = isDarkTheme ? themes.dark : themes.light;

        Object.keys(charts).forEach(chartKey => {
            const chart = charts[chartKey];
            if (chart && chart.options) {
                // 更新图表主题配置
                chart.options.plugins = chart.options.plugins || {};
                chart.options.plugins.legend = chart.options.plugins.legend || {};
                chart.options.plugins.legend.labels = chart.options.plugins.legend.labels || {};
                chart.options.plugins.legend.labels.color = currentTheme.textColor;

                chart.options.scales = chart.options.scales || {};
                if (chart.options.scales.x) {
                    chart.options.scales.x.ticks = chart.options.scales.x.ticks || {};
                    chart.options.scales.x.ticks.color = currentTheme.textColor;
                    chart.options.scales.x.grid = chart.options.scales.x.grid || {};
                    chart.options.scales.x.grid.color = currentTheme.gridColor;
                }
                if (chart.options.scales.y) {
                    chart.options.scales.y.ticks = chart.options.scales.y.ticks || {};
                    chart.options.scales.y.ticks.color = currentTheme.textColor;
                    chart.options.scales.y.grid = chart.options.scales.y.grid || {};
                    chart.options.scales.y.grid.color = currentTheme.gridColor;
                }

                chart.update();
            }
        });
    }

    // 检查用户身份验证
    async function checkAuthentication() {
        const token = getToken();
        if (!token) {
            showAccessDenied('未登录', '请先登录以访问数据分析页面');
            setTimeout(() => {
                window.location.href = '/login.html?redirect=' + encodeURIComponent(window.location.pathname);
            }, 3000);
            return;
        }

        try {
            const response = await fetch('/api/validate-token', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (!response.ok) {
                throw new Error('Token验证失败');
            }

            const data = await response.json();
            if (!data.valid) {
                throw new Error('Token无效');
            }

            currentUser = data.user;
            isAdmin = currentUser.role === 'admin';

            // 检查管理员权限
            if (!isAdmin) {
                showAccessDenied('权限不足', '只有管理员才能访问数据分析页面');
                setTimeout(() => {
                    window.location.href = '/price.html';
                }, 3000);
                return;
            }

            // 显示用户信息
            displayUserInfo();

        } catch (error) {
            console.error('身份验证失败:', error);
            localStorage.removeItem('token');
            sessionStorage.removeItem('token');
            showAccessDenied('认证失败', '身份验证失败，请重新登录');
            setTimeout(() => {
                window.location.href = '/login.html?redirect=' + encodeURIComponent(window.location.pathname);
            }, 3000);
        }
    }

    // 显示用户信息
    function displayUserInfo() {
        const userInfo = document.getElementById('userInfo');
        const userBadge = document.getElementById('userBadge');
        const userName = document.getElementById('userName');

        if (userInfo && userBadge && userName) {
            userName.textContent = currentUser.username;

            if (isAdmin) {
                userBadge.classList.add('admin-badge');
                userName.innerHTML = `${currentUser.username} <i class="fas fa-crown ml-1"></i>`;
            }

            userInfo.classList.remove('hidden');
        }
    }

    // 初始化页面
    async function initializePage() {
        // 如果是管理员，显示管理员专用功能
        if (isAdmin) {
            document.getElementById('userSelectContainer').classList.remove('hidden');
            document.getElementById('adminControls').classList.remove('hidden');
            document.getElementById('adminSection').classList.remove('hidden');

            // 加载用户列表
            await loadUserList();
        }

        // 初始化时间滑轨
        initializeTimeSlider();
    }

    // 初始化时间滑轨
    function initializeTimeSlider() {
        const slider = document.getElementById('timeSlider');
        const startDate = document.getElementById('startDate');
        const endDate = document.getElementById('endDate');

        function updateDateDisplay() {
            const days = parseInt(slider.value);
            const end = new Date();
            const start = new Date();
            start.setDate(start.getDate() - days);

            startDate.textContent = start.toLocaleDateString('zh-CN');
            endDate.textContent = end.toLocaleDateString('zh-CN');
        }

        updateDateDisplay();

        slider.addEventListener('input', function () {
            updateDateDisplay();

            // 根据滑轨值更新时间范围选择
            const days = parseInt(this.value);
            const timeRange = document.getElementById('timeRange');

            if (days <= 7) {
                timeRange.value = '7d';
            } else if (days <= 30) {
                timeRange.value = '30d';
            } else if (days <= 90) {
                timeRange.value = '90d';
            } else {
                timeRange.value = 'all';
            }

            // 延迟加载数据，避免频繁请求
            clearTimeout(window.sliderTimeout);
            window.sliderTimeout = setTimeout(() => {
                loadAnalyticsData();
            }, 500);
        });
    }

    // 绑定事件监听器
    function bindEventListeners() {
        // 主题切换
        document.getElementById('themeToggle').addEventListener('click', toggleTheme);

        // 时间范围变化
        document.getElementById('timeRange').addEventListener('change', function () {
            // 同步更新滑轨
            const slider = document.getElementById('timeSlider');
            switch (this.value) {
                case '7d': slider.value = 7; break;
                case '30d': slider.value = 30; break;
                case '90d': slider.value = 90; break;
                case 'all': slider.value = 365; break;
            }

            // 更新日期显示
            const event = new Event('input');
            slider.dispatchEvent(event);

            loadAnalyticsData();
        });

        // 用户选择变化 (仅管理员)
        const userSelect = document.getElementById('userSelect');
        if (userSelect) {
            userSelect.addEventListener('change', loadAnalyticsData);
        }

        // 刷新按钮
        document.getElementById('refreshBtn').addEventListener('click', () => {
            loadAnalyticsData(true);
        });

        // 清除缓存按钮 (仅管理员)
        if (isAdmin) {
            const clearCacheBtn = document.getElementById('clearCacheBtn');
            if (clearCacheBtn) {
                clearCacheBtn.addEventListener('click', clearCache);
            }
        }

        // 图表控制
        document.getElementById('refreshChart').addEventListener('click', () => refreshChart());
        document.getElementById('toggleAnimation').addEventListener('click', () => toggleChartAnimation());
    }

    // 刷新图表
    function refreshChart() {
        try {
            loadAnalyticsData(true); // 强制刷新数据
            showSuccess('图表已刷新');
        } catch (error) {
            console.error('刷新图表失败:', error);
            showError('刷新图表失败');
        }
    }

    // 切换图表动画
    function toggleChartAnimation() {
        const button = document.getElementById('toggleAnimation');
        const icon = button.querySelector('i');

        try {
            // 检查当前动画状态（基于按钮图标）
            const isCurrentlyDisabled = icon.classList.contains('fa-play');

            Object.keys(charts).forEach(chartKey => {
                const chart = charts[chartKey];
                if (chart && chart.options) {
                    if (isCurrentlyDisabled) {
                        // 启用动画 - 根据图表类型设置不同的动画
                        if (chartKey === 'activityTrend') {
                            // 折线图动画
                            chart.options.animation = {
                                duration: 1500,
                                easing: 'easeInOutQuart',
                                delay: (context) => context.dataIndex * 100
                            };
                            chart.options.animations = {
                                y: { from: (ctx) => ctx.chart.scales.y.getPixelForValue(0), duration: 1500, easing: 'easeOutBounce' },
                                opacity: { from: 0, to: 1, duration: 1000 }
                            };
                        } else if (chartKey === 'operationType') {
                            // 饼图动画
                            chart.options.animation = { duration: 2000, easing: 'easeInOutQuart' };
                            chart.options.animations = {
                                radius: { from: 0, to: '100%', duration: 1500, easing: 'easeOutElastic' },
                                rotation: { from: -Math.PI, to: 0, duration: 2000, easing: 'easeInOutQuart' }
                            };
                        } else {
                            // 柱状图动画
                            chart.options.animation = {
                                duration: 1500,
                                easing: 'easeInOutQuart',
                                delay: (context) => context.dataIndex * 100
                            };
                            chart.options.animations = {
                                y: { from: (ctx) => ctx.chart.scales.y.getPixelForValue(0), duration: 1200, easing: 'easeOutBounce' }
                            };
                        }
                    } else {
                        // 禁用动画
                        chart.options.animation = false;
                        chart.options.animations = {};
                    }
                    chart.update('none'); // 使用 'none' 模式立即更新，不使用动画
                }
            });

            // 更新按钮图标和状态
            if (isCurrentlyDisabled) {
                // 启用动画
                icon.className = 'fas fa-pause';
                button.title = '禁用动画';
                showSuccess('图表动画已启用');
            } else {
                // 禁用动画
                icon.className = 'fas fa-play';
                button.title = '启用动画';
                showSuccess('图表动画已禁用');
            }
        } catch (error) {
            console.error('切换动画失败:', error);
            showError('切换动画失败');
        }
    }

    // 加载用户列表 (仅管理员)
    async function loadUserList() {
        if (!isAdmin) return;

        try {
            const response = await sendAuthenticatedRequest('/api/analytics/all-users-overview');
            if (response.success) {
                const userSelect = document.getElementById('userSelect');
                userSelect.innerHTML = '<option value="">所有用户</option>';

                response.data.userStats.forEach(user => {
                    const option = document.createElement('option');
                    option.value = user.id;
                    option.textContent = `${user.username} (${user.role})`;
                    userSelect.appendChild(option);
                });
            }
        } catch (error) {
            console.error('加载用户列表失败:', error);
        }
    }

    // 加载分析数据
    async function loadAnalyticsData(forceRefresh = false) {
        showLoading(true);
        clearErrors();

        try {
            const timeRange = document.getElementById('timeRange').value;
            const userSelect = document.getElementById('userSelect');
            const selectedUserId = isAdmin && userSelect ? userSelect.value : null;

            // 并行加载多个数据源
            const promises = [
                loadUserStats(timeRange, selectedUserId),
                loadActivityTrends(timeRange, selectedUserId),
                loadOperationHabits(timeRange, selectedUserId)
            ];

            if (isAdmin && !selectedUserId) {
                promises.push(loadAllUsersOverview(timeRange));
            }

            const results = await Promise.allSettled(promises);

            // 检查是否有失败的请求
            const failures = results.filter(result => result.status === 'rejected');
            if (failures.length > 0) {
                console.warn('部分数据加载失败:', failures);
                showError('部分数据加载失败，请检查网络连接');
            }

        } catch (error) {
            console.error('加载分析数据失败:', error);
            showError('数据加载失败: ' + error.message);
        } finally {
            showLoading(false);
        }
    }

    // 加载用户统计数据
    async function loadUserStats(timeRange, userId) {
        try {
            const params = new URLSearchParams({ timeRange });
            if (userId) params.append('userId', userId);

            const response = await sendAuthenticatedRequest(`/api/analytics/user-stats?${params}`);
            if (response.success) {
                displayStatsCards(response.data);
            }
        } catch (error) {
            console.error('加载用户统计失败:', error);
            throw error;
        }
    }

    // 加载活跃度趋势
    async function loadActivityTrends(timeRange, userId) {
        try {
            const params = new URLSearchParams({ timeRange });
            if (userId) params.append('userId', userId);

            const response = await sendAuthenticatedRequest(`/api/analytics/activity-trends?${params}`);
            if (response.success) {
                displayActivityTrendChart(response.data);
                displayTimeDistributionChart(response.data);
            }
        } catch (error) {
            console.error('加载活跃度趋势失败:', error);
            throw error;
        }
    }

    // 加载操作习惯
    async function loadOperationHabits(timeRange, userId) {
        try {
            const params = new URLSearchParams({ timeRange });
            if (userId) params.append('userId', userId);

            const response = await sendAuthenticatedRequest(`/api/analytics/operation-habits?${params}`);
            if (response.success) {
                displayOperationTypeChart(response.data);
                displayAmountDistributionChart(response.data);
            }
        } catch (error) {
            console.error('加载操作习惯失败:', error);
            throw error;
        }
    }

    // 显示统计卡片
    function displayStatsCards(data) {
        const container = document.getElementById('statsCards');

        const cards = [
            {
                title: '价格记录',
                value: data.priceRecords.count,
                icon: 'fas fa-file-invoice-dollar',
                color: 'from-blue-500 to-blue-600'
            },
            {
                title: '今日总金额',
                value: `¥${data.todayStats.totalAmount.toLocaleString()}`,
                icon: 'fas fa-calendar-day',
                color: 'from-red-500 to-red-600'
            },
            {
                title: '总金额',
                value: `¥${data.priceRecords.totalAmount.toLocaleString()}`,
                icon: 'fas fa-coins',
                color: 'from-green-500 to-green-600'
            },
            {
                title: '数据转换',
                value: data.rawDataConversions.count,
                icon: 'fas fa-exchange-alt',
                color: 'from-purple-500 to-purple-600'
            },
            {
                title: '平均金额',
                value: `¥${data.priceRecords.avgAmount.toFixed(2)}`,
                icon: 'fas fa-chart-line',
                color: 'from-orange-500 to-orange-600'
            }
        ];

        container.innerHTML = cards.map(card => `
                    <div class="stat-card bg-gradient-to-r ${card.color}">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="stat-number">${card.value}</div>
                                <div class="stat-label">${card.title}</div>
                            </div>
                            <div class="text-4xl opacity-80">
                                <i class="${card.icon}"></i>
                            </div>
                        </div>
                    </div>
                `).join('');
    }

    // 显示活跃度趋势图
    function displayActivityTrendChart(data) {
        const ctx = document.getElementById('activityTrendChart').getContext('2d');
        const currentTheme = isDarkTheme ? themes.dark : themes.light;

        // 销毁现有图表
        if (charts.activityTrend) {
            charts.activityTrend.destroy();
        }

        // 准备数据
        const dates = data.dailyPriceRecords.map(item => item.date);
        const priceRecords = data.dailyPriceRecords.map(item => item.count);
        const rawDataCounts = data.dailyRawData.map(item => item.count);

        charts.activityTrend = new Chart(ctx, {
            type: 'line',
            data: {
                labels: dates,
                datasets: [
                    {
                        label: '价格记录',
                        data: priceRecords,
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: '数据转换',
                        data: rawDataCounts,
                        borderColor: '#8b5cf6',
                        backgroundColor: 'rgba(139, 92, 246, 0.1)',
                        tension: 0.4,
                        fill: true
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    duration: 1500,
                    easing: 'easeInOutQuart',
                    delay: (context) => {
                        // 为每个数据点添加延迟，创建波浪效果
                        return context.dataIndex * 100;
                    }
                },
                animations: {
                    // 数据点从下往上出现
                    y: {
                        from: (ctx) => ctx.chart.scales.y.getPixelForValue(0),
                        duration: 1500,
                        easing: 'easeOutBounce'
                    },
                    // 透明度渐变
                    opacity: {
                        from: 0,
                        to: 1,
                        duration: 1000
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                plugins: {
                    legend: {
                        labels: {
                            color: currentTheme.textColor,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        backgroundColor: currentTheme.tooltipBg,
                        titleColor: currentTheme.textColor,
                        bodyColor: currentTheme.textColor,
                        borderColor: currentTheme.tooltipBorder,
                        borderWidth: 1
                    },

                },
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            unit: 'day',
                            displayFormats: {
                                day: 'MM-dd'
                            }
                        },
                        ticks: {
                            color: currentTheme.textColor,
                            maxTicksLimit: 10
                        },
                        grid: {
                            color: currentTheme.gridColor
                        }
                    },
                    y: {
                        beginAtZero: true,
                        ticks: {
                            color: currentTheme.textColor
                        },
                        grid: {
                            color: currentTheme.gridColor
                        }
                    }
                }
            }
        });
    }

    // 显示操作类型分布图
    function displayOperationTypeChart(data) {
        const ctx = document.getElementById('operationTypeChart').getContext('2d');
        const currentTheme = isDarkTheme ? themes.dark : themes.light;

        // 销毁现有图表
        if (charts.operationType) {
            charts.operationType.destroy();
        }

        const operationTypes = data.operationTypes || [];
        const labels = operationTypes.map(item => item.operation_type || '未知');
        const counts = operationTypes.map(item => item.count);

        charts.operationType = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: counts,
                    backgroundColor: [
                        '#3b82f6', '#8b5cf6', '#10b981', '#f59e0b',
                        '#ef4444', '#6366f1', '#14b8a6', '#f97316'
                    ],
                    borderWidth: 2,
                    borderColor: currentTheme.backgroundColor
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    duration: 2000,
                    easing: 'easeInOutQuart'
                },
                animations: {
                    // 饼图扇形从中心向外扩展
                    radius: {
                        from: 0,
                        to: '100%',
                        duration: 1500,
                        easing: 'easeOutElastic'
                    },
                    // 旋转动画
                    rotation: {
                        from: -Math.PI,
                        to: 0,
                        duration: 2000,
                        easing: 'easeInOutQuart'
                    }
                },
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: currentTheme.textColor,
                            usePointStyle: true,
                            padding: 20
                        }
                    },
                    tooltip: {
                        backgroundColor: currentTheme.tooltipBg,
                        titleColor: currentTheme.textColor,
                        bodyColor: currentTheme.textColor,
                        borderColor: currentTheme.tooltipBorder,
                        borderWidth: 1
                    }
                }
            }
        });
    }

    // 显示金额分布图
    function displayAmountDistributionChart(data) {
        const ctx = document.getElementById('amountDistributionChart').getContext('2d');
        const currentTheme = isDarkTheme ? themes.dark : themes.light;

        // 销毁现有图表
        if (charts.amountDistribution) {
            charts.amountDistribution.destroy();
        }

        const amountDistribution = data.amountDistribution || [];
        const labels = amountDistribution.map(item => item.amount_range);
        const counts = amountDistribution.map(item => item.count);

        charts.amountDistribution = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: '记录数量',
                    data: counts,
                    backgroundColor: 'rgba(16, 185, 129, 0.8)',
                    borderColor: '#10b981',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    duration: 1500,
                    easing: 'easeInOutQuart',
                    delay: (context) => {
                        // 柱状图从左到右依次出现
                        return context.dataIndex * 200;
                    }
                },
                animations: {
                    // 柱子从底部向上生长
                    y: {
                        from: (ctx) => ctx.chart.scales.y.getPixelForValue(0),
                        duration: 1200,
                        easing: 'easeOutBounce'
                    },
                    // 宽度动画
                    x: {
                        from: (ctx) => ctx.chart.scales.x.getPixelForValue(ctx.dataIndex) + ctx.chart.scales.x.width / 2,
                        duration: 800,
                        easing: 'easeOutQuart'
                    }
                },
                plugins: {
                    legend: {
                        labels: {
                            color: currentTheme.textColor
                        }
                    },
                    tooltip: {
                        backgroundColor: currentTheme.tooltipBg,
                        titleColor: currentTheme.textColor,
                        bodyColor: currentTheme.textColor,
                        borderColor: currentTheme.tooltipBorder,
                        borderWidth: 1
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            color: currentTheme.textColor
                        },
                        grid: {
                            color: currentTheme.gridColor
                        }
                    },
                    y: {
                        beginAtZero: true,
                        ticks: {
                            color: currentTheme.textColor
                        },
                        grid: {
                            color: currentTheme.gridColor
                        }
                    }
                }
            }
        });
    }

    // 显示时间分布图
    function displayTimeDistributionChart(data) {
        const ctx = document.getElementById('timeDistributionChart').getContext('2d');
        const currentTheme = isDarkTheme ? themes.dark : themes.light;

        // 销毁现有图表
        if (charts.timeDistribution) {
            charts.timeDistribution.destroy();
        }

        const hourlyActivity = data.hourlyActivity || [];

        // 创建24小时的完整数据
        const hours = Array.from({ length: 24 }, (_, i) => i);
        const activities = hours.map(hour => {
            const found = hourlyActivity.find(item => item.hour === hour);
            return found ? found.total_operations : 0;
        });

        charts.timeDistribution = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: hours.map(h => `${h}:00`),
                datasets: [{
                    label: '操作次数',
                    data: activities,
                    backgroundColor: 'rgba(245, 158, 11, 0.8)',
                    borderColor: '#f59e0b',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    duration: 2000,
                    easing: 'easeInOutQuart',
                    delay: (context) => {
                        // 24小时柱状图波浪式出现
                        return Math.abs(context.dataIndex - 12) * 50;
                    }
                },
                animations: {
                    // 柱子从底部向上生长
                    y: {
                        from: (ctx) => ctx.chart.scales.y.getPixelForValue(0),
                        duration: 1500,
                        easing: 'easeOutElastic'
                    },
                    // 透明度渐变
                    opacity: {
                        from: 0,
                        to: 0.8,
                        duration: 1000
                    }
                },
                plugins: {
                    legend: {
                        labels: {
                            color: currentTheme.textColor
                        }
                    },
                    tooltip: {
                        backgroundColor: currentTheme.tooltipBg,
                        titleColor: currentTheme.textColor,
                        bodyColor: currentTheme.textColor,
                        borderColor: currentTheme.tooltipBorder,
                        borderWidth: 1
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            color: currentTheme.textColor,
                            maxTicksLimit: 12
                        },
                        grid: {
                            color: currentTheme.gridColor
                        }
                    },
                    y: {
                        beginAtZero: true,
                        ticks: {
                            color: currentTheme.textColor
                        },
                        grid: {
                            color: currentTheme.gridColor
                        }
                    }
                }
            }
        });
    }

    // 工具函数
    function showLoading(show) {
        const indicator = document.getElementById('loadingIndicator');
        if (show) {
            indicator.classList.remove('hidden');
        } else {
            indicator.classList.add('hidden');
        }
    }

    function showError(message) {
        const container = document.getElementById('errorContainer');
        container.innerHTML = `<div class="error-message">${message}</div>`;
    }

    function clearErrors() {
        document.getElementById('errorContainer').innerHTML = '';
    }

    function showSuccess(message) {
        const container = document.getElementById('errorContainer');
        container.innerHTML = `<div class="success-message">${message}</div>`;
        setTimeout(() => {
            container.innerHTML = '';
        }, 3000);
    }

    // 清除缓存 (仅管理员)
    async function clearCache() {
        if (!isAdmin) return;

        try {
            const response = await sendAuthenticatedRequest('/api/analytics/cache', 'DELETE');
            if (response.success) {
                showSuccess('缓存已清除');
                loadAnalyticsData(true);
            }
        } catch (error) {
            console.error('清除缓存失败:', error);
            showError('清除缓存失败: ' + error.message);
        }
    }

    // 显示访问拒绝页面
    function showAccessDenied(title, message) {
        // 隐藏主要内容
        document.body.style.overflow = 'hidden';

        // 创建访问拒绝页面
        const accessDeniedHtml = `
                    <div class="access-denied" id="accessDeniedPage">
                        <div class="access-denied-content">
                            <div class="access-denied-icon">🚫</div>
                            <div class="access-denied-title">${title}</div>
                            <div class="access-denied-message">${message}</div>

                            <div class="access-denied-buttons">
                                <a href="/login.html" class="access-denied-btn primary">
                                    <i class="fas fa-sign-in-alt"></i> 重新登录
                                </a>
                                <a href="/price.html" class="access-denied-btn">
                                    <i class="fas fa-arrow-left"></i> 返回主页
                                </a>
                            </div>

                            <div class="access-denied-countdown">
                                <span id="countdownText">3秒后自动跳转...</span>
                            </div>
                        </div>
                    </div>
                `;

        // 插入到页面
        document.body.insertAdjacentHTML('beforeend', accessDeniedHtml);

        // 倒计时
        let countdown = 3;
        const countdownElement = document.getElementById('countdownText');
        const countdownInterval = setInterval(() => {
            countdown--;
            if (countdown > 0) {
                countdownElement.textContent = `${countdown}秒后自动跳转...`;
            } else {
                countdownElement.textContent = '正在跳转...';
                clearInterval(countdownInterval);
            }
        }, 1000);
    }

    // 加载所有用户概览 (仅管理员)
    async function loadAllUsersOverview(timeRange) {
        if (!isAdmin) return;

        try {
            const params = new URLSearchParams({ timeRange });
            const response = await sendAuthenticatedRequest(`/api/analytics/all-users-overview?${params}`);

            if (response.success) {
                displayUserOverviewTable(response.data);
            }
        } catch (error) {
            console.error('加载用户概览失败:', error);
            throw error;
        }
    }

    // 显示用户概览表格
    function displayUserOverviewTable(data) {
        const container = document.getElementById('userOverviewTable');

        if (!data.userStats || data.userStats.length === 0) {
            container.innerHTML = `
                        <div class="text-center py-8 text-gray-500">
                            <i class="fas fa-users text-4xl mb-4"></i>
                            <p>暂无用户数据</p>
                        </div>
                    `;
            return;
        }

        // 创建表格
        const table = `
                    <div class="mb-6">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                            <div class="stat-card-small" style="border-left: 4px solid #3b82f6;">
                                <div class="stat-label" style="color: #3b82f6;">总用户数</div>
                                <div class="stat-value" style="color: #3b82f6;">${data.systemStats.total_users}</div>
                            </div>
                            <div class="stat-card-small" style="border-left: 4px solid #10b981;">
                                <div class="stat-label" style="color: #10b981;">总价格记录</div>
                                <div class="stat-value" style="color: #10b981;">${data.systemStats.total_price_records}</div>
                            </div>
                            <div class="stat-card-small" style="border-left: 4px solid #8b5cf6;">
                                <div class="stat-label" style="color: #8b5cf6;">总数据转换</div>
                                <div class="stat-value" style="color: #8b5cf6;">${data.systemStats.total_raw_data}</div>
                            </div>
                            <div class="stat-card-small" style="border-left: 4px solid #f59e0b;">
                                <div class="stat-label" style="color: #f59e0b;">系统总金额</div>
                                <div class="stat-value" style="color: #f59e0b;">¥${data.systemStats.total_system_amount.toLocaleString()}</div>
                            </div>
                        </div>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="min-w-full data-table">
                            <thead>
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">用户</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">角色</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">价格记录</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">总金额</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">数据转换</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">价格项目</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">最后活跃</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${data.userStats.map(user => `
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0">
                                                    <div class="user-avatar bg-gradient-to-r from-blue-500 to-purple-500">
                                                        ${user.username.charAt(0).toUpperCase()}
                                                    </div>
                                                </div>
                                                <div class="ml-3">
                                                    <div class="text-sm font-medium">${user.username}</div>
                                                    <div class="text-xs opacity-60">ID: ${user.id}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="role-badge ${user.role === 'admin' ? 'role-admin' : 'role-user'}">
                                                ${user.role === 'admin' ? '管理员' : '用户'}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                                            ${user.price_record_count}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            ¥${user.total_amount.toLocaleString()}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                                            ${user.raw_data_count}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                                            ${user.price_item_count}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm opacity-75">
                                            ${user.last_price_record ? new Date(user.last_price_record).toLocaleDateString('zh-CN') : '无'}
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>

                    ${data.topUsers && data.topUsers.length > 0 ? `
                        <div class="mt-6">
                            <h4 class="text-lg font-semibold mb-4">🏆 最活跃用户</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                                ${data.topUsers.slice(0, 5).map((user, index) => `
                                    <div class="bg-gradient-to-r from-yellow-400 to-orange-500 p-4 rounded-lg text-white">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <div class="text-lg font-bold">#${index + 1}</div>
                                                <div class="text-sm">${user.username}</div>
                                                <div class="text-xs opacity-90">${user.total_operations} 次操作</div>
                                            </div>
                                            <div class="text-2xl">
                                                ${index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : '🏅'}
                                            </div>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    ` : ''}
                `;

        container.innerHTML = table;
    }

})();