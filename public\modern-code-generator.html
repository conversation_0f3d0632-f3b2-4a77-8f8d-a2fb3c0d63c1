<!DOCTYPE html>
<html lang="zh-CN" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>现代化代码生成器 - 电脑配件管理系统</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="/js/modern-code-generator.js"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- 现代化响应式样式 -->
    <link rel="stylesheet" href="/css/modern-responsive.css">
    
    <!-- 主题管理器 -->
    <script src="/js/theme-manager.js"></script>
    
    <!-- 代码生成器样式 -->
    <link rel="stylesheet" href="/css/modern-code-generator.css">

</head>

<body class="h-full bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100">
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="container-responsive">
            <div class="flex justify-between items-center h-16">
                <!-- 左侧标题 -->
                <div class="flex items-center space-x-4">
                    <button onclick="history.back()" 
                            class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors touch-target">
                        <i class="fas fa-arrow-left text-gray-600 dark:text-gray-300"></i>
                    </button>
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-code text-2xl text-blue-600 dark:text-blue-400"></i>
                        <div>
                            <h1 class="text-xl font-bold text-gray-900 dark:text-white">现代化代码生成器</h1>
                            <p class="text-sm text-gray-500 dark:text-gray-400 hidden sm:block">快速生成响应式管理模块</p>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧操作按钮 -->
                <div class="flex items-center space-x-2">
                    <!-- 主题切换按钮 -->
                    <button data-theme-toggle 
                            class="theme-toggle-btn"
                            title="切换主题">
                        <i class="fas fa-moon"></i>
                    </button>
                    
                    <!-- 首页按钮 -->
                    <a href="/pc-components.html" 
                       class="btn btn-primary">
                        <i class="fas fa-home"></i>
                        <span class="hidden sm:inline">首页</span>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="container-responsive py-6">
        <!-- 页面标题和说明 -->
        <div class="text-center mb-8">
            <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                <i class="fas fa-magic text-blue-600 dark:text-blue-400 mr-3"></i>
                现代化代码生成器
            </h2>
            <p class="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
                基于现代化模板系统，自动生成响应式、支持主题切换的管理模块。
                只需配置字段信息，即可生成完整的CRUD功能页面。
            </p>
        </div>
        
        <!-- 功能特性展示 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="card text-center">
                <div class="p-3 bg-blue-100 dark:bg-blue-900 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                    <i class="fas fa-mobile-alt text-2xl text-blue-600 dark:text-blue-400"></i>
                </div>
                <h3 class="text-lg font-semibold mb-2">响应式设计</h3>
                <p class="text-gray-600 dark:text-gray-400">移动端优先，完美适配各种设备</p>
            </div>
            
            <div class="card text-center">
                <div class="p-3 bg-purple-100 dark:bg-purple-900 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                    <i class="fas fa-palette text-2xl text-purple-600 dark:text-purple-400"></i>
                </div>
                <h3 class="text-lg font-semibold mb-2">主题切换</h3>
                <p class="text-gray-600 dark:text-gray-400">支持亮色/深色主题，状态持久化</p>
            </div>
            
            <div class="card text-center">
                <div class="p-3 bg-green-100 dark:bg-green-900 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                    <i class="fas fa-cogs text-2xl text-green-600 dark:text-green-400"></i>
                </div>
                <h3 class="text-lg font-semibold mb-2">动态字段</h3>
                <p class="text-gray-600 dark:text-gray-400">智能字段生成，自动添加标准字段</p>
            </div>
        </div>
        
        <!-- 代码生成器表单 -->
        <div class="grid-responsive">
            <!-- 左侧配置区域 -->
            <div class="space-y-6">
                <!-- 基本信息配置 -->
                <div class="card">
                    <div class="flex items-center space-x-3 mb-6">
                        <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                            <i class="fas fa-info-circle text-blue-600 dark:text-blue-400"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold">基本信息</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400">配置模块的基本信息</p>
                        </div>
                    </div>
                    
                    <form id="generatorForm" class="space-y-4">
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-tag mr-1"></i>中文名称 <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="chineseName" name="chineseName" required
                                   class="form-input" placeholder="例如：显卡、内存条、主板">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-code mr-1"></i>英文名称 <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="englishName" name="englishName" required
                                   class="form-input" placeholder="例如：graphics-card、memory、motherboard">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-database mr-1"></i>数据表名 <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="tableName" name="tableName" required
                                   class="form-input" placeholder="例如：graphics_cards、memory_modules、motherboards">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-icons mr-1"></i>图标类名
                            </label>
                            <div class="relative">
                                <select id="iconClass" name="iconClass" class="form-input pr-10">
                                    <option value="">请选择图标</option>
                                    <optgroup label="硬件组件">
                                        <option value="fas fa-microchip">🔲 fas fa-microchip (CPU/处理器)</option>
                                        <option value="fas fa-memory">📱 fas fa-memory (内存)</option>
                                        <option value="fas fa-hdd">💾 fas fa-hdd (硬盘)</option>
                                        <option value="fas fa-desktop">🖥️ fas fa-desktop (显示器)</option>
                                        <option value="fas fa-keyboard">⌨️ fas fa-keyboard (键盘)</option>
                                        <option value="fas fa-mouse">🖱️ fas fa-mouse (鼠标)</option>
                                        <option value="fas fa-volume-up">🔊 fas fa-volume-up (声卡/音频)</option>
                                        <option value="fas fa-wifi">📶 fas fa-wifi (网卡/无线)</option>
                                        <option value="fas fa-usb">🔌 fas fa-usb (USB设备)</option>
                                        <option value="fas fa-plug">🔌 fas fa-plug (电源)</option>
                                    </optgroup>
                                    <optgroup label="存储设备">
                                        <option value="fas fa-save">💾 fas fa-save (存储)</option>
                                        <option value="fas fa-database">🗄️ fas fa-database (数据库)</option>
                                        <option value="fas fa-server">🖥️ fas fa-server (服务器)</option>
                                        <option value="fas fa-compact-disc">💿 fas fa-compact-disc (光盘)</option>
                                    </optgroup>
                                    <optgroup label="网络设备">
                                        <option value="fas fa-network-wired">🌐 fas fa-network-wired (有线网络)</option>
                                        <option value="fas fa-router">📡 fas fa-router (路由器)</option>
                                        <option value="fas fa-satellite">📡 fas fa-satellite (卫星)</option>
                                        <option value="fas fa-broadcast-tower">📡 fas fa-broadcast-tower (信号塔)</option>
                                    </optgroup>
                                    <optgroup label="外设设备">
                                        <option value="fas fa-print">🖨️ fas fa-print (打印机)</option>
                                        <option value="fas fa-camera">📷 fas fa-camera (摄像头)</option>
                                        <option value="fas fa-headphones">🎧 fas fa-headphones (耳机)</option>
                                        <option value="fas fa-gamepad">🎮 fas fa-gamepad (游戏手柄)</option>
                                    </optgroup>
                                    <optgroup label="通用图标">
                                        <option value="fas fa-cog">⚙️ fas fa-cog (设置/配置)</option>
                                        <option value="fas fa-tools">🔧 fas fa-tools (工具)</option>
                                        <option value="fas fa-box">📦 fas fa-box (包装/产品)</option>
                                        <option value="fas fa-cube">🧊 fas fa-cube (立方体)</option>
                                        <option value="fas fa-cubes">🧊 fas fa-cubes (多个立方体)</option>
                                        <option value="fas fa-th">⚏ fas fa-th (网格)</option>
                                        <option value="fas fa-list">📋 fas fa-list (列表)</option>
                                        <option value="fas fa-tag">🏷️ fas fa-tag (标签)</option>
                                        <option value="fas fa-tags">🏷️ fas fa-tags (多个标签)</option>
                                    </optgroup>
                                </select>
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                    <i id="selectedIcon" class="text-gray-400"></i>
                                </div>
                            </div>
                            <div class="mt-2">
                                <div id="iconPreview" class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                                    <span>选择图标后将在此处显示预览</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-align-left mr-1"></i>页面描述
                            </label>
                            <textarea id="pageDescription" name="pageDescription"
                                      class="form-input resize-none" rows="2"
                                      placeholder="例如：管理显卡型号、性能参数、价格等信息"></textarea>
                        </div>
                    </form>
                </div>
                
                <!-- 字段配置区域 -->
                <div class="card">
                    <div class="flex items-center justify-between mb-6">
                        <div class="flex items-center space-x-3">
                            <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                                <i class="fas fa-list text-green-600 dark:text-green-400"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold">字段配置</h3>
                                <p class="text-sm text-gray-500 dark:text-gray-400">配置业务字段，系统会自动添加标准字段</p>
                            </div>
                        </div>
                        
                        <button type="button" id="addFieldBtn" class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            <span class="hidden sm:inline">添加字段</span>
                        </button>
                    </div>
                    
                    <div id="fieldsContainer" class="space-y-4">
                        <!-- 动态生成的字段配置项 -->
                    </div>
                    
                    <div class="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                        <div class="flex items-start space-x-2">
                            <i class="fas fa-info-circle text-blue-600 dark:text-blue-400 mt-1"></i>
                            <div class="text-sm text-blue-800 dark:text-blue-200">
                                <p class="font-medium mb-1">系统将自动添加以下标准字段：</p>
                                <ul class="list-disc list-inside space-y-1 text-xs">
                                    <li>price (价格) - 数字类型</li>
                                    <li>notes (备注) - 文本类型</li>
                                    <li>image_url (图片URL) - 字符串类型</li>
                                    <li>created_at (创建时间) - 时间戳</li>
                                    <li>updated_at (更新时间) - 时间戳</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 生成按钮 -->
                <div class="card">
                    <button type="button" id="generateBtn" class="btn btn-primary w-full text-lg py-4">
                        <i class="fas fa-magic mr-2"></i>
                        生成现代化模块
                    </button>
                    
                    <div id="progressContainer" class="hidden mt-4">
                        <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
                            <span id="progressText">准备生成...</span>
                            <span id="progressPercent">0%</span>
                        </div>
                        <div class="progress-bar">
                            <div id="progressFill" class="progress-fill" style="width: 0%"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 右侧预览区域 -->
            <div class="space-y-6">
                <!-- 预览卡片 -->
                <div class="card">
                    <div class="flex items-center space-x-3 mb-6">
                        <div class="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                            <i class="fas fa-eye text-purple-600 dark:text-purple-400"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold">实时预览</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400">查看生成的模块效果</p>
                        </div>
                    </div>
                    
                    <div id="previewContainer" class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center">
                        <i class="fas fa-eye-slash text-4xl text-gray-400 dark:text-gray-500 mb-4"></i>
                        <p class="text-gray-500 dark:text-gray-400">配置完成后将显示预览</p>
                    </div>
                </div>
                
                <!-- 生成历史 -->
                <div class="card">
                    <div class="flex items-center space-x-3 mb-6">
                        <div class="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                            <i class="fas fa-history text-yellow-600 dark:text-yellow-400"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold">生成历史</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400">最近生成的模块</p>
                        </div>
                    </div>
                    
                    <div id="historyContainer" class="space-y-3">
                        <div class="text-center text-gray-500 dark:text-gray-400 py-8">
                            <i class="fas fa-clock text-2xl mb-2"></i>
                            <p>暂无生成历史</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Toast 通知容器 -->
    <div id="toastContainer" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <!-- JavaScript -->
    <script src="/js/modern-code-generator-ui.js"></script>
</body>
</html>
