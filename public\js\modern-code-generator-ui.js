/**
 * 现代化代码生成器UI控制器
 * 处理用户界面交互和代码生成流程
 */

class ModernCodeGeneratorUI {
    constructor() {
        this.fields = [];
        this.fieldIdCounter = 0;
        this.isGenerating = false;
        
        // 字段类型配置
        this.fieldTypes = {
            'string': { name: '字符串', icon: 'fas fa-font', placeholder: '文本内容' },
            'integer': { name: '整数', icon: 'fas fa-hashtag', placeholder: '数字' },
            'float': { name: '小数', icon: 'fas fa-calculator', placeholder: '小数' },
            'text': { name: '长文本', icon: 'fas fa-align-left', placeholder: '多行文本' },
            'boolean': { name: '布尔值', icon: 'fas fa-toggle-on', placeholder: '是/否' },
            'date': { name: '日期', icon: 'fas fa-calendar', placeholder: '日期' },
            'datetime': { name: '日期时间', icon: 'fas fa-clock', placeholder: '日期时间' }
        };
        
        this.init();
    }
    
    /**
     * 初始化UI控制器
     */
    init() {
        console.log('初始化现代化代码生成器UI...');
        
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initializeUI());
        } else {
            this.initializeUI();
        }
    }
    
    /**
     * 初始化用户界面
     */
    initializeUI() {
        // 缓存DOM元素
        this.cacheElements();
        
        // 设置事件监听器
        this.setupEventListeners();
        
        // 添加默认字段
        this.addDefaultFields();
        
        // 加载生成历史
        this.loadGenerationHistory();
        
        console.log('现代化代码生成器UI初始化完成');
    }
    
    /**
     * 缓存DOM元素
     */
    cacheElements() {
        this.elements = {
            form: document.getElementById('generatorForm'),
            chineseName: document.getElementById('chineseName'),
            englishName: document.getElementById('englishName'),
            tableName: document.getElementById('tableName'),
            iconClass: document.getElementById('iconClass'),
            pageDescription: document.getElementById('pageDescription'),
            fieldsContainer: document.getElementById('fieldsContainer'),
            addFieldBtn: document.getElementById('addFieldBtn'),
            generateBtn: document.getElementById('generateBtn'),
            progressContainer: document.getElementById('progressContainer'),
            progressText: document.getElementById('progressText'),
            progressPercent: document.getElementById('progressPercent'),
            progressFill: document.getElementById('progressFill'),
            previewContainer: document.getElementById('previewContainer'),
            historyContainer: document.getElementById('historyContainer')
        };
    }
    
    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 基本信息输入自动填充
        this.elements.chineseName.addEventListener('input', () => this.autoFillNames());
        this.elements.englishName.addEventListener('input', () => this.autoFillTableName());

        // 图标选择器
        this.elements.iconClass.addEventListener('change', () => this.updateIconPreview());

        // 添加字段按钮
        this.elements.addFieldBtn.addEventListener('click', () => this.addField());

        // 生成按钮
        this.elements.generateBtn.addEventListener('click', () => this.generateCode());

        // 表单验证
        this.elements.form.addEventListener('input', () => this.validateForm());

        // 键盘快捷键
        document.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));

        // 初始化图标预览
        this.updateIconPreview();
    }

    /**
     * 更新图标预览
     */
    updateIconPreview() {
        const iconClass = this.elements.iconClass.value;
        const selectedIcon = document.getElementById('selectedIcon');
        const iconPreview = document.getElementById('iconPreview');

        if (iconClass) {
            // 更新选择器右侧的图标
            selectedIcon.className = iconClass + ' text-gray-600 dark:text-gray-400';

            // 更新预览区域
            const displayName = this.getIconDisplayName(iconClass);

            iconPreview.innerHTML = `
                <div class="flex items-center space-x-3">
                    <i class="${iconClass} text-xl text-blue-600 dark:text-blue-400"></i>
                    <div>
                        <span class="font-medium text-gray-900 dark:text-white">${displayName}</span>
                        <div class="text-xs text-gray-500 dark:text-gray-400 font-mono">${iconClass}</div>
                    </div>
                </div>
            `;
        } else {
            // 清空预览
            selectedIcon.className = 'text-gray-400';
            iconPreview.innerHTML = '<span class="text-gray-500 dark:text-gray-400">选择图标后将在此处显示预览</span>';
        }

        // 更新主预览区域
        this.updatePreview();
    }

    /**
     * 获取图标显示名称
     */
    getIconDisplayName(iconClass) {
        const iconMap = {
            'fas fa-microchip': 'CPU/处理器',
            'fas fa-memory': '内存',
            'fas fa-hdd': '硬盘',
            'fas fa-desktop': '显示器',
            'fas fa-keyboard': '键盘',
            'fas fa-mouse': '鼠标',
            'fas fa-volume-up': '声卡/音频',
            'fas fa-wifi': '网卡/无线',
            'fas fa-usb': 'USB设备',
            'fas fa-plug': '电源',
            'fas fa-save': '存储',
            'fas fa-database': '数据库',
            'fas fa-server': '服务器',
            'fas fa-compact-disc': '光盘',
            'fas fa-network-wired': '有线网络',
            'fas fa-router': '路由器',
            'fas fa-satellite': '卫星',
            'fas fa-broadcast-tower': '信号塔',
            'fas fa-print': '打印机',
            'fas fa-camera': '摄像头',
            'fas fa-headphones': '耳机',
            'fas fa-gamepad': '游戏手柄',
            'fas fa-cog': '设置/配置',
            'fas fa-tools': '工具',
            'fas fa-box': '包装/产品',
            'fas fa-cube': '立方体',
            'fas fa-cubes': '多个立方体',
            'fas fa-th': '网格',
            'fas fa-list': '列表',
            'fas fa-tag': '标签',
            'fas fa-tags': '多个标签'
        };

        return iconMap[iconClass] || '自定义图标';
    }

    /**
     * 自动填充名称
     */
    autoFillNames() {
        const chineseName = this.elements.chineseName.value.trim();
        if (!chineseName) return;
        
        // 如果英文名称为空，尝试自动生成
        if (!this.elements.englishName.value.trim()) {
            const englishName = this.generateEnglishName(chineseName);
            this.elements.englishName.value = englishName;
            this.autoFillTableName();
        }
        
        // 如果图标类名为空，设置默认图标
        if (!this.elements.iconClass.value.trim()) {
            this.elements.iconClass.value = 'fas fa-microchip';
            this.updateIconPreview();
        }
        
        // 如果页面描述为空，生成默认描述
        if (!this.elements.pageDescription.value.trim()) {
            this.elements.pageDescription.value = `管理${chineseName}的详细信息、规格参数、价格等数据`;
        }
        
        this.updatePreview();
    }
    
    /**
     * 自动填充表名
     */
    autoFillTableName() {
        const englishName = this.elements.englishName.value.trim();
        if (!englishName) return;
        
        if (!this.elements.tableName.value.trim()) {
            // 转换为复数形式的表名
            let tableName = englishName.replace(/-/g, '_');
            if (!tableName.endsWith('s')) {
                tableName += 's';
            }
            this.elements.tableName.value = tableName;
        }
        
        this.updatePreview();
    }
    
    /**
     * 生成英文名称
     */
    generateEnglishName(chineseName) {
        const nameMap = {
            '显卡': 'graphics-card',
            '内存': 'memory',
            '内存条': 'memory-module',
            '主板': 'motherboard',
            '处理器': 'processor',
            'CPU': 'cpu',
            '硬盘': 'hard-drive',
            '固态硬盘': 'ssd',
            '机械硬盘': 'hdd',
            '电源': 'power-supply',
            '机箱': 'case',
            '散热器': 'cooler',
            '风扇': 'fan',
            '声卡': 'sound-card',
            '网卡': 'network-card',
            '键盘': 'keyboard',
            '鼠标': 'mouse',
            '显示器': 'monitor',
            '音响': 'speaker',
            '耳机': 'headphone'
        };
        
        return nameMap[chineseName] || chineseName.toLowerCase().replace(/\s+/g, '-');
    }
    
    /**
     * 添加默认字段
     */
    addDefaultFields() {
        // 添加一些常用的默认字段
        this.addField('brand', '品牌', 'string', true);
        this.addField('model', '型号', 'string', true);
    }
    
    /**
     * 添加字段
     */
    addField(name = '', displayName = '', type = 'string', required = false) {
        const fieldId = `field_${++this.fieldIdCounter}`;
        const field = {
            id: fieldId,
            name: name || `field${this.fieldIdCounter}`,
            displayName: displayName || `字段${this.fieldIdCounter}`,
            type: type,
            required: required
        };
        
        this.fields.push(field);
        this.renderField(field);
        this.updatePreview();
    }
    
    /**
     * 渲染字段配置项
     */
    renderField(field) {
        const fieldElement = document.createElement('div');
        fieldElement.className = 'field-item';
        fieldElement.dataset.fieldId = field.id;
        
        fieldElement.innerHTML = `
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center space-x-2">
                    <div class="p-2 bg-gray-100 dark:bg-gray-700 rounded-lg">
                        <i class="${this.fieldTypes[field.type].icon} text-gray-600 dark:text-gray-300"></i>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900 dark:text-white">字段配置</h4>
                        <p class="text-xs text-gray-500 dark:text-gray-400">配置字段的基本信息</p>
                    </div>
                </div>
                
                <div class="flex items-center space-x-2">
                    <button type="button" class="p-2 text-gray-400 hover:text-red-600 transition-colors" 
                            onclick="codeGeneratorUI.removeField('${field.id}')">
                        <i class="fas fa-trash"></i>
                    </button>
                    <div class="drag-handle cursor-move p-2 text-gray-400 hover:text-gray-600">
                        <i class="fas fa-grip-vertical"></i>
                    </div>
                </div>
            </div>
            
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">字段名称</label>
                    <input type="text" value="${field.name}" 
                           class="form-input text-sm" placeholder="field_name"
                           onchange="codeGeneratorUI.updateField('${field.id}', 'name', this.value)">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">显示名称</label>
                    <input type="text" value="${field.displayName}" 
                           class="form-input text-sm" placeholder="字段显示名称"
                           onchange="codeGeneratorUI.updateField('${field.id}', 'displayName', this.value)">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">字段类型</label>
                    <select class="form-input text-sm" 
                            onchange="codeGeneratorUI.updateField('${field.id}', 'type', this.value)">
                        ${Object.entries(this.fieldTypes).map(([key, config]) => 
                            `<option value="${key}" ${field.type === key ? 'selected' : ''}>${config.name}</option>`
                        ).join('')}
                    </select>
                </div>
                
                <div class="flex items-center space-x-3">
                    <label class="flex items-center space-x-2 cursor-pointer">
                        <input type="checkbox" ${field.required ? 'checked' : ''} 
                               class="rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500"
                               onchange="codeGeneratorUI.updateField('${field.id}', 'required', this.checked)">
                        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">必填字段</span>
                    </label>
                </div>
            </div>
        `;
        
        this.elements.fieldsContainer.appendChild(fieldElement);
        
        // 添加拖拽功能
        this.makeDraggable(fieldElement);
    }
    
    /**
     * 更新字段
     */
    updateField(fieldId, property, value) {
        const field = this.fields.find(f => f.id === fieldId);
        if (field) {
            field[property] = value;
            
            // 如果是类型变更，更新图标
            if (property === 'type') {
                const fieldElement = document.querySelector(`[data-field-id="${fieldId}"]`);
                const icon = fieldElement.querySelector('.fa-solid, .fas');
                if (icon) {
                    icon.className = this.fieldTypes[value].icon + ' text-gray-600 dark:text-gray-300';
                }
            }
            
            this.updatePreview();
        }
    }
    
    /**
     * 移除字段
     */
    removeField(fieldId) {
        this.fields = this.fields.filter(f => f.id !== fieldId);
        const fieldElement = document.querySelector(`[data-field-id="${fieldId}"]`);
        if (fieldElement) {
            fieldElement.remove();
        }
        this.updatePreview();
    }
    
    /**
     * 使字段可拖拽
     */
    makeDraggable(element) {
        const handle = element.querySelector('.drag-handle');
        let isDragging = false;
        
        handle.addEventListener('mousedown', (e) => {
            isDragging = true;
            element.classList.add('dragging');
            // 这里可以添加更复杂的拖拽逻辑
        });
        
        document.addEventListener('mouseup', () => {
            if (isDragging) {
                isDragging = false;
                element.classList.remove('dragging');
            }
        });
    }
    
    /**
     * 更新预览
     */
    updatePreview() {
        const chineseName = this.elements.chineseName.value.trim();
        const englishName = this.elements.englishName.value.trim();
        
        if (!chineseName || !englishName) {
            this.elements.previewContainer.innerHTML = `
                <i class="fas fa-eye-slash text-4xl text-gray-400 dark:text-gray-500 mb-4"></i>
                <p class="text-gray-500 dark:text-gray-400">请先填写基本信息</p>
            `;
            return;
        }
        
        const previewHTML = `
            <div class="text-left">
                <div class="flex items-center space-x-3 mb-4">
                    <i class="${this.elements.iconClass.value || 'fas fa-microchip'} text-2xl text-blue-600 dark:text-blue-400"></i>
                    <div>
                        <h4 class="font-semibold text-gray-900 dark:text-white">${chineseName}管理</h4>
                        <p class="text-sm text-gray-500 dark:text-gray-400">${this.elements.pageDescription.value || '管理模块'}</p>
                    </div>
                </div>
                
                <div class="space-y-2 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">英文名称:</span>
                        <span class="font-mono text-blue-600 dark:text-blue-400">${englishName}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">数据表名:</span>
                        <span class="font-mono text-green-600 dark:text-green-400">${this.elements.tableName.value}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">字段数量:</span>
                        <span class="font-semibold text-purple-600 dark:text-purple-400">${this.fields.length + 5} 个</span>
                    </div>
                </div>
                
                <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                    <h5 class="font-medium text-gray-900 dark:text-white mb-2">业务字段:</h5>
                    <div class="space-y-1 text-xs">
                        ${this.fields.map(field => `
                            <div class="flex items-center justify-between">
                                <span class="flex items-center space-x-2">
                                    <i class="${this.fieldTypes[field.type].icon} text-gray-400"></i>
                                    <span>${field.displayName}</span>
                                </span>
                                <span class="font-mono text-gray-500">${field.name}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;
        
        this.elements.previewContainer.innerHTML = previewHTML;
    }
    
    /**
     * 验证表单
     */
    validateForm() {
        const isValid = this.elements.chineseName.value.trim() && 
                       this.elements.englishName.value.trim() && 
                       this.elements.tableName.value.trim() &&
                       this.fields.length > 0;
        
        this.elements.generateBtn.disabled = !isValid || this.isGenerating;
        
        return isValid;
    }
    
    /**
     * 处理键盘快捷键
     */
    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + Enter: 生成代码
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            e.preventDefault();
            if (this.validateForm()) {
                this.generateCode();
            }
        }
        
        // Ctrl/Cmd + N: 添加字段
        if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
            e.preventDefault();
            this.addField();
        }
    }
    
    /**
     * 生成代码
     */
    async generateCode() {
        if (!this.validateForm() || this.isGenerating) {
            return;
        }
        
        this.isGenerating = true;
        this.showProgress(true);
        
        try {
            const config = {
                chineseName: this.elements.chineseName.value.trim(),
                englishName: this.elements.englishName.value.trim(),
                tableName: this.elements.tableName.value.trim(),
                iconClass: this.elements.iconClass.value.trim() || 'fas fa-microchip',
                pageDescription: this.elements.pageDescription.value.trim() || '管理模块',
                fields: this.fields.map(field => ({
                    name: field.name,
                    type: field.type,
                    displayName: field.displayName,
                    required: field.required
                }))
            };
            
            console.log('开始生成代码，配置:', config);
            
            // 模拟生成进度
            await this.simulateProgress();
            
            // 发送生成请求
            const response = await fetch('/api/modern-code-generator/generate-code', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(config)
            });
            
            if (!response.ok) {
                throw new Error(`生成失败: ${response.status} ${response.statusText}`);
            }
            
            const result = await response.json();
            
            if (result.success) {
                this.showSuccess(config);
                this.saveToHistory(config);
                this.resetForm();
            } else {
                throw new Error(result.message || '生成失败');
            }
            
        } catch (error) {
            console.error('代码生成失败:', error);
            this.showError(error.message);
        } finally {
            this.isGenerating = false;
            this.showProgress(false);
        }
    }
    
    /**
     * 显示进度
     */
    showProgress(show) {
        this.elements.progressContainer.classList.toggle('hidden', !show);
        this.elements.generateBtn.disabled = show;
        
        if (show) {
            this.elements.generateBtn.innerHTML = '<div class="loading-spinner mr-2"></div>生成中...';
        } else {
            this.elements.generateBtn.innerHTML = '<i class="fas fa-magic mr-2"></i>生成现代化模块';
        }
    }
    
    /**
     * 模拟生成进度
     */
    async simulateProgress() {
        const steps = [
            { text: '创建数据库表...', percent: 20 },
            { text: '生成HTML页面...', percent: 40 },
            { text: '生成JavaScript文件...', percent: 60 },
            { text: '生成路由文件...', percent: 80 },
            { text: '完成生成...', percent: 100 }
        ];
        
        for (const step of steps) {
            this.elements.progressText.textContent = step.text;
            this.elements.progressPercent.textContent = step.percent + '%';
            this.elements.progressFill.style.width = step.percent + '%';
            
            await new Promise(resolve => setTimeout(resolve, 500));
        }
    }
    
    /**
     * 显示成功消息
     */
    showSuccess(config) {
        if (window.themeManager) {
            window.themeManager.showThemeChangeNotification(`${config.chineseName}模块生成成功！`);
        }

        // 显示成功页面和操作选项
        const successHTML = `
            <div class="text-center py-8">
                <div class="animate-bounce-gentle">
                    <i class="fas fa-check-circle text-5xl text-green-600 dark:text-green-400 mb-4"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">🎉 生成成功！</h3>
                <p class="text-gray-600 dark:text-gray-400 mb-6">${config.chineseName}管理模块已成功生成</p>

                <div class="space-y-3">
                    <button onclick="codeGeneratorUI.jumpToGeneratedPage('${config.englishName}')"
                            class="w-full px-6 py-3 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white font-medium rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl">
                        <i class="fas fa-rocket mr-2"></i>
                        立即体验 ${config.chineseName}管理
                    </button>

                    <button onclick="codeGeneratorUI.backToMainPage()"
                            class="w-full px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-all duration-200">
                        <i class="fas fa-home mr-2"></i>
                        返回主页查看新卡片
                    </button>

                    <button onclick="codeGeneratorUI.generateAnother()"
                            class="w-full px-6 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-lg transition-all duration-200">
                        <i class="fas fa-plus mr-2"></i>
                        生成另一个模块
                    </button>
                </div>

                <div class="mt-6 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div class="flex items-start space-x-2">
                        <i class="fas fa-info-circle text-green-600 dark:text-green-400 mt-1"></i>
                        <div class="text-sm text-green-800 dark:text-green-200">
                            <p class="font-medium mb-1">生成的内容包括：</p>
                            <ul class="list-disc list-inside space-y-1 text-xs">
                                <li>响应式HTML页面 (支持移动端)</li>
                                <li>现代化JavaScript功能</li>
                                <li>完整的CRUD操作</li>
                                <li>主题切换支持</li>
                                <li>数据库表结构</li>
                                <li>API路由配置</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.elements.previewContainer.innerHTML = successHTML;

        // 3秒后自动显示跳转提示
        setTimeout(() => {
            this.showAutoJumpPrompt(config);
        }, 3000);
    }

    /**
     * 跳转到生成的页面
     */
    jumpToGeneratedPage(englishName) {
        // 显示跳转提示
        if (window.themeManager) {
            window.themeManager.showThemeChangeNotification('正在跳转到新页面...');
        }

        // 延迟跳转以显示提示
        setTimeout(() => {
            window.open(`/${englishName}-info.html`, '_blank');
        }, 500);
    }

    /**
     * 返回主页面
     */
    backToMainPage() {
        if (window.themeManager) {
            window.themeManager.showThemeChangeNotification('返回主页面...');
        }

        setTimeout(() => {
            window.location.href = '/pc-components.html';
        }, 500);
    }

    /**
     * 生成另一个模块
     */
    generateAnother() {
        this.resetForm();

        if (window.themeManager) {
            window.themeManager.showThemeChangeNotification('已重置表单，可以生成新模块');
        }

        // 滚动到表单顶部
        this.elements.form.scrollIntoView({ behavior: 'smooth' });
    }

    /**
     * 显示自动跳转提示
     */
    showAutoJumpPrompt(config) {
        const prompt = document.createElement('div');
        prompt.className = `
            fixed bottom-4 right-4 z-50 bg-blue-600 text-white p-4 rounded-lg shadow-lg
            transform translate-x-full transition-transform duration-300
            max-w-sm
        `;

        prompt.innerHTML = `
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <i class="fas fa-rocket text-xl"></i>
                    <div>
                        <p class="font-medium">想要立即体验吗？</p>
                        <p class="text-xs opacity-90">点击查看生成的${config.chineseName}管理页面</p>
                    </div>
                </div>
                <button onclick="this.parentElement.parentElement.remove()"
                        class="ml-3 text-white hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="mt-3 flex space-x-2">
                <button onclick="codeGeneratorUI.jumpToGeneratedPage('${config.englishName}'); this.closest('.fixed').remove();"
                        class="flex-1 bg-white text-blue-600 px-3 py-1 rounded text-sm font-medium hover:bg-gray-100 transition-colors">
                    立即查看
                </button>
                <button onclick="this.closest('.fixed').remove();"
                        class="px-3 py-1 border border-white text-white rounded text-sm hover:bg-white hover:text-blue-600 transition-colors">
                    稍后
                </button>
            </div>
        `;

        document.body.appendChild(prompt);

        // 显示动画
        setTimeout(() => {
            prompt.classList.remove('translate-x-full');
        }, 100);

        // 10秒后自动隐藏
        setTimeout(() => {
            if (prompt.parentElement) {
                prompt.classList.add('translate-x-full');
                setTimeout(() => {
                    if (prompt.parentElement) {
                        prompt.parentElement.removeChild(prompt);
                    }
                }, 300);
            }
        }, 10000);
    }
    
    /**
     * 显示错误消息
     */
    showError(message) {
        if (window.themeManager) {
            window.themeManager.showThemeChangeNotification(`生成失败: ${message}`);
        }
    }
    
    /**
     * 保存到历史记录
     */
    saveToHistory(config) {
        const history = JSON.parse(localStorage.getItem('codeGeneratorHistory') || '[]');
        history.unshift({
            ...config,
            timestamp: new Date().toISOString()
        });
        
        // 只保留最近10条记录
        if (history.length > 10) {
            history.splice(10);
        }
        
        localStorage.setItem('codeGeneratorHistory', JSON.stringify(history));
        this.loadGenerationHistory();
    }
    
    /**
     * 加载生成历史
     */
    loadGenerationHistory() {
        const history = JSON.parse(localStorage.getItem('codeGeneratorHistory') || '[]');
        
        if (history.length === 0) {
            this.elements.historyContainer.innerHTML = `
                <div class="text-center text-gray-500 dark:text-gray-400 py-8">
                    <i class="fas fa-clock text-2xl mb-2"></i>
                    <p>暂无生成历史</p>
                </div>
            `;
            return;
        }
        
        const historyHTML = history.map(item => `
            <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div class="flex items-center space-x-3">
                    <i class="${item.iconClass} text-blue-600 dark:text-blue-400"></i>
                    <div>
                        <h4 class="font-medium text-gray-900 dark:text-white">${item.chineseName}</h4>
                        <p class="text-xs text-gray-500 dark:text-gray-400">${new Date(item.timestamp).toLocaleString()}</p>
                    </div>
                </div>
                <a href="/${item.englishName}-info.html" target="_blank" 
                   class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300">
                    <i class="fas fa-external-link-alt"></i>
                </a>
            </div>
        `).join('');
        
        this.elements.historyContainer.innerHTML = historyHTML;
    }
    
    /**
     * 重置表单
     */
    resetForm() {
        this.elements.form.reset();
        this.fields = [];
        this.fieldIdCounter = 0;
        this.elements.fieldsContainer.innerHTML = '';
        this.addDefaultFields();
        this.updatePreview();
    }
}

// 初始化UI控制器
const codeGeneratorUI = new ModernCodeGeneratorUI();
