const db = require('../config/db');

async function updateStatusColumn() {
    try {
        console.log('开始更新revenue_records表的status字段...');
        
        // 检查字段是否存在
        const [columns] = await db.query(`
            SELECT COLUMN_NAME, COLUMN_TYPE
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'revenue_records' 
            AND COLUMN_NAME = 'status'
        `);
        
        if (columns.length === 0) {
            console.log('status字段不存在，先添加字段...');
            // 添加status字段
            await db.query(`
                ALTER TABLE revenue_records 
                ADD COLUMN status ENUM('未做单', '已做单', '已关闭') NOT NULL DEFAULT '未做单' 
                COMMENT '订单状态：未做单、已做单、已关闭'
                AFTER total_revenue
            `);
            console.log('成功添加status字段');
        } else {
            console.log('status字段已存在，检查是否需要更新...');
            console.log('当前字段类型:', columns[0].COLUMN_TYPE);
            
            // 检查是否包含'已关闭'状态
            if (!columns[0].COLUMN_TYPE.includes('已关闭')) {
                console.log('更新status字段以支持"已关闭"状态...');
                await db.query(`
                    ALTER TABLE revenue_records 
                    MODIFY COLUMN status ENUM('未做单', '已做单', '已关闭') NOT NULL DEFAULT '未做单' 
                    COMMENT '订单状态：未做单、已做单、已关闭'
                `);
                console.log('成功更新status字段');
            } else {
                console.log('status字段已包含所有必要的状态值');
            }
        }
        
        // 为现有记录设置默认状态（如果有NULL值）
        const [result] = await db.query(`
            UPDATE revenue_records 
            SET status = '未做单' 
            WHERE status IS NULL OR status = ''
        `);
        
        if (result.affectedRows > 0) {
            console.log(`更新了 ${result.affectedRows} 条现有记录的状态为"未做单"`);
        }
        
        // 验证更新结果
        const [updatedColumns] = await db.query(`
            SELECT COLUMN_TYPE
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'revenue_records' 
            AND COLUMN_NAME = 'status'
        `);
        
        console.log('更新后的字段类型:', updatedColumns[0].COLUMN_TYPE);
        console.log('status字段更新完成！');
        
    } catch (error) {
        console.error('更新status字段失败:', error);
        throw error;
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    updateStatusColumn()
        .then(() => {
            console.log('脚本执行完成');
            process.exit(0);
        })
        .catch((error) => {
            console.error('脚本执行失败:', error);
            process.exit(1);
        });
}

module.exports = updateStatusColumn;
