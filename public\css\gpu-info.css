/*
====================================================
电脑配件管理系统 - 样式与动画设计说明
====================================================

## PC端样式
- 布局结构：表格式布局，完整展示数据列
- 视觉元素：完整表头与列标签，标签式展示特性数据
- 交互方式：鼠标悬停效果，行背景变化，操作按钮末列

## 移动端样式
- 布局结构：卡片式布局，每个项目显示为独立卡片
- 视觉元素：更大字体和交互区域，精简信息展示
- 交互方式：触摸反馈效果，卡片缩放，底部操作按钮

## 动画效果
- 共用动画：淡入+上移，列表逐项延迟显示，按钮悬停效果
- PC专属：表格行悬停效果，操作按钮上移，图片预览放大
- 移动端：卡片触摸缩小反馈，按钮点击波纹，阴影深度变化

## 暗黑模式适配
- 自定义CSS变量定义暗色主题
- 增强对比度：标签和按钮更鲜明
- 边框和分隔线使用低透明度白色
*/

/* 基础变量定义 - 亮色主题 */
:root {
    --bg-primary: #f9fafb;
    --bg-secondary: #ffffff;
    --bg-card: #ffffff;
    --bg-input: #ffffff;
    --text-primary: #111827;
    --text-secondary: #4b5563;
    --border-color: #e5e7eb;
    --input-bg: #ffffff;
    --input-text: #111827;
    --button-primary-bg: #dc2626;
    --button-primary-hover: #b91c1c;
    --button-primary-text: #ffffff;
    --highlight-color: #ef4444;
    --accent-color: #f87171;
    --card-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 暗黑模式样式 */
.dark-mode {
    --bg-primary: #121212;
    --bg-secondary: #1e1e1e;
    --bg-card: #252525;
    --bg-input: #2a2a2a;
    --text-primary: #f5f5f5;
    --text-secondary: #a0aec0;
    --border-color: #333333;
    --input-bg: #2a2a2a;
    --input-text: #e2e8f0;
    --button-primary-bg: #dc2626;
    --button-primary-hover: #b91c1c;
    --button-primary-text: #ffffff;
    --highlight-color: #ef4444;
    --accent-color: #f87171;
    --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

body.dark-mode {
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.dark-mode body {
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.dark-mode .bg-white {
    background-color: var(--bg-secondary);
}

.dark-mode .bg-gray-50 {
    background-color: var(--bg-primary);
}

.dark-mode .bg-gray-100 {
    background-color: #1a1a1a;
}

.dark-mode .text-gray-700,
.dark-mode .text-gray-800,
.dark-mode .text-gray-900 {
    color: var(--text-primary);
}

.dark-mode .text-gray-500,
.dark-mode .text-gray-600 {
    color: var(--text-secondary);
}

.dark-mode .border-gray-200,
.dark-mode .border-gray-300 {
    border-color: var(--border-color);
}

/* 确保左侧表单区域和右侧内容区背景色统一 */
.dark-mode .shadow-md {
    box-shadow: var(--card-shadow);
}

/* 表格容器背景色修复 */
.dark-mode .rounded-lg {
    background-color: var(--bg-secondary);
}

.dark-mode .text-gray-600,
.dark-mode .text-gray-700,
.dark-mode .text-gray-800 {
    color: var(--text-secondary);
}

/* 输入框暗黑模式 */
.dark-mode input:not([type="checkbox"]),
.dark-mode select,
.dark-mode textarea {
    background-color: var(--input-bg);
    color: var(--input-text);
    border-color: var(--border-color);
}

.dark-mode input::placeholder,
.dark-mode textarea::placeholder {
    color: #6b7280;
}

/* 表格暗黑模式统一 */
.dark-mode table {
    background-color: transparent; /* 移除表格背景色 */
}

.dark-mode thead {
    background-color: rgba(255, 255, 255, 0.03); /* 轻微高亮表头 */
}

.dark-mode thead th {
    color: #9ca3af !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05) !important;
}

.dark-mode tbody tr {
    border-color: rgba(255, 255, 255, 0.05) !important;
}

.dark-mode tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.03) !important;
}

/* 表格内的GPU规格标签增强 */
.dark-mode .memory-tag,
.dark-mode span[class*="gddr"] {
    background-color: rgba(79, 70, 229, 0.2) !important;
    color: #818cf8 !important;
    border: 1px solid rgba(79, 70, 229, 0.3) !important;
}

/* GPU功耗标签增强 */
.dark-mode span.text-red-700,
.dark-mode span.bg-red-50 {
    background-color: rgba(220, 38, 38, 0.15) !important;
    color: #ef4444 !important;
    border: 1px solid rgba(220, 38, 38, 0.2) !important;
}

/* 暗色主题下的按钮样式统一 */
.dark-mode .bg-red-600 {
    background-color: var(--button-primary-bg);
}

.dark-mode .hover\:bg-red-700:hover {
    background-color: var(--button-primary-hover);
}

/* 移动端卡片暗黑模式优化 */
@media (max-width: 640px) {
    .dark-mode .gpu-card-outer-container {
        margin-bottom: 15px !important;
    }

    /* 卡片整体样式 */
    .dark-mode .gpu-card {
        background-color: var(--bg-secondary);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        border: 1px solid rgba(255, 255, 255, 0.05);
    }

    /* 卡片头部 */
    .dark-mode .gpu-card-header {
        background-color: rgba(0, 0, 0, 0.3);
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    }

    /* 型号文本 */
    .dark-mode .gpu-model-text {
        color: var(--text-primary);
    }

    /* 品牌标签 */
    .dark-mode .gpu-brand-badge {
        background-color: rgba(239, 68, 68, 0.2);
        color: #f87171;
        border-color: rgba(239, 68, 68, 0.3);
    }

    /* 卡片主体 */
    .dark-mode .gpu-card-body {
        background-color: var(--bg-secondary);
    }

    /* 图片容器 */
    .dark-mode .gpu-card-image {
        background-color: rgba(0, 0, 0, 0.2);
        border-color: rgba(255, 255, 255, 0.1);
    }

    /* 信息文本 */
    .dark-mode .gpu-chipset-text {
        color: #f87171;
    }

    .dark-mode .gpu-info-text {
        color: var(--text-secondary);
    }

    /* 标签样式 */
    .dark-mode .gpu-spec-tag {
        background-color: rgba(255, 255, 255, 0.1);
        color: rgba(255, 255, 255, 0.9);
        border-color: rgba(255, 255, 255, 0.15);
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }

    /* 显存标签 */
    .dark-mode .gpu-memory-tag {
        background-color: rgba(79, 70, 229, 0.2);
        color: #818cf8;
        border-color: rgba(79, 70, 229, 0.3);
    }

    /* 功耗标签 */
    .dark-mode .gpu-power-tag {
        background-color: rgba(239, 68, 68, 0.2);
        color: #f87171;
        border-color: rgba(239, 68, 68, 0.3);
    }

    /* 卡片底部 */
    .dark-mode .gpu-card-footer {
        background-color: rgba(0, 0, 0, 0.2);
        border-top: 1px solid rgba(255, 255, 255, 0.05);
    }

    /* 操作按钮 */
    .dark-mode .gpu-view-btn {
        color: #60a5fa;
    }

    .dark-mode .gpu-edit-btn {
        color: #34d399;
    }

    .dark-mode .gpu-delete-btn {
        color: #f87171;
    }

    /* 按钮触摸效果 */
    .dark-mode .gpu-action-btn:active {
        background-color: rgba(255, 255, 255, 0.1);
    }
}

/* GPU卡片动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

/* PC端卡片操作按钮悬停动画 */
@media (min-width: 641px) {
    .gpu-action-btn {
        transition: transform 0.2s ease, background-color 0.2s ease;
    }

    .gpu-action-btn:hover {
        transform: translateY(-2px);
    }

    .gpu-card img, .image-preview {
        transition: all 0.3s ease;
    }

    .gpu-card img:hover, .image-preview:hover {
        transform: scale(1.05);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }
}

/* 移动端卡片交互动画 */
@media (max-width: 640px) {
    .gpu-card {
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .gpu-card:active {
        transform: scale(0.99);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    }

    .gpu-action-btn {
        transition: background-color 0.15s ease;
    }

    .gpu-action-btn:active {
        background-color: rgba(0, 0, 0, 0.05);
    }

    .dark-mode .gpu-card:active {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
    }

    .dark-mode .gpu-action-btn:active {
        background-color: rgba(255, 255, 255, 0.15);
    }
}

/* 暗黑模式开关样式 */
.dark-mode #darkModeToggle {
    background-color: #fbbf24;
    color: #92400e;
}

.dark-mode #darkModeToggle:hover {
    background-color: #f59e0b;
}

/* 表单区域暗色主题优化 */
.dark-mode .border-gray-200,
.dark-mode .border-gray-300 {
    border-color: var(--border-color);
}

/* 表单卡片暗色主题 */
.dark-mode .border-gray-200.rounded-md {
    background-color: rgba(255, 255, 255, 0.03);
    border-color: rgba(255, 255, 255, 0.1);
}

/* 智能识别输入区域暗夜模式样式 */
.dark-mode .border-gray-200 {
    border-color: #333 !important;
}

.dark-mode .bg-red-50 {
    background-color: rgba(239, 68, 68, 0.08) !important;
    border-color: rgba(239, 68, 68, 0.2) !important;
}

.dark-mode #smartInput {
    background-color: #2a2a2a;
    color: #e2e8f0;
    border-color: #4a5568;
}

.dark-mode #autoFillBtn {
    background-color: #dc2626;
}

.dark-mode #autoFillBtn:hover {
    background-color: #b91c1c;
}

.dark-mode .text-red-600 {
    color: #f87171 !important;
}

/* 标题文本颜色 */
.dark-mode .text-gray-700.mb-2 {
    color: #d1d5db !important;
}

/* 表单标签文本 */
.dark-mode .block.text-sm.font-medium.text-gray-700 {
    color: #d1d5db !important;
}

/* 卡片暗黑模式 */
.dark-mode .gpu-card {
    background-color: var(--bg-card);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.dark-mode .gpu-card-header {
    background-color: rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.05);
}

.dark-mode .gpu-card-footer {
    border-color: rgba(255, 255, 255, 0.05);
    background-color: rgba(0, 0, 0, 0.15);
}

.dark-mode .gpu-card-image {
    border-color: rgba(255, 255, 255, 0.05);
    background-color: rgba(0, 0, 0, 0.2);
}

.dark-mode .divide-gray-200 > * + * {
    border-color: rgba(255, 255, 255, 0.05);
}

/* 移动端卡片布局基础样式 */
@media (max-width: 640px) {
    /* 隐藏表头和应用卡片式布局 */
    #gpuTableBody tr {
        display: block;
        margin-bottom: 6px;
        border: none;
    }

    #gpuTableBody td {
        display: block;
        border: none;
        padding: 0;
    }

    table thead {
        display: none;
    }

    table, tbody, tr {
        display: block;
        width: 100%;
        border: none;
    }

    /* 卡片容器 */
    .gpu-card-outer-container {
        width: 100%;
        max-width: 100%;
        box-sizing: border-box;
        padding: 0;
        margin-bottom: 6px;
        position: relative;
        animation: fadeIn 0.3s ease-in-out;
    }

    /* 基础卡片样式 */
    .gpu-card {
        width: 100%;
        border-radius: 6px;
        overflow: hidden;
        background-color: white;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.06);
        display: flex;
        flex-direction: column;
        min-width: 0;
        border: none;
    }

    /* 卡片头部 */
    .gpu-card-header {
        padding: 6px 8px;
        background-color: #f8f9fa;
        border-bottom: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    /* 型号文本 */
    .gpu-model-text {
        font-weight: 600;
        font-size: 0.85rem;
        color: #111827;
        flex-grow: 1;
        margin-right: 6px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 70%;
    }

    /* 品牌标签 */
    .gpu-brand-badge {
        padding: 1px 4px;
        font-size: 0.6rem;
        font-weight: 600;
        border-radius: 3px;
        background-color: rgba(220, 38, 38, 0.1);
        color: #b91c1c;
        border: 1px solid rgba(220, 38, 38, 0.2);
        white-space: nowrap;
    }

    /* 卡片主体 */
    .gpu-card-body {
        padding: 6px 8px;
        display: flex;
        gap: 8px;
        background-color: white;
    }

    /* 图片容器 */
    .gpu-card-image {
        width: 40px;
        height: 40px;
        flex-shrink: 0;
        border-radius: 4px;
        border: 1px solid rgba(220, 38, 38, 0.15);
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f9fafb;
    }

    .gpu-card-image img {
        width: 100%;
        height: 100%;
        object-fit: contain;
    }

    /* 信息容器 */
    .gpu-card-info {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: 2px;
    }

    /* 芯片组文本 */
    .gpu-chipset-text {
        font-weight: 600;
        font-size: 0.8rem;
        color: #dc2626;
    }

    /* 信息文本 */
    .gpu-info-text {
        font-size: 0.7rem;
        color: #4b5563;
    }

    /* 标签组 */
    .gpu-spec-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 2px;
        margin-top: 2px;
    }

    /* 规格标签 */
    .gpu-spec-tag {
        padding: 0px 3px;
        font-size: 0.6rem;
        font-weight: 500;
        border-radius: 2px;
        display: inline-flex;
        align-items: center;
    }

    /* 显存标签 */
    .gpu-memory-tag {
        background-color: rgba(79, 70, 229, 0.1);
        color: #4f46e5;
        border: 1px solid rgba(79, 70, 229, 0.2);
    }

    /* 功耗标签 */
    .gpu-power-tag {
        background-color: rgba(220, 38, 38, 0.1);
        color: #dc2626;
        border: 1px solid rgba(220, 38, 38, 0.2);
    }

    /* 卡片底部 */
    .gpu-card-footer {
        padding: 4px 8px;
        background-color: #fcfcfc;
        border-top: 1px solid #eee;
        display: flex;
        justify-content: space-around;
        align-items: center;
    }

    /* 操作按钮样式 */
    .gpu-action-btn {
        padding: 3px 6px;
        border-radius: 3px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-size: 0.7rem;
        font-weight: 500;
        transition: background-color 0.15s ease;
    }

    .gpu-view-btn {
        color: #2563eb;
    }

    .gpu-edit-btn {
        color: #047857;
    }

    .gpu-delete-btn {
        color: #dc2626;
    }

    /* 卡片动画 - 根据加载顺序逐渐显示 */
    .gpu-card-outer-container:nth-child(n) {
        animation-delay: calc(0.05s * var(--card-index, 0));
    }
}

/* PC端表格样式优化 - 参照CPU信息页面 */
.table-modern {
    border-collapse: separate;
    border-spacing: 0;
}

.table-modern thead th {
    background-color: #f9fafb;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: 0.75rem;
    padding: 0.75rem 1rem;
    border-bottom: 2px solid #e5e7eb;
}

.table-modern tbody tr {
    transition: background-color 0.15s ease;
}

.table-modern tbody tr:hover {
    background-color: #f9fafb;
}

/* 品牌标签样式 */
.brand-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.brand-nvidia {
    background-color: rgba(118, 185, 0, 0.1);
    color: #76b900;
    border: 1px solid rgba(118, 185, 0, 0.2);
}

.brand-amd {
    background-color: rgba(237, 28, 36, 0.1);
    color: #ed1c24;
    border: 1px solid rgba(237, 28, 36, 0.2);
}

.brand-intel {
    background-color: rgba(0, 113, 197, 0.1);
    color: #0071c5;
    border: 1px solid rgba(0, 113, 197, 0.2);
}

/* 显存标签 */
.memory-tag {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
    background-color: rgba(79, 70, 229, 0.1);
    color: #4f46e5;
    border: 1px solid rgba(79, 70, 229, 0.2);
}

/* 操作按钮 */
.action-buttons {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
}

.action-buttons button {
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
    background-color: transparent;
    border: none;
    cursor: pointer;
}

.btn-view {
    color: #3b82f6;
}

.btn-view:hover {
    color: #2563eb;
    background-color: rgba(59, 130, 246, 0.1);
}

.btn-edit {
    color: #10b981;
}

.btn-edit:hover {
    color: #059669;
    background-color: rgba(16, 185, 129, 0.1);
}

.btn-delete {
    color: #ef4444;
}

.btn-delete:hover {
    color: #dc2626;
    background-color: rgba(239, 68, 68, 0.1);
}

/* 图片容器悬停效果 */
.gpu-image-container {
    position: relative;
    overflow: hidden;
    border-radius: 0.375rem;
}

.img-overlay {
    position: absolute;
    inset: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.3);
    opacity: 0;
    transition: opacity 0.2s ease;
    border-radius: 0.375rem;
}

.gpu-image-container:hover .img-overlay {
    opacity: 1;
}

/* 表格行一致高度 */
tbody tr {
    height: 4rem;
}

/* PC端表格暗黑模式样式 */
.dark-mode .table-modern thead th {
    background-color: rgba(255, 255, 255, 0.03);
    color: #9ca3af;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.dark-mode .table-modern tbody tr {
    border-color: rgba(255, 255, 255, 0.05);
}

.dark-mode .table-modern tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.03);
}

/* PC端品牌标签深色模式 */
.dark-mode .brand-badge {
    background-color: rgba(75, 85, 99, 0.2);
    color: #9ca3af;
    border: 1px solid rgba(75, 85, 99, 0.3);
}

.dark-mode .brand-nvidia {
    background-color: rgba(118, 185, 0, 0.15) !important;
    color: #9bc455 !important;
    border: 1px solid rgba(118, 185, 0, 0.25) !important;
}

.dark-mode .brand-amd {
    background-color: rgba(237, 28, 36, 0.15) !important;
    color: #f87171 !important;
    border: 1px solid rgba(237, 28, 36, 0.25) !important;
}

.dark-mode .brand-intel {
    background-color: rgba(0, 113, 197, 0.15) !important;
    color: #60a5fa !important;
    border: 1px solid rgba(0, 113, 197, 0.25) !important;
}

/* PC端操作按钮深色模式 */
.dark-mode .btn-view {
    color: #60a5fa;
}

.dark-mode .btn-view:hover {
    color: #93c5fd;
    background-color: rgba(59, 130, 246, 0.15);
}

.dark-mode .btn-edit {
    color: #34d399;
}

.dark-mode .btn-edit:hover {
    color: #6ee7b7;
    background-color: rgba(16, 185, 129, 0.15);
}

.dark-mode .btn-delete {
    color: #f87171;
}

.dark-mode .btn-delete:hover {
    color: #fca5a5;
    background-color: rgba(239, 68, 68, 0.15);
}

/* 表格文本深色模式 */
.dark-mode .text-gray-900 {
    color: #e5e7eb !important;
}

/* 图片占位符深色模式 */
.dark-mode .gpu-image-container .bg-gray-100 {
    background-color: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark-mode .gpu-image-container .text-gray-400 {
    color: #9ca3af;
}

/* 型号和尺寸文本深色模式 */
.dark-mode .text-sm.font-medium.text-gray-900 {
    color: #e5e7eb !important;
}

.dark-mode .text-xs.text-gray-500 {
    color: #9ca3af !important;
}

/* 分页按钮深色模式 */
.dark-mode #pageNumbers button.bg-white,
.dark-mode button.border.border-gray-300 {
    background-color: var(--bg-secondary);
    color: var(--text-secondary);
    border-color: rgba(255, 255, 255, 0.1);
}

.dark-mode #pageNumbers button.bg-white:hover,
.dark-mode button.border.border-gray-300:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.dark-mode #pageNumbers button.bg-red-600 {
    background-color: var(--button-primary-bg);
    border-color: var(--button-primary-bg);
}

.dark-mode #pageInfo {
    color: var(--text-secondary);
}

.dark-mode #pageJump {
    background-color: var(--input-bg);
    color: var(--input-text);
    border-color: rgba(255, 255, 255, 0.1);
}

/* 搜索和筛选区域深色模式 */
.dark-mode #gpuSearch {
    background-color: var(--input-bg);
    color: var(--input-text);
    border-color: rgba(255, 255, 255, 0.1);
}

.dark-mode #gpuSearch::placeholder {
    color: #6b7280;
}

.dark-mode .fa-search {
    color: #6b7280;
}

.dark-mode #brandFilter,
.dark-mode #chipsetFilter {
    background-color: var(--input-bg);
    color: var(--input-text);
    border-color: rgba(255, 255, 255, 0.1);
}

.dark-mode #brandFilter option,
.dark-mode #chipsetFilter option {
    background-color: var(--bg-secondary);
}

/* 移动端筛选器优化 */
@media (max-width: 640px) {
    /* 主容器在移动端垂直排列 */
    .flex.flex-col.sm\\:flex-row.sm\\:items-center.gap-3.sm\\:gap-4 {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }

    /* 筛选器容器布局优化 */
    .flex.flex-wrap.gap-2.sm\\:gap-4.items-center.sm\\:flex-none {
        justify-content: flex-start;
        align-items: stretch;
        width: 100%;
    }

    /* 品牌和芯片组筛选器移动端优化 */
    #brandFilter,
    #chipsetFilter {
        font-size: 0.875rem;
        padding: 8px 6px 8px 12px;
        height: 38px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
    }

    #brandFilter option,
    #chipsetFilter option {
        font-size: 0.875rem;
        padding: 4px 8px;
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    /* 重置按钮移动端优化 */
    #resetFilterBtn {
        height: 38px;
        padding: 8px 12px;
        font-size: 0.875rem;
        white-space: nowrap;
        min-width: 70px;
    }

    /* 当屏幕特别小时，让筛选器垂直排列 */
    @media (max-width: 380px) {
        .flex.flex-wrap.gap-2.sm\\:gap-4.items-center.sm\\:flex-none {
            flex-direction: column;
            align-items: stretch;
        }

        #brandFilter,
        #chipsetFilter,
        #resetFilterBtn {
            width: 100%;
            max-width: none;
            min-width: auto;
        }
    }
}

/* 下拉框选项文本优化 */
select option {
    padding: 8px 12px;
    font-size: 0.875rem;
    line-height: 1.25;
}

/* 移动端下拉框选项特殊处理 */
@media (max-width: 640px) {
    select option {
        padding: 6px 10px;
        font-size: 0.8rem;
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    /* 确保长文本在选项中正确显示 */
    #chipsetFilter option {
        direction: ltr;
        unicode-bidi: embed;
    }

    /* 移动端筛选器容器优化 */
    .flex.flex-wrap.gap-2.sm\\:gap-4.items-stretch {
        gap: 6px;
        align-items: center;
    }

    /* 移动端下拉框样式统一 */
    #brandFilter,
    #chipsetFilter {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
        background-position: right 6px center;
        background-repeat: no-repeat;
        background-size: 16px 16px;
        padding-right: 28px;
    }
}

/* 表格记录总数文本 */
.dark-mode #totalCount {
    color: var(--text-secondary);
}

/* 移动端卡片新样式 - 参考CPU信息页面 */
@media (max-width: 640px) {
    /* 新卡片样式 */
    .gpu-card-new {
        width: 100%;
        max-width: 100%;
        overflow: hidden;
        background-color: white;
        border-radius: 6px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        margin-bottom: 5px;
        display: flex;
        flex-direction: column;
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    /* 卡片标题栏 */
    .gpu-card-new > div:first-child {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 5px 8px;
        background-color: #f9fafb;
        border-bottom: 1px solid #f0f0f0;
    }

    /* 卡片主体内容 */
    .gpu-card-new > div:nth-child(2) {
        display: flex;
        padding: 6px 8px;
        gap: 6px;
    }

    /* 图片容器 */
    .gpu-card-new .flex-shrink-0 {
        width: 36px;
        height: 36px;
        background-color: #f3f4f6;
        border: 1px solid #e5e7eb;
        border-radius: 4px;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .gpu-card-new .flex-shrink-0 img {
        object-fit: contain;
        width: 100%;
        height: 100%;
    }

    /* 信息区域 */
    .gpu-card-new .flex-grow {
        display: flex;
        flex-direction: column;
        gap: 2px;
        min-width: 0; /* 解决文本溢出问题 */
    }

    /* 标签容器 */
    .gpu-card-new .flex.flex-wrap {
        gap: 2px;
        margin-top: 2px;
    }

    /* 卡片底部 */
    .gpu-card-new > div:last-child {
        display: flex;
        justify-content: space-around;
        padding: 4px 0;
        background-color: #fafafa;
        border-top: 1px solid #f0f0f0;
    }

    /* 操作按钮 */
    .gpu-card-new button {
        display: flex;
        align-items: center;
        padding: 3px 6px;
        font-size: 0.7rem;
        border-radius: 3px;
        background-color: transparent;
        transition: background-color 0.15s ease;
    }

    /* 型号标题样式 */
    .gpu-model-new {
        font-weight: 600;
        font-size: 0.8rem;
        color: #111827;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 68%;
    }
}

/* 移动端卡片深色模式优化 - 参考CPU页面 */
.dark-mode .gpu-card-new {
    background-color: var(--bg-secondary) !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3) !important;
    border: 1px solid rgba(255, 255, 255, 0.05) !important;
}

/* 卡片头部暗色样式 */
.dark-mode .gpu-card-new > div:first-child {
    background-color: rgba(0, 0, 0, 0.2) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05) !important;
}

/* 卡片内容区域暗色样式 */
.dark-mode .gpu-card-new > div:nth-child(2) {
    background-color: var(--bg-secondary) !important;
}

/* 卡片底部暗色样式 */
.dark-mode .gpu-card-new > div:last-child {
    background-color: rgba(0, 0, 0, 0.1) !important;
    border-top: 1px solid rgba(255, 255, 255, 0.05) !important;
}

/* 卡片文本深色模式 */
.dark-mode .gpu-model-new {
    color: #e5e7eb !important;
}

/* 图片容器深色模式 */
.dark-mode .gpu-card-new .flex-shrink-0 {
    background-color: rgba(0, 0, 0, 0.2) !important;
    border-color: rgba(255, 255, 255, 0.1) !important;
}

/* 标签深色模式增强 */
.dark-mode .gpu-card-new .badge-gpu {
    background-color: rgba(75, 85, 99, 0.3) !important;
    border: 1px solid rgba(75, 85, 99, 0.4) !important;
    color: #e5e7eb !important;
}

/* 不同类型标签样式 */
.dark-mode .gpu-card-new .badge-nvidia {
    background-color: rgba(118, 185, 0, 0.15) !important;
    color: #a3e635 !important;
    border-color: rgba(118, 185, 0, 0.25) !important;
}

.dark-mode .gpu-card-new .badge-amd {
    background-color: rgba(239, 68, 68, 0.15) !important;
    color: #f87171 !important;
    border-color: rgba(239, 68, 68, 0.25) !important;
}

.dark-mode .gpu-card-new .badge-memory {
    background-color: rgba(79, 70, 229, 0.15) !important;
    color: #818cf8 !important;
    border-color: rgba(79, 70, 229, 0.25) !important;
}

.dark-mode .gpu-card-new .badge-power {
    background-color: rgba(220, 38, 38, 0.15) !important;
    color: #ef4444 !important;
    border-color: rgba(220, 38, 38, 0.2) !important;
}
