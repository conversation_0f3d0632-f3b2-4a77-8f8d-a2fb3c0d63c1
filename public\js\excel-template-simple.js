let templateData = null;
let currentData = [];

// 页面加载时初始化空表格
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否有引用数据
    checkForReferenceData();
});

// 检查是否有引用数据
function checkForReferenceData() {
    const urlParams = new URLSearchParams(window.location.search);
    const refId = urlParams.get('ref');

    if (refId) {
        // 有引用ID，尝试加载引用数据
        loadReferenceData();
    } else {
        // 没有引用，初始化空表格
        initializeEmptyTable();
    }
}

// 加载引用数据
function loadReferenceData() {
    try {
        const editorDataStr = localStorage.getItem('excelEditorData');
        if (editorDataStr) {
            const editorData = JSON.parse(editorDataStr);

            if (editorData.isReference && editorData.data) {
                console.log('加载引用数据:', editorData);

                // 显示引用信息
                showReferenceInfo(editorData);

                // 使用引用数据创建表格
                if (editorData.data.length > 0) {
                    const headers = editorData.data[0] || [];
                    const dataRows = editorData.data.slice(1);

                    // 确保有足够的列
                    const maxCols = Math.max(headers.length, 10);
                    const extendedHeaders = [...headers];
                    for (let i = headers.length; i < maxCols; i++) {
                        extendedHeaders.push(`列${String.fromCharCode(65 + i)}`);
                    }

                    // 确保有足够的行
                    const maxRows = Math.max(dataRows.length, 20);

                    createTable(extendedHeaders, maxRows, dataRows);

                    // 设置模板数据
                    templateData = {
                        sheetName: editorData.sheetName,
                        data: editorData.data
                    };
                }

                // 清除localStorage中的数据
                localStorage.removeItem('excelEditorData');
            } else {
                initializeEmptyTable();
            }
        } else {
            initializeEmptyTable();
        }
    } catch (error) {
        console.error('加载引用数据失败:', error);
        initializeEmptyTable();
    }
}

// 显示引用信息
function showReferenceInfo(editorData) {
    const infoDiv = document.createElement('div');
    infoDiv.className = 'bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4';
    infoDiv.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                    <span class="text-blue-700 text-sm">
                        <strong>引用数据：</strong>${editorData.templateName} - ${editorData.remarks}
                    </span>
                    <button onclick="clearReference()" class="ml-auto text-blue-600 hover:text-blue-800 text-sm">
                        <i class="fas fa-times mr-1"></i>清除引用
                    </button>
                </div>
            `;

    // 插入到页面顶部
    const container = document.querySelector('.container');
    const firstChild = container.firstElementChild;
    container.insertBefore(infoDiv, firstChild.nextElementSibling);
}

// 清除引用
function clearReference() {
    // 移除引用信息显示
    const infoDiv = document.querySelector('.bg-blue-50');
    if (infoDiv) {
        infoDiv.remove();
    }

    // 重新初始化空表格
    initializeEmptyTable();
    templateData = null;

    // 更新URL，移除ref参数
    const url = new URL(window.location);
    url.searchParams.delete('ref');
    window.history.replaceState({}, document.title, url);
}

// 初始化空表格
function initializeEmptyTable() {
    const headers = ['列A', '列B', '列C', '列D', '列E', '列F', '列G', '列H', '列I', '列J'];
    const rows = 20;

    createTable(headers, rows);
}

// 创建表格
function createTable(headers, rows, data = null) {
    const tableHeader = document.getElementById('tableHeader');
    const tableBody = document.getElementById('tableBody');

    // 清空现有内容
    tableHeader.innerHTML = '';
    tableBody.innerHTML = '';

    console.log('创建表格 - 表头:', headers);
    console.log('创建表格 - 数据:', data);

    // 创建表头
    headers.forEach((header, index) => {
        const th = document.createElement('th');
        th.textContent = header;
        // 为创收列添加特殊样式
        if (header === '创收') {
            th.style.backgroundColor = '#e0f2fe';
            th.style.color = '#0369a1';
            th.style.fontWeight = 'bold';
        }
        tableHeader.appendChild(th);
    });

    // 创建表格行
    for (let i = 0; i < rows; i++) {
        const tr = document.createElement('tr');

        headers.forEach((header, colIndex) => {
            const td = document.createElement('td');
            const input = document.createElement('input');
            input.type = 'text';
            input.value = (data && data[i] && data[i][colIndex]) ? data[i][colIndex] : '';

            // 创收列设为只读，并添加特殊样式
            if (header === '创收') {
                input.readOnly = true;
                input.style.backgroundColor = '#f0f9ff';
                input.style.color = '#0369a1';
                input.style.fontWeight = 'bold';
                input.style.textAlign = 'center';
                input.placeholder = '自动计算';
            } else {
                input.addEventListener('input', function() {
                    calculateRevenue(tr, headers);
                    updateCurrentData();
                });
            }

            td.appendChild(input);
            tr.appendChild(td);
        });

        tableBody.appendChild(tr);
    }

    // 初始计算所有行的创收
    console.log('开始计算所有行的创收...');
    const allRows = tableBody.querySelectorAll('tr');
    allRows.forEach((row, index) => {
        console.log(`计算第 ${index + 1} 行的创收`);
        calculateRevenue(row, headers);
    });

    updateCurrentData();
}

// 计算创收（补差-差价）
function calculateRevenue(row, headers) {
    const inputs = row.querySelectorAll('input');

    // 找到差价、补差、创收列的索引
    const diffPriceIndex = headers.indexOf('差价');
    const supplementIndex = headers.indexOf('补差');
    const revenueIndex = headers.indexOf('创收');

    console.log('列索引 - 差价:', diffPriceIndex, '补差:', supplementIndex, '创收:', revenueIndex);

    if (diffPriceIndex !== -1 && supplementIndex !== -1 && revenueIndex !== -1) {
        const diffPriceValue = parseFloat(inputs[diffPriceIndex].value) || 0;
        const supplementValue = parseFloat(inputs[supplementIndex].value) || 0;
        const revenue = supplementValue - diffPriceValue;

        console.log(`计算创收: ${supplementValue} - ${diffPriceValue} = ${revenue}`);

        inputs[revenueIndex].value = revenue.toString();
    }
}

// 更新当前数据
function updateCurrentData() {
    const table = document.getElementById('dataTable');
    const rows = table.querySelectorAll('tbody tr');
    currentData = [];

    rows.forEach(row => {
        const rowData = [];
        const inputs = row.querySelectorAll('input');
        inputs.forEach(input => {
            rowData.push(input.value);
        });
        currentData.push(rowData);
    });
}

// 加载Excel模板
async function loadTemplate() {
    try {
        console.log('正在加载Excel模板...');

        const response = await fetch('/api/excel-template/template', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });

        if (!response.ok) {
            throw new Error('加载模板失败');
        }

        const result = await response.json();

        if (result.success) {
            templateData = result.data;
            console.log('模板数据:', templateData);

            // 使用模板数据创建表格
            if (templateData.data && templateData.data.length > 0) {
                const headers = templateData.data[0] || [];
                const dataRows = templateData.data.slice(1);

                // 确保有足够的列
                const maxCols = Math.max(headers.length, 10);
                const extendedHeaders = [...headers];
                for (let i = headers.length; i < maxCols; i++) {
                    extendedHeaders.push(`列${String.fromCharCode(65 + i)}`);
                }

                // 确保有足够的行
                const maxRows = Math.max(dataRows.length, 20);

                // 传递数据行（不包含表头）
                createTable(extendedHeaders, maxRows, dataRows);
            }

            alert('模板加载成功！您现在可以编辑数据或粘贴新数据。');
        } else {
            throw new Error(result.message);
        }
    } catch (error) {
        console.error('加载模板失败:', error);
        alert('加载模板失败: ' + error.message);
    }
}

// 聚焦粘贴区域
function focusPasteArea() {
    document.getElementById('pasteArea').focus();
}

// 清空粘贴区域
function clearPasteArea() {
    document.getElementById('pasteArea').value = '';
}

// 解析Excel数据
function parseExcelData() {
    const pasteData = document.getElementById('pasteArea').value.trim();
    if (!pasteData) {
        alert('请先粘贴Excel数据');
        return;
    }

    try {
        // 按行分割数据
        const lines = pasteData.split('\n');
        const parsedData = [];

        lines.forEach(line => {
            if (line.trim()) {
                // 按制表符或多个空格分割列
                const columns = line.split(/\t|  +/);
                parsedData.push(columns);
            }
        });

        if (parsedData.length > 0) {
            // 使用固定表头，不解析第一行作为表头
            const headers = ['品类', '型号', '状态', '数量', '成本', '差价', '补差', '创收'];

            console.log('解析数据 - 固定表头:', headers);
            console.log('解析数据 - 原始数据:', parsedData);

            // 所有行都是数据行（包括第一行）
            const dataRows = parsedData;

            // 创建表格
            const maxRows = Math.max(dataRows.length, 20);
            createTable(headers, maxRows, dataRows);

            alert('数据解析成功！');
        } else {
            alert('未能解析到有效数据');
        }
    } catch (error) {
        console.error('解析数据失败:', error);
        alert('解析数据失败，请检查数据格式');
    }
}

// 清空表格
function clearTable() {
    if (confirm('确定要清空表格和粘贴区域吗？此操作不可恢复。')) {
        // 清空粘贴区域
        clearPasteArea();

        // 重新初始化空表格
        initializeEmptyTable();

        // 清除引用信息（如果有）
        const infoDiv = document.querySelector('.bg-blue-50');
        if (infoDiv) {
            infoDiv.remove();
        }

        // 重置模板数据
        templateData = null;

        // 更新URL，移除ref参数（如果有）
        const url = new URL(window.location);
        if (url.searchParams.has('ref')) {
            url.searchParams.delete('ref');
            window.history.replaceState({}, document.title, url);
        }

        alert('表格和粘贴区域已清空！');
    }
}

// 显示保存模态框
function showSaveModal() {
    updateCurrentData();
    document.getElementById('saveModal').style.display = 'block';
}

// 关闭保存模态框
function closeSaveModal() {
    document.getElementById('saveModal').style.display = 'none';
}

// 保存数据
document.getElementById('saveForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    try {
        const templateName = document.getElementById('templateName').value.trim();
        const remarks = document.getElementById('remarks').value.trim();

        if (!templateName || !remarks) {
            alert('请填写模板名称和备注信息');
            return;
        }

        updateCurrentData();

        // 获取表头
        const headers = [];
        const headerCells = document.querySelectorAll('#tableHeader th');
        headerCells.forEach(th => {
            headers.push(th.textContent);
        });

        // 组合表头和数据
        const fullData = [headers, ...currentData];

        const response = await fetch('/api/excel-template/save', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify({
                templateName: templateName,
                remarks: remarks,
                data: fullData,
                sheetName: templateData ? templateData.sheetName : 'Sheet1'
            })
        });

        if (response.ok) {
            const result = await response.json();
            alert('数据保存成功！');
            console.log('保存结果:', result);
            closeSaveModal();

            // 清空表单
            document.getElementById('templateName').value = '';
            document.getElementById('remarks').value = '';

            // 清空粘贴区域
            clearPasteArea();

            // 清空表格，重新初始化
            initializeEmptyTable();

            // 清除引用信息（如果有）
            const infoDiv = document.querySelector('.bg-blue-50');
            if (infoDiv) {
                infoDiv.remove();
            }

            // 重置模板数据
            templateData = null;

            // 更新URL，移除ref参数（如果有）
            const url = new URL(window.location);
            if (url.searchParams.has('ref')) {
                url.searchParams.delete('ref');
                window.history.replaceState({}, document.title, url);
            }
        } else {
            throw new Error('保存失败');
        }
    } catch (error) {
        console.error('保存数据时出错:', error);
        alert('保存数据时出错: ' + error.message);
    }
});

// 跳转到记录页面
function goToRecords() {
    window.location.href = '/excel-template-records.html';
}

// 返回上一页
function goBack() {
    window.history.back();
}

// 点击模态框外部关闭
window.onclick = function(event) {
    const modal = document.getElementById('saveModal');
    if (event.target === modal) {
        closeSaveModal();
    }
}