// analytics.js - 数据分析页面JavaScript

// 使用立即执行函数避免全局变量冲突
(function() {
    'use strict';

    // 页面级变量
    let currentUser = null;
    let charts = {};
    let isAdmin = false;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', async function() {
    try {
        // 检查用户身份验证
        await checkAuthentication();
        
        // 初始化页面
        await initializePage();
        
        // 绑定事件监听器
        bindEventListeners();
        
        // 初始化增强功能
        initializeEnhancements();

        // 加载初始数据
        await loadAnalyticsData();
        
    } catch (error) {
        console.error('页面初始化失败:', error);
        showError('页面初始化失败，请刷新重试');
    }
});

// 检查用户身份验证
async function checkAuthentication() {
    const token = getToken();
    if (!token) {
        window.location.href = '/login.html?redirect=' + encodeURIComponent(window.location.pathname);
        return;
    }

    try {
        const response = await fetch('/api/validate-token', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            throw new Error('Token验证失败');
        }

        const data = await response.json();
        if (!data.valid) {
            throw new Error('Token无效');
        }

        currentUser = data.user;
        isAdmin = currentUser.role === 'admin';
        
        // 显示用户信息
        displayUserInfo();
        
    } catch (error) {
        console.error('身份验证失败:', error);
        localStorage.removeItem('token');
        window.location.href = '/login.html?redirect=' + encodeURIComponent(window.location.pathname);
    }
}

// 显示用户信息
function displayUserInfo() {
    const userInfo = document.getElementById('userInfo');
    const userBadge = document.getElementById('userBadge');
    const userName = document.getElementById('userName');
    
    if (userInfo && userBadge && userName) {
        userName.textContent = currentUser.username;
        
        if (isAdmin) {
            userBadge.classList.add('admin-badge');
            userName.innerHTML = `${currentUser.username} <i class="fas fa-crown ml-1"></i>`;
        }
        
        userInfo.classList.remove('hidden');
    }
}

// 初始化页面
async function initializePage() {
    // 如果是管理员，显示管理员专用功能
    if (isAdmin) {
        document.getElementById('userSelectContainer').classList.remove('hidden');
        document.getElementById('adminControls').classList.remove('hidden');
        document.getElementById('adminSection').classList.remove('hidden');
        
        // 加载用户列表
        await loadUserList();
    }
}

// 加载用户列表 (仅管理员)
async function loadUserList() {
    if (!isAdmin) return;
    
    try {
        const response = await sendAuthenticatedRequest('/api/analytics/all-users-overview');
        if (response.success) {
            const userSelect = document.getElementById('userSelect');
            userSelect.innerHTML = '<option value="">所有用户</option>';
            
            response.data.userStats.forEach(user => {
                const option = document.createElement('option');
                option.value = user.id;
                option.textContent = `${user.username} (${user.role})`;
                userSelect.appendChild(option);
            });
        }
    } catch (error) {
        console.error('加载用户列表失败:', error);
    }
}

// 绑定事件监听器
function bindEventListeners() {
    // 时间范围变化
    document.getElementById('timeRange').addEventListener('change', loadAnalyticsData);
    
    // 用户选择变化 (仅管理员)
    if (isAdmin) {
        const userSelect = document.getElementById('userSelect');
        if (userSelect) {
            userSelect.addEventListener('change', loadAnalyticsData);
        }
    }
    
    // 刷新按钮
    document.getElementById('refreshBtn').addEventListener('click', () => {
        showRefreshAnimation();
        loadAnalyticsData(true); // 强制刷新，不使用缓存
    });
    
    // 清除缓存按钮 (仅管理员)
    if (isAdmin) {
        const clearCacheBtn = document.getElementById('clearCacheBtn');
        if (clearCacheBtn) {
            clearCacheBtn.addEventListener('click', clearCache);
        }
    }
}

// 加载分析数据
async function loadAnalyticsData(forceRefresh = false) {
    showLoading(true);
    clearErrors();
    
    try {
        const timeRange = document.getElementById('timeRange').value;
        const userSelect = document.getElementById('userSelect');
        const selectedUserId = isAdmin && userSelect ? userSelect.value : null;
        
        // 并行加载多个数据源
        const promises = [
            loadUserStats(timeRange, selectedUserId),
            loadActivityTrends(timeRange, selectedUserId),
            loadOperationHabits(timeRange, selectedUserId)
        ];
        
        if (isAdmin && !selectedUserId) {
            promises.push(loadAllUsersOverview(timeRange));
        }
        
        const results = await Promise.allSettled(promises);
        
        // 检查是否有失败的请求
        const failures = results.filter(result => result.status === 'rejected');
        if (failures.length > 0) {
            console.warn('部分数据加载失败:', failures);
            showError('部分数据加载失败，请检查网络连接');
        } else {
            // 所有数据加载成功，添加图表交互功能
            setTimeout(() => {
                addChartInteractions();
            }, 500);
        }
        
    } catch (error) {
        console.error('加载分析数据失败:', error);
        showError('数据加载失败: ' + error.message);
    } finally {
        showLoading(false);
    }
}

// 加载用户统计数据
async function loadUserStats(timeRange, userId) {
    try {
        const params = new URLSearchParams({ timeRange });
        if (userId) params.append('userId', userId);
        
        const response = await sendAuthenticatedRequest(`/api/analytics/user-stats?${params}`);
        if (response.success) {
            displayStatsCards(response.data);
        }
    } catch (error) {
        console.error('加载用户统计失败:', error);
        throw error;
    }
}

// 加载活跃度趋势
async function loadActivityTrends(timeRange, userId) {
    try {
        const params = new URLSearchParams({ timeRange });
        if (userId) params.append('userId', userId);
        
        const response = await sendAuthenticatedRequest(`/api/analytics/activity-trends?${params}`);
        if (response.success) {
            displayActivityTrendChart(response.data);
            displayTimeDistributionChart(response.data);
        }
    } catch (error) {
        console.error('加载活跃度趋势失败:', error);
        throw error;
    }
}

// 加载操作习惯
async function loadOperationHabits(timeRange, userId) {
    try {
        const params = new URLSearchParams({ timeRange });
        if (userId) params.append('userId', userId);
        
        const response = await sendAuthenticatedRequest(`/api/analytics/operation-habits?${params}`);
        if (response.success) {
            displayOperationTypeChart(response.data);
            displayAmountDistributionChart(response.data);
        }
    } catch (error) {
        console.error('加载操作习惯失败:', error);
        throw error;
    }
}

// 加载所有用户概览 (仅管理员)
async function loadAllUsersOverview(timeRange) {
    if (!isAdmin) return;
    
    try {
        const params = new URLSearchParams({ timeRange });
        const response = await sendAuthenticatedRequest(`/api/analytics/all-users-overview?${params}`);
        if (response.success) {
            displayUserOverviewTable(response.data);
        }
    } catch (error) {
        console.error('加载用户概览失败:', error);
        throw error;
    }
}

// 显示统计卡片
function displayStatsCards(data) {
    const container = document.getElementById('statsCards');
    
    const cards = [
        {
            title: '价格记录',
            value: data.priceRecords.count,
            icon: 'fas fa-file-invoice-dollar',
            color: 'from-blue-500 to-blue-600'
        },
        {
            title: '总金额',
            value: `¥${data.priceRecords.totalAmount.toLocaleString()}`,
            icon: 'fas fa-coins',
            color: 'from-green-500 to-green-600'
        },
        {
            title: '数据转换',
            value: data.rawDataConversions.count,
            icon: 'fas fa-exchange-alt',
            color: 'from-purple-500 to-purple-600'
        },
        {
            title: '平均金额',
            value: `¥${data.priceRecords.avgAmount.toFixed(2)}`,
            icon: 'fas fa-chart-line',
            color: 'from-orange-500 to-orange-600'
        }
    ];
    
    container.innerHTML = cards.map(card => `
        <div class="stat-card bg-gradient-to-r ${card.color}">
            <div class="flex items-center justify-between">
                <div>
                    <div class="stat-number">${card.value}</div>
                    <div class="stat-label">${card.title}</div>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="${card.icon}"></i>
                </div>
            </div>
        </div>
    `).join('');
}

// 显示活跃度趋势图
function displayActivityTrendChart(data) {
    const ctx = document.getElementById('activityTrendChart').getContext('2d');
    
    // 销毁现有图表
    if (charts.activityTrend) {
        charts.activityTrend.destroy();
    }
    
    // 准备数据
    const dates = data.dailyPriceRecords.map(item => item.date);
    const priceRecords = data.dailyPriceRecords.map(item => item.count);
    const rawDataCounts = data.dailyRawData.map(item => item.count);
    
    charts.activityTrend = new Chart(ctx, {
        type: 'line',
        data: {
            labels: dates,
            datasets: [
                {
                    label: '价格记录',
                    data: priceRecords,
                    borderColor: '#3b82f6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.4
                },
                {
                    label: '数据转换',
                    data: rawDataCounts,
                    borderColor: '#8b5cf6',
                    backgroundColor: 'rgba(139, 92, 246, 0.1)',
                    tension: 0.4
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

// 工具函数
function showLoading(show) {
    const indicator = document.getElementById('loadingIndicator');
    if (show) {
        indicator.classList.remove('hidden');
    } else {
        indicator.classList.add('hidden');
    }
}

function showError(message) {
    const container = document.getElementById('errorContainer');
    container.innerHTML = `<div class="error-message">${message}</div>`;
}

function clearErrors() {
    document.getElementById('errorContainer').innerHTML = '';
}

// 清除缓存 (仅管理员)
async function clearCache() {
    if (!isAdmin) return;
    
    try {
        const response = await sendAuthenticatedRequest('/api/analytics/cache', 'DELETE');
        if (response.success) {
            showSuccess('缓存已清除');
            loadAnalyticsData(true);
        }
    } catch (error) {
        console.error('清除缓存失败:', error);
        showError('清除缓存失败: ' + error.message);
    }
}

// 显示操作类型分布图
function displayOperationTypeChart(data) {
    const ctx = document.getElementById('operationTypeChart').getContext('2d');

    // 销毁现有图表
    if (charts.operationType) {
        charts.operationType.destroy();
    }

    const operationTypes = data.operationTypes || [];
    const labels = operationTypes.map(item => item.operation_type || '未知');
    const counts = operationTypes.map(item => item.count);

    charts.operationType = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: counts,
                backgroundColor: [
                    '#3b82f6', '#8b5cf6', '#10b981', '#f59e0b',
                    '#ef4444', '#6366f1', '#14b8a6', '#f97316'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// 显示金额分布图
function displayAmountDistributionChart(data) {
    const ctx = document.getElementById('amountDistributionChart').getContext('2d');

    // 销毁现有图表
    if (charts.amountDistribution) {
        charts.amountDistribution.destroy();
    }

    const amountDistribution = data.amountDistribution || [];
    const labels = amountDistribution.map(item => item.amount_range);
    const counts = amountDistribution.map(item => item.count);

    charts.amountDistribution = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: '记录数量',
                data: counts,
                backgroundColor: 'rgba(59, 130, 246, 0.8)',
                borderColor: '#3b82f6',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

// 显示时间分布图
function displayTimeDistributionChart(data) {
    const ctx = document.getElementById('timeDistributionChart').getContext('2d');

    // 销毁现有图表
    if (charts.timeDistribution) {
        charts.timeDistribution.destroy();
    }

    const hourlyActivity = data.hourlyActivity || [];

    // 创建24小时的完整数据
    const hours = Array.from({length: 24}, (_, i) => i);
    const activities = hours.map(hour => {
        const found = hourlyActivity.find(item => item.hour === hour);
        return found ? found.total_operations : 0;
    });

    charts.timeDistribution = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: hours.map(h => `${h}:00`),
            datasets: [{
                label: '操作次数',
                data: activities,
                backgroundColor: 'rgba(139, 92, 246, 0.8)',
                borderColor: '#8b5cf6',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

// 显示用户概览表格 (仅管理员)
function displayUserOverviewTable(data) {
    if (!isAdmin) return;

    const container = document.getElementById('userOverviewTable');
    const userStats = data.userStats || [];

    if (userStats.length === 0) {
        container.innerHTML = '<p class="text-gray-500 text-center py-4">暂无用户数据</p>';
        return;
    }

    const tableHTML = `
        <table class="min-w-full bg-white border border-gray-200 rounded-lg overflow-hidden">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用户</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">角色</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">价格记录</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">总金额</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">数据转换</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最后活跃</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                ${userStats.map(user => `
                    <tr class="hover:bg-gray-50">
                        <td class="px-4 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="text-sm font-medium text-gray-900">${user.username}</div>
                            </div>
                        </td>
                        <td class="px-4 py-4 whitespace-nowrap">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                user.role === 'admin' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'
                            }">
                                ${user.role === 'admin' ? '管理员' : '普通用户'}
                            </span>
                        </td>
                        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">${user.price_record_count}</td>
                        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">¥${parseFloat(user.total_amount).toLocaleString()}</td>
                        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">${user.raw_data_count}</td>
                        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                            ${user.last_price_record ? new Date(user.last_price_record).toLocaleDateString() : '无记录'}
                        </td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;

    container.innerHTML = tableHTML;
}

// 添加数据导出功能
function exportData() {
    if (!currentUser) return;

    const timeRange = document.getElementById('timeRange').value;
    const userSelect = document.getElementById('userSelect');
    const selectedUserId = isAdmin && userSelect ? userSelect.value : null;

    // 构建导出数据
    const exportData = {
        user: currentUser,
        timeRange: timeRange,
        selectedUserId: selectedUserId,
        exportTime: new Date().toISOString(),
        charts: {}
    };

    // 收集图表数据
    Object.keys(charts).forEach(chartKey => {
        if (charts[chartKey] && charts[chartKey].data) {
            exportData.charts[chartKey] = charts[chartKey].data;
        }
    });

    // 创建下载链接
    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    const url = URL.createObjectURL(dataBlob);

    const link = document.createElement('a');
    link.href = url;
    link.download = `analytics-data-${timeRange}-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    showSuccess('数据导出成功');
}

// 添加数据刷新动画
function showRefreshAnimation() {
    const refreshBtn = document.getElementById('refreshBtn');
    const icon = refreshBtn.querySelector('i');

    if (icon) {
        icon.classList.add('fa-spin');
        refreshBtn.disabled = true;

        setTimeout(() => {
            icon.classList.remove('fa-spin');
            refreshBtn.disabled = false;
        }, 2000);
    }
}

// 添加图表交互功能
function addChartInteractions() {
    // 为所有图表添加点击事件
    Object.keys(charts).forEach(chartKey => {
        const chart = charts[chartKey];
        if (chart && chart.canvas) {
            chart.canvas.onclick = function(evt) {
                const points = chart.getElementsAtEventForMode(evt, 'nearest', { intersect: true }, true);

                if (points.length) {
                    const firstPoint = points[0];
                    const label = chart.data.labels[firstPoint.index];
                    const value = chart.data.datasets[firstPoint.datasetIndex].data[firstPoint.index];

                    showTooltip(`${label}: ${value}`, evt.clientX, evt.clientY);
                }
            };
        }
    });
}

// 显示自定义提示框
function showTooltip(text, x, y) {
    // 移除现有提示框
    const existingTooltip = document.getElementById('customTooltip');
    if (existingTooltip) {
        existingTooltip.remove();
    }

    // 创建新提示框
    const tooltip = document.createElement('div');
    tooltip.id = 'customTooltip';
    tooltip.className = 'fixed bg-gray-800 text-white px-3 py-2 rounded-lg text-sm z-50 pointer-events-none';
    tooltip.textContent = text;
    tooltip.style.left = x + 'px';
    tooltip.style.top = (y - 40) + 'px';

    document.body.appendChild(tooltip);

    // 3秒后自动移除
    setTimeout(() => {
        if (tooltip.parentNode) {
            tooltip.parentNode.removeChild(tooltip);
        }
    }, 3000);
}

// 添加键盘快捷键支持
function addKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + R: 刷新数据
        if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
            e.preventDefault();
            loadAnalyticsData(true);
            showRefreshAnimation();
        }

        // Ctrl/Cmd + E: 导出数据
        if ((e.ctrlKey || e.metaKey) && e.key === 'e') {
            e.preventDefault();
            exportData();
        }

        // Esc: 清除错误消息
        if (e.key === 'Escape') {
            clearErrors();
        }
    });
}

// 添加响应式图表调整
function addResponsiveCharts() {
    window.addEventListener('resize', function() {
        Object.keys(charts).forEach(chartKey => {
            if (charts[chartKey]) {
                charts[chartKey].resize();
            }
        });
    });
}

// 初始化增强功能
function initializeEnhancements() {
    addKeyboardShortcuts();
    addResponsiveCharts();

    // 添加导出按钮
    const refreshBtn = document.getElementById('refreshBtn');
    if (refreshBtn && refreshBtn.parentNode) {
        const exportBtn = document.createElement('button');
        exportBtn.className = 'bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors';
        exportBtn.innerHTML = '<i class="fas fa-download mr-2"></i>导出数据';
        exportBtn.onclick = exportData;

        refreshBtn.parentNode.insertBefore(exportBtn, refreshBtn.nextSibling);
    }
}

function showSuccess(message) {
    const container = document.getElementById('errorContainer');
    container.innerHTML = `<div class="success-message">${message}</div>`;
    setTimeout(() => {
        container.innerHTML = '';
    }, 3000);
}

})(); // 结束立即执行函数
