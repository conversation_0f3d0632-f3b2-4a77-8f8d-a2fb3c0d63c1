const express = require('express');
const router = express.Router();
const db = require('../config/db');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const sharp = require('sharp');
const jwt = require('jsonwebtoken');

// 文件上传配置
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        const uploadDir = path.join(__dirname, '..', 'public', 'uploads', 'phrases');
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const extension = path.extname(file.originalname);
        cb(null, 'phrase-' + uniqueSuffix + extension);
    }
});

const upload = multer({
    storage: storage,
    limits: { fileSize: 10 * 1024 * 1024 }, // 10MB
    fileFilter: function (req, file, cb) {
        cb(null, true);
    }
});

// 获取所有常用话术
router.get('/', async (req, res) => {
    try {
        const [rows] = await db.query('SELECT * FROM common_phrases ORDER BY updated_at DESC');
        res.json(rows);
    } catch (error) {
        console.error('获取常用话术失败:', error);
        res.status(500).json({ error: '获取常用话术失败' });
    }
});

// 按ID获取单个话术
router.get('/:id', async (req, res) => {
    try {
        const [rows] = await db.query('SELECT * FROM common_phrases WHERE id = ?', [req.params.id]);
        if (rows.length === 0) {
            return res.status(404).json({ error: '话术未找到' });
        }
        res.json(rows[0]);
    } catch (error) {
        console.error('获取话术详情失败:', error);
        res.status(500).json({ error: '获取话术详情失败' });
    }
});

// 按类别获取话术
router.get('/category/:category', async (req, res) => {
    try {
        const [rows] = await db.query('SELECT * FROM common_phrases WHERE category = ? ORDER BY updated_at DESC', [req.params.category]);
        res.json(rows);
    } catch (error) {
        console.error('按类别获取话术失败:', error);
        res.status(500).json({ error: '按类别获取话术失败' });
    }
});

// 按类型获取话术
router.get('/type/:type', async (req, res) => {
    try {
        const [rows] = await db.query('SELECT * FROM common_phrases WHERE type = ? ORDER BY updated_at DESC', [req.params.type]);
        res.json(rows);
    } catch (error) {
        console.error('按类型获取话术失败:', error);
        res.status(500).json({ error: '按类型获取话术失败' });
    }
});

// 添加新话术
router.post('/', upload.single('file'), async (req, res) => {
    try {
        const { content = '', type, category, tags } = req.body;
        let file_url = null;
        let processedContent = content.trim();

        // 处理文件上传
        if (req.file) {
            // 检查文件类型是否为图片
            const isImage = req.file.mimetype.startsWith('image/');
            
            if (isImage) {
                // 对图片进行WebP转换
                const originalPath = req.file.path;
                const filename = path.basename(req.file.filename, path.extname(req.file.filename));
                const webpFilename = `${filename}.webp`;
                const webpPath = path.join(req.file.destination, webpFilename);
                
                await sharp(originalPath)
                    .webp({ quality: 100, lossless: true })
                    .toFile(webpPath);
                
                // 删除原始文件
                fs.unlinkSync(originalPath);
                
                file_url = `/uploads/phrases/${webpFilename}`;
            } else {
                file_url = `/uploads/phrases/${req.file.filename}`;
            }
        }

        // 确保至少有内容或文件
        if (!processedContent && !file_url) {
            return res.status(400).json({ error: '话术需要内容或文件' });
        }

        // 根据内容和文件类型自动判断类型
        let detectedType = 'text'; // 默认为文本类型
        
        // 根据文件类型自动判断
        if (req.file) {
            if (req.file.mimetype.startsWith('image/')) {
                detectedType = 'image';
                // 如果没有提供内容，对于图片类型可以设置一个默认描述
                if (!processedContent) {
                    processedContent = `图片话术 #${new Date().toLocaleString('zh-CN')}`;
                }
            } else {
                detectedType = 'file';
                // 如果没有提供内容，对于文件类型可以设置默认描述
                if (!processedContent) {
                    processedContent = `文件话术 #${new Date().toLocaleString('zh-CN')}`;
                }
            }
        } 
        // 根据内容判断链接
        else if (processedContent.match(/^https?:\/\//i)) {
            detectedType = 'link';
        }
        
        // 如果明确指定了类型，则使用指定的类型
        if (type && type !== 'text') {
            detectedType = type;
        }

        const [result] = await db.query(
            'INSERT INTO common_phrases (content, type, category, tags, file_url) VALUES (?, ?, ?, ?, ?)',
            [processedContent, detectedType, category, tags, file_url]
        );

        res.status(201).json({ 
            id: result.insertId,
            message: '话术添加成功' 
        });
    } catch (error) {
        console.error('添加话术失败:', error);
        res.status(500).json({ error: '添加话术失败' });
    }
});

// 更新话术
router.put('/:id', upload.single('file'), async (req, res) => {
    try {
        const { content = '', type, category, tags } = req.body;
        const id = req.params.id;
        let processedContent = content.trim();
        
        // 检查话术是否存在
        const [existingRows] = await db.query('SELECT * FROM common_phrases WHERE id = ?', [id]);
        if (existingRows.length === 0) {
            return res.status(404).json({ error: '话术未找到' });
        }
        
        let file_url = existingRows[0].file_url;
        
        // 处理文件上传
        if (req.file) {
            // 如果有新文件上传，删除旧文件
            if (file_url) {
                const oldFilePath = path.join(__dirname, '..', 'public', file_url);
                if (fs.existsSync(oldFilePath)) {
                    fs.unlinkSync(oldFilePath);
                }
            }
            
            // 检查文件类型是否为图片
            const isImage = req.file.mimetype.startsWith('image/');
            
            if (isImage) {
                // 对图片进行WebP转换
                const originalPath = req.file.path;
                const filename = path.basename(req.file.filename, path.extname(req.file.filename));
                const webpFilename = `${filename}.webp`;
                const webpPath = path.join(req.file.destination, webpFilename);
                
                await sharp(originalPath)
                    .webp({ quality: 100, lossless: true })
                    .toFile(webpPath);
                
                // 删除原始文件
                fs.unlinkSync(originalPath);
                
                file_url = `/uploads/phrases/${webpFilename}`;
            } else {
                file_url = `/uploads/phrases/${req.file.filename}`;
            }
        }
        
        // 确保至少有内容或文件
        if (!processedContent && !file_url) {
            return res.status(400).json({ error: '话术需要内容或文件' });
        }
        
        // 根据内容和文件类型自动判断类型
        let detectedType = existingRows[0].type; // 默认保持原有类型
        
        // 根据新上传的文件自动判断
        if (req.file) {
            if (req.file.mimetype.startsWith('image/')) {
                detectedType = 'image';
                // 如果没有提供内容，对于图片类型可以设置一个默认描述
                if (!processedContent) {
                    processedContent = `图片话术 #${new Date().toLocaleString('zh-CN')}`;
                }
            } else {
                detectedType = 'file';
                // 如果没有提供内容，对于文件类型可以设置默认描述
                if (!processedContent) {
                    processedContent = `文件话术 #${new Date().toLocaleString('zh-CN')}`;
                }
            }
        } 
        // 如果没有文件但有内容变更，可能需要调整类型
        else if (processedContent !== existingRows[0].content) {
            if (processedContent.match(/^https?:\/\//i)) {
                detectedType = 'link';
            } else if (!file_url) {
                detectedType = 'text';
            }
        }
        
        // 如果明确指定了类型，则使用指定的类型
        if (type) {
            detectedType = type;
        }
        
        await db.query(
            'UPDATE common_phrases SET content = ?, type = ?, category = ?, tags = ?, file_url = ? WHERE id = ?',
            [processedContent, detectedType, category, tags, file_url, id]
        );
        
        res.json({ message: '话术更新成功' });
    } catch (error) {
        console.error('更新话术失败:', error);
        res.status(500).json({ error: '更新话术失败' });
    }
});

// 删除话术
router.delete('/:id', async (req, res) => {
    try {
        const id = req.params.id;
        
        // 检查话术是否存在
        const [existingRows] = await db.query('SELECT * FROM common_phrases WHERE id = ?', [id]);
        if (existingRows.length === 0) {
            return res.status(404).json({ error: '话术未找到' });
        }
        
        // 如果有关联文件，删除文件
        if (existingRows[0].file_url) {
            const filePath = path.join(__dirname, '..', 'public', existingRows[0].file_url);
            if (fs.existsSync(filePath)) {
                fs.unlinkSync(filePath);
            }
        }
        
        await db.query('DELETE FROM common_phrases WHERE id = ?', [id]);
        
        res.json({ message: '话术删除成功' });
    } catch (error) {
        console.error('删除话术失败:', error);
        res.status(500).json({ error: '删除话术失败' });
    }
});

// 增加使用次数
router.post('/:id/use', async (req, res) => {
    try {
        const id = req.params.id;
        
        await db.query('UPDATE common_phrases SET usage_count = usage_count + 1 WHERE id = ?', [id]);
        
        res.json({ message: '使用次数更新成功' });
    } catch (error) {
        console.error('更新使用次数失败:', error);
        res.status(500).json({ error: '更新使用次数失败' });
    }
});

// 获取所有类别
router.get('/meta/categories', async (req, res) => {
    try {
        const [rows] = await db.query('SELECT DISTINCT category FROM common_phrases WHERE category IS NOT NULL');
        const categories = rows.map(row => row.category);
        res.json(categories);
    } catch (error) {
        console.error('获取类别失败:', error);
        res.status(500).json({ error: '获取类别失败' });
    }
});

module.exports = router; 