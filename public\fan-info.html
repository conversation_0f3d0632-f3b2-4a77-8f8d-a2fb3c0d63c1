<!DOCTYPE html>
<html lang="zh-CN" class="light">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>风扇信息管理 - 电脑配件综合管理系统</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">

    <!-- Tailwind CSS v3 -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Viewer.js 图片查看器 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/viewerjs/1.11.6/viewer.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/viewerjs/1.11.6/viewer.min.js"></script>
    
    <!-- Chart.js 图表库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- 本地脚本 -->
    <script src="/js/js-pinyin.js"></script>
    <script src="/js/pinyin-wrapper.js"></script>
    <script src="/js/main.js"></script>

    <!-- 自定义样式 -->
    <link rel="stylesheet" href="/css/fan-info.css">
    <script src="/js/permission-helper.js"></script>
</head>

<body class="bg-slate-50 text-slate-800 dark:bg-slate-900 dark:text-slate-100 min-h-screen transition-colors duration-300">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 py-6">
        <!-- 顶部导航栏 -->
        <header class="mb-8">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-3">
                    <div class="bg-primary-500 text-white p-2 rounded-lg shadow-md">
                        <i class="fas fa-fan text-xl"></i>
                    </div>
                    <h1 class="text-2xl sm:text-3xl font-bold text-primary-700 dark:text-primary-400">
                        风扇信息管理
                    </h1>
                </div>
                
                <!-- 右侧操作区 -->
                <div class="flex items-center space-x-3">
                    <!-- 主题切换按钮 -->
                    <button id="themeToggle" class="p-2 rounded-full hover:bg-slate-200 dark:hover:bg-slate-700 transition-colors">
                        <i class="fas fa-moon text-lg text-primary-600 dark:hidden"></i>
                        <i class="fas fa-sun text-lg text-primary-400 hidden dark:block"></i>
                    </button>
                    
                    <!-- 导航链接 -->
                    <div class="flex space-x-2">
                        <a href="/pc-components.html" class="inline-flex items-center px-3 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-md shadow-sm transition-colors">
                            <i class="fas fa-arrow-left mr-2"></i>
                            <span>返回配件列表</span>
                        </a>
                        <a href="/" class="inline-flex items-center px-3 py-2 bg-slate-600 hover:bg-slate-700 text-white rounded-md shadow-sm transition-colors">
                            <i class="fas fa-home mr-2"></i>
                            <span>首页</span>
                    </a>
                </div>
            </div>
            </div>
            <p class="text-slate-600 dark:text-slate-400 mt-2 text-sm">记录和管理机箱风扇配置信息，轻松追踪不同型号的风扇性能参数</p>
        </header>

        <!-- 页面主体：左侧表单，右侧数据表 -->
        <div class="grid grid-cols-1 lg:grid-cols-12 gap-6">
            <!-- 左侧表单区域 -->
            <div class="lg:col-span-4">
                <div class="bg-white dark:bg-slate-800 rounded-xl shadow-lg p-5 transition-all">
                    <h2 class="flex items-center text-xl font-semibold text-primary-700 dark:text-primary-400 mb-4">
                        <i class="fas fa-plus-circle mr-2 text-primary-500"></i>
                        添加风扇信息
                </h2>

                    <!-- 智能识别输入 -->
                    <div class="mb-6 bg-primary-50 dark:bg-slate-700/30 rounded-lg p-5 border border-primary-200 dark:border-primary-700/30 shadow-sm hover:shadow-md transition-shadow">
                        <h3 class="text-sm font-medium text-primary-800 dark:text-primary-300 mb-3 flex items-center">
                            <i class="fas fa-magic mr-1.5 text-primary-500"></i>
                            智能识别输入
                        </h3>
                        <div class="flex flex-col gap-3">
                            <div>
                                <input 
                                    type="text" 
                                    id="smartInput"
                                    placeholder="粘贴风扇参数（示例：猫头鹰 NF-A12x25 PWM 120mm 2000RPM）" 
                                    class="w-full px-3 py-2.5 bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-500 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:text-white shadow-sm"
                                >
                            </div>
                            <div class="flex justify-end">
                                <button 
                                    type="button" 
                                    id="autoFillBtn"
                                    class="btn-click bg-gradient-to-r from-primary-600 to-primary-500 text-white px-5 py-2.5 rounded-md hover:from-primary-700 hover:to-primary-600 text-sm transition-all flex items-center justify-center shadow-sm hover:shadow hover:scale-[1.02] active:scale-[0.98]"
                                >
                                    <i class="fas fa-robot mr-2"></i>智能解析
                                </button>
                            </div>
                        </div>
                        <p class="mt-2 text-xs text-primary-700 dark:text-primary-400 flex items-center">
                            <i class="fas fa-info-circle mr-1"></i>
                            支持格式：品牌 型号 尺寸 转速 风量 噪音 轴承类型 价格
                        </p>
                    </div>
                    
                    <!-- 表单 -->
                    <form id="fanForm" class="space-y-5">
                        <!-- 1. 基本信息 -->
                        <div class="border border-slate-200 dark:border-slate-700 rounded-lg p-5">
                            <h3 class="text-sm font-medium text-slate-700 dark:text-slate-300 mb-4 flex items-center">
                                <i class="fas fa-info-circle mr-2 text-primary-500"></i>
                                基本信息
                            </h3>

                            <div class="space-y-5">
                                <div>
                                    <label for="model" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                                        风扇型号 <span class="text-red-500">*</span>
                                    </label>
                                    <input 
                                        type="text" 
                                        id="model"
                                        class="w-full px-3 py-2 bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:text-white text-sm"
                                        placeholder="如: Arctic P12 PWM" 
                                        required
                                    >
                                </div>

                                <div class="space-y-5">
                                    <div>
                                        <label for="brand" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                                            品牌 <span class="text-red-500">*</span>
                                        </label>
                                        <select 
                                            id="brand"
                                            class="w-full px-3 py-2 bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:text-white text-sm"
                                            required
                                        >
                                            <option value="">选择品牌</option>
                                            <option value="猫头鹰">猫头鹰 (Noctua)</option>
                                            <option value="九州风神">九州风神 (Deepcool)</option>
                                            <option value="酷冷至尊">酷冷至尊 (Cooler Master)</option>
                                            <option value="酷马">酷马 (Thermaltake)</option>
                                            <option value="海盗船">海盗船 (Corsair)</option>
                                            <option value="利民">利民 (Thermalright)</option>
                                            <option value="Arctic">Arctic</option>
                                            <option value="Be Quiet">Be Quiet</option>
                                            <option value="爱国者">爱国者 (Aigo)</option>
                                            <option value="安钛克">安钛克 (Antec)</option>
                                            <option value="快睿">快睿 (Scythe)</option>
                                            <option value="银欣">银欣 (SilverStone)</option>
                                            <option value="其他">其他</option>
                                        </select>
                                    </div>

                                    <div>
                                        <label for="size" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                                            尺寸 (mm) <span class="text-red-500">*</span>
                                        </label>
                                        <select 
                                            id="size"
                                            class="w-full px-3 py-2 bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:text-white text-sm"
                                            required
                                        >
                                            <option value="">选择尺寸</option>
                                            <option value="80">80mm</option>
                                            <option value="92">92mm</option>
                                            <option value="120">120mm</option>
                                            <option value="140">140mm</option>
                                            <option value="200">200mm</option>
                                            <option value="other">其他尺寸</option>
                                        </select>
                                    </div>
                                </div>
                        </div>
                    </div>

                        <!-- 2. 规格信息 -->
                        <div class="border border-slate-200 dark:border-slate-700 rounded-lg p-5">
                            <h3 class="text-sm font-medium text-slate-700 dark:text-slate-300 mb-4 flex items-center">
                                <i class="fas fa-microchip mr-2 text-primary-500"></i>
                                规格信息
                            </h3>

                            <div class="space-y-5">
                                <div>
                                    <label for="thickness" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                                        厚度 (mm)
                                    </label>
                                    <input 
                                        type="number" 
                                        id="thickness"
                                        class="w-full px-3 py-2 bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:text-white text-sm"
                                        placeholder="如: 25"
                                    >
                                </div>

                                <div>
                                    <label for="rpm" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                                        转速范围 (RPM)
                                    </label>
                                    <div class="grid grid-cols-2 gap-2">
                                        <div>
                                            <input 
                                                type="number" 
                                                id="speed_min"
                                                class="w-full px-3 py-2 bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:text-white text-sm"
                                                placeholder="最低转速"
                                            >
                                        </div>
                                        <div>
                                            <input 
                                                type="number" 
                                                id="speed_max"
                                                class="w-full px-3 py-2 bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:text-white text-sm"
                                                placeholder="最高转速"
                                            >
                                        </div>
                                    </div>
                                    <input 
                                        type="hidden" 
                                        id="rpm"
                                        class="w-full px-3 py-2 bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:text-white text-sm"
                                        placeholder="如: 800-1500"
                                    >
                                </div>

                                <div>
                                    <label for="airflow" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                                        风量 (CFM)
                                    </label>
                                    <input 
                                        type="text" 
                                        id="airflow"
                                        class="w-full px-3 py-2 bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:text-white text-sm"
                                        placeholder="如: 56.3"
                                    >
                                </div>

                                <div>
                                    <label for="noiseLevel" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                                        噪音级别 (dBA)
                                    </label>
                                    <input 
                                        type="text" 
                                        id="noiseLevel"
                                        class="w-full px-3 py-2 bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:text-white text-sm"
                                        placeholder="如: 22.5"
                                    >
                                </div>

                                <div>
                                    <label for="staticPressure" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                                        静压 (mmH₂O)
                                    </label>
                                    <input 
                                        type="text" 
                                        id="staticPressure"
                                        class="w-full px-3 py-2 bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:text-white text-sm"
                                        placeholder="如: 1.45"
                                    >
                                </div>

                                <div>
                                    <label for="connector" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                                        接口类型
                                    </label>
                                    <select 
                                        id="connector"
                                        class="w-full px-3 py-2 bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:text-white text-sm"
                                    >
                                        <option value="">选择接口类型</option>
                                        <option value="3-pin">3-pin</option>
                                        <option value="4-pin PWM">4-pin PWM</option>
                                        <option value="ARGB">5V ARGB</option>
                                        <option value="RGB">12V RGB</option>
                                        <option value="Molex">Molex</option>
                                        <option value="SATA">SATA</option>
                                        <option value="其他">其他</option>
                                    </select>
                                </div>

                                <div>
                                    <label for="pwm" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                                        PWM支持
                                    </label>
                                    <select 
                                        id="pwm"
                                        class="w-full px-3 py-2 bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:text-white text-sm"
                                    >
                                        <option value="">选择</option>
                                        <option value="1">支持</option>
                                        <option value="0">不支持</option>
                                    </select>
                                </div>
                            </div>
                    </div>

                        <!-- 3. 其他特性 -->
                        <div class="border border-slate-200 dark:border-slate-700 rounded-lg p-5">
                            <h3 class="text-sm font-medium text-slate-700 dark:text-slate-300 mb-4 flex items-center">
                                <i class="fas fa-cogs mr-2 text-primary-500"></i>
                                其他特性
                            </h3>

                            <div class="space-y-5">
                                <div>
                                    <label for="bearing_type" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                                        轴承类型
                                    </label>
                                    <select 
                                        id="bearing_type"
                                        class="w-full px-3 py-2 bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:text-white text-sm"
                                    >
                                        <option value="">选择轴承类型</option>
                                        <option value="液压轴承">液压轴承</option>
                                        <option value="双滚珠轴承">双滚珠轴承</option>
                                        <option value="磁悬浮轴承">磁悬浮轴承</option>
                                        <option value="FDB轴承">FDB轴承</option>
                                        <option value="SSO轴承">SSO轴承</option>
                                        <option value="其他">其他</option>
                                    </select>
                                </div>

                                <div>
                                    <label for="rgbLighting" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                                        RGB灯效
                                    </label>
                                    <select 
                                        id="rgbLighting"
                                        class="w-full px-3 py-2 bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:text-white text-sm"
                                    >
                                        <option value="">选择</option>
                                        <option value="1">有</option>
                                        <option value="0">无</option>
                                    </select>
                                </div>
                                
                                <div>
                                    <label for="color" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                                        颜色
                                    </label>
                                    <input 
                                        type="text" 
                                        id="color"
                                        class="w-full px-3 py-2 bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:text-white text-sm"
                                        placeholder="如: 黑色、白色等"
                                    >
                                </div>

                                <div>
                                    <label for="warranty" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                                        保修期 (年)
                                    </label>
                                    <input 
                                        type="number" 
                                        id="warranty"
                                        class="w-full px-3 py-2 bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:text-white text-sm"
                                        placeholder="如: 5"
                                    >
                                </div>

                                <div>
                                    <label for="price" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                                        价格
                                    </label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <span class="text-slate-500 dark:text-slate-400 sm:text-sm">¥</span>
                                        </div>
                                        <input 
                                            type="number" 
                                            id="price"
                                            class="w-full pl-8 px-3 py-2 bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:text-white text-sm"
                                            placeholder="0.00" 
                                            min="0" 
                                            step="0.01"
                                        >
                                    </div>
                                </div>
                            </div>

                            <div class="mt-3">
                                <label for="notes" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                                    备注
                                </label>
                                <textarea 
                                    id="notes" 
                                    rows="3"
                                    class="w-full px-3 py-2 bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:text-white text-sm"
                                    placeholder="其他需要备注的信息..."
                                ></textarea>
                        </div>
                    </div>

                        <!-- 4. 图片上传 -->
                        <div class="border border-slate-200 dark:border-slate-700 rounded-lg p-5">
                            <h3 class="text-sm font-medium text-slate-700 dark:text-slate-300 mb-4 flex items-center">
                                <i class="fas fa-image mr-2 text-primary-500"></i>
                                图片上传
                            </h3>

                            <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-slate-300 dark:border-slate-600 border-dashed rounded-lg">
                            <div class="space-y-1 text-center">
                                    <i class="fas fa-cloud-upload-alt text-3xl text-slate-400"></i>
                                    <div class="flex text-sm text-slate-600 dark:text-slate-400 mt-2">
                                        <label for="fanImage" class="relative cursor-pointer bg-white dark:bg-slate-700 rounded-md font-medium text-primary-600 dark:text-primary-400 hover:text-primary-500">
                                        <span>上传图片</span>
                                        <input id="fanImage" name="fanImage" type="file" accept="image/*" class="sr-only">
                                    </label>
                                        <p class="pl-1">或将图片拖拽到此处</p>
                                </div>
                                    <p class="text-xs text-slate-500 dark:text-slate-400">PNG, JPG, GIF 格式，大小不超过5MB（将自动转换为WebP格式）</p>
                            </div>
                        </div>

                        <!-- 上传进度条 -->
                        <div id="uploadProgressContainer" class="mt-2 hidden">
                            <div class="bg-gray-200 dark:bg-gray-700 rounded-full h-3 mb-2 overflow-hidden">
                                <div id="uploadProgressBar" class="bg-gradient-to-r from-teal-500 to-teal-600 dark:from-teal-400 dark:to-teal-500 h-3 rounded-full transition-all duration-500 ease-out relative" style="width: 0%">
                                    <!-- 进度条光泽效果 -->
                                    <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-20 animate-pulse"></div>
                                </div>
                            </div>
                            <div class="flex justify-between items-center text-xs">
                                <span id="uploadProgressText" class="text-gray-600 dark:text-gray-400 flex items-center">
                                    <i id="uploadProgressIcon" class="fas fa-upload mr-1"></i>
                                    准备上传...
                                </span>
                                <span id="uploadProgressPercent" class="text-teal-600 dark:text-teal-400 font-medium">0%</span>
                            </div>
                        </div>

                            <div id="imagePreviewContainer" class="mt-4 hidden">
                                <div class="relative inline-block group">
                                    <img id="imagePreview" src="#" alt="预览图" class="h-28 rounded-md shadow-md transition-transform duration-300 group-hover:scale-105">
                                    <button
                                        type="button"
                                        id="removeImageBtn"
                                        class="absolute -top-2 -right-2 bg-red-500 hover:bg-red-600 text-white rounded-full w-6 h-6 flex items-center justify-center transition-transform duration-200 transform group-hover:scale-110"
                                    >
                                    <i class="fas fa-times text-xs"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                        <button 
                            type="submit" 
                            class="w-full bg-gradient-to-r from-primary-600 to-primary-500 hover:from-primary-700 hover:to-primary-600 text-white py-3 px-6 rounded-md shadow-md transition-all duration-200 hover:shadow-lg flex items-center justify-center btn-click text-base"
                        >
                            <i class="fas fa-save mr-2"></i> 保存风扇信息
                        </button>
                </form>
                </div>
            </div>

            <!-- 右侧列表区域 -->
            <div class="lg:col-span-8">
                <!-- 搜索和过滤 -->
                <div class="bg-white dark:bg-slate-800 rounded-xl shadow-lg p-5 mb-6 transition-all">
                    <h2 class="text-xl font-semibold text-primary-700 dark:text-primary-400 mb-4 flex items-center">
                        <i class="fas fa-fan mr-2 text-primary-500"></i>
                        风扇列表
                    </h2>
                    
                    <div class="flex flex-col sm:flex-row items-center gap-4">
                        <div class="w-full sm:flex-1 relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-search text-slate-400"></i>
                                </div>
                            <input 
                                type="text" 
                                id="fanSearch" 
                                placeholder="搜索风扇型号、品牌等..."
                                class="w-full pl-10 pr-3 py-2 bg-slate-100 dark:bg-slate-700 border-0 rounded-lg shadow-inner focus:outline-none focus:ring-2 focus:ring-primary-500 dark:text-white text-sm"
                            >
                        </div>

                        <div class="flex flex-wrap sm:flex-nowrap gap-2 w-full sm:w-auto">
                            <select 
                                id="brandFilter"
                                class="flex-1 sm:w-36 px-3 py-2 bg-slate-100 dark:bg-slate-700 border-0 rounded-lg shadow-inner focus:outline-none focus:ring-2 focus:ring-primary-500 dark:text-white text-sm"
                            >
                                <option value="all">所有品牌</option>
                                <option value="猫头鹰">猫头鹰</option>
                                <option value="九州风神">九州风神</option>
                                <option value="酷冷至尊">酷冷至尊</option>
                                <option value="海盗船">海盗船</option>
                                <option value="Arctic">Arctic</option>
                                <option value="其他">其他品牌</option>
                            </select>

                            <select 
                                id="sizeFilter"
                                class="flex-1 sm:w-36 px-3 py-2 bg-slate-100 dark:bg-slate-700 border-0 rounded-lg shadow-inner focus:outline-none focus:ring-2 focus:ring-primary-500 dark:text-white text-sm"
                            >
                                <option value="all">所有尺寸</option>
                                <option value="80">80mm</option>
                                <option value="92">92mm</option>
                                <option value="120">120mm</option>
                                <option value="140">140mm</option>
                                <option value="200">200mm</option>
                            </select>

                            <button 
                                id="resetFilterBtn"
                                class="btn-click flex-none bg-slate-200 dark:bg-slate-600 hover:bg-slate-300 dark:hover:bg-slate-500 text-slate-700 dark:text-slate-200 py-2 px-4 rounded-lg transition-colors flex items-center"
                            >
                                <i class="fas fa-sync-alt mr-1"></i> 重置
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 风扇列表表格 -->
                <div class="bg-white dark:bg-slate-800 rounded-xl shadow-lg transition-all overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-slate-200 dark:divide-slate-700">
                            <thead class="bg-slate-50 dark:bg-slate-700/50">
                                <tr class="text-left border-b dark:border-slate-600">
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">
                                        风扇型号
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider hidden sm:table-cell">
                                        品牌
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">
                                        尺寸
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider hidden sm:table-cell">
                                        转速
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">
                                        操作
                                    </th>
                                </tr>
                            </thead>
                            <tbody id="fanTableBody" class="bg-white dark:bg-slate-800 divide-y divide-slate-200 dark:divide-slate-700">
                                <!-- 数据将通过JavaScript填充 -->
                                <tr>
                                    <td colspan="5" class="px-6 py-4 text-center text-sm text-slate-500 dark:text-slate-400">
                                        <div class="flex flex-col items-center space-y-2">
                                            <i class="fas fa-spinner fa-spin text-xl text-primary-500"></i>
                                            <span>加载中...</span>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页控件 -->
                    <div class="px-6 py-4 border-t border-slate-200 dark:border-slate-700 flex flex-wrap sm:flex-nowrap justify-between items-center">
                        <div class="text-sm text-slate-700 dark:text-slate-300 mb-4 sm:mb-0" id="totalCount">
                            共 0 条记录
                        </div>
                        
                        <div class="flex items-center space-x-1 sm:space-x-2">
                            <button 
                                id="firstPage" 
                                title="首页"
                                class="btn-click px-2 py-1 border border-slate-300 dark:border-slate-600 rounded-md text-sm bg-white dark:bg-slate-700 text-slate-700 dark:text-slate-200 hover:bg-slate-50 dark:hover:bg-slate-600 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                <i class="fas fa-angle-double-left"></i>
                            </button>
                            
                            <button 
                                id="prevPage" 
                                title="上一页"
                                class="btn-click px-2 py-1 border border-slate-300 dark:border-slate-600 rounded-md text-sm bg-white dark:bg-slate-700 text-slate-700 dark:text-slate-200 hover:bg-slate-50 dark:hover:bg-slate-600 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            
                            <div id="pageNumbers" class="hidden sm:flex space-x-1">
                                <!-- 页码将通过JavaScript填充 -->
                            </div>
                            
                            <span id="pageInfo" class="px-2 py-1 text-sm text-slate-600 dark:text-slate-400 whitespace-nowrap">
                                第 <span id="currentPageDisplay" class="font-medium text-primary-600 dark:text-primary-400">1</span> / 
                                <span id="totalPagesDisplay">1</span> 页
                            </span>
                            
                            <button 
                                id="nextPage" 
                                title="下一页"
                                class="btn-click px-2 py-1 border border-slate-300 dark:border-slate-600 rounded-md text-sm bg-white dark:bg-slate-700 text-slate-700 dark:text-slate-200 hover:bg-slate-50 dark:hover:bg-slate-600 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                <i class="fas fa-chevron-right"></i>
                            </button>
                            
                            <button 
                                id="lastPage" 
                                title="尾页"
                                class="btn-click px-2 py-1 border border-slate-300 dark:border-slate-600 rounded-md text-sm bg-white dark:bg-slate-700 text-slate-700 dark:text-slate-200 hover:bg-slate-50 dark:hover:bg-slate-600 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                <i class="fas fa-angle-double-right"></i>
                            </button>
                            
                            <div class="hidden sm:flex items-center ml-2">
                                <span class="text-sm text-slate-600 dark:text-slate-400">跳转到</span>
                                <input 
                                    type="number" 
                                    id="pageJump" 
                                    min="1" 
                                    class="w-12 ml-1 px-2 py-1 bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:text-white"
                                >
                                <button 
                                    id="goToPage" 
                                    class="btn-click ml-1 px-2 py-1 bg-primary-600 hover:bg-primary-700 text-white text-sm rounded-md"
                                >
                                    确定
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 风扇详情模态框 -->
        <div id="fanModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden backdrop-blur-sm transition-all duration-300">
            <div class="bg-white dark:bg-slate-800 rounded-xl shadow-xl p-5 sm:p-6 max-w-4xl w-full max-h-[90vh] overflow-y-auto border border-slate-200 dark:border-slate-700 transition-all duration-200 transform scale-95 opacity-0">
                <!-- 模态框头部 -->
                <div class="flex justify-between items-center mb-5 pb-3 border-b border-slate-200 dark:border-slate-700">
                    <h3 class="text-xl font-semibold text-slate-800 dark:text-slate-200 flex items-center">
                        <i class="fas fa-fan text-primary-500 mr-2"></i>
                        风扇详情
                    </h3>
                    <button id="closeModal" class="text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200 transition-colors p-1 rounded-full hover:bg-slate-100 dark:hover:bg-slate-700">
                        <i class="fas fa-times text-lg"></i>
                    </button>
                </div>

                <!-- 详情内容区 -->
                <div id="fanDetails" class="space-y-6">
                    <!-- 详情会动态填充 -->
                    <div class="flex justify-center items-center py-12">
                        <div class="flex flex-col items-center">
                            <i class="fas fa-spinner fa-spin text-3xl text-primary-500 mb-4"></i>
                            <p class="text-slate-600 dark:text-slate-400">加载中...</p>
                        </div>
                    </div>
                </div>

                <!-- 模态框底部按钮 -->
                <div class="mt-6 pt-4 border-t border-slate-200 dark:border-slate-700 flex justify-end space-x-3">
                    <button 
                        id="editBtn"
                        class="btn-click inline-flex items-center px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg shadow-sm text-sm transition-colors"
                    >
                        <i class="fas fa-edit mr-1.5"></i> 编辑
                    </button>
                    <button 
                        id="deleteBtn"
                        class="btn-click inline-flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg shadow-sm text-sm transition-colors"
                    >
                        <i class="fas fa-trash mr-1.5"></i> 删除
                    </button>
                    <button 
                        id="closeModalBtn"
                        class="btn-click inline-flex items-center px-4 py-2 bg-slate-200 hover:bg-slate-300 dark:bg-slate-700 dark:hover:bg-slate-600 text-slate-800 dark:text-slate-200 rounded-lg shadow-sm text-sm transition-colors"
                    >
                        <i class="fas fa-times mr-1.5"></i> 关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="/js/fan-info.js"></script>

    <script src="js/upload-progress.js"></script>
    <script src="js/fan-info.js"></script>
</body>

</html> 