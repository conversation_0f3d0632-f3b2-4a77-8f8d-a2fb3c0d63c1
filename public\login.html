<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录/注册 - 电脑配件综合管理系统</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center">
    <div class="max-w-md w-full mx-4">
        <div class="bg-white rounded-lg shadow-lg p-6 sm:p-8">
            <div class="text-center mb-8">
                <h1 class="text-2xl sm:text-3xl font-bold text-indigo-700 flex items-center justify-center">
                    <i class="fas fa-truck mr-3"></i> 电脑配件综合管理系统
                </h1>
                <p class="text-gray-600 mt-2" id="formTitle">请登录以继续使用</p>
            </div>
            
            <!-- 登录表单 -->
            <form id="loginForm" class="space-y-4">
                <div>
                    <label for="username" class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                    <input type="text" id="username" name="username" required
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                </div>
                
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                    <input type="password" id="password" name="password" required
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                </div>
                
                <button type="submit"
                    class="w-full bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    登录
                </button>
                
                <div class="text-center mt-4">
                    <p class="text-sm text-gray-600">
                        没有账号? <a href="#" id="showRegister" class="text-indigo-600 hover:text-indigo-800">注册新用户</a>
                    </p>
                </div>
            </form>
            
            <!-- 注册表单 (初始隐藏) -->
            <form id="registerForm" class="space-y-4 hidden">
                <div>
                    <label for="reg_username" class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                    <input type="text" id="reg_username" name="reg_username" required minlength="6"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    <p class="text-xs text-gray-500 mt-1">用户名至少6个字符，不能包含空格</p>
                </div>
                
                <div>
                    <label for="reg_password" class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                    <input type="password" id="reg_password" name="reg_password" required minlength="6"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    <p class="text-xs text-gray-500 mt-1">密码至少6个字符，必须包含字母和数字</p>
                </div>
                
                <div>
                    <label for="confirm_password" class="block text-sm font-medium text-gray-700 mb-1">确认密码</label>
                    <input type="password" id="confirm_password" name="confirm_password" required minlength="6"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                </div>
                
                <button type="submit"
                    class="w-full bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    注册
                </button>
                
                <div class="text-center mt-4">
                    <p class="text-sm text-gray-600">
                        已有账号? <a href="#" id="showLogin" class="text-indigo-600 hover:text-indigo-800">返回登录</a>
                    </p>
                </div>
            </form>
        </div>
    </div>

    <!-- Toast通知 -->
    <div id="toast" class="fixed top-4 right-4 max-w-xs bg-white rounded-lg shadow-lg p-4 z-50 transform transition-transform duration-300 translate-y-[-100%]">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <i id="toastIcon" class="fas fa-check-circle text-green-400"></i>
            </div>
            <div class="ml-3 text-sm font-medium text-gray-900" id="toastMessage"></div>
        </div>
    </div>
    <script src="/js/login.js"></script>
</body>
</html> 