:root {
    /* 亮色主题变量 */
    --bg-color: #f9fafb;
    --text-color: #1a202c;
    --card-bg: #ffffff;
    --border-color: #e5e7eb;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --header-bg: #ffffff;
    --header-text: #4338ca;
    --btn-primary-bg: #4f46e5;
    --btn-primary-text: #ffffff;
    --btn-primary-hover: #4338ca;
    --card-header-bg: rgba(79, 70, 229, 0.05);
    --card-border: rgba(79, 70, 229, 0.15);
    --toast-bg-success: #4CAF50;
    --toast-bg-error: #F44336;
    --toast-text: #ffffff;
    --table-header-bg: #f3f4f6;
    --table-header-text: #6b7280;
    --table-row-hover: #f9fafb;
    --form-bg: #ffffff;
    --form-smart-bg: #eef2ff;
    --input-border: #d1d5db;
    --input-focus-border: #4f46e5;
    --input-focus-ring: rgba(79, 70, 229, 0.2);
}

/* 暗色主题变量 */
:root.dark-theme {
    --bg-color: #121212;
    --text-color: #ffffff;
    --card-bg: #1e1e2f;
    --border-color: #383846;
    --shadow-color: rgba(0, 0, 0, 0.5);
    --header-bg: #1e1e2f;
    --header-text: #a7a7ff;
    --btn-primary-bg: #7f5af0;
    --btn-primary-text: #ffffff;
    --btn-primary-hover: #9270ff;
    --card-header-bg: rgba(127, 90, 240, 0.1);
    --card-border: rgba(127, 90, 240, 0.2);
    --toast-bg-success: #065f46;
    --toast-bg-error: #991b1b;
    --toast-text: #ffffff;
    --table-header-bg: #181825;
    --table-header-text: #a0aec0;
    --table-row-hover: #2a2a3d;
    --form-bg: #1e1e2f;
    --form-smart-bg: #1d1d2e;
    --input-bg: #2d2f33;
    --input-border: #444444;
    --input-text: #ffffff;
    --input-placeholder: #777777;
    --input-focus-border: #3b82f6;
    --input-focus-ring: rgba(59, 130, 246, 0.25);
    --label-text: #b8c0e0;
    --table-text: #e2e8f0;
    --table-border: #2d2d3d;
}

/* 应用变量到全局样式 */
body {
    background-color: var(--bg-color);
    color: var(--text-color);
    transition: background-color 0.3s ease, color 0.3s ease;
}

.bg-white,
.bg-gray-50 {
    background-color: var(--card-bg);
    transition: background-color 0.3s ease;
}

.bg-gray-50 {
    background-color: var(--bg-color);
}

.shadow-md {
    box-shadow: 0 4px 6px var(--shadow-color);
    transition: box-shadow 0.3s ease;
}

/* 边框样式 */
.border,
.border-gray-200,
.border-b,
.border-gray-300,
.border-indigo-200 {
    border-color: var(--border-color) !important;
    transition: border-color 0.3s ease;
}

/* 表单元素应用主题变量 */
input,
select,
textarea {
    background-color: var(--card-bg);
    color: var(--text-color);
    border-color: var(--input-border);
    transition: all 0.3s ease;
}

input:focus,
select:focus,
textarea:focus {
    border-color: var(--input-focus-border);
    box-shadow: 0 0 0 3px var(--input-focus-ring);
}

/* 按钮应用主题变量 */
.bg-indigo-600 {
    background-color: var(--btn-primary-bg);
    color: var(--btn-primary-text);
    transition: all 0.3s ease;
}

.bg-indigo-600:hover {
    background-color: var(--btn-primary-hover);
}

/* 表格应用主题变量 */
thead.bg-gray-50 {
    background-color: var(--table-header-bg);
}

thead th {
    color: var(--table-header-text) !important;
}

tr.hover\:bg-gray-50:hover {
    background-color: var(--table-row-hover) !important;
}

/* 模态框应用主题变量 */
.bg-white.rounded-lg {
    background-color: var(--card-bg);
    color: var(--text-color);
}

/* 标签应用主题变量 */
.text-gray-600,
.text-gray-500,
.text-gray-700 {
    color: var(--text-color);
    opacity: 0.8;
}

/* 表单区域应用智能识别背景 */
.bg-indigo-50 {
    background-color: var(--form-smart-bg);
    transition: background-color 0.3s ease;
}

/* 主题切换按钮样式 */
#theme-toggle {
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 50%;
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    font-size: 18px;
    box-shadow: 0 2px 4px var(--shadow-color);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
}

#theme-toggle:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px var(--shadow-color);
}

.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.image-preview {
    transition: all 0.3s ease;
}

.image-preview:hover {
    transform: scale(1.05);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 全局表格修复 */
.table-wrapper {
    width: 100%;
    max-width: 100%;
    overflow-x: auto;
}

/* 确保内容不会被裁剪 */
* {
    box-sizing: border-box;
}

.container {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
}

/* 表格容器内部元素修复 */
.table-wrapper table {
    width: 100%;
}

/* 文本截断样式 */
.text-truncate {
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: calc(100% - 10px) !important;
    display: inline-block !important;
}

/* 表格样式 */
.table-compact td {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}

.model-column {
    min-width: 180px;
}

/* 标签样式 */
.spec-tag {
    padding: 3px 8px !important;
    border-radius: 4px !important;
    font-size: 0.75rem !important;
    font-weight: 500 !important;
    line-height: 1.5 !important;
    display: inline-block !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: fit-content !important;
    margin: 2px 4px 2px 0 !important;
}

.spec-tag.socket {
    background-color: rgba(180, 83, 9, 0.1) !important;
    color: #b45309 !important;
    border: 1px solid rgba(180, 83, 9, 0.2) !important;
}

.spec-tag.cores {
    background-color: rgba(22, 101, 52, 0.1) !important;
    color: #166534 !important;
    border: 1px solid rgba(22, 101, 52, 0.2) !important;
}

.spec-tag.freq {
    background-color: rgba(76, 29, 149, 0.1) !important;
    color: #4c1d95 !important;
    border: 1px solid rgba(76, 29, 149, 0.2) !important;
}

.spec-tag.process {
    background-color: rgba(6, 95, 70, 0.1) !important;
    color: #065f46 !important;
    border: 1px solid rgba(6, 95, 70, 0.2) !important;
}

.spec-tag.tdp {
    background-color: rgba(194, 65, 12, 0.1) !important;
    color: #c2410c !important;
    border: 1px solid rgba(194, 65, 12, 0.2) !important;
}

/* 品牌标签 */
.case-badge {
    padding: 4px 10px !important;
    font-weight: 600 !important;
    border-radius: 4px !important;
    display: inline-block !important;
}

.case-badge {
    background-color: rgba(79, 70, 229, 0.1) !important;
    color: #4F46E5 !important;
    border: 1px solid rgba(79, 70, 229, 0.2) !important;
}

/* 移动端优化 */
@media (max-width: 640px) {

    /* 页面容器修复 */
    .container {
        padding-left: 8px !important;
        padding-right: 8px !important;
    }

    /* 确保表格容器大小正确 */
    .table-container,
    .table-wrapper,
    .inline-block.min-w-full,
    .inline-block.min-w-full table {
        width: 100% !important;
        max-width: 100% !important;
        box-sizing: border-box !important;
        overflow-x: hidden !important;
    }

    .mobile-stack {
        flex-direction: column;
    }

    .mobile-full-width {
        width: 100%;
    }

    .mobile-p-2 {
        padding: 0.5rem;
    }

    .mobile-text-sm {
        font-size: 0.875rem;
        line-height: 1.25rem;
    }

    .mobile-hidden {
        display: none;
    }

    .mobile-flex-col {
        flex-direction: column;
    }

    .mobile-w-full {
        width: 100%;
    }

    .mobile-mt-2 {
        margin-top: 0.5rem;
    }

    /* 修复重置按钮溢出问题 */
    .form-footer-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        justify-content: flex-end;
        width: 100%;
    }

    .form-footer-buttons button {
        flex: 0 0 auto;
    }

    /* 卡片式布局 */
    .case-card-outer-container {
        width: 100% !important;
        max-width: 100% !important;
        margin: 0 0 16px 0 !important;
        box-sizing: border-box !important;
        padding: 0 4px;
        position: relative;
    }

    .case-card-new {
        width: 100%;
        border-radius: 12px;
        overflow: hidden;
        background-color: white;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.07);
        display: flex;
        flex-direction: column;
        min-width: 0;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    /* 标签组 */
    .tag-group {
        display: flex;
        flex-wrap: wrap;
        gap: 6px;
        margin: 8px 0;
        width: 100%;
        box-sizing: border-box !important;
    }

    /* 分页控件移动端优化 */
    .pagination-mobile {
        display: flex;
        justify-content: space-between;
        width: 100%;
        align-items: center;
    }

    .page-controls-mobile {
        display: flex;
        gap: 2px;
    }

    .page-controls-mobile button {
        min-width: 28px;
        height: 28px;
        padding: 0;
        font-size: 0.75rem;
    }

    #pageInfo {
        font-size: 0.7rem;
        padding: 2px 4px;
        white-space: nowrap;
    }
}

/* Dark Theme overrides & improvements */
.dark-theme body {
    background-color: var(--bg-color);
}

.dark-theme .bg-gray-50 {
    background-color: var(--bg-color);
}

.dark-theme .bg-white {
    background-color: var(--card-bg);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
    border: 1px solid var(--border-color);
}

/* 修复表单文字颜色 */
.dark-theme label,
.dark-theme .text-gray-700,
.dark-theme .font-medium {
    color: var(--label-text);
}

.dark-theme h2,
.dark-theme h3,
.dark-theme h4 {
    color: var(--text-color);
}

.dark-theme .text-gray-600,
.dark-theme .text-gray-500,
.dark-theme .text-gray-800,
.dark-theme .text-gray-900 {
    color: var(--text-color);
}

/* 智能识别背景 */
.dark-theme .bg-indigo-50 {
    background-color: var(--form-smart-bg);
    border-color: rgba(127, 90, 240, 0.3);
}

.dark-theme .text-indigo-700 {
    color: #a5b4fc;
}

/* 确保智能识别按钮与背景协调 */
.dark-theme #autoFillBtn {
    background-color: var(--btn-primary-bg);
    color: white;
    border: none;
}

.dark-theme #autoFillBtn:hover {
    background-color: var(--btn-primary-hover);
}

/* 确保智能识别文本框背景色 */
.dark-theme #smartInput {
    background-color: rgba(45, 47, 51, 0.8);
    border-color: var(--input-border);
}

/* 确保边框颜色一致性 */
.dark-theme .border-indigo-200 {
    border-color: rgba(127, 90, 240, 0.3) !important;
}

/* 表格修复 */
.dark-theme thead th {
    color: var(--label-text) !important;
    border-bottom: 1px solid var(--table-border);
    background-color: var(--table-header-bg);
}

.dark-theme tbody td {
    color: var(--table-text);
    border-bottom: 1px solid var(--table-border) !important;
}

.dark-theme .border-gray-200,
.dark-theme .divide-gray-200>*,
.dark-theme .border-b-2,
.dark-theme .border-b,
.dark-theme .border-t,
.dark-theme .border-l,
.dark-theme .border-r {
    border-color: var(--table-border) !important;
}

.dark-theme .text-gray-500.uppercase {
    color: var(--label-text) !important;
}

/* 确保表格行交替色 */
.dark-theme #caseTableBody tr:nth-child(odd) {
    background-color: var(--card-bg);
}

.dark-theme #caseTableBody tr:nth-child(even) {
    background-color: rgba(30, 30, 47, 0.7);
}

.dark-theme #caseTableBody tr {
    border-bottom: 1px solid var(--table-border);
}

/* 表格文本颜色 */
.dark-theme .text-sm.font-medium.text-gray-900,
.dark-theme .text-xs.text-gray-500 {
    color: var(--table-text) !important;
}

/* 修复搜索框 */
.dark-theme .relative .absolute .text-gray-400 {
    color: #777777;
}

/* 修复分页控制 */
.dark-theme #totalCount,
.dark-theme #pageInfo {
    color: var(--label-text);
}

/* 移除表格单元格边框 */
.dark-theme table {
    border-collapse: collapse;
}

.dark-theme table th,
.dark-theme table td {
    border: none;
}

.dark-theme .divide-y.divide-gray-200>tr {
    border-top: none;
    border-bottom: 1px solid var(--table-border);
}

/* 确保卡片的一致性 */
.dark-theme .border.border-gray-200 {
    border-color: var(--border-color) !important;
}

/* 确保下拉菜单背景色 */
.dark-theme select option {
    background-color: var(--card-bg);
    color: var(--text-color);
}

/* 模态框内容颜色 */
.dark-theme #detailsModal .space-y-2 p,
.dark-theme #detailsModal .font-medium,
.dark-theme #detailsModal .font-semibold,
.dark-theme #detailsModal .text-gray-900,
.dark-theme #detailsModal .text-gray-600,
.dark-theme #detailsModal h4 {
    color: var(--table-text);
}

/* 机箱详情模态框颜色修复 */
.dark-theme #detailsModal .grid .border,
.dark-theme #detailsModal .grid .bg-white,
.dark-theme #detailsModal div[style*="background-color: white"],
.dark-theme #detailsModal .bg-purple-50,
.dark-theme #detailsModal .bg-indigo-50 {
    background-color: var(--card-bg) !important;
    border-color: var(--border-color) !important;
}

/* 机箱详情布局区域 */
.dark-theme #detailsModal .grid-cols-1,
.dark-theme #detailsModal .grid-cols-2 {
    gap: 0.75rem !important;
}

/* 机箱详情标题 */
.dark-theme #detailsModal .text-indigo-700,
.dark-theme #detailsModal .text-purple-700,
.dark-theme #detailsModal .font-bold.text-indigo-700,
.dark-theme #detailsModal .font-bold.text-purple-700 {
    color: #a5b4fc !important;
}

/* 机箱详情模态框内的高亮区域 */
.dark-theme #detailsModal .bg-indigo-50,
.dark-theme #detailsModal .bg-purple-50 {
    background-color: rgba(127, 90, 240, 0.08) !important;
    border: 1px solid rgba(127, 90, 240, 0.12);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 确保模态框中所有区块背景颜色正确 */
.dark-theme #detailsModal .p-4,
.dark-theme #detailsModal .p-6,
.dark-theme #detailsModal .p-3,
.dark-theme #detailsModal .rounded-lg {
    background-color: var(--card-bg);
}

/* 模态框顶部标题栏 */
.dark-theme #detailsModal .border-b {
    border-color: var(--border-color) !important;
}

/* 文本颜色调整 */
.dark-theme #detailsModal .text-xl,
.dark-theme #detailsModal .text-lg,
.dark-theme #detailsModal .text-2xl,
.dark-theme #detailsModal .font-bold:not(.text-red-600):not(.text-indigo-700):not(.text-purple-700) {
    color: var(--text-color);
}

/* 模态框中的红色价格 */
.dark-theme #detailsModal .text-red-600,
.dark-theme #detailsModal .font-bold.text-red-600 {
    color: #f87171 !important;
}

/* 模态框中的图片区域 */
.dark-theme #detailsModal .bg-gray-50 {
    background-color: rgba(255, 255, 255, 0.02) !important;
    border: 1px solid var(--border-color);
}

/* 详情模态框中的图片容器 */
.dark-theme #detailsModal .details-image-container {
    background-color: rgba(0, 0, 0, 0.2) !important;
    border: 1px solid var(--border-color);
}

/* 详情模态框中的图片 */
.dark-theme #detailsModal .details-image {
    filter: brightness(1.1) contrast(1.05);
}

/* 详情高亮区块 */
.dark-theme #detailsModal .details-highlight-box {
    background-color: rgba(127, 90, 240, 0.08) !important;
    border: 1px solid rgba(127, 90, 240, 0.15);
}

/* 详情区块 */
.dark-theme #detailsModal .details-section {
    background-color: var(--card-bg) !important;
    border-color: var(--border-color) !important;
}

/* 确保深色模式下模态框中的分割线颜色正确 */
.dark-theme #detailsModal .border-b,
.dark-theme #detailsModal .border-t {
    border-color: var(--border-color) !important;
}

/* 特别强调背景色应该为深色的区域 */
.dark-theme .bg-white.rounded-lg.p-6,
.dark-theme .bg-white.p-4,
.dark-theme .bg-white.p-6,
.dark-theme .bg-white.p-3 {
    background-color: var(--card-bg) !important;
}

/* File input custom styling */
.dark-theme .file\:bg-indigo-50 {
    background-color: rgba(127, 90, 240, 0.1) !important;
}

.dark-theme .file\:text-indigo-700 {
    color: var(--header-text) !important;
}

.dark-theme .hover\:file\:bg-indigo-100:hover {
    background-color: rgba(127, 90, 240, 0.2) !important;
}

/* Pagination controls */
.dark-theme #pageNumbers button {
    border-color: var(--border-color);
}

.dark-theme #pageNumbers button:not(.bg-indigo-600) {
    background-color: var(--card-bg);
    color: var(--text-color);
}

.dark-theme #pageNumbers button:hover:not(.bg-indigo-600) {
    background-color: var(--table-row-hover);
}

.dark-theme #pageJump {
    background-color: var(--input-bg);
    color: var(--text-color);
}

/* Other border fixes */
.dark-theme .border,
.dark-theme .border-t,
.dark-theme .border-b,
.dark-theme .border-l,
.dark-theme .border-r,
.dark-theme .border-x,
.dark-theme .border-y {
    border-color: var(--border-color) !important;
}

/* 确保按钮点击事件正确传递 */
.mobile-action-btn {
    position: relative;
    z-index: 20;
    pointer-events: auto;
}

/* 确保图片预览不会捕获按钮事件 - 移除pointer-events:none */
.case-card-new img {
    pointer-events: auto;
    background-color: transparent !important;
}

/* 确保默认图片在深色模式下可见 */
.dark-theme .case-card-new img[src*="default-case.png"] {
    filter: brightness(1.5) contrast(0.9) invert(0.1);
    opacity: 0.85;
}

/* 仅图片容器可点击，避免事件冒泡问题 */
.case-card-new div[style*="overflow: hidden"] {
    position: relative;
    z-index: 10;
}

/* 卡片图片容器样式 */
.case-image-container {
    transition: all 0.3s ease;
}

.case-image {
    transition: all 0.3s ease;
}

/* 深色模式下图片容器增强 */
.dark-theme .case-image-container {
    background-color: rgba(255, 255, 255, 0.03);
    border-color: rgba(127, 90, 240, 0.3) !important;
    box-shadow: 0 0 8px rgba(127, 90, 240, 0.1);
}

.dark-theme .case-image {
    opacity: 0.95;
    filter: brightness(1.15) contrast(1.05);
}

/* 鼠标悬停效果 */
.dark-theme .case-image-container:hover {
    border-color: rgba(127, 90, 240, 0.5) !important;
    box-shadow: 0 0 12px rgba(127, 90, 240, 0.2);
}

.dark-theme .case-image-container:hover .case-image {
    opacity: 1;
    filter: brightness(1.25) contrast(1.1);
    transform: scale(1.05);
}

/* 文件上传按钮 */
.dark-theme input[type="file"] {
    color: var(--label-text);
}

/* Card styles */
.dark-theme .rounded-lg {
    border-radius: 8px;
}

.dark-theme .shadow-md {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* Form controls */
.dark-theme input,
.dark-theme select,
.dark-theme textarea {
    background-color: var(--input-bg);
    color: var(--input-text);
    border: 1px solid var(--input-border);
    border-radius: 6px;
}

.dark-theme input:focus,
.dark-theme select:focus,
.dark-theme textarea:focus {
    border-color: var(--input-focus-border);
    box-shadow: 0 0 0 3px var(--input-focus-ring);
}

.dark-theme ::placeholder {
    color: var(--input-placeholder);
    opacity: 1;
}

.dark-theme .border-gray-300 {
    border-color: var(--input-border) !important;
}

/* Button styles */
.dark-theme .bg-indigo-600 {
    background-color: var(--btn-primary-bg);
}

.dark-theme .bg-indigo-600:hover {
    background-color: var(--btn-primary-hover);
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

.dark-theme .hover\:bg-indigo-700:hover {
    background-color: var(--btn-primary-hover) !important;
}

.dark-theme .bg-white.hover\:bg-gray-50:hover {
    background-color: #252538 !important;
}

/* Improved tag styles for dark theme */
.dark-theme .spec-tag {
    background-color: rgba(160, 174, 192, 0.1) !important;
    color: #ffffff !important;
    border-color: rgba(160, 174, 192, 0.2) !important;
    border-radius: 6px;
}

.dark-theme .spec-tag.socket {
    background-color: rgba(234, 88, 12, 0.2) !important;
    color: #fff !important;
    border-color: rgba(234, 88, 12, 0.3) !important;
}

.dark-theme .spec-tag.cores {
    background-color: rgba(22, 163, 74, 0.2) !important;
    color: #fff !important;
    border-color: rgba(22, 163, 74, 0.3) !important;
}

.dark-theme .spec-tag.freq {
    background-color: rgba(124, 58, 237, 0.2) !important;
    color: #fff !important;
    border-color: rgba(124, 58, 237, 0.3) !important;
}

.dark-theme .spec-tag.process {
    background-color: rgba(14, 165, 233, 0.2) !important;
    color: #fff !important;
    border-color: rgba(14, 165, 233, 0.3) !important;
}

.dark-theme .spec-tag.tdp {
    background-color: rgba(239, 68, 68, 0.2) !important;
    color: #fff !important;
    border-color: rgba(239, 68, 68, 0.3) !important;
}

.dark-theme .case-badge {
    background-color: rgba(127, 90, 240, 0.2) !important;
    color: #ffffff !important;
    border-color: rgba(127, 90, 240, 0.3) !important;
    border-radius: 6px;
}

/* Table styles */
.dark-theme .table-wrapper table {
    border-color: var(--border-color);
}

.dark-theme .divide-gray-200>tr {
    border-color: var(--border-color) !important;
}

.dark-theme #caseTableBody tr:hover {
    background-color: var(--table-row-hover);
}

/* Action button hover effects */
/* For desktop table */
#caseTableBody td .flex button {
    transition: all 0.2s ease-in-out;
}

#caseTableBody td .flex button:hover {
    transform: scale(1.25);
    box-shadow: 0 0 4px rgba(255, 255, 255, 0.2);
}

.dark-theme #caseTableBody .text-blue-600 {
    color: #90cdf4;
}

.dark-theme #caseTableBody .text-green-600 {
    color: #9ae6b4;
}

.dark-theme #caseTableBody .text-red-600 {
    color: #feb2b2;
}

.dark-theme #caseTableBody .text-blue-600:hover {
    color: #3b82f6 !important;
}

.dark-theme #caseTableBody .text-green-600:hover {
    color: #10b981 !important;
}

.dark-theme #caseTableBody .text-red-600:hover {
    color: #ef4444 !important;
}

/* For mobile cards */
.mobile-action-btn {
    padding: 4px 8px;
    border-radius: 6px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: transparent;
    font-size: 0.8rem;
    font-weight: 500;
    transition: all 0.2s ease-out;
    flex: 1;
}

.mobile-action-btn.mobile-action-view {
    color: #3B82F6;
}

.mobile-action-btn.mobile-action-edit {
    color: #10B981;
}

.mobile-action-btn.mobile-action-delete {
    color: #EF4444;
}

.dark-theme .mobile-action-btn.mobile-action-view {
    color: #90cdf4;
}

.dark-theme .mobile-action-btn.mobile-action-edit {
    color: #9ae6b4;
}

.dark-theme .mobile-action-btn.mobile-action-delete {
    color: #feb2b2;
}

.mobile-action-btn:hover {
    transform: translateY(-2px);
}

.dark-theme .mobile-action-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Modal styles */
.dark-theme #detailsModal .bg-white,
.dark-theme #deleteConfirmModal .bg-white {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
}

.dark-theme #detailsModal .border-b,
.dark-theme #deleteConfirmModal .border-b {
    border-color: var(--border-color);
}

/* Card style for mobile */
.dark-theme .case-card-new {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
}

.dark-theme .case-card-new cardHeader {
    background-color: rgba(127, 90, 240, 0.1);
    border-color: var(--border-color);
}

/* 修复移动端卡片白底问题 */
.dark-theme .case-card-outer-container {
    background-color: transparent;
}

/* 卡片内部样式 */
.dark-theme .case-card-new {
    background-color: var(--card-bg) !important;
}

.dark-theme .case-card-new div[style*="background-color: white"] {
    background-color: var(--card-bg) !important;
}

/* 卡片头部 */
.dark-theme .case-card-new div[style*="background-color: rgba(79, 70, 229, 0.05)"] {
    background-color: var(--card-header-bg) !important;
}

/* 卡片中的文字颜色 */
.dark-theme .case-card-new div[style*="color: #1a202c"] {
    color: var(--text-color) !important;
}

.dark-theme .case-card-new div[style*="color: #4A5568"],
.dark-theme .case-card-new div[style*="color: #718096"] {
    color: var(--label-text) !important;
}

/* 价格信息修复 */
.dark-theme .case-card-new span[style*="color: #E53E3E"] {
    color: #f87171 !important;
}

.dark-theme .case-card-new span[style*="color: #718096"] {
    color: var(--label-text) !important;
}

/* 确保图片容器边框 */
.dark-theme .case-card-new div[style*="border: 1px solid rgba(79, 70, 229, 0.2)"] {
    background-color: rgba(255, 255, 255, 0.03) !important;
    border-color: rgba(255, 255, 255, 0.15) !important;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}

/* 覆盖所有可能的白色背景 */
.dark-theme .case-card-new div[style*="background-color"] {
    background-color: var(--card-bg) !important;
}

/* 标签样式修复 */
.dark-theme .case-card-new span[style*="background-color: rgba(66, 153, 225, 0.1)"] {
    background-color: rgba(59, 130, 246, 0.2) !important;
    color: #93c5fd !important;
    border-color: rgba(59, 130, 246, 0.3) !important;
}

.dark-theme .case-card-new span[style*="background-color: rgba(56, 178, 172, 0.1)"] {
    background-color: rgba(20, 184, 166, 0.2) !important;
    color: #5eead4 !important;
    border-color: rgba(20, 184, 166, 0.3) !important;
}

.dark-theme .case-card-new span[style*="background-color: rgba(237, 100, 166, 0.1)"] {
    background-color: rgba(219, 39, 119, 0.2) !important;
    color: #fbcfe8 !important;
    border-color: rgba(219, 39, 119, 0.3) !important;
}

.dark-theme .case-card-new span[style*="background-color: rgba(154, 230, 180, 0.1)"] {
    background-color: rgba(34, 197, 94, 0.2) !important;
    color: #86efac !important;
    border-color: rgba(34, 197, 94, 0.3) !important;
}

/* 卡片底部 */
.dark-theme .case-card-new div[style*="border-top: 1px solid"] {
    border-top-color: var(--border-color) !important;
}

/* 最终覆盖规则 - 确保所有卡片样式正确 */
.dark-theme .case-card-outer-container * {
    background-color: inherit;
}

.dark-theme .case-card-outer-container>div {
    background-color: var(--card-bg) !important;
}

/* 确保操作按钮区域背景色正确 */
.dark-theme .case-card-new div[style*="background-color: white"][style*="border-top"] {
    background-color: var(--card-bg) !important;
}

/* 所有卡片中的链接颜色 */
.dark-theme .case-card-new a {
    color: var(--header-text);
}

/* 修复操作按钮在移动端的颜色 */
.dark-theme .case-card-new .mobile-action-btn {
    background-color: transparent !important;
}

/* 卡片图片区域修复 */
.dark-theme .case-card-new img {
    background-color: transparent !important;
    border: 1px solid rgba(255, 255, 255, 0.1);
    padding: 2px;
    border-radius: 4px;
    filter: brightness(1.1);
    max-width: 100%;
    max-height: 100%;
}

/* 详情模态框中的图片容器 */
.dark-theme #detailsModal .details-image-container {
    background-color: rgba(0, 0, 0, 0.2) !important;
    border: 1px solid var(--border-color);
}

/* 详情模态框中的图片 */
.dark-theme #detailsModal .details-image {
    filter: brightness(1.1) contrast(1.05);
}

/* 详情高亮区块 */
.dark-theme #detailsModal .details-highlight-box {
    background-color: rgba(127, 90, 240, 0.08) !important;
    border: 1px solid rgba(127, 90, 240, 0.15);
}

/* 详情区块 */
.dark-theme #detailsModal .details-section {
    background-color: var(--card-bg) !important;
    border-color: var(--border-color) !important;
}

/* 确保深色模式下模态框中的分割线颜色正确 */
.dark-theme #detailsModal .border-b,
.dark-theme #detailsModal .border-t {
    border-color: var(--border-color) !important;
}

.dark-theme #detailsModal .tag-group {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin: 8px 0;
    width: 100%;
    box-sizing: border-box !important;
}

/* 自定义滚动条样式 */
.custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(203, 213, 225, 0.5) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(203, 213, 225, 0.5);
    border-radius: 3px;
}

.custom-scrollbar:hover::-webkit-scrollbar-thumb {
    background-color: rgba(148, 163, 184, 0.7);
}

.dark-theme .custom-scrollbar {
    scrollbar-color: rgba(71, 85, 105, 0.5) transparent;
}

.dark-theme .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(71, 85, 105, 0.5);
}

.dark-theme .custom-scrollbar:hover::-webkit-scrollbar-thumb {
    background-color: rgba(100, 116, 139, 0.7);
}

/* 详情模态框内容区域滚动 */
#detailsModal .modal-content {
    max-height: calc(90vh - 140px);
    overflow-y: auto;
}

/* 确保亮色主题下所有模态框内容都是白色背景 */
#detailsModal .bg-white {
    background-color: #ffffff !important;
}

#detailsModal .modal-content {
    max-height: calc(90vh - 140px);
    overflow-y: auto;
    background-color: #ffffff !important;
}

.dark-theme #detailsModal .bg-white,
.dark-theme #detailsModal .modal-content {
    background-color: var(--card-bg) !important;
}

/* 确保模态框中所有内容背景在亮色主题下都是白色 */
#detailsModal:not(.dark-theme) * {
    background-color: inherit;
}

#detailsModal:not(.dark-theme)>div {
    background-color: #ffffff !important;
}