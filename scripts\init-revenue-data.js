/**
 * 初始化收入管理系统演示数据
 */

const db = require('../config/db');

async function initRevenueData() {
    try {
        console.log('开始初始化收入管理演示数据...');
        
        // 检查是否已有数据
        const [existingRecords] = await db.query('SELECT COUNT(*) as count FROM revenue_records');
        if (existingRecords[0].count > 0) {
            console.log('数据库中已有收入记录，跳过初始化');
            return;
        }
        
        // 确保有用户数据
        const [users] = await db.query('SELECT id FROM users LIMIT 1');
        if (users.length === 0) {
            console.log('没有找到用户，创建默认用户...');
            const bcrypt = require('bcrypt');
            const hashedPassword = await bcrypt.hash('admin', 10);
            await db.query(
                'INSERT INTO users (username, password, role) VALUES (?, ?, ?)',
                ['admin', hashedPassword, 'admin']
            );
            console.log('默认管理员用户已创建 (用户名: admin, 密码: admin)');
        }
        
        // 获取用户ID
        const [userResult] = await db.query('SELECT id FROM users LIMIT 1');
        const userId = userResult[0].id;
        
        // 创建演示数据
        const demoData = [
            {
                shop_name: '华强北电脑城',
                order_number: 'HQB001',
                product_info: '微星 B760M BOMBER WIFI DDR4 爆破弹 +40',
                total_revenue: 40.00,
                revenue_notes: '主板升级订单，客户满意'
            },
            {
                shop_name: '华强北电脑城',
                order_number: 'HQB002',
                product_info: 'RTX 4060 Ti 显卡 +120',
                total_revenue: 120.00,
                revenue_notes: '显卡升级，性能提升明显'
            },
            {
                shop_name: '赛格电子市场',
                order_number: 'SEG001',
                product_info: 'Intel i5-13400F 处理器 +80',
                total_revenue: 80.00,
                revenue_notes: 'CPU升级订单'
            },
            {
                shop_name: '赛格电子市场',
                order_number: 'SEG002',
                product_info: '金士顿 32GB DDR4 内存 +60',
                total_revenue: 60.00,
                revenue_notes: '内存扩容订单'
            },
            {
                shop_name: '电脑城一楼',
                order_number: 'DC001',
                product_info: '三星 980 PRO 1TB SSD +90',
                total_revenue: 90.00,
                revenue_notes: '存储升级，速度提升'
            },
            {
                shop_name: '电脑城一楼',
                order_number: 'DC002',
                product_info: '海盗船 RM750x 电源 +70',
                total_revenue: 70.00,
                revenue_notes: '电源升级，稳定性更好'
            },
            {
                shop_name: '华强北电脑城',
                order_number: 'HQB003',
                product_info: '酷冷至尊 ML240L RGB 水冷 +50',
                total_revenue: 50.00,
                revenue_notes: '散热升级订单'
            },
            {
                shop_name: '赛格电子市场',
                order_number: 'SEG003',
                product_info: '技嘉 RTX 4070 SUPER 显卡 +200',
                total_revenue: 200.00,
                revenue_notes: '高端显卡订单，利润丰厚'
            },
            {
                shop_name: '电脑城二楼',
                order_number: 'DC2001',
                product_info: 'AMD Ryzen 7 7700X 处理器 +150',
                total_revenue: 150.00,
                revenue_notes: 'AMD高端处理器订单'
            },
            {
                shop_name: '电脑城二楼',
                order_number: 'DC2002',
                product_info: '华硕 ROG STRIX B650E 主板 +180',
                total_revenue: 180.00,
                revenue_notes: '高端主板配套订单'
            }
        ];
        
        // 插入演示数据
        for (const record of demoData) {
            await db.query(`
                INSERT INTO revenue_records 
                (shop_name, order_number, product_info, total_revenue, revenue_notes, user_id, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 30) DAY))
            `, [
                record.shop_name,
                record.order_number,
                record.product_info,
                record.total_revenue,
                record.revenue_notes,
                userId
            ]);
        }
        
        console.log(`成功创建 ${demoData.length} 条演示数据`);
        console.log('收入管理演示数据初始化完成！');
        
        // 显示统计信息
        const [stats] = await db.query(`
            SELECT 
                COUNT(*) as total_records,
                SUM(total_revenue) as total_revenue,
                COUNT(DISTINCT shop_name) as total_shops
            FROM revenue_records
        `);
        
        console.log('统计信息:');
        console.log(`- 总记录数: ${stats[0].total_records}`);
        console.log(`- 总收入: ¥${stats[0].total_revenue}`);
        console.log(`- 店铺数量: ${stats[0].total_shops}`);
        
    } catch (error) {
        console.error('初始化收入管理演示数据失败:', error);
        throw error;
    }
}

// 如果直接运行此文件，则执行初始化
if (require.main === module) {
    initRevenueData()
        .then(() => {
            console.log('演示数据初始化成功完成');
            process.exit(0);
        })
        .catch(error => {
            console.error('演示数据初始化失败:', error);
            process.exit(1);
        });
}

module.exports = { initRevenueData };
