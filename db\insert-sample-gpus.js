const db = require('./index');

// 示例GPU数据
const sampleGpus = [
    {
        brand: '华硕',
        model: 'ROG STRIX RTX 4090 OC',
        chipset: 'RTX 4090',
        memory_size: 24,
        memory_type: 'GDDR6X',
        memory_bus: 384,
        core_clock: 2230,
        boost_clock: 2640,
        tdp: 450,
        dimensions: '357 × 149 × 70',
        power_connectors: '16-pin',
        recommended_psu: 850,
        display_ports: 3,
        hdmi_ports: 1,
        price: 12999.00,
        notes: '旗舰级显卡，适合4K游戏和专业渲染'
    },
    {
        brand: '技嘉',
        model: 'GAMING OC RTX 4080',
        chipset: 'RTX 4080',
        memory_size: 16,
        memory_type: 'GDDR6X',
        memory_bus: 256,
        core_clock: 2205,
        boost_clock: 2550,
        tdp: 320,
        dimensions: '336 × 140 × 75',
        power_connectors: '16-pin',
        recommended_psu: 750,
        display_ports: 3,
        hdmi_ports: 1,
        price: 8999.00,
        notes: '高端显卡，性能强劲'
    },
    {
        brand: '微星',
        model: 'GAMING X TRIO RTX 4070 Ti',
        chipset: 'RTX 4070 Ti',
        memory_size: 12,
        memory_type: 'GDDR6X',
        memory_bus: 192,
        core_clock: 2310,
        boost_clock: 2760,
        tdp: 285,
        dimensions: '336 × 140 × 78',
        power_connectors: '16-pin',
        recommended_psu: 700,
        display_ports: 3,
        hdmi_ports: 1,
        price: 6499.00,
        notes: '中高端显卡，性价比不错'
    },
    {
        brand: '七彩虹',
        model: 'iGame RTX 4070',
        chipset: 'RTX 4070',
        memory_size: 12,
        memory_type: 'GDDR6X',
        memory_bus: 192,
        core_clock: 1920,
        boost_clock: 2475,
        tdp: 200,
        dimensions: '300 × 120 × 50',
        power_connectors: '8-pin',
        recommended_psu: 650,
        display_ports: 3,
        hdmi_ports: 1,
        price: 4799.00,
        notes: '主流显卡，适合1440p游戏'
    },
    {
        brand: '索泰',
        model: 'RTX 4060 Ti Twin Edge',
        chipset: 'RTX 4060 Ti',
        memory_size: 8,
        memory_type: 'GDDR6',
        memory_bus: 128,
        core_clock: 2310,
        boost_clock: 2535,
        tdp: 160,
        dimensions: '229 × 112 × 40',
        power_connectors: '8-pin',
        recommended_psu: 550,
        display_ports: 3,
        hdmi_ports: 1,
        price: 3199.00,
        notes: '入门级显卡，适合1080p游戏'
    },
    {
        brand: '蓝宝石',
        model: 'RX 7900 XTX NITRO+',
        chipset: 'RX 7900 XTX',
        memory_size: 24,
        memory_type: 'GDDR6',
        memory_bus: 384,
        core_clock: 2270,
        boost_clock: 2680,
        tdp: 355,
        dimensions: '310 × 135 × 70',
        power_connectors: '8-pin x2',
        recommended_psu: 800,
        display_ports: 4,
        hdmi_ports: 1,
        price: 7999.00,
        notes: 'AMD旗舰显卡，光追性能优秀'
    },
    {
        brand: '迪兰恒进',
        model: 'RX 7800 XT',
        chipset: 'RX 7800 XT',
        memory_size: 16,
        memory_type: 'GDDR6',
        memory_bus: 256,
        core_clock: 2124,
        boost_clock: 2430,
        tdp: 263,
        dimensions: '267 × 135 × 50',
        power_connectors: '8-pin x2',
        recommended_psu: 700,
        display_ports: 4,
        hdmi_ports: 1,
        price: 4299.00,
        notes: 'AMD中高端显卡'
    },
    {
        brand: '影驰',
        model: 'RTX 3060 Ti 星曜',
        chipset: 'RTX 3060 Ti',
        memory_size: 8,
        memory_type: 'GDDR6',
        memory_bus: 256,
        core_clock: 1410,
        boost_clock: 1665,
        tdp: 200,
        dimensions: '280 × 115 × 41',
        power_connectors: '8-pin',
        recommended_psu: 600,
        display_ports: 3,
        hdmi_ports: 1,
        price: 2899.00,
        notes: '上一代中端显卡，性价比高'
    }
];

// 插入示例数据
async function insertSampleGpus() {
    try {
        console.log('开始插入示例GPU数据...');
        
        // 检查是否已有数据
        const [existingData] = await db.query('SELECT COUNT(*) as count FROM gpus');
        if (existingData[0].count > 0) {
            console.log('GPU表中已有数据，跳过插入示例数据');
            return;
        }
        
        // 插入示例数据
        for (const gpu of sampleGpus) {
            await db.query(`
                INSERT INTO gpus (
                    brand, model, chipset, memory_size, memory_type, memory_bus,
                    core_clock, boost_clock, tdp, dimensions, power_connectors,
                    recommended_psu, display_ports, hdmi_ports, price, notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                gpu.brand, gpu.model, gpu.chipset, gpu.memory_size, gpu.memory_type,
                gpu.memory_bus, gpu.core_clock, gpu.boost_clock, gpu.tdp,
                gpu.dimensions, gpu.power_connectors, gpu.recommended_psu,
                gpu.display_ports, gpu.hdmi_ports, gpu.price, gpu.notes
            ]);
        }
        
        console.log(`成功插入 ${sampleGpus.length} 条示例GPU数据`);
    } catch (error) {
        console.error('插入示例GPU数据失败:', error);
    }
}

// 如果直接运行此文件，则执行插入
if (require.main === module) {
    insertSampleGpus()
        .then(() => {
            console.log('示例数据插入完成，程序退出');
            process.exit(0);
        })
        .catch(error => {
            console.error('示例数据插入失败:', error);
            process.exit(1);
        });
} else {
    module.exports = { insertSampleGpus };
}
