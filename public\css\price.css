/* Styles for price.html */

:root {
    /* Shared */
    --transition-duration: 0.3s;
    --transition-timing: cubic-bezier(0.175, 0.885, 0.32, 1.275);
    /* Bouncy transition */
    --smooth-transition: cubic-bezier(0.4, 0, 0.2, 1);
    --bounce-transition: cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* New "Slate & Sage" Palette */
    --clay-primary: #8F9D7D;
    /* <PERSON> Green */
    --clay-primary-dark: #6A7F5B;
    /* Darker Sage */
    --color-success: #7CB342;
    --color-success-dark: #689F38;
    --color-error: #E57373;
    --color-error-dark: #D32F2F;


    /* Light Mode (Default) */
    --clay-bg: #F0F2F0;
    --clay-container-bg: #E4E6E4;
    --clay-text: #4A4F45;
    --clay-secondary-text: #7A8175;
    --clay-border: #D8DAD8;
    --clay-light-shadow-color: rgba(255, 255, 255, 0.9);
    --clay-dark-shadow-color: rgba(200, 203, 200, 0.8);
}

.dark-mode {
    /* New "Midnight Neon" Dark Mode */
    --clay-bg: #2C2F33;
    /* Near Black */
    --clay-container-bg: #36393F;
    /* Darker container */
    --clay-text: #FFFFFF;
    --clay-secondary-text: #99AAB5;
    --clay-border: #00D1FF;
    /* Neon Cyan */

    /* Override accent colors for dark mode to fit the new theme */
    --clay-primary: #00D1FF;
    /* Neon Cyan */
    --clay-primary-dark: #00b8e6;
    /* Darker Neon */
    --clay-secondary: #8A63D2;
    /* Neon Purple */
    --color-success: #43B581;
    --color-danger: #F04747;
    --color-danger-glow: rgba(240, 71, 71, 0.7);


    --body-bg-dark: #2C2F33;
    --neon-gradient: linear-gradient(135deg, var(--clay-primary), var(--clay-secondary));
}

/* Enhanced Clay Shadows */
:root {
    --clay-shadow-outset: 8px 8px 18px var(--clay-dark-shadow-color), -8px -8px 18px var(--clay-light-shadow-color);
    --clay-shadow-inset: inset 7px 7px 15px var(--clay-dark-shadow-color), inset -7px -7px 15px var(--clay-light-shadow-color);
    --clay-shadow-button-active: inset 4px 4px 8px var(--clay-dark-shadow-color), inset -4px -4px 8px var(--clay-light-shadow-color);
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background-color: var(--clay-bg);
    color: var(--clay-text);
    max-width: 1280px;
    margin: 0 auto;
    padding: 40px;
    line-height: 1.8;
    font-size: 16px;
    position: relative;
    overflow-x: hidden;
    transition: background-color var(--transition-duration), color var(--transition-duration);
}

/* Subtle noise texture for clay effect */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 400 400' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='1.5' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
    opacity: 0.05;
    z-index: -1;
    pointer-events: none;
}

.container {
    background-color: var(--clay-bg);
    border-radius: 50px;
    box-shadow: var(--clay-shadow-outset);
    padding: 50px;
    margin-top: 30px;
    position: relative;
    border: 1px solid var(--clay-light-shadow-color);
    animation: clay-pop-in 0.8s var(--transition-timing) backwards;
    transition: background-color var(--transition-duration), box-shadow var(--transition-duration);
}

@keyframes clay-pop-in {
    from {
        opacity: 0;
        transform: scale(0.9);
    }

    to {
        opacity: 1;
        transform: scale(1);
    }
}

.clay-button {
    background: linear-gradient(45deg, var(--clay-bg), var(--clay-container-bg), var(--clay-bg));
    background-size: 200% 200%;
    animation: gradientShift 12s ease infinite;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 20px;
    font-size: 20px;
    color: var(--clay-secondary-text);
    cursor: pointer;
    transition: all var(--transition-duration) var(--transition-timing);
    box-shadow: var(--clay-shadow-outset);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}
.clay-button:hover {
    color: var(--clay-primary);
    transform: translateY(-2px);
}
.clay-button:active {
    box-shadow: var(--clay-shadow-inset);
    transform: translateY(1px);
}

.clay-button i {
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
    margin: 0 !important;
    padding: 0 !important;
    width: 100%;
    height: 100%;
    text-align: center;
    vertical-align: middle;
}

/* 确保链接形式的clay-button也正确显示 */
a.clay-button {
    text-decoration: none;
    display: flex !important;
    align-items: center;
    justify-content: center;
}

/* 强制覆盖FontAwesome可能的默认样式 */
.clay-button .fas,
.clay-button .far,
.clay-button .fab {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 100% !important;
    height: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    line-height: 1 !important;
    text-align: center !important;
    vertical-align: middle !important;
}

.back-button.left {
    position: absolute;
    top: 25px;
    left: 25px;
}

/* 右上角按钮组容器 */
.header-buttons {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    display: flex;
    gap: 10px;
    align-items: center;
}

.theme-toggle {
    /* 样式继承自clay-button，无需额外定位 */
}

/* 确保analytics按钮在按钮组中正确显示 */
#analyticsButton {
    display: flex;
    align-items: center;
    justify-content: center;
}

h1 {
    text-align: center;
    color: var(--clay-text);
    margin-bottom: 50px;
    margin-top: 20px;
    font-weight: 700;
    font-size: 36px;
    letter-spacing: -0.5px;
    text-shadow: 1px 1px 2px var(--clay-light-shadow-color);
}

.main-content,
.form-section[style*="margin-top: 20px;"] {
    animation: clay-pop-in 0.8s var(--transition-timing) backwards 0.2s;
}

.main-content {
    display: flex;
    gap: 40px;
}

.left-panel,
.right-panel {
    flex: 1;
}

.form-section {
    margin-bottom: 30px;
    background-color: var(--clay-bg);
    border-radius: 30px;
    padding: 35px;
    border: none;
    box-shadow: var(--clay-shadow-inset);
    transition: background-color var(--transition-duration), box-shadow var(--transition-duration);
}

.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.form-group {
    flex: 1;
    min-width: 0;
}

label {
    display: block;
    margin-bottom: 10px;
    font-weight: 500;
    color: var(--clay-secondary-text);
    font-size: 16px;
    padding-left: 10px;
}

input,
select,
.dynamic-input {
    width: 100%;
    padding: 16px;
    border: none;
    border-radius: 20px;
    font-size: 16px;
    font-family: inherit;
    transition: all var(--transition-duration) var(--transition-timing);
    box-sizing: border-box;
    background-color: var(--clay-bg);
    color: var(--clay-text);
    box-shadow: var(--clay-shadow-inset);
    /* Inset by default */
}

input:focus,
select:focus,
.dynamic-input:focus {
    outline: none;
    background-color: var(--clay-bg);
    box-shadow: inset 8px 8px 16px var(--clay-dark-shadow-color), inset -8px -8px 16px var(--clay-light-shadow-color);
    transform: scale(1.01);
}

.button,
.add-button,
.quick-button,
.action-btn,
.pagination-button {
    background: linear-gradient(45deg, var(--clay-primary), var(--clay-secondary), #4CAF50, #2196F3);
    background-size: 300% 300%;
    animation: gradientShift 10s ease infinite;
    color: white;
    border: none;
    border-radius: 25px;
    padding: 18px;
    font-weight: 600;
    font-size: 16px;
    cursor: pointer;
    transition: all var(--transition-duration) var(--transition-timing);
    box-shadow: 5px 5px 10px var(--clay-dark-shadow-color), -5px -5px 10px var(--clay-light-shadow-color);
    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.2);
}

.button:hover,
.add-button:hover,
.quick-button:hover,
.action-btn:hover,
.pagination-button:not(:disabled):hover {
    transform: translateY(-4px);
    box-shadow: 8px 8px 16px var(--clay-dark-shadow-color), -8px -8px 16px var(--clay-light-shadow-color);
    background-color: var(--clay-primary-dark);
    filter: brightness(1.05);
}

.button:active,
.add-button:active,
.quick-button:active,
.action-btn:active,
.pagination-button:active {
    transform: translateY(2px);
    box-shadow: var(--clay-shadow-inset);
    filter: brightness(0.95);
}

.add-button {
    width: 100%;
}

.calculate-button {
    background: linear-gradient(45deg, #4CAF50, #2196F3, #9C27B0, #FF5722);
    background-size: 400% 400%;
    animation: gradientShift 12s ease infinite;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    position: relative;
    overflow: hidden;
}

.calculate-button i {
    font-size: 16px;
    line-height: 1;
}

/* 确保所有按钮内的图标和文本都正确对齐 */
.button i,
.add-button i,
.quick-button i,
.action-btn i,
.pagination-button i,
.calculate-button i,
.reset-button i {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin: 0; /* 移除margin，使用gap控制间距 */
}

/* 为所有按钮添加flex布局以确保图标和文本对齐 */
.button,
.add-button,
.quick-button,
.calculate-button,
.reset-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.reset-button {
    background: linear-gradient(45deg, transparent, rgba(255, 87, 34, 0.1), rgba(244, 67, 54, 0.1), transparent);
    background-size: 300% 300%;
    animation: gradientShift 15s ease infinite;
    color: var(--clay-text);
    border: 2px solid transparent;
    background-clip: padding-box;
    box-shadow: 5px 5px 10px var(--clay-dark-shadow-color), -5px -5px 10px var(--clay-light-shadow-color);
    position: relative;
    overflow: hidden;
}

.reset-button:hover {
    background: var(--clay-container-bg);
}

.button-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
}

.dynamic-input {
    height: 250px;
    resize: vertical;
    margin-bottom: 25px;
}

.result-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 30px;
    background: var(--clay-bg);
    border-radius: 40px;
    margin-bottom: 20px;
    box-shadow: var(--clay-shadow-inset);
    transition: background-color var(--transition-duration), box-shadow var(--transition-duration);
}

@keyframes total-pop {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.15) rotate(-3deg);
    }

    100% {
        transform: scale(1) rotate(0deg);
    }
}

.total-amount {
    font-size: 48px;
    font-weight: 700;
    color: var(--clay-primary);
    text-shadow: 2px 2px 4px var(--clay-light-shadow-color);
    transition: color 0.3s;
}

.total-amount.updated {
    animation: total-pop 0.5s var(--transition-timing);
}

/* 快速选择按钮样式 */
.quick-items {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
}

/* 单行布局的快速选择按钮 */
.quick-items-single-row .quick-button {
    width: 100%;
    flex: none;
}

.quick-button {
    padding: 16px 20px;
    flex: 1;
    min-width: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.quantity-button {
    background: #A7B896;
    /* A lighter shade of Sage */
}

/* 气泡通知卡片样式 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    min-width: 250px;
    max-width: 350px;
    padding: 12px 16px;
    border-radius: 12px;
    color: white;
    font-weight: 500;
    font-size: 15px;
    opacity: 0;
    z-index: 1000;
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    gap: 10px;
    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.1);
    transform: translateX(100%) scale(0.9);
}

.toast.show {
    opacity: 1;
    transform: translateX(0) scale(1);
    animation: slide-in-right 0.3s ease-out forwards;
}

.toast.success {
    background: var(--color-success);
    border-left: 4px solid var(--color-success-dark);
}

.toast.error {
    background: var(--color-error);
    border-left: 4px solid var(--color-error-dark);
}

.toast.info {
    background: #3498db;
    border-left: 4px solid #2980b9;
}

/* Hide scrollbars completely in table containers - for all themes */
.table-container,
#historyContainer,
#rawTableModal .table-container,
#rawTableModal .modal-body,
#rawTableModal pre,
#rawTableModal .modal-content,
#rawTableDetailModal pre,
#rawTableDetailModal .modal-body,
#rawTableList,
#rawTableDetail,
#detailModal .modal-body,
.modal pre {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    overflow-y: auto;
}

.table-container::-webkit-scrollbar,
#historyContainer::-webkit-scrollbar,
#rawTableModal .table-container::-webkit-scrollbar,
#rawTableModal .modal-body::-webkit-scrollbar,
#rawTableModal pre::-webkit-scrollbar,
#rawTableModal .modal-content::-webkit-scrollbar,
#rawTableDetailModal pre::-webkit-scrollbar,
#rawTableDetailModal .modal-body::-webkit-scrollbar,
#rawTableList::-webkit-scrollbar,
#rawTableDetail::-webkit-scrollbar,
#detailModal .modal-body::-webkit-scrollbar,
.modal pre::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
    width: 0;
    height: 0;
}

.dark-mode .toast.success {
    background: var(--color-success);
}

.dark-mode .toast.info {
    background: #3498db;
}

@keyframes slide-in-right {
    0% {
        opacity: 0;
        transform: translateX(100%) scale(0.9);
    }
    100% {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

@keyframes slide-out-right {
    0% {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateX(100%) scale(0.9);
    }
}

.toast i {
    font-size: 18px;
    margin-right: 5px;
}

.toast span {
    flex: 1;
}

.action-btn {
    width: 40px;
    height: 40px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 15px;
}

.action-btn[title="编辑"] {
    background-color: var(--color-success);
}

.action-btn[title="应用此数据"] {
    background-color: var(--color-success);
}

/* Red */
.action-btn[title="删除"] {
    background-color: var(--color-error);
}

.danger-button {
    background: var(--color-error);
}
.danger-button:hover {
    background: var(--color-error-dark);
}

/* 添加响应式设计 - 移动端适配 */
@media (max-width: 992px) {
    body {
        padding: 20px;
        max-width: 100%;
    }

    .container {
        padding: 35px 25px;
        margin-top: 20px;
        border-radius: 30px;
    }

    .main-content {
        flex-direction: column;
    }

    .left-panel,
    .right-panel {
        width: 100%;
    }
}

@media (max-width: 768px) {
    body {
        padding: 10px;
    }

    .container {
        padding: 20px 15px;
        margin-top: 10px;
        border-radius: 20px;
    }

    h1 {
        font-size: 24px;
        margin-bottom: 25px;
        margin-top: 20px;
    }

    .form-row {
        flex-direction: column;
        gap: 15px;
    }

    .form-section {
        padding: 20px;
        margin-bottom: 20px;
        border-radius: 20px;
    }

    .quick-items {
        gap: 8px;
    }

    .quick-button {
        min-width: unset;
        flex: 1 0 calc(50% - 8px);
        padding: 10px 8px;
        font-size: 13px;
        border-radius: 15px;
    }

    .button-container {
        gap: 8px;
    }

    .button {
        padding: 12px 8px;
        font-size: 14px;
        gap: 6px; /* 移动端减小间距 */
    }

    .button i,
    .calculate-button i {
        font-size: 14px; /* 移动端图标稍小 */
    }

    /* 历史记录表格适配 */
    #historyContainer {
        overflow-x: auto;
    }

    /* 表格适配 */
    #historyContainer table {
        min-width: 100%;
    }

    #historyContainer th,
    #historyContainer td {
        padding: 8px 5px;
        font-size: 13px;
    }

    .action-btn {
        width: 32px;
        height: 32px;
        border-radius: 10px;
    }

    /* 详情弹窗适配 */
    #detailModal>div {
        width: 95%;
        padding: 15px;
        margin: 30px auto;
        max-height: 90vh;
    }

    .main-content {
        flex-direction: column;
    }

    .left-panel,
    .right-panel {
        width: 100%;
    }
}

/* 特小屏幕设备（如iPhone SE等） */
@media (max-width: 380px) {
    .container {
        padding: 15px 10px;
    }

    .quick-button {
        flex: 1 0 100%;
    }

    .action-btn {
        width: 28px;
        height: 28px;
        font-size: 12px;
    }

    #historyContainer th {
        font-size: 12px;
    }

    #historyContainer td {
        font-size: 12px;
        padding: 6px 3px;
    }
}

/* 超小屏设备的表格垂直显示 */
@media (max-width: 480px) {

    #historyContainer table,
    #historyContainer thead,
    #historyContainer tbody,
    #historyContainer th,
    #historyContainer td,
    #historyContainer tr {
        display: block;
    }

    #historyContainer thead tr {
        position: absolute;
        top: -9999px;
        left: -9999px;
    }

    #historyContainer tr {
        border: none;
        box-shadow: var(--clay-shadow-outset);
        margin-bottom: 15px;
        border-radius: 20px;
        background-color: var(--clay-bg);
        padding: 10px;
        transition: background-color var(--transition-duration), box-shadow var(--transition-duration);
    }

    #historyContainer td {
        position: relative;
        padding-left: 40% !important;
        text-align: right !important;
        border: none;
        border-bottom: 1px solid var(--clay-border);
    }

    #historyContainer td:last-child {
        border-bottom: 0;
        text-align: center !important;
        padding-left: 10px !important;
    }

    #historyContainer td::before {
        content: attr(data-label);
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
        width: 35%;
        white-space: nowrap;
        font-weight: bold;
        text-align: left;
    }
}

/* 表格样式改进 */
#historyContainer,
.table-container {
    border-radius: 25px;
    overflow: hidden;
    box-shadow: var(--clay-shadow-inset);
    border: none;
    background-color: var(--clay-bg);
    transition: background-color var(--transition-duration), box-shadow var(--transition-duration);
}

#historyContainer table,
.detail-table,
.raw-data-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    color: var(--clay-text);
}

#historyContainer thead {
    background-color: transparent;
}

#historyContainer th,
.detail-table th,
.raw-data-table th {
    padding: 16px;
    font-weight: 600;
    text-align: left;
    border-bottom: 2px solid var(--clay-border);
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: var(--clay-secondary-text);
}

#historyContainer td,
.detail-table td,
.raw-data-table td {
    padding: 16px;
    border-bottom: 1px solid var(--clay-border);
    transition: background-color 0.3s, border-color 0.3s;
}

#historyContainer tr:last-child td,
.raw-data-table tr:last-child td {
    border-bottom: none;
}

#historyContainer tbody tr:hover,
.raw-data-table tr:hover {
    background-color: rgba(97, 232, 225, 0.1);
    /* Lighter hover for dark mode */
}

/* 分页控件样式 */
.pagination-container {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    align-items: center;
}

.pagination-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    box-shadow: var(--clay-shadow-inset);
    transform: translateY(1px);
}

.pagination-info {
    font-size: 14px;
    color: var(--clay-secondary-text);
}

/* 弹窗样式改进 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(92, 75, 66, 0.4);
    z-index: 1000;
    overflow-y: auto;
    backdrop-filter: blur(8px) saturate(120%);
}

.modal-content {
    background: var(--clay-bg);
    width: 80%;
    max-width: 600px;
    margin: 50px auto;
    padding: 30px;
    border-radius: 40px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: var(--clay-shadow-outset);
    border: 1px solid var(--clay-light-shadow-color);
    animation: clay-pop-in 0.5s forwards;
    transition: background-color var(--transition-duration), box-shadow var(--transition-duration), border-color var(--transition-duration);
}

.dark-mode .modal-content {
    background-color: var(--clay-container-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    /* For Safari */
    border: 1px solid var(--clay-border);
    box-shadow: var(--glass-shadow);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--clay-border);
}

.dark-mode .modal-header {
    border-bottom-color: var(--clay-primary);
}

.modal-title {
    font-size: 22px;
    font-weight: 600;
    color: var(--clay-text);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: var(--clay-secondary-text);
    cursor: pointer;
    transition: all 0.3s var(--transition-timing);
}

.modal-close:hover {
    color: var(--clay-primary);
    transform: rotate(90deg) scale(1.2);
}

.modal-body {
    margin-bottom: 25px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 30px;
    border-top: 1px solid var(--clay-border);
    padding-top: 20px;
}

/* 详细表格数据样式 */
.detail-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
    margin-bottom: 10px;
    color: var(--clay-text);
}

.detail-table th {
    background-color: transparent;
    padding: 12px 16px;
    font-weight: 600;
    text-align: left;
    border: 1px solid var(--clay-border);
    font-size: 14px;
}

.detail-table td {
    padding: 8px 12px;
    border: 1px solid var(--clay-border);
}

.detail-table td.numeric {
    text-align: right;
}

.detail-table tr:hover {
    background-color: rgba(143, 157, 125, 0.1);
}

.raw-data-table th {
    white-space: nowrap;
}

.raw-data-table tr:last-child td {
    border-bottom: none;
}

.dark-mode .raw-data-table,
.dark-mode .detail-table {
    color: var(--clay-text);
}

.dark-mode .detail-table th,
.dark-mode .raw-data-table th,
.dark-mode .detail-table td,
.dark-mode .raw-data-table td,
.dark-mode #historyContainer th,
.dark-mode #historyContainer td {
    border-color: var(--clay-border);
}

.dark-mode #historyContainer td,
.dark-mode .raw-data-table td {
    border-bottom: none;
}

.dark-mode #historyContainer th,
.dark-mode .raw-data-table th {
    border-bottom: 1px solid var(--clay-container-bg);
}

/* Custom Scrollbar for Dark Mode */
.dark-mode::-webkit-scrollbar,
.dark-mode .modal-content::-webkit-scrollbar,
.dark-mode textarea::-webkit-scrollbar {
    width: 12px;
}

.dark-mode::-webkit-scrollbar-track,
.dark-mode .modal-content::-webkit-scrollbar-track,
.dark-mode textarea::-webkit-scrollbar-track {
    background: #2C2F33;
    /* Match the new body background */
}

.dark-mode::-webkit-scrollbar-thumb,
.dark-mode .modal-content::-webkit-scrollbar-thumb,
.dark-mode textarea::-webkit-scrollbar-thumb {
    background-color: var(--clay-container-bg);
    border-radius: 20px;
    border: 3px solid #2C2F33;
    /* Match the new body background */
}

.dark-mode::-webkit-scrollbar-thumb:hover,
.dark-mode .modal-content::-webkit-scrollbar-thumb:hover,
.dark-mode textarea::-webkit-scrollbar-thumb:hover {
    background-color: var(--clay-primary);
}


/* Apply glass overrides to dark mode */
body.dark-mode {
    background-image: none;
    background-color: var(--body-bg-dark);
    background-attachment: fixed;
    text-shadow: none;
}

.dark-mode .container {
    background-color: var(--clay-bg);
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
    border: 1px solid #333;
    box-shadow: 0 0 40px rgba(0, 0, 0, 0.5);
}

.dark-mode .clay-button {
    background: linear-gradient(45deg, var(--clay-container-bg), #2A2D3A, var(--clay-container-bg));
    background-size: 200% 200%;
    animation: gradientShift 16s ease infinite;
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
    border: 1px solid transparent;
    box-shadow: none;
    color: var(--clay-secondary-text);
    position: relative;
    overflow: hidden;
}

.dark-mode .clay-button:hover {
    color: var(--clay-primary);
    border-color: var(--clay-primary);
}

.dark-mode .clay-button i {
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
    margin: 0 !important;
    padding: 0 !important;
    width: 100%;
    height: 100%;
    text-align: center;
    vertical-align: middle;
}

/* 暗色模式下的链接形式clay-button */
.dark-mode a.clay-button {
    text-decoration: none;
    display: flex !important;
    align-items: center;
    justify-content: center;
}

/* 暗色模式下强制覆盖FontAwesome样式 */
.dark-mode .clay-button .fas,
.dark-mode .clay-button .far,
.dark-mode .clay-button .fab {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 100% !important;
    height: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    line-height: 1 !important;
    text-align: center !important;
    vertical-align: middle !important;
}


.dark-mode .form-section {
    background-color: transparent;
    box-shadow: none;
    border: 1px solid var(--clay-container-bg);
    padding: 25px;
}

.dark-mode input,
.dark-mode select,
.dark-mode .dynamic-input,
.dark-mode textarea {
    background-color: var(--clay-bg);
    border: 1px solid var(--clay-container-bg);
    box-shadow: none;
    color: var(--clay-text);
}

.dark-mode input:focus,
.dark-mode select:focus,
.dark-mode .dynamic-input:focus,
.dark-mode textarea:focus {
    box-shadow: 0 0 10px rgba(97, 232, 225, 0.5);
    border-color: var(--clay-primary);
    background-color: var(--clay-bg);
}

.dark-mode .button,
.dark-mode .add-button,
.dark-mode .quick-button,
.dark-mode .pagination-button,
.dark-mode .calculate-button {
    box-shadow: none;
    background: linear-gradient(45deg, #00D4FF, #5B73FF, #A855F7, #EC4899, #F59E0B);
    background-size: 500% 500%;
    animation: gradientShift 14s ease infinite;
    color: #ffffff;
    text-shadow: none;
    position: relative;
    overflow: hidden;
}

.dark-mode .button:hover,
.dark-mode .add-button:hover,
.dark-mode .quick-button:hover,
.dark-mode .pagination-button:not(:disabled):hover {
    box-shadow: 0 0 20px var(--clay-secondary);
    /* Use purple for glow */
    filter: brightness(1.1);
    transform: translateY(-2px);
}

.dark-mode .button:active,
.dark-mode .add-button:active,
.dark-mode .quick-button:active,
.dark-mode .pagination-button:active {
    box-shadow: none;
    transform: translateY(1px);
    filter: brightness(0.95);
}

.dark-mode .reset-button {
    background: linear-gradient(45deg, transparent, rgba(239, 68, 68, 0.1), rgba(245, 101, 101, 0.1), transparent);
    background-size: 300% 300%;
    animation: gradientShift 18s ease infinite;
    border: 1px solid var(--clay-secondary-text);
    color: var(--clay-secondary-text);
    position: relative;
    overflow: hidden;
}

.dark-mode .reset-button:hover {
    background: var(--clay-secondary-text);
    color: #1A1B26;
    border-color: var(--clay-secondary-text);
}

.dark-mode .result-container {
    background-color: var(--clay-bg);
    box-shadow: none;
    border: 1px solid var(--clay-container-bg);
}

.dark-mode .total-amount {
    color: var(--clay-primary);
    text-shadow: 0 0 15px var(--clay-primary), 0 0 25px var(--clay-primary);
    /* Add glow */
}

.dark-mode #historyContainer,
.dark-mode .table-container {
    background-color: var(--clay-container-bg);
    box-shadow: none;
    border: 1px solid var(--clay-container-bg);
}

.dark-mode .toast.success {
    background: var(--color-success);
}

.dark-mode .toast.info {
    background: #3498db;
}

.dark-mode .action-btn {
    box-shadow: none;
}

.dark-mode .action-btn[title="查看"],
.dark-mode .action-btn[title="查看详情"] {
    background: #00D1FF;
}

.dark-mode .action-btn[title="编辑"],
.dark-mode .action-btn[title="应用"],
.dark-mode .action-btn[title="应用此数据"] {
    background: #43B581;
}

.dark-mode .action-btn[title="删除"] {
    background: #F04747;
}

.dark-mode .action-btn:hover {
    transform: none;
    filter: none;
    box-shadow: none;
}

.dark-mode .danger-button {
    background: var(--color-error);
}

.dark-mode .button:hover,
.dark-mode .add-button:hover,
.dark-mode .quick-button:hover,
.dark-mode .pagination-button:not(:disabled):hover {
    box-shadow: 0 0 20px var(--clay-secondary);
    /* Use purple for glow */
    filter: brightness(1.1);
    transform: translateY(-2px);
}

.dark-mode .button:active,
.dark-mode .add-button:active,
.dark-mode .quick-button:active,
.dark-mode .pagination-button:active {
    box-shadow: none;
    transform: translateY(1px);
    filter: brightness(0.95);
}

.dark-mode .reset-button {
    background: transparent;
    border: 1px solid var(--clay-secondary-text);
    color: var(--clay-secondary-text);
}

.dark-mode .reset-button:hover {
    background: var(--clay-secondary-text);
    color: #1A1B26;
    border-color: var(--clay-secondary-text);
}

.dark-mode .result-container {
    background-color: var(--clay-bg);
    box-shadow: none;
    border: 1px solid var(--clay-container-bg);
}

.dark-mode .total-amount {
    color: var(--clay-primary);
    text-shadow: 0 0 15px var(--clay-primary), 0 0 25px var(--clay-primary);
    /* Add glow */
}

.dark-mode #historyContainer,
.dark-mode .table-container {
    background-color: var(--clay-container-bg);
    box-shadow: none;
    border: 1px solid var(--clay-container-bg);
}

@keyframes custom-bounce-in {
    from,
    20%,
    40%,
    60%,
    80%,
    to {
        animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    }

    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale3d(0.3, 0.3, 0.3);
    }

    20% {
        transform: translate(-50%, -50%) scale3d(1.1, 1.1, 1.1);
    }

    40% {
        transform: translate(-50%, -50%) scale3d(0.9, 0.9, 0.9);
    }

    60% {
        opacity: 1;
        transform: translate(-50%, -50%) scale3d(1.03, 1.03, 1.03);
    }

    80% {
        transform: translate(-50%, -50%) scale3d(0.97, 0.97, 0.97);
    }

    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale3d(1, 1, 1);
    }
}

@keyframes custom-bounce-out {
    20% {
        transform: translate(-50%, -50%) scale3d(0.9, 0.9, 0.9);
    }

    50%,
    55% {
        opacity: 1;
        transform: translate(-50%, -50%) scale3d(1.1, 1.1, 1.1);
    }

    to {
        opacity: 0;
        transform: translate(-50%, -50%) scale3d(0.3, 0.3, 0.3);
    }
}

.dark-mode .toast.success {
    background: var(--color-success);
}

.dark-mode .toast.info {
    background: #3498db;
}

/* 数据分析图表样式 */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
    margin-bottom: 30px;
}

.chart-container .no-data-message {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--clay-secondary-text);
    font-size: 16px;
    font-weight: 500;
}

.chart-container canvas {
    width: 100% !important;
    height: 100% !important;
    border-radius: 15px;
}

.chart-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--clay-text);
}

.chart-description {
    font-size: 14px;
    color: var(--clay-secondary-text);
    margin-bottom: 20px;
}

.data-filter {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 20px;
    align-items: center;
}

.data-filter label {
    margin-bottom: 0;
}

.data-filter select {
    min-width: 120px;
    padding: 10px;
}

/* 可编辑表格样式 */
.editable-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    table-layout: fixed; /* 添加固定表格布局 */
    counter-reset: row-number;
    position: relative;
}

.editable-table thead th {
    background-color: var(--clay-primary);
    color: white;
    padding: 12px 15px;
    text-align: left;
    font-weight: 500;
    position: sticky;
    top: 0;
    z-index: 10;
    white-space: nowrap; /* 防止表头换行 */
}

/* 设置各列的宽度 */
.editable-table th:nth-child(1),
.editable-table td:nth-child(1) {
    width: 35%;
}

.editable-table th:nth-child(2),
.editable-table td:nth-child(2) {
    width: 12%;
}

.editable-table th:nth-child(3),
.editable-table td:nth-child(3),
.editable-table th:nth-child(4),
.editable-table td:nth-child(4),
.editable-table th:nth-child(5),
.editable-table td:nth-child(5),
.editable-table th:nth-child(6),
.editable-table td:nth-child(6),
.editable-table th:nth-child(7),
.editable-table td:nth-child(7) {
    width: 7%;
    text-align: center;
}

.editable-table th:last-child,
.editable-table td:last-child {
    width: 10%;
    text-align: center;
}

.editable-table tbody td {
    padding: 10px 15px;
    border-bottom: 1px solid var(--clay-border);
    transition: background-color 0.2s;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 操作按钮样式 */
.editable-table .action-btn {
    background-color: var(--clay-primary);
    color: white;
    border: none;
    border-radius: 8px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    margin: 0 auto;
}

.editable-table .action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.editable-table tbody tr:hover td {
    background-color: rgba(0, 209, 255, 0.05);
}

.editable-table tbody tr:last-child td {
    border-bottom: none;
}

.editable-table td[contenteditable="true"] {
    cursor: text;
    outline: none;
    border: 2px solid transparent;
    transition: border-color 0.2s;
}

.editable-table td[contenteditable="true"]:focus {
    border-color: var(--clay-primary);
    background-color: rgba(0, 209, 255, 0.1);
}

.editable-table td.numeric {
    text-align: right;
}

.editable-table td.invalid-cell {
    background-color: rgba(255, 0, 0, 0.1);
    color: var(--color-error);
}

/* 行号已由上面的样式定义 */

/* 模态框内表格容器样式 */
#tableEditModal .table-container {
    max-height: 60vh;
    overflow-y: auto;
    border-radius: 8px;
    background-color: var(--clay-bg);
    scrollbar-width: thin;
}

#tableEditModal .table-container::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

#tableEditModal .table-container::-webkit-scrollbar-track {
    background: rgba(0,0,0,0.05);
    border-radius: 4px;
}

#tableEditModal .table-container::-webkit-scrollbar-thumb {
    background: var(--clay-primary);
    border-radius: 4px;
    opacity: 0.8;
}

/* 搜索高亮样式 */
.search-highlight {
    background-color: rgba(255, 235, 59, 0.5);
    border-radius: 3px;
    padding: 0 2px;
    font-weight: bold;
}

.dark-mode .search-highlight {
    background-color: rgba(0, 209, 255, 0.3);
}

/* 表格编辑模态框样式 */
#tableEditModal .modal-content {
    max-width: 90%;
    width: 1100px;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

#tableEditModal .modal-body {
    flex: 1;
    overflow: auto;
    padding: 20px;
    scrollbar-width: thin;
}

#tableEditModal .modal-body::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

#tableEditModal .modal-body::-webkit-scrollbar-track {
    background: rgba(0,0,0,0.05);
    border-radius: 4px;
}

#tableEditModal .modal-body::-webkit-scrollbar-thumb {
    background: var(--clay-primary);
    border-radius: 4px;
    opacity: 0.8;
}

#tableEditModal .modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--clay-border);
    background-color: var(--clay-bg);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#tableEditModal .modal-footer {
    padding: 15px 20px;
    border-top: 1px solid var(--clay-border);
    background-color: var(--clay-bg);
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* 动画样式 */
@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

#tableEditModal .modal-content {
    animation: modalFadeIn 0.3s ease-out;
}

/* 行号显示 */
.editable-table tbody tr {
    counter-increment: row-number;
    position: relative;
}

.editable-table tbody tr td:first-child {
    position: relative;
    padding-left: 30px; /* 为行号留出空间 */
}

.editable-table tbody tr td:first-child::before {
    content: counter(row-number);
    position: absolute;
    left: 8px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: var(--clay-secondary-text);
    opacity: 0.7;
    background-color: var(--clay-bg);
    border-radius: 10px;
}

/* 表格搜索样式 */
#tableSearchInput {
    border: 1px solid var(--clay-border);
    border-radius: 8px;
    padding: 8px 12px;
    background-color: var(--clay-bg);
    color: var(--clay-text);
    width: 200px;
}

#tableSearchInput:focus {
    border-color: var(--clay-primary);
    outline: none;
    box-shadow: 0 0 0 2px rgba(0, 209, 255, 0.2);
}

/* 添加不同价格按钮的样式 - 更符合页面整体风格 */
.quick-button.price-20 {
    background: linear-gradient(45deg, #2196F3, #42a5f5, #64B5F6, #90CAF9);
    background-size: 400% 400%;
    animation: gradientShift 12s ease infinite;
    position: relative;
    overflow: hidden;
}

.quick-button.price-30 {
    background: linear-gradient(45deg, #00BCD4, #4dd0e1, #80DEEA, #B2EBF2);
    background-size: 400% 400%;
    animation: gradientShift 14s ease infinite;
    position: relative;
    overflow: hidden;
}

.quick-button.price-40 {
    background: linear-gradient(45deg, #26c6da, #80deea, #B2EBF2, #E0F2F1);
    background-size: 400% 400%;
    animation: gradientShift 16s ease infinite;
    position: relative;
    overflow: hidden;
}

.quick-button.price-50 {
    background: linear-gradient(45deg, #673AB7, #9575cd, #BA68C8, #CE93D8);
    background-size: 400% 400%;
    animation: gradientShift 10s ease infinite;
    position: relative;
    overflow: hidden;
}

.quick-button.price-special {
    background: linear-gradient(45deg, #ff5252, #ff8a80, #FFAB91, #FFCCBC);
    background-size: 400% 400%;
    animation: gradientShift 8s ease infinite;
    position: relative;
    overflow: hidden;
}

.quick-button.price-orange {
    background: linear-gradient(45deg, #FF9800, #FFB74D, #FFCC02, #FFE082);
    background-size: 400% 400%;
    animation: gradientShift 13s ease infinite;
    position: relative;
    overflow: hidden;
}

/* 深色主题下的按钮样式调整 */
.dark-mode .quick-button.price-20 {
    background: linear-gradient(45deg, #1976D2, #2196F3, #42A5F5, #64B5F6);
    background-size: 400% 400%;
    animation: gradientShift 11s ease infinite;
    box-shadow: 0 4px 8px rgba(33, 150, 243, 0.3);
}

.dark-mode .quick-button.price-30 {
    background: linear-gradient(45deg, #0097A7, #00BCD4, #26C6DA, #4DD0E1);
    background-size: 400% 400%;
    animation: gradientShift 13s ease infinite;
    box-shadow: 0 4px 8px rgba(0, 188, 212, 0.3);
}

.dark-mode .quick-button.price-40 {
    background: linear-gradient(45deg, #00ACC1, #26C6DA, #4DD0E1, #80DEEA);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    box-shadow: 0 4px 8px rgba(38, 198, 218, 0.3);
}

.dark-mode .quick-button.price-50 {
    background: linear-gradient(45deg, #5E35B1, #673AB7, #7986CB, #9575CD);
    background-size: 400% 400%;
    animation: gradientShift 9s ease infinite;
    box-shadow: 0 4px 8px rgba(103, 58, 183, 0.3);
}

.dark-mode .quick-button.price-special {
    background: linear-gradient(45deg, #E53935, #FF5252, #FF7043, #FF8A65);
    background-size: 400% 400%;
    animation: gradientShift 7s ease infinite;
    box-shadow: 0 4px 8px rgba(244, 67, 54, 0.3);
}

.dark-mode .quick-button.price-orange {
    background: linear-gradient(45deg, #F57C00, #FF9800, #FFB74D, #FFCC02);
    background-size: 400% 400%;
    animation: gradientShift 12s ease infinite;
    box-shadow: 0 4px 8px rgba(255, 152, 0, 0.3);
}

/* 价格标签样式 - 更协调的颜色 */
.price-badge {
    display: inline-block;
    background-color: rgba(255, 255, 255, 0.25);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    margin-left: 5px;
    font-weight: bold;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.dark-mode .price-badge {
    background-color: rgba(0, 0, 0, 0.25);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
}

/* 移除不同价格标签的颜色，使用统一风格 */
.price-20 .price-badge,
.price-30 .price-badge,
.price-40 .price-badge,
.price-50 .price-badge,
.dark-mode .price-20 .price-badge,
.dark-mode .price-30 .price-badge,
.dark-mode .price-40 .price-badge,
.dark-mode .price-50 .price-badge {
    background-color: rgba(0, 0, 0, 0.25);
}

/* 按钮悬停效果 - 增强版 */
.quick-button.price-20:hover,
.quick-button.price-30:hover,
.quick-button.price-40:hover,
.quick-button.price-50:hover,
.quick-button.price-special:hover,
.quick-button.price-orange:hover {
    filter: brightness(1.1);
    transform: translateY(-4px) scale(1.03);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
    transition: all 0.3s var(--bounce-transition);
}

/* 利润总计区域样式 */
.profit-total-section {
    background-color: var(--clay-primary);
    color: white;
    transition: all 0.4s var(--smooth-transition);
    position: relative;
    overflow: hidden;
}

.profit-total-section::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
        to right,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.3) 50%,
        rgba(255, 255, 255, 0) 100%
    );
    transform: rotate(30deg);
    animation: shimmer 3s infinite;
    pointer-events: none;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) rotate(30deg); }
    100% { transform: translateX(100%) rotate(30deg); }
}

/* 深色主题下的利润总计区域 */
.dark-mode .profit-total-section {
    background: linear-gradient(135deg, #4A5568 0%, #2D3748 100%);
    color: #E2E8F0;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* 按钮按下效果 */
.quick-button.price-20:active,
.quick-button.price-30:active,
.quick-button.price-40:active,
.quick-button.price-50:active,
.quick-button.price-special:active,
.quick-button.price-orange:active {
    filter: brightness(0.95);
    transform: translateY(1px) scale(0.98);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.1s ease;
}

/* 错误信息样式 */
.error-message {
    color: var(--color-error);
    font-size: 14px;
    margin-top: 5px;
    min-height: 20px; /* 防止布局抖动 */
    font-weight: 500;
    transition: all 0.3s;
}

.input-error {
    /* 使用红色外发光效果来高亮错误输入框 */
    box-shadow: var(--clay-shadow-inset), 0 0 0 2px var(--color-error) !important;
}

/* ===== 新增动画样式 ===== */

/* 页面加载动画优化 */
.container {
    animation: containerFadeIn 0.8s var(--smooth-transition);
}

@keyframes containerFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 标题动画 */
h1 {
    animation: titleSlideIn 1s var(--bounce-transition);
}

@keyframes titleSlideIn {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* 面板动画 */
.left-panel, .right-panel {
    animation: panelSlideUp 0.8s var(--smooth-transition);
}

.left-panel {
    animation-delay: 0.2s;
}

.right-panel {
    animation-delay: 0.4s;
}

@keyframes panelSlideUp {
    from {
        opacity: 0;
        transform: translateY(40px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 输入框聚焦动画 */
input[type="text"], input[type="number"], textarea, select {
    transition: all 0.3s var(--smooth-transition);
    position: relative;
}

input[type="text"]:focus,
input[type="number"]:focus,
textarea:focus,
select:focus {
    transform: translateY(-2px);
    box-shadow: var(--clay-shadow-inset), 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* 按钮动画增强 - 添加渐变动态配色 */
.button, .clay-button, .quick-button, .add-button {
    transition: all 0.3s var(--smooth-transition);
    position: relative;
    overflow: hidden;
    background: linear-gradient(45deg, var(--clay-primary), var(--clay-secondary));
    background-size: 200% 200%;
    animation: gradientShift 8s ease infinite;
}

/* 渐变动态效果 */
@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    25% {
        background-position: 100% 0%;
    }
    50% {
        background-position: 100% 100%;
    }
    75% {
        background-position: 0% 100%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* 按钮悬停时的渐变加速效果 */
.button:hover, .clay-button:hover, .quick-button:hover, .add-button:hover, .calculate-button:hover {
    animation-duration: 4s;
    filter: brightness(1.1) saturate(1.2);
}

/* 按钮点击时的脉冲效果 */
@keyframes pulseGlow {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    }
}

.button:active, .clay-button:active, .quick-button:active, .add-button:active, .calculate-button:active {
    animation: pulseGlow 0.6s ease-out;
}

/* 彩虹渐变效果 - 用于特殊按钮 */
@keyframes rainbowGradient {
    0% { background-position: 0% 50%; }
    14% { background-position: 25% 50%; }
    28% { background-position: 50% 50%; }
    42% { background-position: 75% 50%; }
    57% { background-position: 100% 50%; }
    71% { background-position: 75% 50%; }
    85% { background-position: 50% 50%; }
    100% { background-position: 0% 50%; }
}

/* 为特殊功能按钮添加彩虹效果 */
.quick-button.price-special,
.quick-button.price-orange {
    background: linear-gradient(45deg, #ff0000, #ff8000, #ffff00, #80ff00, #00ff00, #00ff80, #00ffff, #0080ff, #0000ff, #8000ff, #ff00ff, #ff0080);
    background-size: 1200% 1200%;
    animation: rainbowGradient 20s ease infinite;
}

.dark-mode .quick-button.price-special,
.dark-mode .quick-button.price-orange {
    background: linear-gradient(45deg, #cc0000, #cc6600, #cccc00, #66cc00, #00cc00, #00cc66, #00cccc, #0066cc, #0000cc, #6600cc, #cc00cc, #cc0066);
    background-size: 1200% 1200%;
    animation: rainbowGradient 20s ease infinite;
}

/* 按钮点击波纹效果 */
.button::before, .clay-button::before, .quick-button::before, .add-button::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.button:active::before,
.clay-button:active::before,
.quick-button:active::before,
.add-button:active::before {
    width: 300px;
    height: 300px;
}

/* 按钮悬停效果增强 */
.button:hover, .clay-button:hover, .quick-button:hover, .add-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.button:active, .clay-button:active, .quick-button:active, .add-button:active {
    transform: translateY(-1px);
    transition: transform 0.1s;
}

/* 总金额显示动画 */
.total-amount {
    transition: all 0.4s var(--bounce-transition);
}

.total-amount.updating {
    transform: scale(1.1);
    color: var(--clay-primary);
}

/* 结果容器动画 */
.result-container {
    transition: all 0.3s var(--smooth-transition);
}

.result-container:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* 快捷按钮网格动画 */
.quick-button {
    animation: buttonFadeIn 0.6s var(--smooth-transition);
}

.quick-button:nth-child(1) { animation-delay: 0.1s; }
.quick-button:nth-child(2) { animation-delay: 0.2s; }
.quick-button:nth-child(3) { animation-delay: 0.3s; }
.quick-button:nth-child(4) { animation-delay: 0.4s; }
.quick-button:nth-child(5) { animation-delay: 0.5s; }
.quick-button:nth-child(6) { animation-delay: 0.6s; }

@keyframes buttonFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.8);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* 移除可能有问题的模态框动画 */

/* 表格行动画 */
.history-table tbody tr {
    animation: tableRowSlideIn 0.5s var(--smooth-transition);
}

.history-table tbody tr:nth-child(odd) {
    animation-delay: 0.1s;
}

.history-table tbody tr:nth-child(even) {
    animation-delay: 0.2s;
}

@keyframes tableRowSlideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--clay-primary);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 成功提示动画 */
.success-flash {
    animation: successFlash 0.6s var(--smooth-transition);
}

@keyframes successFlash {
    0% { background-color: transparent; }
    50% { background-color: rgba(124, 179, 66, 0.3); }
    100% { background-color: transparent; }
}

/* 错误提示动画 */
.error-shake {
    animation: errorShake 0.5s var(--smooth-transition);
}

@keyframes errorShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* 数字计数动画 */
.count-up {
    animation: countUp 0.8s var(--bounce-transition);
}

@keyframes countUp {
    from {
        transform: scale(0.8);
        opacity: 0.5;
    }
    50% {
        transform: scale(1.1);
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

/* 悬停时的微妙动画 */
.form-section:hover {
    transform: translateY(-2px);
    transition: transform 0.3s var(--smooth-transition);
}

/* 输入框标签动画 */
.form-group label {
    transition: all 0.3s var(--smooth-transition);
}

.form-group input:focus + label,
.form-group input:not(:placeholder-shown) + label {
    transform: translateY(-5px) scale(0.9);
    color: var(--clay-primary);
}

/* 按钮点击波纹效果 */
@keyframes ripple {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(4);
        opacity: 0;
    }
}

/* 页面切换动画 */
.page-transition {
    animation: pageSlideIn 0.6s var(--smooth-transition);
}

@keyframes pageSlideIn {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 波纹效果 */
.ripple-effect {
    position: absolute;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.4);
    transform: scale(0);
    animation: ripple 0.6s linear;
    pointer-events: none;
}

@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* 输入框焦点动画 */
.input-focus-animation {
    box-shadow: 0 0 0 2px var(--clay-primary-dark) !important;
    transform: translateY(-3px);
    transition: all 0.3s var(--bounce-transition);
}

/* 表格行悬停效果 */
.editable-table tbody tr:hover {
    background-color: rgba(143, 157, 125, 0.1);
    transition: background-color 0.3s ease;
}

/* 表格单元格编辑动画 */
.editable-table td[contenteditable="true"]:focus {
    background-color: rgba(143, 157, 125, 0.2);
    box-shadow: inset 0 0 0 2px var(--clay-primary);
    outline: none;
    transition: all 0.3s var(--smooth-transition);
}

/* 表格排序图标动画 */
.sort-icon {
    transition: transform 0.3s var(--bounce-transition);
}

.sort-icon.active {
    transform: rotate(180deg);
    color: var(--clay-primary);
}

/* 加载中动画 */
.loading {
    display: inline-block;
    position: relative;
    width: 80px;
    height: 80px;
}

.loading div {
    position: absolute;
    top: 33px;
    width: 13px;
    height: 13px;
    border-radius: 50%;
    background: var(--clay-primary);
    animation-timing-function: cubic-bezier(0, 1, 1, 0);
}

.loading div:nth-child(1) {
    left: 8px;
    animation: loading1 0.6s infinite;
}

.loading div:nth-child(2) {
    left: 8px;
    animation: loading2 0.6s infinite;
}

.loading div:nth-child(3) {
    left: 32px;
    animation: loading2 0.6s infinite;
}

.loading div:nth-child(4) {
    left: 56px;
    animation: loading3 0.6s infinite;
}

@keyframes loading1 {
    0% { transform: scale(0); }
    100% { transform: scale(1); }
}

@keyframes loading2 {
    0% { transform: translate(0, 0); }
    100% { transform: translate(24px, 0); }
}

@keyframes loading3 {
    0% { transform: scale(1); }
    100% { transform: scale(0); }
}

/* 内部备注硬盘分区选择器样式 */
#diskPartitionSelect, #diskPartitionInput {
    transition: all 0.3s var(--smooth-transition);
}

#diskPartitionSelect:focus, #diskPartitionInput:focus {
    outline: none;
    border-color: var(--clay-primary);
    box-shadow: 0 0 0 2px rgba(143, 157, 125, 0.2);
}

/* 深色主题下的选择器样式 */
.dark-mode #diskPartitionSelect,
.dark-mode #diskPartitionInput {
    background-color: var(--clay-container-bg);
    border-color: var(--clay-border);
    color: var(--clay-text);
}

.dark-mode #diskPartitionSelect option {
    background-color: var(--clay-container-bg);
    color: var(--clay-text);
}

/* 选择器悬停效果 */
#diskPartitionSelect:hover,
#diskPartitionInput:hover {
    border-color: var(--clay-primary-dark);
    transform: translateY(-1px);
}

/* 计算器按钮样式 */
.calculator-btn {
    padding: 20px;
    border: none;
    border-radius: 15px;
    font-size: 20px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: var(--clay-shadow-button);
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.calculator-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--clay-shadow-button-hover);
}

.calculator-btn:active {
    transform: translateY(0);
    box-shadow: var(--clay-shadow-button-active);
}

/* 数字按钮 */
.number-btn {
    background: linear-gradient(135deg, var(--clay-container-bg) 0%, var(--clay-bg) 100%);
    color: var(--clay-text);
}

.number-btn:hover {
    background: linear-gradient(135deg, var(--clay-primary) 0%, var(--clay-primary-dark) 100%);
    color: white;
}

/* 运算符按钮 */
.operator-btn {
    background: linear-gradient(135deg, var(--clay-primary) 0%, var(--clay-primary-dark) 100%);
    color: white;
}

.operator-btn:hover {
    background: linear-gradient(135deg, var(--clay-primary-dark) 0%, var(--clay-primary) 100%);
}

/* 等号按钮 */
.equals-btn {
    background: linear-gradient(135deg, var(--color-success) 0%, var(--color-success-dark) 100%);
    color: white;
}

.equals-btn:hover {
    background: linear-gradient(135deg, var(--color-success-dark) 0%, var(--color-success) 100%);
}

/* 重置按钮 */
.reset-btn {
    background: linear-gradient(135deg, var(--color-error) 0%, var(--color-error-dark) 100%);
    color: white;
}

.reset-btn:hover {
    background: linear-gradient(135deg, var(--color-error-dark) 0%, var(--color-error) 100%);
}

/* 计算器显示屏样式 */
#calculatorDisplay {
    font-family: 'Courier New', monospace;
    letter-spacing: 2px;
}

/* 暗色模式下的计算器按钮调整 */
.dark-mode .number-btn {
    background: linear-gradient(135deg, var(--clay-container-bg) 0%, #4A4F54 100%);
    color: var(--clay-text);
    border: 1px solid var(--clay-border);
}

.dark-mode .number-btn:hover {
    background: linear-gradient(135deg, var(--clay-primary) 0%, var(--clay-primary-dark) 100%);
    border-color: var(--clay-primary);
}
