const express = require('express');
const createResourceRouter = require('./resource-handler');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const sharp = require('sharp');
const db = require('../config/db');

// 创建一个自定义的Express路由器
const router = express.Router();

// 配置multer用于RAM图片上传
const ramStorage = multer.diskStorage({
    destination: function (req, file, cb) {
        const uploadDir = path.join(__dirname, '..', 'public', 'uploads', 'ram');
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const fileExtension = path.extname(file.originalname);
        cb(null, 'ram-' + uniqueSuffix + fileExtension);
    }
});

const ramUpload = multer({ storage: ramStorage });

// 辅助函数：安全地解析数字
const parseIntSafe = (value) => value === '' || value === undefined || value === null ? null : (Number.isNaN(parseInt(value)) ? null : parseInt(value));
const parseFloatSafe = (value) => value === '' || value === undefined || value === null ? null : (Number.isNaN(parseFloat(value)) ? null : parseFloat(value));

// 直接实现自己的路由处理程序，不使用通用资源处理器
// 获取所有RAM
router.get('/', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const offset = (page - 1) * limit;
        const search = req.query.search || '';
        const brand = req.query.brand || '';
        const capacity = req.query.capacity || '';

        let whereClause = '';
        let params = [];
        let conditions = [];

        // 构建WHERE子句
        if (search) {
            conditions.push(`(brand LIKE ? OR model LIKE ? OR type LIKE ?)`);
            params.push(...Array(3).fill(`%${search}%`));
        }

        if (brand && brand !== 'all') {
            conditions.push(`brand = ?`);
            params.push(brand);
        }

        if (capacity && capacity !== 'all') {
            conditions.push(`capacity = ?`);
            params.push(parseInt(capacity));
        }

        if (conditions.length > 0) {
            whereClause = `WHERE ${conditions.join(' AND ')}`;
        }

        const countQuery = `SELECT COUNT(*) as total FROM ram ${whereClause}`;
        const [countResult] = await db.query(countQuery, params);
        const total = countResult[0].total;

        const dataQuery = `SELECT * FROM ram ${whereClause} ORDER BY created_at DESC LIMIT ? OFFSET ?`;
        const [rams] = await db.query(dataQuery, [...params, limit, offset]);

        res.json({
            data: rams,
            total,
            page,
            pages: Math.ceil(total / limit)
        });
    } catch (error) {
        res.status(500).json({ message: '获取RAM列表失败', error: error.message });
    }
});

// 获取单个RAM
router.get('/:id', async (req, res) => {
    try {
        const [rams] = await db.query('SELECT * FROM ram WHERE id = ?', [req.params.id]);
        if (rams.length > 0) {
            res.json(rams[0]);
        } else {
            res.status(404).json({ message: '未找到指定的RAM' });
        }
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// 创建RAM
router.post('/', ramUpload.single('image'), async (req, res) => {
    try {
        console.log("接收到的表单数据:", req.body);
        
        // 处理字段映射
        const {
            brand, model, capacity, type: memoryType, speed, 
            modules, kit_size, casLatency, voltage, 
            heatSpreader, rgbLighting, rgb, 
            color, price, notes
        } = req.body;
        
        let imageUrl = null;
        if (req.file) {
            const tempPath = req.file.path;
            const outputFilename = path.basename(tempPath, path.extname(tempPath)) + '.webp';
            const outputPath = path.join(path.dirname(tempPath), outputFilename);
            await sharp(tempPath).webp({ quality: 100, lossless: true }).toFile(outputPath);
            fs.unlinkSync(tempPath);
            imageUrl = `/uploads/ram/${outputFilename}`;
        } else {
            imageUrl = '/images/default-ram.png';
        }

        // 优先使用kit_size，如果不存在则使用modules
        const finalKitSize = kit_size || modules;
        // 优先使用rgb，如果不存在则使用rgbLighting
        const finalRgb = rgb || rgbLighting;
        
        const ramData = {
            brand, 
            model, 
            type: memoryType,
            capacity: parseIntSafe(capacity),
            speed: parseIntSafe(speed),
            kit_size: parseIntSafe(finalKitSize),
            cas_latency: parseIntSafe(casLatency),
            voltage: parseFloatSafe(voltage),
            heat_spreader: parseIntSafe(heatSpreader),
            rgb: parseIntSafe(finalRgb),
            color,
            price: parseFloatSafe(price),
            notes,
            image_url: imageUrl
        };
        
        console.log("处理后的数据:", ramData);
        
        // 生成SQL并记录
        const sql = `INSERT INTO ram SET 
            brand = ?,
            model = ?,
            type = ?,
            capacity = ?,
            speed = ?,
            kit_size = ?,
            cas_latency = ?,
            voltage = ?,
            heat_spreader = ?,
            rgb = ?,
            color = ?,
            price = ?,
            notes = ?,
            image_url = ?`;
        
        console.log("执行的SQL:", sql);
        console.log("SQL参数:", Object.values(ramData));
        
        const [result] = await db.query('INSERT INTO ram SET ?', ramData);
        res.status(201).json({ id: result.insertId, ...ramData });
    } catch (error) {
        console.error('创建RAM失败:', error);
        res.status(500).json({ message: '创建RAM失败', error: error.message });
    }
});

// 更新RAM
router.put('/:id', ramUpload.single('image'), async (req, res) => {
    try {
        console.log("接收到的更新表单数据:", req.body);
        
        const { id } = req.params;
        const {
            brand, model, capacity, type: memoryType, speed, 
            modules, kit_size, casLatency, voltage, 
            heatSpreader, rgbLighting, rgb, 
            color, price, notes
        } = req.body;

        // 优先使用kit_size，如果不存在则使用modules
        const finalKitSize = kit_size || modules;
        // 优先使用rgb，如果不存在则使用rgbLighting
        const finalRgb = rgb || rgbLighting;

        const ramData = {
            brand, 
            model, 
            type: memoryType,
            capacity: parseIntSafe(capacity),
            speed: parseIntSafe(speed),
            kit_size: parseIntSafe(finalKitSize),
            cas_latency: parseIntSafe(casLatency),
            voltage: parseFloatSafe(voltage),
            heat_spreader: parseIntSafe(heatSpreader),
            rgb: parseIntSafe(finalRgb),
            color,
            price: parseFloatSafe(price),
            notes
        };

        if (req.file) {
            const tempPath = req.file.path;
            const outputFilename = path.basename(tempPath, path.extname(tempPath)) + '.webp';
            const outputPath = path.join(path.dirname(tempPath), outputFilename);
            await sharp(tempPath).webp({ quality: 100, lossless: true }).toFile(outputPath);
            fs.unlinkSync(tempPath);
            ramData.image_url = `/uploads/ram/${outputFilename}`;
        }
        
        console.log("处理后的更新数据:", ramData);
        console.log("SQL参数:", Object.values(ramData));

        await db.query('UPDATE ram SET ? WHERE id = ?', [ramData, id]);
        res.json({ id: parseInt(id), ...ramData });
    } catch (error) {
        console.error('更新RAM失败:', error);
        res.status(500).json({ message: '更新RAM失败', error: error.message });
    }
});

// 删除RAM
router.delete('/:id', async (req, res) => {
    try {
        await db.query('DELETE FROM ram WHERE id = ?', [req.params.id]);
        res.status(204).send();
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

module.exports = router; 