
// 显示Toast通知
function showToast(message, type = 'success') {
    const toast = document.getElementById('toast');
    const toastMessage = document.getElementById('toastMessage');
    const toastIcon = document.getElementById('toastIcon');

    toastMessage.textContent = message;

    // 设置图标和颜色
    if (type === 'error') {
        toastIcon.className = 'fas fa-times-circle text-red-400';
    } else {
        toastIcon.className = 'fas fa-check-circle text-green-400';
    }

    // 显示Toast
    toast.classList.remove('translate-y-[-100%]');

    // 3秒后隐藏
    setTimeout(() => {
        toast.classList.add('translate-y-[-100%]');
    }, 3000);
}

// 设置cookie的辅助函数
function setCookie(name, value, days) {
    let expires = "";

    if (days) {
        const date = new Date();
        date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
        expires = "; expires=" + date.toUTCString();
    }

    document.cookie = name + "=" + (value || "") + expires + "; path=/";
}

// 获取cookie的辅助函数
function getCookie(name) {
    const nameEQ = name + "=";
    const ca = document.cookie.split(';');
    for (let i = 0; i < ca.length; i++) {
        let c = ca[i];
        while (c.charAt(0) == ' ') c = c.substring(1, c.length);
        if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length, c.length);
    }
    return null;
}

// 登录表单提交
document.getElementById('loginForm').addEventListener('submit', async (e) => {
    e.preventDefault();

    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;

    try {
        console.log('前端尝试登录:', username);
        showToast('正在登录...', 'success');

        const response = await fetch('/api/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ username, password })
        });

        console.log('登录响应状态:', response.status);
        let data;

        try {
            data = await response.json();
            console.log('响应数据:', data);
        } catch (jsonError) {
            console.error('解析JSON失败:', jsonError);
            throw new Error('服务器响应格式错误');
        }

        if (data.token) {
            console.log('登录成功:', data);
            console.log('存储token:', data.token);
            console.log('存储username:', data.username);

            // 将token和用户名存储在localStorage中
            localStorage.setItem('token', data.token);
            localStorage.setItem('username', data.username || data.user.username); // 确保获取用户名

            // 同时将token存储在cookie中，有效期1天
            setCookie('token', data.token, 1);

            // 检查存储是否成功
            console.log('存储后检查 token:', localStorage.getItem('token'));
            console.log('存储后检查 username:', localStorage.getItem('username'));

            showToast('登录成功，即将跳转...', 'success');

            // 检查是否有重定向URL参数
            const urlParams = new URLSearchParams(window.location.search);
            const redirectUrl = urlParams.get('redirect');

            console.log('登录成功后的重定向逻辑:');
            console.log('- 当前URL:', window.location.href);
            console.log('- URL参数:', urlParams.toString());
            console.log('- 重定向URL:', redirectUrl);
            console.log('- 用户角色:', data.user?.role);

            setTimeout(() => {
                if (redirectUrl && redirectUrl !== '/login.html') {
                    // 如果有重定向URL且不是登录页，跳转到指定页面
                    console.log('重定向到指定页面:', decodeURIComponent(redirectUrl));
                    window.location.href = decodeURIComponent(redirectUrl);
                } else if (data.user && data.user.role === 'admin') {
                    // 如果是管理员，跳转到主页
                    console.log('管理员用户，跳转到主页');
                    window.location.href = '/';
                } else {
                    // 如果是普通用户，跳转到组件页面
                    console.log('普通用户，跳转到组件页面');
                    window.location.href = '/pc-components.html';
                }
            }, 1000);
        } else {
            throw new Error(data.error || '登录失败，但未返回明确错误');
        }

    } catch (error) {
        console.error('登录失败:', error);
        showToast(error.message || '登录失败', 'error');
    }
});

// 注册表单提交
document.getElementById('registerForm').addEventListener('submit', async (e) => {
    e.preventDefault();

    const username = document.getElementById('reg_username').value;
    const password = document.getElementById('reg_password').value;
    const confirmPassword = document.getElementById('confirm_password').value;

    // 验证用户名格式
    if (username.length < 6) {
        showToast('用户名至少需要6个字符', 'error');
        return;
    }

    if (username.includes(' ')) {
        showToast('用户名不能包含空格', 'error');
        return;
    }

    // 验证密码格式
    if (password.length < 6) {
        showToast('密码至少需要6个字符', 'error');
        return;
    }

    const hasLetter = /[a-zA-Z]/.test(password);
    const hasNumber = /[0-9]/.test(password);

    if (!hasLetter || !hasNumber) {
        showToast('密码必须同时包含字母和数字', 'error');
        return;
    }

    // 验证密码匹配
    if (password !== confirmPassword) {
        showToast('两次输入的密码不匹配', 'error');
        return;
    }

    try {
        console.log('前端尝试注册:', username);
        showToast('正在注册...', 'success');

        const response = await fetch('/api/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ username, password })
        });

        console.log('注册响应状态:', response.status);
        let data;

        try {
            data = await response.json();
            console.log('响应数据:', data);
        } catch (jsonError) {
            console.error('解析JSON失败:', jsonError);
            throw new Error('服务器响应格式错误');
        }

        if (data.token) {
            console.log('注册成功:', data);
            // 将token和用户名存储在localStorage中
            localStorage.setItem('token', data.token);
            localStorage.setItem('username', data.username);

            // 同时将token存储在cookie中，有效期1天
            setCookie('token', data.token, 1);

            showToast('注册成功，即将跳转...', 'success');

            // 注册成功后始终跳转到组件页面（因为注册的用户都是普通用户）
            setTimeout(() => {
                window.location.href = '/pc-components.html';
            }, 1000);
        } else {
            throw new Error(data.error || '注册失败，但未返回明确错误');
        }

    } catch (error) {
        console.error('注册失败:', error);
        showToast(error.message || '注册失败', 'error');
    }
});

// 表单切换逻辑
document.getElementById('showRegister').addEventListener('click', (e) => {
    e.preventDefault();
    document.getElementById('loginForm').classList.add('hidden');
    document.getElementById('registerForm').classList.remove('hidden');
    document.getElementById('formTitle').textContent = '注册新用户';
});

document.getElementById('showLogin').addEventListener('click', (e) => {
    e.preventDefault();
    document.getElementById('registerForm').classList.add('hidden');
    document.getElementById('loginForm').classList.remove('hidden');
    document.getElementById('formTitle').textContent = '请登录以继续使用';
});

// 检查是否已登录
const token = localStorage.getItem('token') || getCookie('token');
if (token) {
    // 验证token是否有效
    fetch('/api/validate-token', {
        headers: {
            'Authorization': `Bearer ${token}`
        }
    })
        .then(response => response.json())
        .then(data => {
            if (data.valid) {
                console.log('Token有效，用户数据:', data);

                // 检查是否有重定向URL参数
                const urlParams = new URLSearchParams(window.location.search);
                const redirectUrl = urlParams.get('redirect');

                console.log('已登录用户访问登录页面的重定向逻辑:');
                console.log('- 当前URL:', window.location.href);
                console.log('- URL参数:', urlParams.toString());
                console.log('- 重定向URL:', redirectUrl);
                console.log('- 用户角色:', data.user?.role);

                if (redirectUrl && redirectUrl !== '/login.html') {
                    // 如果有重定向URL且不是登录页，跳转到指定页面
                    console.log('重定向到指定页面:', decodeURIComponent(redirectUrl));
                    window.location.href = decodeURIComponent(redirectUrl);
                } else if (data.user && data.user.role === 'admin') {
                    // 如果是管理员，跳转到主页
                    console.log('管理员用户，跳转到主页');
                    window.location.href = '/';
                } else {
                    // 如果是普通用户，跳转到组件页面
                    console.log('普通用户，跳转到组件页面');
                    window.location.href = '/pc-components.html';
                }
            } else {
                // token无效，清除
                localStorage.removeItem('token');
                localStorage.removeItem('username');
                document.cookie = "token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
            }
        })
        .catch(error => {
            console.error('验证token出错:', error);
            // 出错时清除token
            localStorage.removeItem('token');
            localStorage.removeItem('username');
            document.cookie = "token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
        });
}
